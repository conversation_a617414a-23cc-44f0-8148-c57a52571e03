{"name": "amzova-com", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "node scripts/generate-sitemap.cjs && vite build", "build:prod": "node scripts/generate-sitemap.cjs && vite build --mode production", "lint": "eslint .", "preview": "vite preview", "generate-sitemap": "node scripts/generate-sitemap.cjs", "optimize-images": "node scripts/optimize-images.cjs", "analyze-bundle": "npm run build && npx vite-bundle-analyzer dist"}, "dependencies": {"@google/generative-ai": "^0.2.1", "chart.js": "^4.5.0", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^4.1.0", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-qr-code": "^2.0.18", "react-router-dom": "^7.6.3"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/babel__core": "^7.20.5", "@types/babel__generator": "^7.27.0", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.7", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "terser": "^5.43.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "xmldom": "^0.6.0"}}