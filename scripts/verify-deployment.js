#!/usr/bin/env node

/**
 * 部署验证脚本
 * 验证Gemini代理服务器迁移后的完整功能
 */

const FRONTEND_URL = 'https://amzova.com';
const API_URL = 'https://amzova-backend-service.sellerbox.workers.dev';

async function verifyDeployment() {
  console.log('🔍 开始验证部署状态...\n');

  let allTestsPassed = true;

  // 测试1: 前端可访问性
  console.log('1️⃣ 验证前端可访问性...');
  try {
    const frontendResponse = await fetch(FRONTEND_URL);
    if (frontendResponse.ok) {
      console.log('✅ 前端网站正常访问');
      console.log('🌐 状态码:', frontendResponse.status);
    } else {
      console.error('❌ 前端网站访问异常:', frontendResponse.status);
      allTestsPassed = false;
    }
  } catch (error) {
    console.error('❌ 前端网站连接失败:', error.message);
    allTestsPassed = false;
  }

  // 测试2: API服务状态
  console.log('\n2️⃣ 验证API服务状态...');
  try {
    const apiResponse = await fetch(`${API_URL}/api/health`);
    const apiData = await apiResponse.json();
    if (apiData.status === 'ok') {
      console.log('✅ API服务正常运行');
      console.log('🔧 环境:', apiData.environment || 'production');
    } else {
      console.error('❌ API服务状态异常:', apiData);
      allTestsPassed = false;
    }
  } catch (error) {
    console.error('❌ API服务连接失败:', error.message);
    allTestsPassed = false;
  }

  // 测试3: AI服务状态
  console.log('\n3️⃣ 验证AI服务状态...');
  try {
    const aiResponse = await fetch(`${API_URL}/api/ai/health`);
    const aiData = await aiResponse.json();
    if (aiData.success && aiData.status === 'healthy') {
      console.log('✅ AI服务健康状态良好');
      console.log('🤖 提供商:', aiData.provider);
      console.log('📊 响应时间:', aiData.responseTime + 'ms');
      console.log('🔗 代理地址:', aiData.config?.baseUrl);
      
      // 验证是否使用新的代理地址
      if (aiData.config?.baseUrl === 'https://gemini-balance-c1w2.onrender.com') {
        console.log('✅ 已成功迁移到新的Gemini代理服务器');
      } else {
        console.warn('⚠️ 代理地址可能未更新:', aiData.config?.baseUrl);
      }
    } else {
      console.error('❌ AI服务状态异常:', aiData);
      allTestsPassed = false;
    }
  } catch (error) {
    console.error('❌ AI服务连接失败:', error.message);
    allTestsPassed = false;
  }

  // 测试4: 标题修复功能
  console.log('\n4️⃣ 验证标题修复功能...');
  try {
    const titleFixResponse = await fetch(`${API_URL}/api/ai/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        messages: [
          {
            role: 'system',
            content: '你是亚马逊标题合规专家。请严格按照JSON格式返回结果。'
          },
          {
            role: 'user',
            content: '请修复标题："Best Amazing Product!!!" 返回JSON格式：{"fixedTitles":["版本1","版本2","版本3"],"complianceScore":95}'
          }
        ],
        model: 'gemini-2.0-flash',
        temperature: 0.2,
        max_tokens: 500
      })
    });

    const titleFixData = await titleFixResponse.json();
    if (titleFixData.success) {
      console.log('✅ 标题修复功能正常');
      const content = titleFixData.data.choices[0].message.content;
      
      // 检查是否包含JSON格式的响应
      if (content.includes('fixedTitles') || content.includes('"')) {
        console.log('✅ AI返回格式正确');
      } else {
        console.warn('⚠️ AI返回格式可能需要优化');
      }
    } else {
      console.error('❌ 标题修复功能异常:', titleFixData.error);
      allTestsPassed = false;
    }
  } catch (error) {
    console.error('❌ 标题修复功能测试失败:', error.message);
    allTestsPassed = false;
  }

  // 测试5: 标题分析功能
  console.log('\n5️⃣ 验证标题分析功能...');
  try {
    const titleAnalysisResponse = await fetch(`${API_URL}/api/ai/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        messages: [
          {
            role: 'system',
            content: '你是亚马逊SEO专家。请严格按照JSON格式返回结果。'
          },
          {
            role: 'user',
            content: '请分析标题："Wireless Bluetooth Headphones\" 返回JSON格式：{"optimizedTitles":["优化版本1"],"improvements":["改进建议1"]}'
          }
        ],
        model: 'gemini-2.0-flash',
        temperature: 0.3,
        max_tokens: 800
      })
    });

    const titleAnalysisData = await titleAnalysisResponse.json();
    if (titleAnalysisData.success) {
      console.log('✅ 标题分析功能正常');
      const content = titleAnalysisData.data.choices[0].message.content;
      
      // 检查是否包含分析相关的内容
      if (content.includes('optimizedTitles') || content.includes('improvements') || content.includes('优化')) {
        console.log('✅ AI分析功能正确');
      } else {
        console.warn('⚠️ AI分析功能可能需要优化');
      }
    } else {
      console.error('❌ 标题分析功能异常:', titleAnalysisData.error);
      allTestsPassed = false;
    }
  } catch (error) {
    console.error('❌ 标题分析功能测试失败:', error.message);
    allTestsPassed = false;
  }

  // 总结
  console.log('\n' + '='.repeat(50));
  if (allTestsPassed) {
    console.log('🎉 部署验证成功！');
    console.log('\n✅ 验证结果:');
    console.log('- 前端网站正常访问');
    console.log('- API服务正常运行');
    console.log('- AI服务健康状态良好');
    console.log('- 标题修复功能正常');
    console.log('- 标题分析功能正常');
    console.log('- Gemini代理服务器迁移成功');
    
    console.log('\n🚀 新的配置信息:');
    console.log('- 前端地址: https://amzova.com');
    console.log('- API地址: https://amzova-backend-service.sellerbox.workers.dev');
    console.log('- Gemini代理: https://gemini-balance-c1w2.onrender.com');
    console.log('- 模型: gemini-2.0-flash');
    
    console.log('\n📋 用户可以正常使用以下功能:');
    console.log('- 标题合规修改器 (Title Fixer)');
    console.log('- 标题优化器 (Title Analyzer)');
    console.log('- 其他所有工具功能');
    
    process.exit(0);
  } else {
    console.log('❌ 部署验证失败！');
    console.log('\n请检查以上错误信息并修复问题。');
    process.exit(1);
  }
}

// 运行验证
verifyDeployment().catch(error => {
  console.error('💥 验证过程中发生错误:', error);
  process.exit(1);
});