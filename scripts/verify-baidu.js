#!/usr/bin/env node

/**
 * 百度验证文件检查脚本
 */

import https from 'https';

const VERIFICATION_URL = 'https://amzova.com/baidu_verify_codeva-U1LCnWiNN1.html';
const VERIFICATION_URL_NO_EXT = 'https://amzova.com/baidu_verify_codeva-U1LCnWiNN1';
const EXPECTED_CONTENT = '61fc4060742adc29c4340c8a058226d3';

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 获取页面内容
function fetchPage(url) {
  return new Promise((resolve, reject) => {
    https.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data.trim()
        });
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

async function verifyBaiduFile() {
  log('🔍 检查百度验证文件...', 'bold');
  
  // 检查 HTML 文件
  log(`📍 检查 HTML 文件: ${VERIFICATION_URL}`, 'blue');
  
  try {
    const response = await fetchPage(VERIFICATION_URL);
    
    log(`📊 HTTP 状态码: ${response.statusCode}`, response.statusCode === 200 ? 'green' : 'red');
    
    if (response.statusCode === 200) {
      log(`📄 文件内容: "${response.body}"`, 'blue');
      
      if (response.body === EXPECTED_CONTENT) {
        log('✅ 百度验证文件内容正确！', 'green');
      } else {
        log('❌ 文件内容不匹配', 'red');
        log(`期望内容: "${EXPECTED_CONTENT}"`, 'yellow');
        log(`实际内容: "${response.body}"`, 'yellow');
      }
    } else {
      log('❌ HTML 验证文件无法访问', 'red');
      
      if (response.statusCode >= 300 && response.statusCode < 400) {
        log(`重定向到: ${response.headers.location}`, 'yellow');
      }
    }
    
  } catch (error) {
    log(`❌ HTML 文件检查失败: ${error.message}`, 'red');
  }
  
  // 检查无扩展名文件
  log(`\n📍 检查无扩展名文件: ${VERIFICATION_URL_NO_EXT}`, 'blue');
  
  try {
    const response = await fetchPage(VERIFICATION_URL_NO_EXT);
    
    log(`📊 HTTP 状态码: ${response.statusCode}`, response.statusCode === 200 ? 'green' : 'red');
    
    if (response.statusCode === 200) {
      log(`📄 文件内容: "${response.body}"`, 'blue');
      
      if (response.body === EXPECTED_CONTENT) {
        log('✅ 无扩展名验证文件内容正确！', 'green');
        log('💡 可以使用此 URL 进行百度验证', 'green');
      } else {
        log('❌ 文件内容不匹配', 'red');
      }
    } else {
      log('❌ 无扩展名验证文件无法访问', 'red');
    }
    
  } catch (error) {
    log(`❌ 无扩展名文件检查失败: ${error.message}`, 'red');
  }
}

// 检查 Meta 标签验证
async function checkMetaVerification() {
  log('\n🔍 检查 Meta 标签验证...', 'bold');
  
  try {
    const response = await fetchPage('https://amzova.com/');
    
    if (response.statusCode === 200) {
      const metaMatch = response.body.match(/<meta[^>]*name=["\']baidu-site-verification["\'][^>]*content=["\']([^"\']+)["\'][^>]*>/i);
      
      if (metaMatch && metaMatch[1]) {
        const metaCode = metaMatch[1];
        log(`✅ 找到百度 Meta 验证标签: ${metaCode}`, 'green');
        
        if (metaCode === 'codeva-U1LCnWiNN1') {
          log('✅ Meta 验证码正确！', 'green');
        } else {
          log('⚠️ Meta 验证码与文件验证码不匹配', 'yellow');
        }
      } else {
        log('❌ 未找到百度 Meta 验证标签', 'red');
      }
    } else {
      log('❌ 无法访问首页检查 Meta 标签', 'red');
    }
    
  } catch (error) {
    log(`❌ Meta 标签检查失败: ${error.message}`, 'red');
  }
}

// 主函数
async function main() {
  await verifyBaiduFile();
  await checkMetaVerification();
  
  log('\n📋 百度搜索资源平台验证步骤:', 'bold');
  log('1. 访问: https://ziyuan.baidu.com/', 'blue');
  log('2. 添加网站: https://amzova.com', 'blue');
  log('3. 选择验证方法: 文件验证', 'blue');
  log('4. 上传验证文件: baidu_verify_codeva-U1LCnWiNN1.html', 'blue');
  log('5. 点击完成验证', 'blue');
  log('6. 验证成功后提交 Sitemap: https://amzova.com/sitemap.xml', 'blue');
  
  log('\n📚 更多信息请查看 SEO_SUBMISSION_GUIDE.md', 'blue');
}

main().catch(error => {
  log(`❌ 验证失败: ${error.message}`, 'red');
  process.exit(1);
});
