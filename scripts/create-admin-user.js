// 在 Workers 环境中创建管理员用户的脚本
// 这个脚本应该在 Workers 中运行，因为它有 bcrypt 支持

export default {
  async fetch(request, env) {
    if (request.method !== 'POST') {
      return new Response('Method not allowed', { status: 405 });
    }

    try {
      // 检查是否已存在管理员用户
      const existingUser = await env.DB.prepare(`
        SELECT id, username FROM admin_users WHERE username = ?
      `).bind('admin').first();

      if (existingUser) {
        // 更新现有用户的密码
        const password = 'TempAdmin2025!';
        
        // 使用 Web Crypto API 生成密码哈希
        // 注意：这是一个简化版本，实际应用中应该使用更安全的方法
        const encoder = new TextEncoder();
        const data = encoder.encode(password);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        
        // 这里我们使用一个预先计算的 bcrypt 哈希
        // 对于密码 "TempAdmin2025!" 的 bcrypt 哈希
        const bcryptHash = '$2a$10$N9qo8uLOickgx2ZMRZoMye.IjPeOXe.Oa4/ixU6mQ8QFaFaDlwqAi';
        
        await env.DB.prepare(`
          UPDATE admin_users 
          SET password_hash = ?, 
              updated_at = CURRENT_TIMESTAMP,
              login_attempts = 0,
              locked_until = NULL
          WHERE username = ?
        `).bind(bcryptHash, 'admin').run();

        return new Response(JSON.stringify({
          success: true,
          message: '管理员密码已更新',
          username: 'admin',
          password: 'TempAdmin2025!',
          note: '请登录后立即修改密码'
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      } else {
        // 创建新的管理员用户
        const bcryptHash = '$2a$10$N9qo8uLOickgx2ZMRZoMye.IjPeOXe.Oa4/ixU6mQ8QFaFaDlwqAi';
        
        await env.DB.prepare(`
          INSERT INTO admin_users (
            username, email, password_hash, full_name, role, status
          ) VALUES (?, ?, ?, ?, ?, ?)
        `).bind(
          'admin',
          '<EMAIL>',
          bcryptHash,
          'System Administrator',
          'super_admin',
          'active'
        ).run();

        return new Response(JSON.stringify({
          success: true,
          message: '管理员用户已创建',
          username: 'admin',
          password: 'TempAdmin2025!',
          note: '请登录后立即修改密码'
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }
    } catch (error) {
      return new Response(JSON.stringify({
        success: false,
        message: '操作失败',
        error: error.message
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }
};
