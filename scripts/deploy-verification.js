#!/usr/bin/env node

/**
 * 部署验证脚本
 * 验证AI服务迁移后的生产环境部署
 */

import https from 'https';

const API_BASE_URL = 'https://api.amzova.com';

/**
 * 发送HTTP请求
 */
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'DeploymentVerification/1.0',
        ...options.headers
      }
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: data
          });
        }
      });
    });

    req.on('error', reject);

    if (options.body) {
      req.write(JSON.stringify(options.body));
    }

    req.end();
  });
}

/**
 * 验证API健康状态
 */
async function verifyAPIHealth() {
  console.log('🔍 验证API健康状态...');
  
  try {
    const response = await makeRequest(`${API_BASE_URL}/api/health`);
    
    if (response.status === 200 && response.data.status === 'ok') {
      console.log('✅ API健康检查通过');
      return true;
    } else {
      console.log('❌ API健康检查失败:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ API健康检查失败:', error.message);
    return false;
  }
}

/**
 * 验证AI服务健康状态
 */
async function verifyAIHealth() {
  console.log('🤖 验证AI服务健康状态...');
  
  try {
    const response = await makeRequest(`${API_BASE_URL}/api/ai/health`);
    
    if (response.status === 200 && response.data.success && response.data.status === 'healthy') {
      console.log('✅ AI服务健康检查通过');
      console.log(`   - 提供商: ${response.data.provider}`);
      console.log(`   - 模型: ${response.data.model}`);
      console.log(`   - 响应时间: ${response.data.responseTime}ms`);
      return true;
    } else {
      console.log('❌ AI服务健康检查失败:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ AI服务健康检查失败:', error.message);
    return false;
  }
}

/**
 * 验证AI聊天功能
 */
async function verifyAIChat() {
  console.log('💬 验证AI聊天功能...');
  
  const testRequest = {
    messages: [
      {
        role: 'user',
        content: 'Hello, please respond with "AI service is working" to confirm the service is operational.'
      }
    ],
    model: 'gemini-2.0-flash',
    temperature: 0.1,
    max_tokens: 20
  };

  try {
    const startTime = Date.now();
    const response = await makeRequest(`${API_BASE_URL}/api/ai/chat`, {
      method: 'POST',
      body: testRequest
    });
    const responseTime = Date.now() - startTime;

    if (response.status === 200 && response.data.success) {
      console.log('✅ AI聊天功能正常');
      console.log(`   - 响应时间: ${responseTime}ms`);
      console.log(`   - 提供商: ${response.data.provider}`);
      
      // 检查响应内容
      const aiResponse = response.data.data?.choices?.[0]?.message?.content || '';
      if (aiResponse.toLowerCase().includes('working')) {
        console.log('✅ AI响应内容正确');
        return true;
      } else {
        console.log('⚠️  AI响应内容异常:', aiResponse);
        return false;
      }
    } else {
      console.log('❌ AI聊天功能失败:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ AI聊天功能失败:', error.message);
    return false;
  }
}

/**
 * 验证标题修复功能
 */
async function verifyTitleFix() {
  console.log('🔧 验证标题修复功能...');
  
  const testRequest = {
    messages: [
      {
        role: 'system',
        content: '你是专业的亚马逊标题合规专家。请严格按照JSON格式返回结果，不要添加任何额外的文字或代码块标记。'
      },
      {
        role: 'user',
        content: `请修复以下标题：

原标题: "Amazing! Bluetooth Headphones $ Best Quality"
错误信息: "Title contains prohibited special characters"

请返回JSON格式：
{
  "fixedTitles": ["修复版本1", "修复版本2", "修复版本3"],
  "complianceScore": 95
}`
      }
    ],
    model: 'gemini-2.0-flash',
    temperature: 0.2,
    max_tokens: 1000
  };

  try {
    const startTime = Date.now();
    const response = await makeRequest(`${API_BASE_URL}/api/ai/chat`, {
      method: 'POST',
      body: testRequest
    });
    const responseTime = Date.now() - startTime;

    if (response.status === 200 && response.data.success) {
      console.log('✅ 标题修复API调用成功');
      console.log(`   - 响应时间: ${responseTime}ms`);
      
      // 尝试解析AI响应
      const aiResponse = response.data.data?.choices?.[0]?.message?.content || '';
      try {
        // 尝试提取JSON
        const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          const result = JSON.parse(jsonMatch[0]);
          if (result.fixedTitles && Array.isArray(result.fixedTitles) && result.fixedTitles.length >= 3) {
            console.log('✅ 标题修复功能正常');
            console.log(`   - 生成了${result.fixedTitles.length}个修复版本`);
            console.log(`   - 合规评分: ${result.complianceScore || 'N/A'}`);
            return true;
          } else {
            console.log('⚠️  标题修复结果格式异常');
            return false;
          }
        } else {
          console.log('⚠️  无法从AI响应中提取JSON');
          return false;
        }
      } catch (parseError) {
        console.log('⚠️  AI响应JSON解析失败:', parseError.message);
        return false;
      }
    } else {
      console.log('❌ 标题修复功能失败:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ 标题修复功能失败:', error.message);
    return false;
  }
}

/**
 * 验证数据库连接
 */
async function verifyDatabase() {
  console.log('🗄️  验证数据库连接...');
  
  try {
    const response = await makeRequest(`${API_BASE_URL}/api/db-test`);
    
    if (response.status === 200 && response.data.status === 'ok') {
      console.log('✅ 数据库连接正常');
      return true;
    } else {
      console.log('❌ 数据库连接失败:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ 数据库连接失败:', error.message);
    return false;
  }
}

/**
 * 主验证流程
 */
async function runVerification() {
  console.log('🚀 开始生产环境部署验证');
  console.log('='.repeat(60));
  console.log('');

  const results = {
    apiHealth: false,
    aiHealth: false,
    aiChat: false,
    titleFix: false,
    database: false
  };

  // 1. API健康检查
  results.apiHealth = await verifyAPIHealth();
  console.log('');

  // 2. AI服务健康检查
  results.aiHealth = await verifyAIHealth();
  console.log('');

  // 3. AI聊天功能测试
  results.aiChat = await verifyAIChat();
  console.log('');

  // 4. 标题修复功能测试
  results.titleFix = await verifyTitleFix();
  console.log('');

  // 5. 数据库连接测试
  results.database = await verifyDatabase();
  console.log('');

  // 汇总结果
  console.log('📊 验证结果汇总');
  console.log('='.repeat(60));
  console.log(`API健康检查:     ${results.apiHealth ? '✅ 通过' : '❌ 失败'}`);
  console.log(`AI服务健康检查:  ${results.aiHealth ? '✅ 通过' : '❌ 失败'}`);
  console.log(`AI聊天功能:      ${results.aiChat ? '✅ 通过' : '❌ 失败'}`);
  console.log(`标题修复功能:    ${results.titleFix ? '✅ 通过' : '❌ 失败'}`);
  console.log(`数据库连接:      ${results.database ? '✅ 通过' : '❌ 失败'}`);
  console.log('');

  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;

  if (passedTests === totalTests) {
    console.log('🎉 所有验证通过！AI服务迁移部署成功！');
    console.log('');
    console.log('✅ Gemini代理服务正常运行');
    console.log('✅ 标题分析和修复功能可用');
    console.log('✅ 系统整体运行稳定');
    process.exit(0);
  } else {
    console.log(`⚠️  验证完成: ${passedTests}/${totalTests} 项通过`);
    console.log('');
    console.log('❌ 部分功能存在问题，请检查：');
    if (!results.apiHealth) console.log('   - API服务状态');
    if (!results.aiHealth) console.log('   - AI服务配置');
    if (!results.aiChat) console.log('   - AI聊天功能');
    if (!results.titleFix) console.log('   - 标题修复功能');
    if (!results.database) console.log('   - 数据库连接');
    process.exit(1);
  }
}

// 运行验证
runVerification().catch(error => {
  console.error('💥 验证过程发生错误:', error);
  process.exit(1);
});