-- SellerBox D1 数据库初始化脚本
-- 用于全新部署到 Cloudflare D1 (SQLite)
-- 执行命令: wrangler d1 execute sellerbox-production --file=./scripts/init-d1-database.sql

-- ==========================================
-- 用户反馈表
-- ==========================================
CREATE TABLE IF NOT EXISTS user_feedback (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    feedback_type TEXT CHECK(feedback_type IN ('general', 'bug', 'feature', 'improvement')) NOT NULL DEFAULT 'general',
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    message TEXT NOT NULL,
    email TEXT DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    ip_address TEXT DEFAULT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    status TEXT CHECK(status IN ('new', 'in_progress', 'resolved', 'closed')) DEFAULT 'new',
    admin_notes TEXT DEFAULT NULL
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_feedback_type ON user_feedback(feedback_type);
CREATE INDEX IF NOT EXISTS idx_feedback_rating ON user_feedback(rating);
CREATE INDEX IF NOT EXISTS idx_feedback_created_at ON user_feedback(created_at);
CREATE INDEX IF NOT EXISTS idx_feedback_status ON user_feedback(status);

-- ==========================================
-- 汇率数据表
-- ==========================================
CREATE TABLE IF NOT EXISTS exchange_rates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    base_currency TEXT NOT NULL DEFAULT 'USD',
    target_currency TEXT NOT NULL,
    rate REAL NOT NULL,
    api_source TEXT DEFAULT 'freecurrencyapi',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_exchange_currency_pair ON exchange_rates(base_currency, target_currency);
CREATE INDEX IF NOT EXISTS idx_exchange_created_at ON exchange_rates(created_at);
CREATE INDEX IF NOT EXISTS idx_exchange_target_currency ON exchange_rates(target_currency);

-- ==========================================
-- 管理员用户表
-- ==========================================
CREATE TABLE IF NOT EXISTS admin_users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT NOT NULL UNIQUE,
    email TEXT NOT NULL UNIQUE,
    password_hash TEXT NOT NULL,
    full_name TEXT DEFAULT NULL,
    role TEXT CHECK(role IN ('super_admin', 'admin', 'viewer')) DEFAULT 'admin',
    status TEXT CHECK(status IN ('active', 'inactive', 'locked')) DEFAULT 'active',
    last_login DATETIME NULL DEFAULT NULL,
    login_attempts INTEGER DEFAULT 0,
    locked_until DATETIME NULL DEFAULT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_admin_username ON admin_users(username);
CREATE INDEX IF NOT EXISTS idx_admin_email ON admin_users(email);
CREATE INDEX IF NOT EXISTS idx_admin_status ON admin_users(status);

-- ==========================================
-- 管理员会话表
-- ==========================================
CREATE TABLE IF NOT EXISTS admin_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    token_hash TEXT NOT NULL,
    refresh_token_hash TEXT DEFAULT NULL,
    ip_address TEXT DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    expires_at DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_used DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_session_user_id ON admin_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_session_token_hash ON admin_sessions(token_hash);
CREATE INDEX IF NOT EXISTS idx_session_expires_at ON admin_sessions(expires_at);

-- ==========================================
-- 操作日志表
-- ==========================================
CREATE TABLE IF NOT EXISTS admin_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER DEFAULT NULL,
    action TEXT NOT NULL,
    resource TEXT DEFAULT NULL,
    resource_id INTEGER DEFAULT NULL,
    details TEXT DEFAULT NULL, -- JSON格式
    ip_address TEXT DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_log_user_id ON admin_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_log_action ON admin_logs(action);
CREATE INDEX IF NOT EXISTS idx_log_created_at ON admin_logs(created_at);

-- ==========================================
-- 系统日志表 (用于定时任务等系统操作)
-- ==========================================
CREATE TABLE IF NOT EXISTS system_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    level TEXT CHECK(level IN ('debug', 'info', 'warn', 'error')) DEFAULT 'info',
    message TEXT NOT NULL,
    data TEXT DEFAULT NULL, -- JSON格式的额外数据
    source TEXT DEFAULT NULL, -- 日志来源 (如 'cron', 'api', 'system')
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_system_log_level ON system_logs(level);
CREATE INDEX IF NOT EXISTS idx_system_log_source ON system_logs(source);
CREATE INDEX IF NOT EXISTS idx_system_log_created_at ON system_logs(created_at);

-- ==========================================
-- 系统公告表
-- ==========================================
CREATE TABLE IF NOT EXISTS system_announcements (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    type TEXT CHECK(type IN ('info', 'warning', 'success', 'error', 'maintenance')) DEFAULT 'info',
    priority INTEGER DEFAULT 0,
    status TEXT CHECK(status IN ('draft', 'active', 'inactive', 'expired')) DEFAULT 'draft',
    start_time DATETIME NULL DEFAULT NULL,
    end_time DATETIME NULL DEFAULT NULL,
    target_audience TEXT CHECK(target_audience IN ('all', 'admin', 'user')) DEFAULT 'all',
    display_position TEXT CHECK(display_position IN ('header', 'banner', 'popup')) DEFAULT 'header',
    is_closable INTEGER DEFAULT 1, -- SQLite 使用 INTEGER 代替 BOOLEAN
    click_action TEXT DEFAULT NULL,
    view_count INTEGER DEFAULT 0,
    created_by INTEGER DEFAULT NULL,
    updated_by INTEGER DEFAULT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_announcement_status ON system_announcements(status);
CREATE INDEX IF NOT EXISTS idx_announcement_priority ON system_announcements(priority);
CREATE INDEX IF NOT EXISTS idx_announcement_type ON system_announcements(type);
CREATE INDEX IF NOT EXISTS idx_announcement_start_time ON system_announcements(start_time);
CREATE INDEX IF NOT EXISTS idx_announcement_end_time ON system_announcements(end_time);

-- ==========================================
-- 公告多语言支持表
-- ==========================================
CREATE TABLE IF NOT EXISTS announcement_translations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    announcement_id INTEGER NOT NULL,
    language_code TEXT NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (announcement_id) REFERENCES system_announcements(id) ON DELETE CASCADE
);

-- 创建索引和唯一约束
CREATE UNIQUE INDEX IF NOT EXISTS idx_announcement_translation_unique ON announcement_translations(announcement_id, language_code);
CREATE INDEX IF NOT EXISTS idx_announcement_translation_language ON announcement_translations(language_code);

-- ==========================================
-- 工具使用统计表
-- ==========================================
CREATE TABLE IF NOT EXISTS tool_usage (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    tool_id TEXT NOT NULL,
    tool_name TEXT NOT NULL,
    user_ip TEXT DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    session_id TEXT DEFAULT NULL,
    usage_type TEXT CHECK(usage_type IN ('view', 'use', 'calculate')) DEFAULT 'view',
    usage_data TEXT DEFAULT NULL, -- JSON格式
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_tool_usage_tool_id ON tool_usage(tool_id);
CREATE INDEX IF NOT EXISTS idx_tool_usage_created_at ON tool_usage(created_at);
CREATE INDEX IF NOT EXISTS idx_tool_usage_type ON tool_usage(usage_type);
CREATE INDEX IF NOT EXISTS idx_tool_usage_user_ip ON tool_usage(user_ip);

-- ==========================================
-- 页面访问统计表
-- ==========================================
CREATE TABLE IF NOT EXISTS page_views (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    page_path TEXT NOT NULL,
    page_title TEXT DEFAULT NULL,
    user_ip TEXT DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    session_id TEXT DEFAULT NULL,
    referrer TEXT DEFAULT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_page_views_path ON page_views(page_path);
CREATE INDEX IF NOT EXISTS idx_page_views_created_at ON page_views(created_at);
CREATE INDEX IF NOT EXISTS idx_page_views_session_id ON page_views(session_id);

-- ==========================================
-- 用户会话表
-- ==========================================
CREATE TABLE IF NOT EXISTS user_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT NOT NULL UNIQUE,
    user_ip TEXT DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    country TEXT DEFAULT NULL,
    city TEXT DEFAULT NULL,
    first_visit DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_visit DATETIME DEFAULT CURRENT_TIMESTAMP,
    page_views INTEGER DEFAULT 0,
    tool_uses INTEGER DEFAULT 0
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_user_sessions_session_id ON user_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_first_visit ON user_sessions(first_visit);
CREATE INDEX IF NOT EXISTS idx_user_sessions_last_visit ON user_sessions(last_visit);

-- ==========================================
-- 插入初始数据
-- ==========================================

-- 插入默认管理员账户
-- 用户名: admin
-- 密码: TempAdmin2025! (请部署后立即修改)
-- 密码哈希使用 bcrypt 生成
INSERT OR IGNORE INTO admin_users (
    username,
    email,
    password_hash,
    full_name,
    role,
    status
) VALUES (
    'admin',
    '<EMAIL>',
    '$2a$10$N9qo8uLOickgx2ZMRZoMye.IjPeOXe.Oa4/ixU6mQ8QFaFaDlwqAi', -- TempAdmin2025! 的 bcrypt 哈希
    'System Administrator',
    'super_admin',
    'active'
);

-- 插入示例汇率数据
INSERT OR IGNORE INTO exchange_rates (base_currency, target_currency, rate, api_source) VALUES
('USD', 'CNY', 7.2345, 'freecurrencyapi'),
('USD', 'EUR', 0.8567, 'freecurrencyapi'),
('USD', 'GBP', 0.7834, 'freecurrencyapi'),
('USD', 'JPY', 149.56, 'freecurrencyapi'),
('USD', 'CAD', 1.3456, 'freecurrencyapi'),
('USD', 'AUD', 1.4567, 'freecurrencyapi');

-- 插入示例工具使用数据
INSERT OR IGNORE INTO tool_usage (tool_id, tool_name, usage_type, usage_data) VALUES
('title-fixer', '标题修改器', 'use', '{"input_length": 125, "output_count": 3}'),
('currency-converter', '货币换算器', 'calculate', '{"from": "USD", "to": "CNY", "amount": 100}'),
('unit-converter', '单位换算器', 'calculate', '{"from": "kg", "to": "lb", "value": 10}'),
('fba-calculator', 'FBA计算器', 'calculate', '{"product_price": 29.99, "category": "electronics"}'),
('title-analyzer', '标题分析器', 'use', '{"title_length": 80, "keyword_count": 5}'),
('profit-calculator', '利润计算器', 'calculate', '{"revenue": 1000, "costs": 600}');

-- ==========================================
-- 数据库初始化完成
-- ==========================================
