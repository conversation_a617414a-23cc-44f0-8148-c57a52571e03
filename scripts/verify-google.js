#!/usr/bin/env node

/**
 * Google 验证文件检查脚本
 */

import https from 'https';

const VERIFICATION_URL = 'https://amzova.com/googlea175af350543cbcc.html';
const EXPECTED_CONTENT = 'google-site-verification: googlea175af350543cbcc.html';

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 获取页面内容
function fetchPage(url) {
  return new Promise((resolve, reject) => {
    https.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data.trim()
        });
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

async function verifyGoogleFile() {
  log('🔍 检查 Google 验证文件...', 'bold');
  log(`📍 URL: ${VERIFICATION_URL}`, 'blue');
  
  try {
    const response = await fetchPage(VERIFICATION_URL);
    
    log(`📊 HTTP 状态码: ${response.statusCode}`, response.statusCode === 200 ? 'green' : 'red');
    
    if (response.statusCode === 200) {
      log(`📄 文件内容: "${response.body}"`, 'blue');
      
      if (response.body === EXPECTED_CONTENT) {
        log('✅ Google 验证文件内容正确！', 'green');
        log('🎉 可以在 Google Search Console 中进行验证了', 'green');
        
        // 提供验证步骤
        log('\n📋 下一步操作:', 'bold');
        log('1. 访问 Google Search Console: https://search.google.com/search-console/', 'blue');
        log('2. 添加资源: https://amzova.com', 'blue');
        log('3. 选择验证方法: HTML 文件', 'blue');
        log('4. 点击验证按钮', 'blue');
        log('5. 验证成功后提交 Sitemap: https://amzova.com/sitemap.xml', 'blue');
        
      } else {
        log('❌ 文件内容不匹配', 'red');
        log(`期望内容: "${EXPECTED_CONTENT}"`, 'yellow');
        log(`实际内容: "${response.body}"`, 'yellow');
      }
    } else {
      log('❌ 验证文件无法访问', 'red');
      
      if (response.statusCode === 404) {
        log('💡 建议: 检查文件是否正确上传到 public 目录', 'yellow');
      } else if (response.statusCode >= 300 && response.statusCode < 400) {
        log('💡 建议: 检查是否有重定向问题', 'yellow');
        log(`重定向到: ${response.headers.location}`, 'yellow');
      }
    }
    
  } catch (error) {
    log(`❌ 检查失败: ${error.message}`, 'red');
  }
}

// 同时检查 meta 标签验证
async function checkMetaVerification() {
  log('\n🔍 检查 Meta 标签验证...', 'bold');
  
  try {
    const response = await fetchPage('https://amzova.com/');
    
    if (response.statusCode === 200) {
      const metaMatch = response.body.match(/<meta[^>]*name=["\']google-site-verification["\'][^>]*content=["\']([^"\']+)["\'][^>]*>/i);
      
      if (metaMatch && metaMatch[1]) {
        const metaCode = metaMatch[1];
        log(`✅ 找到 Meta 验证标签: ${metaCode}`, 'green');
        
        if (metaCode === 'a175af350543cbcc') {
          log('✅ Meta 验证码正确！', 'green');
        } else {
          log('⚠️ Meta 验证码与文件验证码不匹配', 'yellow');
        }
      } else {
        log('❌ 未找到 Meta 验证标签', 'red');
      }
    } else {
      log('❌ 无法访问首页检查 Meta 标签', 'red');
    }
    
  } catch (error) {
    log(`❌ Meta 标签检查失败: ${error.message}`, 'red');
  }
}

// 主函数
async function main() {
  await verifyGoogleFile();
  await checkMetaVerification();
  
  log('\n📚 更多信息请查看 SEO_SUBMISSION_GUIDE.md', 'blue');
}

main().catch(error => {
  log(`❌ 验证失败: ${error.message}`, 'red');
  process.exit(1);
});
