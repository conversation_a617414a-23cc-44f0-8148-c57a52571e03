#!/usr/bin/env node

/**
 * SEO 检查脚本
 * 检查网站的 SEO 配置是否正确
 */

import https from 'https';
import http from 'http';

const SITE_URL = 'https://amzova.com';
const PAGES_TO_CHECK = [
  '/',
  '/tools',
  '/tools/title-analyzer',
  '/tools/currency-converter',
  '/tools/profit-calculator',
  '/feedback'
];

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 获取页面内容
function fetchPage(url) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https:') ? https : http;
    
    client.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data
        });
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

// 检查页面 SEO
function checkPageSEO(url, response) {
  const { statusCode, headers, body } = response;
  const results = {
    url,
    statusCode,
    issues: [],
    warnings: [],
    passed: []
  };

  // 检查状态码
  if (statusCode === 200) {
    results.passed.push('✅ HTTP 状态码 200');
  } else {
    results.issues.push(`❌ HTTP 状态码: ${statusCode}`);
  }

  // 检查 HTTPS
  if (url.startsWith('https://')) {
    results.passed.push('✅ 使用 HTTPS');
  } else {
    results.issues.push('❌ 未使用 HTTPS');
  }

  // 检查 Content-Type
  const contentType = headers['content-type'];
  if (contentType && contentType.includes('text/html')) {
    results.passed.push('✅ Content-Type 正确');
  } else {
    results.issues.push(`❌ Content-Type: ${contentType}`);
  }

  // 检查 HTML 内容
  if (body) {
    // 检查 title 标签
    const titleMatch = body.match(/<title[^>]*>([^<]+)<\/title>/i);
    if (titleMatch && titleMatch[1].trim()) {
      const title = titleMatch[1].trim();
      results.passed.push(`✅ 页面标题: ${title.substring(0, 60)}${title.length > 60 ? '...' : ''}`);
      
      if (title.length > 60) {
        results.warnings.push('⚠️ 标题可能过长 (>60字符)');
      }
    } else {
      results.issues.push('❌ 缺少页面标题');
    }

    // 检查 meta description
    const descMatch = body.match(/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']+)["\'][^>]*>/i);
    if (descMatch && descMatch[1].trim()) {
      const desc = descMatch[1].trim();
      results.passed.push(`✅ Meta 描述: ${desc.substring(0, 80)}${desc.length > 80 ? '...' : ''}`);
      
      if (desc.length > 160) {
        results.warnings.push('⚠️ Meta 描述可能过长 (>160字符)');
      }
    } else {
      results.issues.push('❌ 缺少 Meta 描述');
    }

    // 检查 H1 标签
    const h1Match = body.match(/<h1[^>]*>([^<]+)<\/h1>/i);
    if (h1Match && h1Match[1].trim()) {
      results.passed.push(`✅ H1 标签: ${h1Match[1].trim().substring(0, 50)}...`);
    } else {
      results.issues.push('❌ 缺少 H1 标签');
    }

    // 检查 meta viewport
    if (body.includes('name="viewport"')) {
      results.passed.push('✅ 移动端适配 (viewport)');
    } else {
      results.issues.push('❌ 缺少移动端适配');
    }

    // 检查 Open Graph
    if (body.includes('property="og:title"')) {
      results.passed.push('✅ Open Graph 标签');
    } else {
      results.warnings.push('⚠️ 缺少 Open Graph 标签');
    }

    // 检查结构化数据
    if (body.includes('application/ld+json')) {
      results.passed.push('✅ 结构化数据 (JSON-LD)');
    } else {
      results.warnings.push('⚠️ 缺少结构化数据');
    }

    // 检查 hreflang
    if (body.includes('hreflang=')) {
      results.passed.push('✅ 多语言支持 (hreflang)');
    } else {
      results.warnings.push('⚠️ 缺少多语言标签');
    }

    // 检查 canonical
    if (body.includes('rel="canonical"')) {
      results.passed.push('✅ Canonical 标签');
    } else {
      results.warnings.push('⚠️ 缺少 Canonical 标签');
    }
  }

  return results;
}

// 检查 robots.txt
async function checkRobotsTxt() {
  log('\n📋 检查 robots.txt...', 'blue');
  
  try {
    const response = await fetchPage(`${SITE_URL}/robots.txt`);
    
    if (response.statusCode === 200) {
      log('✅ robots.txt 可访问', 'green');
      
      if (response.body.includes('Sitemap:')) {
        log('✅ robots.txt 包含 Sitemap 声明', 'green');
      } else {
        log('⚠️ robots.txt 缺少 Sitemap 声明', 'yellow');
      }
      
      if (response.body.includes('User-agent:')) {
        log('✅ robots.txt 包含 User-agent 规则', 'green');
      } else {
        log('⚠️ robots.txt 缺少 User-agent 规则', 'yellow');
      }
    } else {
      log(`❌ robots.txt 不可访问 (${response.statusCode})`, 'red');
    }
  } catch (error) {
    log(`❌ robots.txt 检查失败: ${error.message}`, 'red');
  }
}

// 检查 sitemap.xml
async function checkSitemap() {
  log('\n🗺️ 检查 sitemap.xml...', 'blue');
  
  try {
    const response = await fetchPage(`${SITE_URL}/sitemap.xml`);
    
    if (response.statusCode === 200) {
      log('✅ sitemap.xml 可访问', 'green');
      
      const urlCount = (response.body.match(/<url>/g) || []).length;
      log(`✅ Sitemap 包含 ${urlCount} 个 URL`, 'green');
      
      if (response.body.includes('<lastmod>')) {
        log('✅ Sitemap 包含更新时间', 'green');
      } else {
        log('⚠️ Sitemap 缺少更新时间', 'yellow');
      }
      
      if (response.body.includes('<priority>')) {
        log('✅ Sitemap 包含优先级设置', 'green');
      } else {
        log('⚠️ Sitemap 缺少优先级设置', 'yellow');
      }
    } else {
      log(`❌ sitemap.xml 不可访问 (${response.statusCode})`, 'red');
    }
  } catch (error) {
    log(`❌ sitemap.xml 检查失败: ${error.message}`, 'red');
  }
}

// 主函数
async function main() {
  log('🔍 开始 SEO 检查...', 'bold');
  log(`🌐 网站: ${SITE_URL}`, 'blue');
  
  // 检查 robots.txt 和 sitemap.xml
  await checkRobotsTxt();
  await checkSitemap();
  
  // 检查各个页面
  log('\n📄 检查页面 SEO...', 'blue');
  
  let totalIssues = 0;
  let totalWarnings = 0;
  let totalPassed = 0;
  
  for (const page of PAGES_TO_CHECK) {
    const url = `${SITE_URL}${page}`;
    log(`\n🔍 检查: ${url}`, 'blue');
    
    try {
      const response = await fetchPage(url);
      const results = checkPageSEO(url, response);
      
      // 显示结果
      results.passed.forEach(item => log(`  ${item}`, 'green'));
      results.warnings.forEach(item => log(`  ${item}`, 'yellow'));
      results.issues.forEach(item => log(`  ${item}`, 'red'));
      
      totalPassed += results.passed.length;
      totalWarnings += results.warnings.length;
      totalIssues += results.issues.length;
      
    } catch (error) {
      log(`  ❌ 页面检查失败: ${error.message}`, 'red');
      totalIssues++;
    }
  }
  
  // 总结
  log('\n📊 SEO 检查总结', 'bold');
  log(`✅ 通过项目: ${totalPassed}`, 'green');
  log(`⚠️ 警告项目: ${totalWarnings}`, 'yellow');
  log(`❌ 问题项目: ${totalIssues}`, 'red');
  
  if (totalIssues === 0) {
    log('\n🎉 恭喜！所有 SEO 检查都通过了！', 'green');
  } else if (totalIssues < 5) {
    log('\n👍 SEO 配置良好，有少量问题需要修复', 'yellow');
  } else {
    log('\n⚠️ 发现较多 SEO 问题，建议优先修复', 'red');
  }
  
  log('\n📚 更多 SEO 优化建议，请查看 SEO_SUBMISSION_GUIDE.md', 'blue');
}

// 运行检查
main().catch(error => {
  log(`❌ SEO 检查失败: ${error.message}`, 'red');
  process.exit(1);
});
