#!/bin/bash

# SellerBox 生产环境备份脚本
# 用途: 自动备份数据库和重要配置文件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
BACKUP_DIR="/opt/sellerbox-backups"
CONFIG_BACKUP_DIR="$BACKUP_DIR/configs"
DB_BACKUP_DIR="$BACKUP_DIR/database"
RETENTION_DAYS=30

# 加载环境变量
if [[ -f ".env.production" ]]; then
    set -a
    source <(grep -v '^#' .env.production | grep -v '^$' | grep '=')
    set +a
fi

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建备份目录
create_backup_dirs() {
    log_info "创建备份目录..."
    
    sudo mkdir -p "$BACKUP_DIR" "$CONFIG_BACKUP_DIR" "$DB_BACKUP_DIR"
    sudo chown -R $USER:$USER "$BACKUP_DIR"
    
    log_success "备份目录创建完成"
}

# 备份数据库
backup_database() {
    log_info "开始备份数据库..."
    
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_file="$DB_BACKUP_DIR/sellerbox_db_$timestamp.sql"
    
    # 使用docker exec备份数据库
    if docker-compose -f docker-compose.prod.yml --env-file .env.production exec -T mysql \
        mysqldump -u root -p"$MYSQL_ROOT_PASSWORD" \
        --single-transaction --routines --triggers \
        "$MYSQL_DATABASE" > "$backup_file"; then
        
        # 压缩备份文件
        gzip "$backup_file"
        
        log_success "数据库备份完成: ${backup_file}.gz"
        
        # 记录备份信息
        echo "$(date): 数据库备份成功 - ${backup_file}.gz" >> "$BACKUP_DIR/backup.log"
        
        return 0
    else
        log_error "数据库备份失败"
        rm -f "$backup_file"
        return 1
    fi
}

# 备份配置文件
backup_configs() {
    log_info "开始备份配置文件..."
    
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local config_backup="$CONFIG_BACKUP_DIR/configs_$timestamp.tar.gz"
    
    # 备份重要配置文件
    tar -czf "$config_backup" \
        .env.production \
        docker-compose.prod.yml \
        nginx/ \
        database/init/ \
        PRODUCTION_PASSWORDS.md \
        2>/dev/null || true
    
    if [[ -f "$config_backup" ]]; then
        log_success "配置文件备份完成: $config_backup"
        echo "$(date): 配置文件备份成功 - $config_backup" >> "$BACKUP_DIR/backup.log"
        return 0
    else
        log_error "配置文件备份失败"
        return 1
    fi
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理 $RETENTION_DAYS 天前的旧备份..."
    
    # 清理数据库备份
    find "$DB_BACKUP_DIR" -name "*.sql.gz" -mtime +$RETENTION_DAYS -delete 2>/dev/null || true
    
    # 清理配置备份
    find "$CONFIG_BACKUP_DIR" -name "*.tar.gz" -mtime +$RETENTION_DAYS -delete 2>/dev/null || true
    
    log_success "旧备份清理完成"
}

# 检查备份状态
check_backup_status() {
    log_info "检查备份状态..."
    
    local db_backups=$(find "$DB_BACKUP_DIR" -name "*.sql.gz" -mtime -1 | wc -l)
    local config_backups=$(find "$CONFIG_BACKUP_DIR" -name "*.tar.gz" -mtime -1 | wc -l)
    
    echo "📊 备份统计:"
    echo "  - 数据库备份 (24小时内): $db_backups 个"
    echo "  - 配置文件备份 (24小时内): $config_backups 个"
    echo "  - 备份目录大小: $(du -sh $BACKUP_DIR | cut -f1)"
    
    # 检查最新备份
    local latest_db_backup=$(find "$DB_BACKUP_DIR" -name "*.sql.gz" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2-)
    local latest_config_backup=$(find "$CONFIG_BACKUP_DIR" -name "*.tar.gz" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2-)
    
    if [[ -n "$latest_db_backup" ]]; then
        echo "  - 最新数据库备份: $(basename "$latest_db_backup")"
        echo "  - 备份时间: $(stat -c %y "$latest_db_backup")"
    fi
    
    if [[ -n "$latest_config_backup" ]]; then
        echo "  - 最新配置备份: $(basename "$latest_config_backup")"
        echo "  - 备份时间: $(stat -c %y "$latest_config_backup")"
    fi
}

# 发送备份通知（可选）
send_notification() {
    local status=$1
    local message=$2
    
    # 这里可以添加邮件通知或其他通知方式
    echo "$(date): $status - $message" >> "$BACKUP_DIR/backup.log"
}

# 主函数
main() {
    local action=${1:-"full"}
    
    echo "🔄 SellerBox 备份系统"
    echo "===================="
    echo "开始时间: $(date)"
    echo ""
    
    case $action in
        "database"|"db")
            create_backup_dirs
            if backup_database; then
                send_notification "SUCCESS" "数据库备份成功"
            else
                send_notification "ERROR" "数据库备份失败"
                exit 1
            fi
            ;;
        "config")
            create_backup_dirs
            if backup_configs; then
                send_notification "SUCCESS" "配置文件备份成功"
            else
                send_notification "ERROR" "配置文件备份失败"
                exit 1
            fi
            ;;
        "full")
            create_backup_dirs
            
            local db_success=0
            local config_success=0
            
            if backup_database; then
                db_success=1
                send_notification "SUCCESS" "数据库备份成功"
            else
                send_notification "ERROR" "数据库备份失败"
            fi
            
            if backup_configs; then
                config_success=1
                send_notification "SUCCESS" "配置文件备份成功"
            else
                send_notification "ERROR" "配置文件备份失败"
            fi
            
            cleanup_old_backups
            
            if [[ $db_success -eq 1 && $config_success -eq 1 ]]; then
                log_success "完整备份成功完成"
            else
                log_warning "备份部分失败，请检查日志"
                exit 1
            fi
            ;;
        "status")
            check_backup_status
            ;;
        "cleanup")
            cleanup_old_backups
            ;;
        "help"|*)
            echo "SellerBox 备份脚本"
            echo ""
            echo "使用方法:"
            echo "  ./backup.sh full      - 完整备份（数据库+配置）"
            echo "  ./backup.sh database  - 仅备份数据库"
            echo "  ./backup.sh config    - 仅备份配置文件"
            echo "  ./backup.sh status    - 查看备份状态"
            echo "  ./backup.sh cleanup   - 清理旧备份"
            echo "  ./backup.sh help      - 显示帮助"
            ;;
    esac
    
    echo ""
    echo "完成时间: $(date)"
    echo "===================="
}

# 执行主函数
main $@
