#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { DOMParser } = require('xmldom');

// 验证sitemap.xml文件
function validateSitemap() {
  const sitemapPath = path.join(__dirname, '..', 'public', 'sitemap.xml');
  
  if (!fs.existsSync(sitemapPath)) {
    console.error('❌ sitemap.xml 文件不存在');
    return false;
  }
  
  try {
    const sitemapContent = fs.readFileSync(sitemapPath, 'utf8');
    
    // 检查XML格式
    const parser = new DOMParser();
    const doc = parser.parseFromString(sitemapContent, 'application/xml');
    
    // 检查是否有解析错误
    const parseErrors = doc.getElementsByTagName('parsererror');
    if (parseErrors.length > 0) {
      console.error('❌ XML格式错误:', parseErrors[0].textContent);
      return false;
    }
    
    // 检查根元素
    const urlset = doc.getElementsByTagName('urlset')[0];
    if (!urlset) {
      console.error('❌ 缺少 urlset 根元素');
      return false;
    }
    
    // 检查命名空间
    const xmlns = urlset.getAttribute('xmlns');
    if (xmlns !== 'http://www.sitemaps.org/schemas/sitemap/0.9') {
      console.error('❌ 错误的命名空间:', xmlns);
      return false;
    }
    
    // 检查URL条目
    const urls = doc.getElementsByTagName('url');
    if (urls.length === 0) {
      console.error('❌ 没有找到URL条目');
      return false;
    }
    
    console.log(`✅ XML格式验证通过`);
    console.log(`📊 找到 ${urls.length} 个URL条目`);
    
    // 验证每个URL
    let validUrls = 0;
    let invalidUrls = 0;
    
    for (let i = 0; i < urls.length; i++) {
      const url = urls[i];
      const loc = url.getElementsByTagName('loc')[0];
      const lastmod = url.getElementsByTagName('lastmod')[0];
      const changefreq = url.getElementsByTagName('changefreq')[0];
      const priority = url.getElementsByTagName('priority')[0];
      
      if (!loc || !loc.textContent) {
        console.error(`❌ URL ${i + 1}: 缺少 loc 元素`);
        invalidUrls++;
        continue;
      }
      
      const urlText = loc.textContent;
      if (!urlText.startsWith('https://sellerbox.asia')) {
        console.error(`❌ URL ${i + 1}: 无效的URL格式: ${urlText}`);
        invalidUrls++;
        continue;
      }
      
      if (!lastmod || !lastmod.textContent) {
        console.error(`❌ URL ${i + 1}: 缺少 lastmod 元素`);
        invalidUrls++;
        continue;
      }
      
      // 验证日期格式
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (!dateRegex.test(lastmod.textContent)) {
        console.error(`❌ URL ${i + 1}: 无效的日期格式: ${lastmod.textContent}`);
        invalidUrls++;
        continue;
      }
      
      if (!changefreq || !changefreq.textContent) {
        console.error(`❌ URL ${i + 1}: 缺少 changefreq 元素`);
        invalidUrls++;
        continue;
      }
      
      if (!priority || !priority.textContent) {
        console.error(`❌ URL ${i + 1}: 缺少 priority 元素`);
        invalidUrls++;
        continue;
      }
      
      // 验证优先级范围
      const priorityValue = parseFloat(priority.textContent);
      if (isNaN(priorityValue) || priorityValue < 0 || priorityValue > 1) {
        console.error(`❌ URL ${i + 1}: 无效的优先级值: ${priority.textContent}`);
        invalidUrls++;
        continue;
      }
      
      validUrls++;
    }
    
    console.log(`✅ 有效URL: ${validUrls}`);
    if (invalidUrls > 0) {
      console.log(`❌ 无效URL: ${invalidUrls}`);
    }
    
    // 检查文件大小
    const stats = fs.statSync(sitemapPath);
    const fileSizeKB = Math.round(stats.size / 1024);
    console.log(`📁 文件大小: ${fileSizeKB} KB`);
    
    if (stats.size > 50 * 1024 * 1024) { // 50MB
      console.warn('⚠️  警告: sitemap文件大小超过50MB，建议分割');
    }
    
    if (urls.length > 50000) {
      console.warn('⚠️  警告: URL数量超过50,000，建议分割sitemap');
    }
    
    return invalidUrls === 0;
    
  } catch (error) {
    console.error('❌ 验证过程中出错:', error.message);
    return false;
  }
}

// 验证robots.txt文件
function validateRobots() {
  const robotsPath = path.join(__dirname, '..', 'public', 'robots.txt');
  
  if (!fs.existsSync(robotsPath)) {
    console.error('❌ robots.txt 文件不存在');
    return false;
  }
  
  try {
    const robotsContent = fs.readFileSync(robotsPath, 'utf8');
    
    // 检查是否包含sitemap引用
    if (!robotsContent.includes('Sitemap: https://sellerbox.asia/sitemap.xml')) {
      console.error('❌ robots.txt 中缺少sitemap引用');
      return false;
    }
    
    console.log('✅ robots.txt 验证通过');
    return true;
    
  } catch (error) {
    console.error('❌ robots.txt 验证失败:', error.message);
    return false;
  }
}

// 主函数
function main() {
  console.log('🔍 开始验证SEO文件...\n');
  
  const sitemapValid = validateSitemap();
  console.log('');
  const robotsValid = validateRobots();
  
  console.log('\n📋 验证结果:');
  console.log(`Sitemap: ${sitemapValid ? '✅ 通过' : '❌ 失败'}`);
  console.log(`Robots.txt: ${robotsValid ? '✅ 通过' : '❌ 失败'}`);
  
  if (sitemapValid && robotsValid) {
    console.log('\n🎉 所有SEO文件验证通过！');
    process.exit(0);
  } else {
    console.log('\n💥 验证失败，请修复上述问题');
    process.exit(1);
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { validateSitemap, validateRobots };
