#!/bin/bash

# SellerBox 生产环境监控脚本
# 用途: 监控系统状态、服务健康、资源使用情况

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✅ OK]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[⚠️  WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[❌ ERROR]${NC} $1"
}

# 检查Docker服务状态
check_docker_services() {
    echo "🐳 Docker 服务状态"
    echo "==================="
    
    # 检查Docker是否运行
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker 服务未运行"
        return 1
    fi
    
    log_success "Docker 服务正常运行"
    
    # 检查容器状态
    echo ""
    echo "📦 容器状态:"
    docker-compose -f docker-compose.prod.yml --env-file .env.production ps
    
    echo ""
    echo "🔍 容器健康检查:"
    
    # 检查各个容器的健康状态
    local containers=("sellerbox_mysql" "sellerbox_backend" "sellerbox_frontend" "sellerbox_nginx")
    
    for container in "${containers[@]}"; do
        if docker ps --filter "name=$container" --filter "status=running" | grep -q "$container"; then
            local health=$(docker inspect --format='{{.State.Health.Status}}' "$container" 2>/dev/null || echo "no-healthcheck")
            case $health in
                "healthy")
                    log_success "$container: 健康"
                    ;;
                "unhealthy")
                    log_error "$container: 不健康"
                    ;;
                "starting")
                    log_warning "$container: 启动中"
                    ;;
                "no-healthcheck")
                    log_info "$container: 运行中 (无健康检查)"
                    ;;
                *)
                    log_warning "$container: 状态未知 ($health)"
                    ;;
            esac
        else
            log_error "$container: 未运行"
        fi
    done
}

# 检查网络连接
check_network() {
    echo ""
    echo "🌐 网络连接检查"
    echo "================"
    
    # 检查HTTPS访问
    if curl -s -I https://sellerbox.asia >/dev/null; then
        log_success "HTTPS 网站访问正常"
    else
        log_error "HTTPS 网站访问失败"
    fi
    
    # 检查API健康
    if curl -s https://sellerbox.asia/api/v1/health | grep -q '"success":true'; then
        log_success "API 服务正常"
    else
        log_error "API 服务异常"
    fi
    
    # 检查SSL证书
    local cert_info=$(echo | openssl s_client -servername sellerbox.asia -connect sellerbox.asia:443 2>/dev/null | openssl x509 -noout -dates 2>/dev/null)
    if [[ -n "$cert_info" ]]; then
        local expiry=$(echo "$cert_info" | grep "notAfter" | cut -d= -f2)
        log_success "SSL证书有效，到期时间: $expiry"
    else
        log_warning "无法获取SSL证书信息"
    fi
}

# 检查系统资源
check_system_resources() {
    echo ""
    echo "💻 系统资源使用"
    echo "================"
    
    # CPU使用率
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    echo "🔥 CPU使用率: ${cpu_usage}%"
    
    # 内存使用
    local memory_info=$(free -h | grep "Mem:")
    local memory_used=$(echo $memory_info | awk '{print $3}')
    local memory_total=$(echo $memory_info | awk '{print $2}')
    echo "🧠 内存使用: $memory_used / $memory_total"
    
    # 磁盘使用
    echo "💾 磁盘使用:"
    df -h | grep -E '^/dev/' | while read line; do
        local usage=$(echo $line | awk '{print $5}' | cut -d'%' -f1)
        local mount=$(echo $line | awk '{print $6}')
        local used=$(echo $line | awk '{print $3}')
        local total=$(echo $line | awk '{print $2}')
        
        if [[ $usage -gt 90 ]]; then
            log_error "$mount: $used/$total (${usage}%)"
        elif [[ $usage -gt 80 ]]; then
            log_warning "$mount: $used/$total (${usage}%)"
        else
            log_success "$mount: $used/$total (${usage}%)"
        fi
    done
    
    # Docker资源使用
    echo ""
    echo "🐳 Docker资源使用:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
}

# 检查数据库状态
check_database() {
    echo ""
    echo "🗄️  数据库状态"
    echo "=============="
    
    # 检查数据库连接
    if docker-compose -f docker-compose.prod.yml --env-file .env.production exec -T mysql \
        mysqladmin ping -h localhost --silent 2>/dev/null; then
        log_success "数据库连接正常"
        
        # 获取数据库统计信息
        local db_stats=$(docker-compose -f docker-compose.prod.yml --env-file .env.production exec -T mysql \
            mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
            SELECT 
                (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'sellerbox_db') as tables,
                (SELECT COUNT(*) FROM sellerbox_db.user_feedback) as feedback_count,
                (SELECT COUNT(*) FROM sellerbox_db.admin_users WHERE status = 'active') as active_admins;
            " sellerbox_db 2>/dev/null | tail -n +2)
        
        if [[ -n "$db_stats" ]]; then
            local tables=$(echo $db_stats | awk '{print $1}')
            local feedback=$(echo $db_stats | awk '{print $2}')
            local admins=$(echo $db_stats | awk '{print $3}')
            
            echo "  📊 数据表数量: $tables"
            echo "  💬 用户反馈数: $feedback"
            echo "  👤 活跃管理员: $admins"
        fi
        
    else
        log_error "数据库连接失败"
    fi
}

# 检查日志错误
check_logs() {
    echo ""
    echo "📋 日志检查"
    echo "==========="
    
    # 检查最近的错误日志
    local error_count=$(docker-compose -f docker-compose.prod.yml --env-file .env.production logs --tail=100 backend 2>/dev/null | grep -i error | wc -l)
    
    if [[ $error_count -eq 0 ]]; then
        log_success "后端日志无错误"
    elif [[ $error_count -lt 5 ]]; then
        log_warning "后端日志发现 $error_count 个错误"
    else
        log_error "后端日志发现 $error_count 个错误"
    fi
    
    # 检查Nginx错误日志
    local nginx_errors=$(docker-compose -f docker-compose.prod.yml --env-file .env.production logs --tail=100 nginx 2>/dev/null | grep -i error | wc -l)
    
    if [[ $nginx_errors -eq 0 ]]; then
        log_success "Nginx日志无错误"
    else
        log_warning "Nginx日志发现 $nginx_errors 个错误"
    fi
}

# 检查备份状态
check_backups() {
    echo ""
    echo "💾 备份状态"
    echo "==========="
    
    local backup_dir="/opt/sellerbox-backups"
    
    if [[ -d "$backup_dir" ]]; then
        # 检查最近的数据库备份
        local latest_db_backup=$(find "$backup_dir/database" -name "*.sql.gz" -type f -mtime -1 2>/dev/null | wc -l)
        local latest_config_backup=$(find "$backup_dir/configs" -name "*.tar.gz" -type f -mtime -1 2>/dev/null | wc -l)
        
        if [[ $latest_db_backup -gt 0 ]]; then
            log_success "数据库备份正常 (24小时内: $latest_db_backup 个)"
        else
            log_warning "数据库备份过期 (24小时内无备份)"
        fi
        
        if [[ $latest_config_backup -gt 0 ]]; then
            log_success "配置备份正常 (24小时内: $latest_config_backup 个)"
        else
            log_warning "配置备份过期 (24小时内无备份)"
        fi
        
        # 显示备份目录大小
        local backup_size=$(du -sh "$backup_dir" 2>/dev/null | cut -f1)
        echo "  📦 备份总大小: $backup_size"
        
    else
        log_error "备份目录不存在"
    fi
}

# 生成监控报告
generate_report() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local report_file="/tmp/sellerbox_monitor_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "SellerBox 系统监控报告"
        echo "======================"
        echo "生成时间: $timestamp"
        echo ""
        
        check_docker_services
        check_network
        check_system_resources
        check_database
        check_logs
        check_backups
        
    } | tee "$report_file"
    
    echo ""
    echo "📄 监控报告已保存到: $report_file"
}

# 主函数
main() {
    local action=${1:-"full"}
    
    case $action in
        "docker")
            check_docker_services
            ;;
        "network")
            check_network
            ;;
        "resources")
            check_system_resources
            ;;
        "database")
            check_database
            ;;
        "logs")
            check_logs
            ;;
        "backups")
            check_backups
            ;;
        "report")
            generate_report
            ;;
        "full")
            echo "🔍 SellerBox 系统监控"
            echo "===================="
            echo "开始时间: $(date)"
            echo ""
            
            check_docker_services
            check_network
            check_system_resources
            check_database
            check_logs
            check_backups
            
            echo ""
            echo "完成时间: $(date)"
            echo "===================="
            ;;
        "help"|*)
            echo "SellerBox 监控脚本"
            echo ""
            echo "使用方法:"
            echo "  ./monitor.sh full       - 完整监控检查"
            echo "  ./monitor.sh docker     - 检查Docker服务"
            echo "  ./monitor.sh network    - 检查网络连接"
            echo "  ./monitor.sh resources  - 检查系统资源"
            echo "  ./monitor.sh database   - 检查数据库状态"
            echo "  ./monitor.sh logs       - 检查日志错误"
            echo "  ./monitor.sh backups    - 检查备份状态"
            echo "  ./monitor.sh report     - 生成监控报告"
            echo "  ./monitor.sh help       - 显示帮助"
            ;;
    esac
}

# 执行主函数
main $@
