#!/bin/bash

# Cloudflare Workers AI 配置脚本
# 用于自动配置 Cloudflare Workers AI 环境变量

set -e

echo "🚀 Cloudflare Workers AI 配置脚本"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查必要的工具
check_dependencies() {
    echo -e "${BLUE}检查依赖工具...${NC}"
    
    if ! command -v wrangler &> /dev/null; then
        echo -e "${RED}错误: 未找到 wrangler CLI${NC}"
        echo "请先安装 wrangler: npm install -g wrangler"
        exit 1
    fi
    
    if ! command -v curl &> /dev/null; then
        echo -e "${RED}错误: 未找到 curl${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 依赖检查通过${NC}"
}

# 从 .env.cloudflare 文件读取配置
load_cloudflare_config() {
    echo -e "${BLUE}从 .env.cloudflare 文件读取配置...${NC}"

    if [[ -f ".env.cloudflare" ]]; then
        # 读取 API Token
        CLOUDFLARE_API_KEY=$(grep "^CLOUDFLARE_API_TOKEN=" .env.cloudflare | cut -d'=' -f2)
        # 读取 Account ID
        CLOUDFLARE_ACCOUNT_ID=$(grep "^CLOUDFLARE_ACCOUNT_ID=" .env.cloudflare | cut -d'=' -f2)

        if [[ -n "$CLOUDFLARE_API_KEY" && -n "$CLOUDFLARE_ACCOUNT_ID" ]]; then
            echo -e "${GREEN}✅ 已从 .env.cloudflare 读取配置${NC}"
            echo "API Token: ${CLOUDFLARE_API_KEY:0:8}...${CLOUDFLARE_API_KEY: -4}"
            echo "Account ID: $CLOUDFLARE_ACCOUNT_ID"
            SET_AS_DEFAULT="y"
            return 0
        fi
    fi

    echo -e "${YELLOW}⚠️  未找到 .env.cloudflare 文件或配置不完整${NC}"
    get_user_input
}

# 获取用户输入（备用方法）
get_user_input() {
    echo -e "${BLUE}请输入 Cloudflare 配置信息:${NC}"

    # API Token
    while [[ -z "$CLOUDFLARE_API_KEY" ]]; do
        echo -n "Cloudflare API Token: "
        read -s CLOUDFLARE_API_KEY
        echo
        if [[ -z "$CLOUDFLARE_API_KEY" ]]; then
            echo -e "${RED}API Token 不能为空${NC}"
        fi
    done

    # Account ID
    while [[ -z "$CLOUDFLARE_ACCOUNT_ID" ]]; do
        echo -n "Cloudflare Account ID: "
        read CLOUDFLARE_ACCOUNT_ID
        if [[ -z "$CLOUDFLARE_ACCOUNT_ID" ]]; then
            echo -e "${RED}Account ID 不能为空${NC}"
        fi
    done

    # AI Provider
    echo -n "设置为默认 AI 提供商? (y/n) [y]: "
    read SET_AS_DEFAULT
    SET_AS_DEFAULT=${SET_AS_DEFAULT:-y}
}

# 验证 API 配置
validate_api_config() {
    echo -e "${BLUE}验证 API 配置...${NC}"
    
    # 测试 API 连接
    local test_response=$(curl -s -w "%{http_code}" -o /tmp/cf_test_response \
        -X POST "https://api.cloudflare.com/client/v4/accounts/${CLOUDFLARE_ACCOUNT_ID}/ai/v1/chat/completions" \
        -H "Authorization: Bearer ${CLOUDFLARE_API_KEY}" \
        -H "Content-Type: application/json" \
        -d '{
            "model": "@cf/meta/llama-3.2-3b-instruct",
            "messages": [{"role": "user", "content": "Hello"}],
            "max_tokens": 10
        }')
    
    local http_code="${test_response: -3}"
    
    if [[ "$http_code" == "200" ]]; then
        echo -e "${GREEN}✅ API 配置验证成功${NC}"
        return 0
    else
        echo -e "${RED}❌ API 配置验证失败 (HTTP $http_code)${NC}"
        if [[ -f /tmp/cf_test_response ]]; then
            echo "错误详情:"
            cat /tmp/cf_test_response
            rm -f /tmp/cf_test_response
        fi
        return 1
    fi
}

# 更新本地环境变量
update_local_env() {
    echo -e "${BLUE}更新本地环境变量...${NC}"
    
    local env_file=".env.local"
    
    # 备份现有文件
    if [[ -f "$env_file" ]]; then
        cp "$env_file" "${env_file}.backup.$(date +%Y%m%d_%H%M%S)"
        echo -e "${YELLOW}已备份现有 $env_file${NC}"
    fi
    
    # 更新或添加环境变量
    {
        # 保留现有的非 Cloudflare 相关变量
        if [[ -f "$env_file" ]]; then
            grep -v "^VITE_CLOUDFLARE_" "$env_file" | grep -v "^VITE_AI_PROVIDER=" || true
        fi
        
        # 添加 Cloudflare 配置
        echo ""
        echo "# Cloudflare Workers AI 配置 (自动生成 $(date))"
        echo "VITE_CLOUDFLARE_API_KEY=$CLOUDFLARE_API_KEY"
        echo "VITE_CLOUDFLARE_ACCOUNT_ID=$CLOUDFLARE_ACCOUNT_ID"

        if [[ "$SET_AS_DEFAULT" == "y" || "$SET_AS_DEFAULT" == "Y" ]]; then
            echo "VITE_AI_PROVIDER=cloudflare"
        fi

        # 添加严格免费额度控制
        echo ""
        echo "# 免费额度控制 (严格模式，防止产生费用)"
        echo "VITE_STRICT_FREE_QUOTA=true"
        echo "VITE_DAILY_FREE_QUOTA=10000"
        echo "VITE_QUOTA_WARNING_THRESHOLD=0.8"
    } > "$env_file"
    
    echo -e "${GREEN}✅ 本地环境变量已更新${NC}"
}

# 更新 Cloudflare Pages 环境变量
update_pages_env() {
    echo -e "${BLUE}更新 Cloudflare Pages 环境变量...${NC}"
    
    # 检查是否有 Pages 项目
    local pages_projects=$(wrangler pages project list 2>/dev/null | grep -E "amzova|sellerbox" | head -1 | awk '{print $1}' || echo "")
    
    if [[ -z "$pages_projects" ]]; then
        echo -e "${YELLOW}⚠️  未找到 Cloudflare Pages 项目，跳过 Pages 环境变量配置${NC}"
        echo "您可以稍后在 Cloudflare Dashboard 中手动配置"
        return 0
    fi
    
    echo "找到 Pages 项目: $pages_projects"
    
    # 设置环境变量
    echo "设置 VITE_CLOUDFLARE_API_KEY..."
    wrangler pages secret put VITE_CLOUDFLARE_API_KEY --project-name="$pages_projects" <<< "$CLOUDFLARE_API_KEY"
    
    echo "设置 VITE_CLOUDFLARE_ACCOUNT_ID..."
    wrangler pages secret put VITE_CLOUDFLARE_ACCOUNT_ID --project-name="$pages_projects" <<< "$CLOUDFLARE_ACCOUNT_ID"
    
    if [[ "$SET_AS_DEFAULT" == "y" || "$SET_AS_DEFAULT" == "Y" ]]; then
        echo "设置 VITE_AI_PROVIDER..."
        wrangler pages secret put VITE_AI_PROVIDER --project-name="$pages_projects" <<< "cloudflare"
    fi
    
    echo -e "${GREEN}✅ Cloudflare Pages 环境变量已更新${NC}"
}

# 生成配置报告
generate_report() {
    echo -e "${BLUE}生成配置报告...${NC}"
    
    local report_file="cloudflare-ai-config-report.txt"
    
    {
        echo "Cloudflare Workers AI 配置报告"
        echo "=============================="
        echo "配置时间: $(date)"
        echo ""
        echo "配置信息:"
        echo "- Account ID: $CLOUDFLARE_ACCOUNT_ID"
        echo "- API Token: ${CLOUDFLARE_API_KEY:0:8}...${CLOUDFLARE_API_KEY: -4}"
        echo "- 默认提供商: $([ "$SET_AS_DEFAULT" == "y" ] && echo "是" || echo "否")"
        echo ""
        echo "模型配置:"
        echo "- 高级模型: @cf/meta/llama-3.1-70b-instruct"
        echo "- 标准模型: @cf/meta/llama-3.1-8b-instruct"
        echo "- 经济模型: @cf/meta/llama-3.2-3b-instruct"
        echo ""
        echo "免费额度:"
        echo "- 每日限制: 10,000 Neurons"
        echo "- 付费价格: $0.011/1000 Neurons"
        echo ""
        echo "下一步:"
        echo "1. 重启开发服务器: npm run dev"
        echo "2. 访问任意 AI 工具页面测试功能"
        echo "3. 查看 AI 服务状态组件确认配置"
        echo ""
        echo "故障排除:"
        echo "- 查看浏览器控制台错误信息"
        echo "- 检查 AI 服务状态组件显示"
        echo "- 参考文档: docs/cloudflare-ai-setup.md"
    } > "$report_file"
    
    echo -e "${GREEN}✅ 配置报告已生成: $report_file${NC}"
}

# 主函数
main() {
    echo -e "${GREEN}开始配置 Cloudflare Workers AI...${NC}"
    
    check_dependencies
    load_cloudflare_config
    
    if validate_api_config; then
        update_local_env
        update_pages_env
        generate_report
        
        echo ""
        echo -e "${GREEN}🎉 Cloudflare Workers AI 配置完成！${NC}"
        echo ""
        echo -e "${BLUE}接下来的步骤:${NC}"
        echo "1. 重启开发服务器: npm run dev"
        echo "2. 访问标题分析器或标题修复器测试 AI 功能"
        echo "3. 查看 AI 服务状态组件确认配置正确"
        echo ""
        echo -e "${YELLOW}注意: 请妥善保管您的 API Token，不要提交到版本控制系统${NC}"
    else
        echo -e "${RED}配置失败，请检查 API Token 和 Account ID 是否正确${NC}"
        exit 1
    fi
}

# 运行主函数
main "$@"
