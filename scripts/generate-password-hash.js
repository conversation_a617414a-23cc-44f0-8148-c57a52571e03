#!/usr/bin/env node

// 生成管理员密码哈希的脚本
import bcrypt from 'bcryptjs';

const password = 'TempAdmin2025!';
const saltRounds = 10;

console.log('正在生成密码哈希...');
console.log('密码:', password);

try {
  const hash = bcrypt.hashSync(password, saltRounds);
  console.log('生成的哈希:', hash);
  
  // 验证哈希是否正确
  const isValid = bcrypt.compareSync(password, hash);
  console.log('验证结果:', isValid ? '✅ 正确' : '❌ 错误');
  
  console.log('\n请将以下哈希值复制到数据库初始化脚本中:');
  console.log(`'${hash}'`);
  
} catch (error) {
  console.error('生成哈希失败:', error);
}
