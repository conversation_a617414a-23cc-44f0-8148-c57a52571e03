#!/bin/bash

# AmzOva 生产环境变量配置脚本
# 用于在 Cloudflare Pages 中配置环境变量

echo "🔧 配置 AmzOva 生产环境变量..."

# 项目名称
PROJECT_NAME="amzova-frontend"

# 配置环境变量
echo "📝 配置 SiliconFlow API 密钥..."
wrangler pages secret put VITE_Silicon_API_KEY --project-name=$PROJECT_NAME

echo "📝 配置 Cloudflare Workers AI 密钥..."
wrangler pages secret put VITE_CLOUDFLARE_API_KEY --project-name=$PROJECT_NAME

echo "📝 配置 Cloudflare 账户 ID..."
wrangler pages secret put VITE_CLOUDFLARE_ACCOUNT_ID --project-name=$PROJECT_NAME

echo "📝 配置 AI 模型..."
wrangler pages secret put VITE_MODEL_PREMIUM --project-name=$PROJECT_NAME
wrangler pages secret put VITE_MODEL_STANDARD --project-name=$PROJECT_NAME
wrangler pages secret put VITE_MODEL_ECONOMY --project-name=$PROJECT_NAME

echo "📝 配置默认 AI 提供商..."
wrangler pages secret put VITE_AI_PROVIDER --project-name=$PROJECT_NAME

echo "📝 配置 API 基础 URL..."
wrangler pages secret put VITE_API_BASE_URL --project-name=$PROJECT_NAME

echo "✅ 环境变量配置完成！"
echo "🚀 请重新部署项目以使环境变量生效。"
