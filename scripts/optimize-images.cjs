#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 图片优化建议脚本
// 由于没有安装图片处理库，这个脚本主要提供优化建议和检查

function analyzeImages() {
  const publicDir = path.join(__dirname, '..', 'public');
  const srcImagesDir = path.join(__dirname, '..', 'src', 'images');
  
  const results = {
    analyzed: 0,
    suggestions: [],
    totalSize: 0
  };

  // 检查public目录中的图片
  if (fs.existsSync(publicDir)) {
    const files = fs.readdirSync(publicDir);
    files.forEach(file => {
      const filePath = path.join(publicDir, file);
      const ext = path.extname(file).toLowerCase();
      
      if (['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp'].includes(ext)) {
        const stats = fs.statSync(filePath);
        results.analyzed++;
        results.totalSize += stats.size;
        
        // 分析文件大小和格式
        if (stats.size > 100 * 1024) { // 大于100KB
          results.suggestions.push({
            file: file,
            size: Math.round(stats.size / 1024),
            suggestion: '文件较大，建议压缩或转换为WebP格式'
          });
        }
        
        if (ext === '.png' && stats.size > 50 * 1024) {
          results.suggestions.push({
            file: file,
            size: Math.round(stats.size / 1024),
            suggestion: 'PNG文件建议转换为WebP格式以减小体积'
          });
        }
      }
    });
  }

  // 检查src/images目录
  if (fs.existsSync(srcImagesDir)) {
    const files = fs.readdirSync(srcImagesDir);
    files.forEach(file => {
      const filePath = path.join(srcImagesDir, file);
      const ext = path.extname(file).toLowerCase();
      
      if (['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp'].includes(ext)) {
        const stats = fs.statSync(filePath);
        results.analyzed++;
        results.totalSize += stats.size;
        
        if (stats.size > 100 * 1024) {
          results.suggestions.push({
            file: `src/images/${file}`,
            size: Math.round(stats.size / 1024),
            suggestion: '文件较大，建议压缩'
          });
        }
      }
    });
  }

  return results;
}

// 生成WebP格式建议
function generateWebPSuggestions() {
  return `
# 图片优化建议

## WebP格式转换
WebP格式可以减少20-35%的文件大小，建议使用以下工具转换：

### 在线工具
- https://squoosh.app/ (Google开发的在线图片压缩工具)
- https://tinypng.com/ (PNG/JPEG压缩)

### 命令行工具
\`\`\`bash
# 安装cwebp (WebP转换工具)
sudo apt-get install webp

# 转换PNG到WebP
cwebp -q 80 input.png -o output.webp

# 转换JPEG到WebP
cwebp -q 80 input.jpg -o output.webp
\`\`\`

## 响应式图片
建议为不同设备提供不同尺寸的图片：

\`\`\`html
<picture>
  <source srcset="image-small.webp" media="(max-width: 768px)" type="image/webp">
  <source srcset="image-large.webp" media="(min-width: 769px)" type="image/webp">
  <img src="image.png" alt="描述" loading="lazy">
</picture>
\`\`\`

## 懒加载
所有图片都应该添加 loading="lazy" 属性：

\`\`\`html
<img src="image.jpg" alt="描述" loading="lazy">
\`\`\`
`;
}

// 主函数
function main() {
  console.log('🔍 分析项目中的图片文件...\n');
  
  const results = analyzeImages();
  
  console.log(`📊 分析结果:`);
  console.log(`- 分析了 ${results.analyzed} 个图片文件`);
  console.log(`- 总大小: ${Math.round(results.totalSize / 1024)} KB\n`);
  
  if (results.suggestions.length > 0) {
    console.log('💡 优化建议:');
    results.suggestions.forEach((suggestion, index) => {
      console.log(`${index + 1}. ${suggestion.file} (${suggestion.size}KB)`);
      console.log(`   ${suggestion.suggestion}\n`);
    });
  } else {
    console.log('✅ 所有图片文件大小都在合理范围内\n');
  }
  
  // 生成优化建议文档
  const suggestionsDoc = generateWebPSuggestions();
  const outputPath = path.join(__dirname, '..', 'IMAGE_OPTIMIZATION_GUIDE.md');
  fs.writeFileSync(outputPath, suggestionsDoc, 'utf8');
  
  console.log('📝 图片优化指南已生成: IMAGE_OPTIMIZATION_GUIDE.md');
  console.log('\n🚀 性能优化建议:');
  console.log('1. 将PNG/JPEG图片转换为WebP格式');
  console.log('2. 为所有图片添加 loading="lazy" 属性');
  console.log('3. 使用响应式图片为不同设备提供合适尺寸');
  console.log('4. 考虑使用CDN加速图片加载');
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { analyzeImages, generateWebPSuggestions };
