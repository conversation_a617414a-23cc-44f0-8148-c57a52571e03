#!/usr/bin/env node

/**
 * Cloudflare Workers AI 集成测试脚本
 * 用于验证 API 配置和功能是否正常工作
 */

import https from 'https';
import fs from 'fs';
import path from 'path';

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✅${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}❌${colors.reset} ${msg}`),
  title: (msg) => console.log(`${colors.cyan}🚀 ${msg}${colors.reset}`)
};

// 加载环境变量
function loadEnvVars() {
  const envFiles = ['.env.local', '.env'];
  let envVars = {};
  
  for (const file of envFiles) {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      
      for (const line of lines) {
        const trimmed = line.trim();
        if (trimmed && !trimmed.startsWith('#')) {
          const [key, ...valueParts] = trimmed.split('=');
          if (key && valueParts.length > 0) {
            envVars[key.trim()] = valueParts.join('=').trim();
          }
        }
      }
      
      log.info(`已加载环境变量文件: ${file}`);
      break;
    }
  }
  
  return envVars;
}

// HTTP 请求工具
function makeRequest(options, data) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(body);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// 测试 Cloudflare Workers AI API
async function testCloudflareAI(apiKey, accountId) {
  log.info('测试 Cloudflare Workers AI API 连接...');
  
  const models = [
    '@cf/meta/llama-3.2-3b-instruct',
    '@cf/meta/llama-3.1-8b-instruct',
    '@cf/meta/llama-3.1-70b-instruct'
  ];
  
  const results = [];
  
  for (const model of models) {
    try {
      const startTime = Date.now();
      
      const options = {
        hostname: 'api.cloudflare.com',
        port: 443,
        path: `/client/v4/accounts/${accountId}/ai/v1/chat/completions`,
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'User-Agent': 'AmzOva-Test/1.0'
        }
      };
      
      const requestData = {
        model: model,
        messages: [
          {
            role: 'system',
            content: 'You are a helpful assistant designed to output JSON. Always respond with valid JSON format.'
          },
          {
            role: 'user',
            content: 'Please respond with a simple JSON object containing a "status" field with value "ok" and a "message" field with a greeting.'
          }
        ],
        temperature: 0.3,
        max_tokens: 100,
        response_format: { type: "json_object" }
      };
      
      const response = await makeRequest(options, requestData);
      const responseTime = Date.now() - startTime;
      
      if (response.status === 200 && response.data.success) {
        log.success(`${model}: 测试成功 (${responseTime}ms)`);
        results.push({
          model,
          success: true,
          responseTime,
          response: response.data.result
        });
      } else {
        log.error(`${model}: 测试失败 (HTTP ${response.status})`);
        results.push({
          model,
          success: false,
          error: response.data
        });
      }
    } catch (error) {
      log.error(`${model}: 网络错误 - ${error.message}`);
      results.push({
        model,
        success: false,
        error: error.message
      });
    }
  }
  
  return results;
}

// 测试成本计算
function testCostCalculation() {
  log.info('测试成本计算功能...');
  
  const testCases = [
    { input: 100, output: 200, model: '@cf/meta/llama-3.1-8b-instruct' },
    { input: 500, output: 1000, model: '@cf/meta/llama-3.1-70b-instruct' },
    { input: 50, output: 100, model: '@cf/meta/llama-3.2-3b-instruct' }
  ];
  
  // 模拟成本计算逻辑
  const modelCosts = {
    '@cf/meta/llama-3.1-8b-instruct': { input: 25.608, output: 75.147 },
    '@cf/meta/llama-3.1-70b-instruct': { input: 26.668, output: 204.805 },
    '@cf/meta/llama-3.2-3b-instruct': { input: 10.000, output: 17.300 }
  };
  
  for (const testCase of testCases) {
    const modelCost = modelCosts[testCase.model];
    const totalNeurons = (testCase.input / 1000) * modelCost.input + (testCase.output / 1000) * modelCost.output;
    const cost = (totalNeurons / 1000) * 0.011;
    
    log.success(`${testCase.model}: ${testCase.input}+${testCase.output} tokens = $${cost.toFixed(6)}`);
  }
}

// 生成测试报告
function generateTestReport(results, envVars) {
  const reportPath = 'cloudflare-ai-test-report.json';
  
  const report = {
    timestamp: new Date().toISOString(),
    environment: {
      hasCloudflareConfig: !!(envVars.VITE_CLOUDFLARE_API_KEY && envVars.VITE_CLOUDFLARE_ACCOUNT_ID),
      hasSiliconFlowConfig: !!envVars.VITE_Silicon_API_KEY,
      currentProvider: envVars.VITE_AI_PROVIDER || 'auto-detect'
    },
    testResults: results,
    summary: {
      totalTests: results.length,
      successfulTests: results.filter(r => r.success).length,
      failedTests: results.filter(r => !r.success).length,
      averageResponseTime: results
        .filter(r => r.success && r.responseTime)
        .reduce((sum, r) => sum + r.responseTime, 0) / 
        results.filter(r => r.success && r.responseTime).length || 0
    },
    recommendations: []
  };
  
  // 生成建议
  if (report.summary.failedTests === 0) {
    report.recommendations.push('✅ 所有测试通过，Cloudflare Workers AI 配置正确');
  } else {
    report.recommendations.push('❌ 部分测试失败，请检查 API 配置');
  }
  
  if (report.summary.averageResponseTime > 5000) {
    report.recommendations.push('⚠️ 响应时间较长，可能需要优化网络连接');
  } else if (report.summary.averageResponseTime < 2000) {
    report.recommendations.push('✅ 响应时间优秀，用户体验良好');
  }
  
  if (!report.environment.hasCloudflareConfig) {
    report.recommendations.push('⚠️ 未检测到 Cloudflare 配置，请运行 scripts/setup-cloudflare-ai.sh');
  }
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  log.success(`测试报告已生成: ${reportPath}`);
  
  return report;
}

// 主函数
async function main() {
  log.title('Cloudflare Workers AI 集成测试');
  console.log('=====================================\n');
  
  // 加载环境变量
  const envVars = loadEnvVars();
  
  // 检查配置
  const apiKey = envVars.VITE_CLOUDFLARE_API_KEY;
  const accountId = envVars.VITE_CLOUDFLARE_ACCOUNT_ID;
  
  if (!apiKey || !accountId) {
    log.error('未找到 Cloudflare 配置');
    log.info('请先运行: ./scripts/setup-cloudflare-ai.sh');
    process.exit(1);
  }
  
  log.info(`Account ID: ${accountId}`);
  log.info(`API Key: ${apiKey.substring(0, 8)}...${apiKey.substring(apiKey.length - 4)}`);
  console.log();
  
  // 运行测试
  const results = await testCloudflareAI(apiKey, accountId);
  console.log();
  
  // 测试成本计算
  testCostCalculation();
  console.log();
  
  // 生成报告
  const report = generateTestReport(results, envVars);
  
  // 显示摘要
  log.title('测试摘要');
  console.log(`总测试数: ${report.summary.totalTests}`);
  console.log(`成功: ${colors.green}${report.summary.successfulTests}${colors.reset}`);
  console.log(`失败: ${colors.red}${report.summary.failedTests}${colors.reset}`);
  console.log(`平均响应时间: ${report.summary.averageResponseTime.toFixed(0)}ms`);
  console.log();
  
  // 显示建议
  log.title('建议');
  report.recommendations.forEach(rec => console.log(rec));
  
  // 退出码
  process.exit(report.summary.failedTests > 0 ? 1 : 0);
}

// 运行测试
if (require.main === module) {
  main().catch(error => {
    log.error(`测试失败: ${error.message}`);
    process.exit(1);
  });
}
