#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 支持的语言
const languages = ['zh', 'zh-TW', 'en', 'ja', 'id', 'vi'];

// 工具列表
const tools = [
  'title-analyzer',
  'title-fixer', 
  'unit-converter',
  'currency-converter',
  'fba-calculator',
  'keyword-density-checker',
  'listing-optimizer',
  'profit-calculator',
  'keyword-researcher',
  'competitor-analyzer',
  'review-analyzer',
  'packing-calculator',
  'world-clock',
  'amz-seller-calendar',
  'reference-table',
  'case-converter',
  'keyword-combiner',
  'word-frequency-analyzer',
  'qr-code-generator'
];

// 基础页面
const basePages = [
  { path: '', priority: '1.0' },
  { path: 'tools', priority: '0.9' },
  { path: 'feedback', priority: '0.5' }
];

// 生成sitemap XML
function generateSitemap() {
  const baseUrl = 'https://amzova.com';
  // 使用中国时区的当前日期和时间
  const now = new Date();
  const chinaTime = new Date(now.getTime() + (8 * 60 * 60 * 1000)); // UTC+8
  const currentDate = chinaTime.toISOString().split('T')[0];
  const currentDateTime = chinaTime.toISOString(); // 包含时间的完整ISO格式
  
  let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" 
        xmlns:xhtml="http://www.w3.org/1999/xhtml">
`;

  // 添加基础页面
  basePages.forEach(page => {
    const url = page.path ? `${baseUrl}/${page.path}` : baseUrl;
    
    sitemap += `  <url>
    <loc>${url}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>daily</changefreq>
    <priority>${page.priority}</priority>
`;

    // 添加多语言版本
    languages.forEach(lang => {
      const langUrl = page.path ? `${baseUrl}/${page.path}?lang=${lang}` : `${baseUrl}?lang=${lang}`;
      sitemap += `    <xhtml:link rel="alternate" hreflang="${getLangCode(lang)}" href="${langUrl}" />
`;
    });

    sitemap += `  </url>
`;
  });

  // 添加工具页面
  tools.forEach(tool => {
    const url = `${baseUrl}/tools/${tool}`;
    
    sitemap += `  <url>
    <loc>${url}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
`;

    // 添加多语言版本
    languages.forEach(lang => {
      const langUrl = `${url}?lang=${lang}`;
      sitemap += `    <xhtml:link rel="alternate" hreflang="${getLangCode(lang)}" href="${langUrl}" />
`;
    });

    sitemap += `  </url>
`;
  });

  sitemap += `</urlset>`;
  
  return sitemap;
}

// 获取语言代码
function getLangCode(lang) {
  const langCodes = {
    'zh': 'zh-CN',
    'zh-TW': 'zh-TW', 
    'en': 'en-US',
    'ja': 'ja-JP',
    'id': 'id-ID',
    'vi': 'vi-VN'
  };
  return langCodes[lang] || lang;
}

// 生成robots.txt
function generateRobots() {
  return `User-agent: *
Allow: /

# 主要搜索引擎
User-agent: Googlebot
Allow: /

User-agent: Bingbot
Allow: /

User-agent: Slurp
Allow: /

User-agent: DuckDuckBot
Allow: /

User-agent: Baiduspider
Allow: /

User-agent: YandexBot
Allow: /

User-agent: facebookexternalhit
Allow: /

User-agent: Twitterbot
Allow: /

# 禁止访问的路径
Disallow: /admin/
Disallow: /api/
Disallow: /*.json$
Disallow: /*?*&*
Disallow: /test/

# 网站地图
Sitemap: https://amzova.com/sitemap.xml

# 爬取延迟
Crawl-delay: 1
`;
}

// 主函数
function main() {
  const publicDir = path.join(__dirname, '..', 'public');
  
  // 确保public目录存在
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true });
  }
  
  // 生成sitemap.xml
  const sitemapContent = generateSitemap();
  const sitemapPath = path.join(publicDir, 'sitemap.xml');
  fs.writeFileSync(sitemapPath, sitemapContent, 'utf8');

  // 设置正确的文件权限 (644 - rw-r--r--)
  try {
    fs.chmodSync(sitemapPath, 0o644);
    console.log('✅ sitemap.xml generated successfully with correct permissions (644)');
  } catch (error) {
    console.log('✅ sitemap.xml generated successfully');
    console.warn('⚠️ Could not set file permissions:', error.message);
  }

  // 生成robots.txt
  const robotsContent = generateRobots();
  const robotsPath = path.join(publicDir, 'robots.txt');
  fs.writeFileSync(robotsPath, robotsContent, 'utf8');

  // 设置正确的文件权限 (644 - rw-r--r--)
  try {
    fs.chmodSync(robotsPath, 0o644);
    console.log('✅ robots.txt generated successfully with correct permissions (644)');
  } catch (error) {
    console.log('✅ robots.txt generated successfully');
    console.warn('⚠️ Could not set file permissions:', error.message);
  }
  
  console.log(`📊 Generated sitemap with ${basePages.length + tools.length} pages`);
  console.log(`🌐 Supporting ${languages.length} languages`);
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { generateSitemap, generateRobots };
