server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html index.htm;

    # 启用 gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 百度验证文件
    location = /baidu_verify_codeva-G83azNsIXv.html {
        try_files $uri =404;
        add_header Content-Type text/plain;
        add_header Cache-Control "no-cache";
    }

    # 其他验证文件
    location ~* ^/(baidu_verify_|google|bing|yandex|sogou).*\.(html|txt)$ {
        try_files $uri =404;
        add_header Content-Type text/plain;
        add_header Cache-Control "no-cache";
    }

    # 处理前端路由
    location / {
        try_files $uri $uri/ /index.html;

        # 缓存控制
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # SEO 相关文件
    location ~* \.xml$ {
        try_files $uri =404;
        add_header Content-Type "application/xml; charset=utf-8";
        add_header Cache-Control "public, max-age=3600";
        add_header Vary Accept-Encoding;
    }

    location ~* \.txt$ {
        try_files $uri =404;
        add_header Content-Type "text/plain; charset=utf-8";
        add_header Cache-Control "public, max-age=3600";
        add_header Vary Accept-Encoding;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
    }

    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
}
