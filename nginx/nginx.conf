user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    # 基本设置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # 客户端设置
    client_max_body_size 20M;
    client_body_timeout 60;
    client_header_timeout 60;
    send_timeout 60;

    # Gzip压缩 - SEO优化
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        text/html
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        application/rss+xml
        application/manifest+json
        image/svg+xml
        application/x-font-ttf
        application/font-woff
        application/font-woff2
        font/opentype
        font/woff
        font/woff2;

    # 速率限制 - SEO友好
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
    limit_req_zone $binary_remote_addr zone=search:10m rate=30r/s;  # 搜索引擎爬虫友好

    # 缓存设置
    proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=static_cache:10m max_size=100m inactive=60m use_temp_path=off;

    # 搜索引擎爬虫检测
    map $http_user_agent $is_bot {
        default 0;
        ~*googlebot 1;
        ~*bingbot 1;
        ~*slurp 1;
        ~*duckduckbot 1;
        ~*baiduspider 1;
        ~*yandexbot 1;
        ~*facebookexternalhit 1;
        ~*twitterbot 1;
        ~*linkedinbot 1;
    }

    # 包含站点配置
    include /etc/nginx/conf.d/*.conf;
}
