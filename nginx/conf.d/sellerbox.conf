# HTTP重定向到HTTPS - www域名重定向到非www
server {
    listen 80;
    server_name www.sellerbox.asia;

    # Let's Encrypt验证
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # 重定向到非www的HTTPS版本
    location / {
        return 301 https://sellerbox.asia$request_uri;
    }
}

# HTTP重定向到HTTPS - 主域名
server {
    listen 80;
    server_name sellerbox.asia;

    # Let's Encrypt验证
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # 重定向到HTTPS
    location / {
        return 301 https://sellerbox.asia$request_uri;
    }
}

# HTTP重定向到HTTPS - API域名
server {
    listen 80;
    server_name api.sellerbox.asia;

    # Let's Encrypt验证
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # 重定向到HTTPS
    location / {
        return 301 https://$server_name$request_uri;
    }
}

# HTTPS - www域名重定向到非www
server {
    listen 443 ssl http2;
    server_name www.sellerbox.asia;

    # SSL证书配置
    ssl_certificate /etc/letsencrypt/live/sellerbox.asia-0001/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/sellerbox.asia-0001/privkey.pem;

    # 基本SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 重定向到非www版本
    return 301 https://sellerbox.asia$request_uri;
}

# HTTPS主站点 - 只处理非www域名
server {
    listen 443 ssl http2;
    server_name sellerbox.asia;

    # SSL证书配置
    ssl_certificate /etc/letsencrypt/live/sellerbox.asia-0001/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/sellerbox.asia-0001/privkey.pem;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_stapling on;
    ssl_stapling_verify on;
    
    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' https: data: blob: 'unsafe-inline' 'unsafe-eval'; connect-src 'self' https: wss:;" always;

    # 百度验证文件
    location = /baidu_verify_codeva-G83azNsIXv.html {
        proxy_pass http://frontend:80;
        add_header Content-Type text/plain;
        add_header Cache-Control "no-cache";
    }

    # 其他搜索引擎验证文件
    location ~* ^/(baidu_verify_|google|bing|yandex|sogou).*\.(html|txt)$ {
        proxy_pass http://frontend:80;
        add_header Content-Type text/plain;
        add_header Cache-Control "no-cache";
    }

    # 根目录指向前端容器
    location / {
        proxy_pass http://frontend:80;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    # API健康检查 - 特殊处理 (v1版本)
    location /api/v1/health {
        # CORS响应头
        add_header Access-Control-Allow-Origin $scheme://$host always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" always;
        add_header Access-Control-Allow-Credentials "true" always;

        proxy_pass http://backend:3001/health;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # API健康检查 - 特殊处理 (兼容旧版本)
    location /api/health {
        # CORS响应头
        add_header Access-Control-Allow-Origin $scheme://$host always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" always;
        add_header Access-Control-Allow-Credentials "true" always;

        proxy_pass http://backend:3001/health;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # API代理到后端 - 支持CORS (v1版本)
    location /api/v1/ {
        # CORS预检请求处理
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin $scheme://$host always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" always;
            add_header Access-Control-Allow-Credentials "true" always;
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 204;
        }

        # CORS响应头
        add_header Access-Control-Allow-Origin $scheme://$host always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" always;
        add_header Access-Control-Allow-Credentials "true" always;

        limit_req zone=api burst=20 nodelay;

        # 重写URL，将/api/v1重写为/api
        rewrite ^/api/v1/(.*)$ /api/$1 break;
        proxy_pass http://backend:3001;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # API代理到后端 - 支持CORS (兼容旧版本)
    location /api/ {
        # CORS预检请求处理
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin $scheme://$host always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" always;
            add_header Access-Control-Allow-Credentials "true" always;
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 204;
        }

        # CORS响应头
        add_header Access-Control-Allow-Origin $scheme://$host always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" always;
        add_header Access-Control-Allow-Credentials "true" always;

        limit_req zone=api burst=20 nodelay;

        # 直接代理到后端，保持/api路径
        proxy_pass http://backend:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 60;
        proxy_connect_timeout 60;
        proxy_send_timeout 60;
    }

    # PWA 相关文件
    location ~* \.(webmanifest|manifest\.json)$ {
        proxy_pass http://frontend:80;
        add_header Content-Type application/manifest+json;
        add_header Cache-Control "public, max-age=86400";
        add_header Vary Accept-Encoding;
    }

    # Service Worker
    location /sw.js {
        proxy_pass http://frontend:80;
        add_header Content-Type application/javascript;
        add_header Cache-Control "public, max-age=0, must-revalidate";
        add_header Service-Worker-Allowed "/";
    }

    # SEO 相关文件
    location ~* \.xml$ {
        proxy_pass http://frontend:80;
        add_header Content-Type "application/xml; charset=utf-8";
        add_header Cache-Control "public, max-age=3600";
        add_header Vary Accept-Encoding;
    }

    location ~* \.txt$ {
        proxy_pass http://frontend:80;
        add_header Content-Type "text/plain; charset=utf-8";
        add_header Cache-Control "public, max-age=3600";
        add_header Vary Accept-Encoding;
    }

    # 图片优化和缓存
    location ~* \.(png|jpg|jpeg|gif|ico|svg|webp)$ {
        proxy_pass http://frontend:80;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;

        # 支持 WebP 格式
        location ~* \.webp$ {
            add_header Vary Accept;
        }
    }

    # 字体文件缓存
    location ~* \.(woff|woff2|ttf|eot|otf)$ {
        proxy_pass http://frontend:80;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
    }

    # CSS 和 JS 文件缓存
    location ~* \.(css|js)$ {
        proxy_pass http://frontend:80;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;

        # 启用 Brotli 压缩（如果可用）
        gzip_static on;
    }

    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}

# HTTPS API服务器
server {
    listen 443 ssl http2;
    server_name api.sellerbox.asia;

    # SSL证书配置
    ssl_certificate /etc/letsencrypt/live/sellerbox.asia-0001/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/sellerbox.asia-0001/privkey.pem;

    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_stapling on;
    ssl_stapling_verify on;

    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' https: data: blob: 'unsafe-inline' 'unsafe-eval'; connect-src 'self' https: wss:;" always;

    # CORS配置 - 允许IP和域名访问
    set $cors_origin "";
    if ($http_origin ~* "^https?://(43\.136\.77\.194|sellerbox\.asia|www\.sellerbox\.asia)$") {
        set $cors_origin $http_origin;
    }
    add_header Access-Control-Allow-Origin $cors_origin always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" always;
    add_header Access-Control-Allow-Credentials "true" always;

    # 处理预检请求
    location / {
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin $cors_origin always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" always;
            add_header Access-Control-Allow-Credentials "true" always;
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 204;
        }

        limit_req zone=api burst=20 nodelay;

        proxy_pass http://backend:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 60;
        proxy_connect_timeout 60;
        proxy_send_timeout 60;
    }

    # 健康检查
    location /health {
        access_log off;
        proxy_pass http://backend:3001/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
