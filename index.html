<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="icon" type="image/png" sizes="192x192" href="/icon-192.png" />
    <link rel="icon" type="image/png" sizes="512x512" href="/icon-512.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#232F3E" />

    <!-- 百度站长工具验证 -->
    <meta name="baidu-site-verification" content="codeva-G83azNsIXv" />

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- iOS PWA 支持 -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="AmzOva 爱麦蛙" />

    <!-- Windows PWA 支持 -->
    <meta name="msapplication-TileColor" content="#232F3E" />
    <meta name="msapplication-config" content="/browserconfig.xml" />

    <!-- 基础SEO - 将被React Helmet覆盖 -->
    <title>亚马逊卖家工具箱 - AmzOva 爱麦蛙</title>
    <meta name="description" content="免费的亚马逊运营工具集，无需注册即可使用。包含标题优化、FBA计算、汇率换算等实用工具。" />
    <meta name="keywords" content="亚马逊工具,Amazon工具,FBA计算器,标题优化,汇率换算,免费工具,AmzOva,爱麦蛙" />

    <!-- 预连接到外部资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://api.freecurrencyapi.com" />
    <link rel="preconnect" href="https://api.siliconflow.cn" />

    <!-- DNS预取 -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com" />
    <link rel="dns-prefetch" href="//api.freecurrencyapi.com" />
    <link rel="dns-prefetch" href="//api.siliconflow.cn" />

    <!-- Google AdSense -->
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-6352731815227894"
         crossorigin="anonymous"></script>

    <!-- 结构化数据预设 -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": "AmzOva 爱麦蛙",
      "alternateName": "AmzOva",
      "description": "免费的亚马逊运营工具集",
      "url": "https://amzova.com",
      "applicationCategory": "BusinessApplication",
      "operatingSystem": "Web Browser",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
      },
      "author": {
        "@type": "Person",
        "name": "Lin Dong",
        "email": "<EMAIL>"
      }
    }
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
