# 亚马逊橙黑简约风设计系统

## 🎨 设计理念

本设计系统基于亚马逊官方橙黑简约风格，专为亚马逊运营工具网站设计，强调：
- **简约性**: 遵循亚马逊的极简设计哲学，去除冗余元素
- **专业性**: 体现工具的专业性和可靠性
- **大气感**: 通过合理的留白和层次营造大气的视觉效果
- **一致性**: 统一的视觉语言和交互模式
- **易用性**: 优化用户体验，提高工作效率

## 🎯 色彩系统

### 亚马逊官方色彩
```css
/* 主色调 - 亚马逊橙 */
--amazon-orange: #FF9900;        /* 亚马逊官方橙色 */
--amazon-orange-dark: #E6890A;   /* 悬停状态 */
--amazon-orange-light: #FFF5E6;  /* 浅色背景 */
--amazon-orange-border: #FFD699; /* 边框色 */

/* 深色系 - Squid Ink */
--amazon-squid-ink: #232F3E;     /* 亚马逊深色 */
--amazon-black: #000000;         /* 纯黑色 */
--amazon-white: #FFFFFF;         /* 纯白色 */

/* 中性色 */
--amazon-gray-light: #F8F9FA;    /* 浅灰背景 */
--amazon-gray-border: #E5E5E5;   /* 灰色边框 */
```

### 工具图标色彩系统（简约大气）
```css
/* 蓝色系 - 分析类工具 */
--tool-blue: #3B82F6;

/* 绿色系 - 计算类工具 */
--tool-emerald: #10B981;

/* 紫色系 - 优化类工具 */
--tool-purple: #8B5CF6;

/* 琥珀色系 - 效率类工具 */
--tool-amber: #F59E0B;

/* 玫瑰色系 - 财务类工具 */
--tool-rose: #F43F5E;

/* 靛蓝色系 - 管理类工具 */
--tool-indigo: #6366F1;

/* 灰色系 - 开发中工具 */
--tool-gray: #64748B;
```

### 状态色
```css
--success: #10b981;     /* 成功状态 */
--warning: #f59e0b;     /* 警告状态 */
--error: #ef4444;       /* 错误状态 */
--info: #3b82f6;        /* 信息状态 */
```

## 📝 字体系统

### 字体族
```css
font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
```

### 字体大小
- **大标题**: text-3xl lg:text-4xl (48px/30px)
- **中标题**: text-2xl lg:text-3xl (36px/24px)  
- **小标题**: text-xl lg:text-2xl (24px/20px)
- **正文**: text-base (16px)
- **小字**: text-sm (14px)
- **极小字**: text-xs (12px)

### 字重
- **粗体**: font-bold (700)
- **半粗**: font-semibold (600)
- **中等**: font-medium (500)
- **常规**: font-normal (400)

## 🧩 组件系统

### 按钮组件（亚马逊橙黑风格）

#### 主要按钮 (.btn-primary)
```css
.btn-primary {
  background: #FF9900;        /* 亚马逊橙 */
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 200ms;
  border: 1px solid #FF9900;
}
.btn-primary:hover {
  background: #E6890A;        /* 深橙色 */
  border-color: #E6890A;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}
```

#### 次要按钮 (.btn-secondary)
```css
.btn-secondary {
  background: #FFF5E6;        /* 浅橙背景 */
  color: #232F3E;             /* Squid Ink */
  border: 1px solid #FFD699;  /* 橙色边框 */
  padding: 12px 24px;
  border-radius: 8px;
}
.btn-secondary:hover {
  background: #FFEBCC;
  border-color: #FFCC66;
}
```

#### 轮廓按钮 (.btn-outline)
```css
.btn-outline {
  background: white;
  color: #232F3E;             /* Squid Ink */
  border: 1px solid #E5E5E5;  /* 灰色边框 */
  padding: 12px 24px;
  border-radius: 8px;
}
.btn-outline:hover {
  background: #F8F8F8;
  border-color: #FF9900;      /* 悬停时橙色边框 */
  color: #FF9900;
}
```

### 卡片组件

#### 基础卡片 (.card)
```css
.card {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}
```

#### 工具卡片
```css
.tool-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 300ms;
}
.tool-card:hover {
  border-color: #fdba74;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}
```

### 输入框组件

#### 基础输入框 (.input)
```css
.input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  background: white;
  transition: all 200ms;
}
.input:focus {
  outline: none;
  ring: 2px solid #ea580c;
  border-color: transparent;
}
```

#### 选择框 (.select)
```css
.select {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  background: white;
}
```

### 徽章组件

#### 主要徽章 (.badge-primary)
```css
.badge-primary {
  background: #ffedd5;
  color: #9a3412;
  padding: 4px 12px;
  border-radius: 9999px;
  font-size: 14px;
  font-weight: 500;
}
```

#### 次要徽章 (.badge-secondary)
```css
.badge-secondary {
  background: #f1f5f9;
  color: #1e293b;
  padding: 4px 12px;
  border-radius: 9999px;
}
```

## 📐 间距系统

### 标准间距
- **xs**: 4px
- **sm**: 8px  
- **md**: 16px
- **lg**: 24px
- **xl**: 32px
- **2xl**: 48px
- **3xl**: 64px

### 组件间距
- **卡片内边距**: 24px (p-6)
- **按钮内边距**: 12px 24px (px-6 py-3)
- **输入框内边距**: 12px 16px (px-4 py-3)

## 🎭 阴影系统

```css
--shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
--shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
--shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
--shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
```

## 🔄 动画系统

### 过渡时间
- **快速**: 200ms (按钮、输入框)
- **标准**: 300ms (卡片、悬停效果)
- **缓慢**: 500ms (页面切换)

### 缓动函数
- **标准**: ease-in-out
- **进入**: ease-out  
- **退出**: ease-in

## 📱 响应式断点

```css
/* 移动端 */
@media (max-width: 640px) { /* sm */ }

/* 平板 */  
@media (min-width: 768px) { /* md */ }

/* 桌面端 */
@media (min-width: 1024px) { /* lg */ }

/* 大屏 */
@media (min-width: 1280px) { /* xl */ }
```

## 🎯 使用指南

### 1. 颜色使用原则
- **主色橙色**: 用于主要操作按钮、链接、重要状态
- **灰色系**: 用于文字、边框、背景
- **状态色**: 仅用于表示特定状态

### 2. 组件组合
- 优先使用预定义的组件类
- 保持组件间的视觉一致性
- 遵循间距和阴影规范

### 3. 响应式设计
- 移动端优先设计
- 确保所有组件在不同屏幕尺寸下正常显示
- 合理使用断点进行布局调整

## 🔧 开发规范

### CSS 类命名
- 使用 Tailwind CSS 工具类
- 自定义组件使用语义化命名
- 保持类名简洁明了

### 组件开发
- 每个组件应该是独立的、可复用的
- 遵循设计系统的颜色和间距规范
- 确保组件的可访问性

### 维护更新
- 定期检查设计系统的一致性
- 及时更新过时的设计元素
- 记录所有设计变更
