#!/bin/bash

# 爱麦蛙 (AmzOva) - Cloudflare 部署脚本
# 用于快速部署前端和后端到 Cloudflare

set -e

echo "🚀 开始部署 AmzOva 到 Cloudflare..."

# 检查是否在项目根目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 检查环境变量配置
echo "🔐 检查 Turnstile 配置..."
if grep -q "your_real" .env.local || grep -q "your_real" sellerbox-api/wrangler.toml; then
    echo "❌ 错误: 请先配置真实的 Turnstile 密钥"
    echo ""
    echo "📋 配置步骤:"
    echo "   1. 访问 https://dash.cloudflare.com/turnstile"
    echo "   2. 创建新应用，域名设置为: amzova.com, *.amzova.com, localhost"
    echo "   3. 获取 Site Key 和 Secret Key"
    echo "   4. 更新 .env.local 中的 VITE_TURNSTILE_SITE_KEY"
    echo "   5. 更新 sellerbox-api/wrangler.toml 中的 TURNSTILE_SECRET_KEY"
    echo ""
    echo "💡 提示: 运行 ./check-turnstile.sh 检查配置状态"
    echo ""
    read -p "配置完成后，是否继续部署？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 部署取消，请先完成配置"
        exit 1
    fi
fi

echo "✅ Turnstile 配置检查通过"

echo "📦 步骤 1: 构建前端应用..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ 前端构建失败"
    exit 1
fi

echo "✅ 前端构建完成"

echo "🌐 步骤 2: 部署前端到 Cloudflare Pages..."
wrangler pages deploy dist --project-name=amzova-frontend

if [ $? -ne 0 ]; then
    echo "❌ 前端部署失败"
    exit 1
fi

echo "✅ 前端部署完成"

echo "⚡ 步骤 3: 部署后端 API 到 Cloudflare Workers..."
cd sellerbox-api
wrangler deploy

if [ $? -ne 0 ]; then
    echo "❌ 后端部署失败"
    exit 1
fi

cd ..
echo "✅ 后端部署完成"

echo ""
echo "🎉 部署成功完成!"
echo ""
echo "📊 部署信息:"
echo "  前端: https://amzova.com"
echo "  API:  https://api.amzova.com"
echo ""
echo "🔧 下一步操作:"
echo "  1. 访问网站测试功能"
echo "  2. 检查 Turnstile 验证是否正常工作"
echo "  3. 测试 AI 工具功能"
echo ""