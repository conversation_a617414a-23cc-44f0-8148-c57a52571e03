# AI产品图片生成器

基于Gemini AI技术的智能产品图片生成工具，专为亚马逊卖家设计。

## 🎯 功能特性

### 核心功能
- **智能图片生成**：基于文本描述生成专业产品图片
- **图片编辑**：对现有产品图片进行AI驱动的编辑和优化
- **亚马逊规格适配**：自动适配亚马逊各种图片规格要求
- **多种风格支持**：工作室、生活方式、产品展示、场景化等风格
- **多宽高比支持**：1:1、3:4、4:3、9:16、16:9等比例

### 亚马逊图片规格
1. **主图 (Main Image)**
   - 1:1 正方形比例
   - 纯白背景 (RGB 255,255,255)
   - 产品占比85%
   - 无文字水印

2. **附图 (Additional Images)**
   - 1:1 正方形比例
   - 可以有背景
   - 展示产品功能和细节

3. **A+内容图 (A+ Content)**
   - 16:9 宽屏比例
   - 高质量展示
   - 可包含文字和品牌元素

4. **品牌旗舰店图 (Brand Store)**
   - 16:9 宽屏比例
   - 品牌化设计
   - 可包含营销元素

## 🚀 技术架构

### 前端组件
```
ProductImageGenerator/
├── ProductImageGenerator.tsx    # 主组件
├── ImageUploader.tsx           # 图片上传
├── SpecSelector.tsx            # 规格选择
├── StyleEditor.tsx             # 风格编辑
└── PreviewPanel.tsx            # 预览面板
```

### 后端服务
```
imageGenerator/
├── imageGenerator.ts           # 核心服务
├── amazonSpecs.ts             # 亚马逊规格配置
├── promptEnhancer.ts          # 提示词增强
└── complianceValidator.ts     # 合规性验证
```

### AI模型配置
- **主模型**：Gemini 2.0 Flash (图片生成版本)
- **模型名称**：`gemini-2.0-flash-preview-image-generation`
- **支持功能**：文本转图片、图片编辑、多轮对话

## 📱 用户界面

### 主要区域
1. **左侧配置面板**
   - 图片上传区域
   - 文本描述输入
   - 亚马逊规格选择
   - 风格和比例设置
   - 操作按钮

2. **右侧预览面板**
   - 生成结果展示
   - 下载功能
   - 历史记录（预留）

### 多语言支持
- 简体中文 (zh)
- 繁体中文 (zh-TW)
- 英语 (en)
- 日语 (ja)
- 印尼语 (id)
- 越南语 (vi)

## 🔒 使用限制

### 免费用户限制
- **每日限制**：2次生成
- **每小时限制**：5次生成
- **会话限制**：2次生成
- **冷却时间**：5分钟

### 限制实现
```typescript
const IMAGE_GENERATOR_LIMITS: UsageLimits = {
  hourlyLimit: 5,
  dailyLimit: 2,
  sessionLimit: 2,
  cooldownMinutes: 5
};
```

## 🛠️ 使用方法

### 基本使用流程
1. **上传产品图片**（可选）
2. **输入详细描述**
   ```
   将这个蓝牙耳机放在现代办公桌上，
   背景是简洁的白色，光线柔和，
   展现产品的专业感和科技感
   ```
3. **选择亚马逊规格**
4. **选择图片风格和比例**
5. **点击生成按钮**
6. **预览和下载结果**

### API调用示例
```typescript
import { productImageGenerator } from '@/lib/ai/services/imageGenerator';

const request = {
  prompt: '生成一个蓝牙耳机的产品图片',
  baseImage: 'data:image/jpeg;base64,...', // 可选
  aspectRatio: '1:1',
  style: 'studio',
  amazonSpec: 'main'
};

const result = await productImageGenerator.generateProductImage(request);
console.log(result.imageData); // base64图片数据
```

## 🔧 配置选项

### 图片风格
- **studio**: 专业工作室风格
- **lifestyle**: 生活方式展示
- **product**: 纯产品展示
- **scene**: 场景化展示

### 宽高比选项
- **1:1**: 正方形，适合主图和附图
- **3:4**: 竖版，适合手机展示
- **4:3**: 横版，适合桌面展示
- **9:16**: 手机竖屏，适合社交媒体
- **16:9**: 宽屏，适合A+内容和Banner

## 🚧 当前限制

### 演示版本说明
当前版本使用免费的Gemini 2.0 Flash模型，存在以下限制：
- 暂不支持真实图片生成
- 主要用于UI和功能流程演示
- 为未来集成付费API做准备

### 未来扩展计划
1. **集成Imagen 3/4模型**：获得真实的图片生成能力
2. **批量处理功能**：同时处理多张图片
3. **高级编辑功能**：背景移除、质量增强等
4. **自定义模板**：保存和复用常用设置
5. **历史记录**：查看和管理生成历史

## 📊 性能优化

### 缓存策略
- 图片结果缓存
- 提示词模板缓存
- 用户设置缓存

### 错误处理
- 网络错误重试
- 使用限制提示
- 优雅降级处理

## 🧪 测试

### 单元测试
```bash
npm test src/lib/ai/services/__tests__/imageGenerator.test.ts
```

### 测试覆盖
- 使用限制检查
- 图片生成流程
- 亚马逊规格适配
- 提示词增强
- 合规性验证

## 📝 开发指南

### 添加新的图片规格
```typescript
export const AMAZON_IMAGE_SPECS = {
  // 现有规格...
  newSpec: {
    aspectRatio: '1:1' as const,
    description: '新规格描述',
    requirements: '具体要求说明'
  }
};
```

### 添加新的图片风格
```typescript
// 在组件中添加新选项
const styleOptions = [
  // 现有选项...
  { value: 'newStyle', label: t('styleNewStyle') }
];
```

### 扩展多语言支持
1. 在 `src/locales/modules/tools/ProductImageGenerator/` 添加新语言文件
2. 在 `src/locales/index.ts` 中导入和合并翻译
3. 更新组件中的翻译键

## 🔗 相关文档

- [AI服务配置](../lib/ai/README.md)
- [多语言系统](../locales/readme.md)
- [使用限制系统](../utils/aiUsageLimit.md)
- [组件开发指南](../components/README.md)

## 📞 支持

如有问题或建议，请联系开发团队或提交Issue。
