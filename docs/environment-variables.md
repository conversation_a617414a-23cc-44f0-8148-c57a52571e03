# AmzOva 环境变量配置说明

本文档详细说明 AmzOva 项目的环境变量配置文件结构和使用方法。

## 📁 文件结构

```
项目根目录/
├── .env.example          # 开发者快速入门模板
├── .env.cloudflare       # 生产环境配置 (实际使用)
├── .env.local           # 本地开发配置 (需要创建)
└── docs/
    └── environment-variables.md  # 本文档
```

## 📋 文件说明

### 1. `.env.example` - 开发者快速入门模板

**用途**: 新开发者的配置模板和参考
**状态**: ✅ 当前维护中
**使用方法**:
```bash
# 复制模板文件
cp .env.example .env.local

# 编辑配置
nano .env.local

# 启动开发服务器
npm run dev
```

**包含配置**:
- 项目基础信息
- Cloudflare Workers AI 配置模板
- 开发环境设置
- 详细的配置说明和快速入门指南

### 2. `.env.cloudflare` - 生产环境配置

**用途**: Cloudflare 生产环境的完整配置
**状态**: ✅ 生产环境使用中
**特点**:
- 包含真实的 API 密钥和配置值
- 完整的 Cloudflare 服务配置
- 生产级安全和性能设置
- 已集成 Cloudflare Workers AI

**重要**: 此文件包含敏感信息，不应提交到版本控制系统

### 3. `.env.local` - 本地开发配置

**用途**: 个人本地开发环境配置
**状态**: 需要手动创建
**创建方法**:
```bash
cp .env.example .env.local
```

**特点**:
- 基于 `.env.example` 创建
- 包含个人的 API 密钥
- 本地开发专用设置
- 自动被 Git 忽略

## 🔧 配置项说明

### 核心配置

| 配置项 | 说明 | 示例值 |
|--------|------|--------|
| `PROJECT_NAME` | 项目名称 | `AmzOva` |
| `NODE_ENV` | 环境标识 | `development` / `production` |
| `DOMAIN` | 项目域名 | `amzova.com` |
| `EMAIL` | 联系邮箱 | `<EMAIL>` |
| `VITE_API_BASE_URL` | API 基础 URL | `https://api.amzova.com` (生产) / `http://localhost:8787` (开发) |

### AI 服务配置

| 配置项 | 说明 | 推荐值 |
|--------|------|--------|
| `VITE_AI_PROVIDER` | AI 提供商 | `cloudflare` |
| `VITE_CLOUDFLARE_API_KEY` | Cloudflare API 密钥 | 从 Dashboard 获取 |
| `VITE_CLOUDFLARE_ACCOUNT_ID` | Cloudflare 账户 ID | 从 Dashboard 获取 |
| `VITE_STRICT_FREE_QUOTA` | 严格免费额度控制 | `true` |
| `VITE_DAILY_FREE_QUOTA` | 每日免费额度 | `10000` |

### 模型配置

| 配置项 | 说明 | 当前值 |
|--------|------|--------|
| `VITE_MODEL_PREMIUM` | 高级模型 | `deepseek-ai/DeepSeek-V3` |
| `VITE_MODEL_STANDARD` | 标准模型 | `Qwen/Qwen3-30B-A3B` |
| `VITE_MODEL_ECONOMY` | 经济模型 | `Qwen/Qwen3-8B` |

## 🚀 快速配置指南

### 方法一: 手动配置
```bash
# 1. 复制模板
cp .env.example .env.local

# 2. 编辑配置文件
nano .env.local

# 3. 填写必要的 API 密钥
# - VITE_CLOUDFLARE_API_KEY
# - VITE_CLOUDFLARE_ACCOUNT_ID

# 4. 启动开发服务器
npm run dev
```

### 方法二: 自动配置 (推荐)
```bash
# 运行自动配置脚本
./scripts/setup-cloudflare-ai.sh

# 脚本会自动:
# - 从 .env.cloudflare 读取配置
# - 生成 .env.local 文件
# - 验证 API 连接
# - 配置免费额度控制
```

## 🔐 安全注意事项

### 敏感信息保护
- ❌ **绝不提交** `.env.local` 和 `.env.cloudflare` 到版本控制
- ✅ **只提交** `.env.example` 模板文件
- 🔒 **保护** API 密钥和账户 ID

### Git 忽略配置
确保 `.gitignore` 文件包含:
```gitignore
# 环境变量文件
.env.local
.env.production
.env.development
.env

# 但保留模板文件
!.env.example
```

## 🌍 环境差异

### 开发环境 (.env.local)
- 使用测试 API 密钥
- 启用调试功能
- 本地服务器配置
- 宽松的安全设置

### 生产环境 (.env.cloudflare)
- 使用生产 API 密钥
- 禁用调试功能
- Cloudflare 服务配置
- 严格的安全设置

## 📊 配置验证

### 验证配置是否正确
```bash
# 运行测试脚本
node scripts/test-cloudflare-ai.js

# 检查项目:
# ✅ API 连接正常
# ✅ 模型可用性
# ✅ 免费额度控制
# ✅ 配置完整性
```

### 开发环境检查
启动开发服务器后:
1. 访问任意 AI 工具页面
2. 查看 AI 服务状态组件
3. 确认显示绿色状态
4. 测试 AI 功能是否正常

## 🔧 故障排除

### 常见问题

#### 1. "API key not configured" 错误
**原因**: 环境变量未正确设置
**解决**:
```bash
# 检查文件是否存在
ls -la .env.local

# 检查内容是否正确
cat .env.local | grep CLOUDFLARE

# 重新配置
./scripts/setup-cloudflare-ai.sh
```

#### 2. 配置不生效
**原因**: 环境变量未正确加载
**解决**:
```bash
# 重启开发服务器
npm run dev

# 清除浏览器缓存
# 检查环境变量格式
```

#### 3. 模型配置不一致
**原因**: 不同文件中的模型配置不同步
**解决**: 确保所有文件中的模型配置一致

## 📞 技术支持

如果您在配置过程中遇到问题:

1. **查看文档**: 
   - `docs/cloudflare-ai-setup.md`
   - `docs/free-quota-control.md`

2. **运行诊断**:
   ```bash
   node scripts/test-cloudflare-ai.js
   ```

3. **联系支持**: <EMAIL>

## 📝 更新日志

### 2025-01-24
- ✅ 删除废弃的 `.env.production` 和 `.env.cloudflare.template`
- ✅ 统一 `.env.example` 和 `.env.cloudflare` 的模型配置
- ✅ 添加 Cloudflare Workers AI 严格免费额度控制
- ✅ 优化配置文件结构和说明文档

### 历史版本
- 项目迁移到 Cloudflare 架构
- 集成 Cloudflare Workers AI
- 添加多语言支持
- 实现 PWA 功能

---

**维护者**: AmzOva 开发团队  
**最后更新**: 2025-01-24  
**版本**: v2.0
