# Cloudflare Workers AI 配置指南

本指南将帮助您配置 Cloudflare Workers AI，以替换或补充现有的 SiliconFlow AI 服务，实现更低成本和更好性能。

## 🎯 为什么选择 Cloudflare Workers AI？

### 💰 成本优势
- **免费额度**：每日 10,000 Neurons 免费
- **低成本**：$0.011/1000 Neurons（超出免费额度后）
- **无隐藏费用**：按实际使用量计费

### ⚡ 性能优势
- **全球边缘计算**：200+ 数据中心，低延迟
- **高可用性**：99.9%+ 可用性保证
- **自动扩展**：无需担心并发限制

### 🏗️ 架构优势
- **完美集成**：与现有 Cloudflare 部署架构无缝集成
- **统一管理**：在同一个 Cloudflare 账户下管理
- **简化运维**：减少第三方依赖

## 📋 配置步骤

### 1. 获取 Cloudflare API Token

#### 方法一：创建自定义 Token（推荐）
1. 访问 [Cloudflare API Tokens](https://dash.cloudflare.com/profile/api-tokens)
2. 点击 "Create Token"
3. 选择 "Custom token"
4. 配置权限：
   ```
   Permissions:
   - Account: Cloudflare Workers AI:Edit
   - Zone: Zone:Read (如果需要)
   
   Account Resources:
   - Include: All accounts (或选择特定账户)
   
   Zone Resources:
   - Include: All zones (或选择特定域名)
   ```
5. 点击 "Continue to summary"
6. 点击 "Create Token"
7. 复制生成的 Token

#### 方法二：使用 Global API Key（不推荐）
1. 访问 [Cloudflare API Tokens](https://dash.cloudflare.com/profile/api-tokens)
2. 在 "API Keys" 部分找到 "Global API Key"
3. 点击 "View" 并输入密码
4. 复制 API Key

### 2. 获取 Account ID

1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. 在右侧边栏找到 "Account ID"
3. 点击复制按钮

### 3. 配置环境变量

#### 开发环境
在项目根目录创建或编辑 `.env.local` 文件：

```bash
# Cloudflare Workers AI 配置
VITE_CLOUDFLARE_API_KEY=your_api_token_here
VITE_CLOUDFLARE_ACCOUNT_ID=your_account_id_here

# 设置 AI 提供商为 Cloudflare（可选，系统会自动检测）
VITE_AI_PROVIDER=cloudflare
```

#### 生产环境（Cloudflare Pages）
1. 访问 [Cloudflare Pages Dashboard](https://dash.cloudflare.com/pages)
2. 选择您的项目（amzova-frontend）
3. 进入 "Settings" -> "Environment variables"
4. 添加以下变量：
   ```
   VITE_CLOUDFLARE_API_KEY: your_api_token_here
   VITE_CLOUDFLARE_ACCOUNT_ID: your_account_id_here
   VITE_AI_PROVIDER: cloudflare
   ```
5. 点击 "Save and deploy"

## 🔧 配置验证

### 1. 本地验证
启动开发服务器后，检查浏览器控制台：

```bash
npm run dev
```

在任何使用 AI 功能的工具页面（如标题分析器），查看 AI 服务状态组件：
- ✅ 绿色状态：配置正确，服务可用
- ⚠️ 黄色状态：接近免费额度限制
- ❌ 红色状态：配置错误或服务不可用

### 2. API 测试
您可以使用以下命令测试 API 连接：

```bash
curl -X POST "https://api.cloudflare.com/client/v4/accounts/YOUR_ACCOUNT_ID/ai/v1/chat/completions" \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "@cf/meta/llama-3.1-8b-instruct",
    "messages": [
      {"role": "user", "content": "Hello, world!"}
    ]
  }'
```

## 📊 使用监控

### 免费额度监控
- **每日限制**：10,000 Neurons
- **重置时间**：每日 UTC 00:00
- **监控方式**：AI 服务状态组件会显示当日使用情况

### 成本估算
不同模型的 Neurons 消耗（每1000 tokens）：

| 模型 | 输入 Neurons | 输出 Neurons | 适用场景 |
|------|-------------|-------------|----------|
| llama-3.1-8b-instruct | 25.608 | 75.147 | 标准任务（推荐） |
| llama-3.1-70b-instruct | 26.668 | 204.805 | 复杂任务 |
| llama-3.2-3b-instruct | 10.000 | 17.300 | 简单任务 |

### 使用优化建议
1. **选择合适的模型**：
   - 简单任务使用 `llama-3.2-3b-instruct`
   - 标准任务使用 `llama-3.1-8b-instruct`
   - 复杂任务使用 `llama-3.1-70b-instruct`

2. **优化 Prompt**：
   - 保持 Prompt 简洁明确
   - 避免不必要的上下文
   - 使用结构化输出格式

3. **缓存策略**：
   - 相同输入的结果会被缓存
   - 避免重复的 API 调用

## 🔄 迁移策略

### 渐进式迁移
1. **第一阶段**：保持 SiliconFlow 作为主要服务，Cloudflare 作为备用
2. **第二阶段**：将部分功能迁移到 Cloudflare（如标题合规检查）
3. **第三阶段**：完全迁移到 Cloudflare（保留 SiliconFlow 作为备用）

### 配置示例
```bash
# 双重配置，自动故障转移
VITE_AI_PROVIDER=cloudflare
VITE_CLOUDFLARE_API_KEY=your_cloudflare_token
VITE_CLOUDFLARE_ACCOUNT_ID=your_account_id
VITE_Silicon_API_KEY=your_siliconflow_key  # 备用
```

## 🚨 故障排除

### 常见错误

#### 1. "API key not configured"
- 检查环境变量是否正确设置
- 确认 API Token 权限是否包含 Workers AI

#### 2. "Account ID not found"
- 确认 Account ID 是否正确
- 检查 API Token 是否有访问该账户的权限

#### 3. "Rate limit exceeded"
- 检查是否超出免费额度
- 考虑升级到付费计划或优化使用频率

#### 4. "Model not available"
- 确认使用的模型名称是否正确
- 检查模型是否在您的地区可用

### 调试技巧
1. 打开浏览器开发者工具
2. 查看 Network 标签页的 API 请求
3. 检查 Console 标签页的错误信息
4. 使用 AI 服务状态组件查看实时状态

## 📞 支持

如果您在配置过程中遇到问题：

1. **查看文档**：[Cloudflare Workers AI 官方文档](https://developers.cloudflare.com/workers-ai/)
2. **检查状态**：[Cloudflare 系统状态](https://www.cloudflarestatus.com/)
3. **联系支持**：<EMAIL>

## 🎉 配置完成

配置完成后，您将享受到：
- ✅ 更低的 AI 服务成本
- ✅ 更快的响应速度
- ✅ 更高的服务可用性
- ✅ 更好的全球用户体验

恭喜！您已成功配置 Cloudflare Workers AI 集成。
