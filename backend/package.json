{"name": "sellerbox-backend", "version": "1.0.0", "description": "SellerBox 后端API服务", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest"}, "keywords": ["sellerbox", "feedback", "api", "mysql"], "author": "SellerBox Team", "license": "MIT", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "mysql2": "^3.6.5", "node-fetch": "^3.3.2"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}