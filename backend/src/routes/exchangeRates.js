const express = require('express');
const { body, query, validationResult } = require('express-validator');
const ExchangeRate = require('../models/ExchangeRate');

const router = express.Router();

// 验证错误处理中间件
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: '请求参数验证失败',
      errors: errors.array()
    });
  }
  next();
};

// 验证规则
const validateCurrency = [
  query('base').optional().isLength({ min: 3, max: 3 }).withMessage('基础货币代码必须是3位字符'),
  query('target').optional().isLength({ min: 3, max: 3 }).withMessage('目标货币代码必须是3位字符'),
];

const validateTimeRange = [
  query('range').optional().isIn(['1h', '1d', '1m', '3m']).withMessage('时间范围必须是: 1h, 1d, 1m, 3m')
];

// 获取最新汇率数据
router.get('/latest', validateCurrency, handleValidationErrors, async (req, res, next) => {
  try {
    const baseCurrency = (req.query.base || 'USD').toUpperCase();
    
    const result = await ExchangeRate.getLatestRates(baseCurrency);
    
    if (!result.success) {
      return res.status(404).json(result);
    }
    
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// 获取历史汇率数据（用于趋势图）
router.get('/historical', [...validateCurrency, ...validateTimeRange], handleValidationErrors, async (req, res, next) => {
  try {
    const baseCurrency = (req.query.base || 'USD').toUpperCase();
    const targetCurrency = (req.query.target || 'CNY').toUpperCase();
    const timeRange = req.query.range || '1d';
    
    if (baseCurrency === targetCurrency) {
      return res.status(400).json({
        success: false,
        message: '基础货币和目标货币不能相同'
      });
    }
    
    const result = await ExchangeRate.getHistoricalRates(baseCurrency, targetCurrency, timeRange);
    
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// 获取支持的货币列表
router.get('/currencies', validateCurrency, handleValidationErrors, async (req, res, next) => {
  try {
    const baseCurrency = (req.query.base || 'USD').toUpperCase();
    
    const result = await ExchangeRate.getSupportedCurrencies(baseCurrency);
    
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// 获取汇率统计信息
router.get('/stats', validateCurrency, handleValidationErrors, async (req, res, next) => {
  try {
    const baseCurrency = (req.query.base || 'USD').toUpperCase();
    
    const result = await ExchangeRate.getStats(baseCurrency);
    
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// 检查数据新鲜度
router.get('/freshness', validateCurrency, handleValidationErrors, async (req, res, next) => {
  try {
    const baseCurrency = (req.query.base || 'USD').toUpperCase();
    const maxAgeMinutes = parseInt(req.query.maxAge) || 15;
    
    const result = await ExchangeRate.checkDataFreshness(baseCurrency, maxAgeMinutes);
    
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// 手动触发数据清理（管理员功能）
router.post('/cleanup', async (req, res, next) => {
  try {
    const retentionDays = parseInt(req.body.retentionDays) || 120;
    
    if (retentionDays < 1 || retentionDays > 365) {
      return res.status(400).json({
        success: false,
        message: '保留天数必须在1-365之间'
      });
    }
    
    const result = await ExchangeRate.cleanupOldData(retentionDays);
    
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// 批量保存汇率数据（内部API，用于定时任务）
router.post('/save', [
  body('rates').isObject().withMessage('汇率数据必须是对象格式'),
  body('baseCurrency').optional().isLength({ min: 3, max: 3 }).withMessage('基础货币代码必须是3位字符'),
  body('apiSource').optional().isString().withMessage('API来源必须是字符串')
], handleValidationErrors, async (req, res, next) => {
  try {
    const { rates, baseCurrency = 'USD', apiSource = 'freecurrencyapi' } = req.body;
    
    const result = await ExchangeRate.saveRates(rates, baseCurrency.toUpperCase(), apiSource);
    
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// 获取特定货币对的最新汇率
router.get('/rate/:base/:target', async (req, res, next) => {
  try {
    const baseCurrency = req.params.base.toUpperCase();
    const targetCurrency = req.params.target.toUpperCase();
    
    if (baseCurrency.length !== 3 || targetCurrency.length !== 3) {
      return res.status(400).json({
        success: false,
        message: '货币代码必须是3位字符'
      });
    }
    
    if (baseCurrency === targetCurrency) {
      return res.json({
        success: true,
        data: {
          rate: 1,
          currencyPair: `${baseCurrency}/${targetCurrency}`,
          lastUpdated: new Date().toISOString()
        }
      });
    }
    
    const result = await ExchangeRate.getLatestRates(baseCurrency);
    
    if (!result.success || !result.data.rates[targetCurrency]) {
      return res.status(404).json({
        success: false,
        message: `未找到 ${baseCurrency}/${targetCurrency} 汇率数据`
      });
    }
    
    res.json({
      success: true,
      data: {
        rate: result.data.rates[targetCurrency],
        currencyPair: `${baseCurrency}/${targetCurrency}`,
        lastUpdated: result.data.lastUpdated,
        source: result.data.source
      }
    });
  } catch (error) {
    next(error);
  }
});

// 获取多个货币对的汇率
router.post('/rates/batch', [
  body('pairs').isArray().withMessage('货币对必须是数组格式'),
  body('pairs.*.base').isLength({ min: 3, max: 3 }).withMessage('基础货币代码必须是3位字符'),
  body('pairs.*.target').isLength({ min: 3, max: 3 }).withMessage('目标货币代码必须是3位字符')
], handleValidationErrors, async (req, res, next) => {
  try {
    const { pairs } = req.body;
    const results = [];
    
    for (const pair of pairs) {
      const baseCurrency = pair.base.toUpperCase();
      const targetCurrency = pair.target.toUpperCase();
      
      if (baseCurrency === targetCurrency) {
        results.push({
          currencyPair: `${baseCurrency}/${targetCurrency}`,
          rate: 1,
          success: true
        });
        continue;
      }
      
      try {
        const result = await ExchangeRate.getLatestRates(baseCurrency);
        
        if (result.success && result.data.rates[targetCurrency]) {
          results.push({
            currencyPair: `${baseCurrency}/${targetCurrency}`,
            rate: result.data.rates[targetCurrency],
            lastUpdated: result.data.lastUpdated,
            success: true
          });
        } else {
          results.push({
            currencyPair: `${baseCurrency}/${targetCurrency}`,
            success: false,
            error: '汇率数据不存在'
          });
        }
      } catch (error) {
        results.push({
          currencyPair: `${baseCurrency}/${targetCurrency}`,
          success: false,
          error: error.message
        });
      }
    }
    
    res.json({
      success: true,
      data: results
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
