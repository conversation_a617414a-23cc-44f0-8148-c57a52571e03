const express = require('express');
const router = express.Router();
const Announcement = require('../models/Announcement');
const { authenticateToken, requireRole } = require('../middleware/auth');
const { handleValidationErrors } = require('../middleware/validation');

// 验证公告数据的中间件
const validateAnnouncementData = (req, res, next) => {
  const { title, content, type, priority, status } = req.body;
  
  if (!title || title.trim().length === 0) {
    return res.status(400).json({
      success: false,
      message: '公告标题不能为空'
    });
  }
  
  if (!content || content.trim().length === 0) {
    return res.status(400).json({
      success: false,
      message: '公告内容不能为空'
    });
  }
  
  const validTypes = ['info', 'warning', 'success', 'error', 'maintenance'];
  if (type && !validTypes.includes(type)) {
    return res.status(400).json({
      success: false,
      message: '无效的公告类型'
    });
  }
  
  const validStatuses = ['draft', 'active', 'inactive', 'expired'];
  if (status && !validStatuses.includes(status)) {
    return res.status(400).json({
      success: false,
      message: '无效的公告状态'
    });
  }
  
  if (priority !== undefined && (isNaN(priority) || priority < 0 || priority > 100)) {
    return res.status(400).json({
      success: false,
      message: '优先级必须是0-100之间的数字'
    });
  }
  
  next();
};

// 公共接口：获取活跃公告（无需认证）
router.get('/active', async (req, res) => {
  try {
    const { language = 'zh-CN', user_session } = req.query;
    
    // 获取活跃的公告
    const announcements = await Announcement.getWithTranslations({
      active_only: true,
      target_audience: 'all'
    });
    
    // 获取用户已关闭的公告
    let closedAnnouncements = [];
    if (user_session) {
      closedAnnouncements = await Announcement.getUserClosedAnnouncements(user_session);
    }
    
    // 过滤掉用户已关闭的公告
    const filteredAnnouncements = announcements.filter(
      announcement => !closedAnnouncements.includes(announcement.id)
    );
    
    // 为每个公告添加对应语言的翻译
    const localizedAnnouncements = filteredAnnouncements.map(announcement => {
      const translation = announcement.translations.find(t => t.language_code === language);
      
      return {
        id: announcement.id,
        title: translation ? translation.title : announcement.title,
        content: translation ? translation.content : announcement.content,
        type: announcement.type,
        priority: announcement.priority,
        display_position: announcement.display_position,
        is_closable: announcement.is_closable,
        click_action: announcement.click_action,
        start_time: announcement.start_time,
        end_time: announcement.end_time
      };
    });
    
    res.json({
      success: true,
      data: localizedAnnouncements.sort((a, b) => b.priority - a.priority)
    });
  } catch (error) {
    console.error('获取活跃公告失败:', error);
    res.status(500).json({
      success: false,
      message: '获取公告失败',
      error: error.message
    });
  }
});

// 记录公告查看
router.post('/:id/view', async (req, res) => {
  try {
    const { id } = req.params;
    const { user_session } = req.body;
    const ipAddress = req.clientIP;
    const userAgent = req.get('User-Agent');
    
    if (!user_session) {
      return res.status(400).json({
        success: false,
        message: '缺少用户会话标识'
      });
    }
    
    await Announcement.recordView(id, user_session, ipAddress, userAgent);
    
    res.json({
      success: true,
      message: '查看记录已保存'
    });
  } catch (error) {
    console.error('记录公告查看失败:', error);
    res.status(500).json({
      success: false,
      message: '记录查看失败',
      error: error.message
    });
  }
});

// 记录公告关闭
router.post('/:id/close', async (req, res) => {
  try {
    const { id } = req.params;
    const { user_session } = req.body;
    
    if (!user_session) {
      return res.status(400).json({
        success: false,
        message: '缺少用户会话标识'
      });
    }
    
    const success = await Announcement.recordClose(id, user_session);
    
    res.json({
      success,
      message: success ? '公告已关闭' : '关闭失败'
    });
  } catch (error) {
    console.error('记录公告关闭失败:', error);
    res.status(500).json({
      success: false,
      message: '关闭公告失败',
      error: error.message
    });
  }
});

// 以下是管理员接口，需要认证

// 获取所有公告（管理员）
router.get('/', authenticateToken, requireRole(['super_admin', 'admin', 'viewer']), async (req, res) => {
  try {
    const { status, type, target_audience, page = 1, limit = 20 } = req.query;
    
    const filters = {
      status: status || undefined,
      type: type || undefined,
      target_audience: target_audience || undefined,
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit)
    };
    
    const announcements = await Announcement.getWithTranslations(filters);
    
    res.json({
      success: true,
      data: announcements,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('获取公告列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取公告列表失败',
      error: error.message
    });
  }
});

// 获取单个公告详情（管理员）
router.get('/:id', authenticateToken, requireRole(['super_admin', 'admin', 'viewer']), async (req, res) => {
  try {
    const { id } = req.params;
    const announcement = await Announcement.getById(id);
    
    if (!announcement) {
      return res.status(404).json({
        success: false,
        message: '公告不存在'
      });
    }
    
    // 获取翻译
    announcement.translations = await Announcement.getTranslations(id);
    
    res.json({
      success: true,
      data: announcement
    });
  } catch (error) {
    console.error('获取公告详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取公告详情失败',
      error: error.message
    });
  }
});

// 创建公告（管理员）
router.post('/', 
  authenticateToken, 
  requireRole(['super_admin', 'admin']), 
  validateAnnouncementData,
  async (req, res) => {
    try {
      const announcement = await Announcement.create(req.body, req.user.id);
      
      res.status(201).json({
        success: true,
        message: '公告创建成功',
        data: announcement
      });
    } catch (error) {
      console.error('创建公告失败:', error);
      res.status(500).json({
        success: false,
        message: '创建公告失败',
        error: error.message
      });
    }
  }
);

// 更新公告（管理员）
router.put('/:id', 
  authenticateToken, 
  requireRole(['super_admin', 'admin']), 
  validateAnnouncementData,
  async (req, res) => {
    try {
      const { id } = req.params;
      const announcement = await Announcement.update(id, req.body, req.user.id);
      
      if (!announcement) {
        return res.status(404).json({
          success: false,
          message: '公告不存在'
        });
      }
      
      res.json({
        success: true,
        message: '公告更新成功',
        data: announcement
      });
    } catch (error) {
      console.error('更新公告失败:', error);
      res.status(500).json({
        success: false,
        message: '更新公告失败',
        error: error.message
      });
    }
  }
);

// 删除公告（管理员）
router.delete('/:id', 
  authenticateToken, 
  requireRole(['super_admin', 'admin']),
  async (req, res) => {
    try {
      const { id } = req.params;
      const success = await Announcement.delete(id);
      
      if (!success) {
        return res.status(404).json({
          success: false,
          message: '公告不存在'
        });
      }
      
      res.json({
        success: true,
        message: '公告删除成功'
      });
    } catch (error) {
      console.error('删除公告失败:', error);
      res.status(500).json({
        success: false,
        message: '删除公告失败',
        error: error.message
      });
    }
  }
);

// 获取公告统计（管理员）
router.get('/stats/overview', authenticateToken, requireRole(['super_admin', 'admin', 'viewer']), async (req, res) => {
  try {
    const stats = await Announcement.getStats();
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('获取公告统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取统计数据失败',
      error: error.message
    });
  }
});

module.exports = router;
