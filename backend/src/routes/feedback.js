const express = require('express');
const rateLimit = require('express-rate-limit');
const router = express.Router();
const Feedback = require('../models/Feedback');
const {
  validateFeedback,
  validateStatusUpdate,
  handleValidationErrors
} = require('../middleware/validation');

// 反馈提交特殊限制
const feedbackSubmitLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5分钟
  max: 5, // 每个IP最多5个反馈
  message: {
    success: false,
    message: '反馈提交过于频繁，请5分钟后再试'
  }
});

// 创建新反馈 - 应用严格的频率限制
router.post('/', feedbackSubmitLimiter, validateFeedback, handleValidationErrors, async (req, res, next) => {
  try {
    const feedbackData = {
      ...req.body,
      user_agent: req.get('User-Agent'),
      ip_address: req.clientIP
    };

    const result = await Feedback.create(feedbackData);
    
    res.status(201).json(result);
  } catch (error) {
    next(error);
  }
});

// 获取反馈列表（支持分页和筛选）
router.get('/', async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    
    const filters = {};
    if (req.query.feedback_type) filters.feedback_type = req.query.feedback_type;
    if (req.query.status) filters.status = req.query.status;
    if (req.query.rating) filters.rating = parseInt(req.query.rating);

    const result = await Feedback.getAll(page, limit, filters);
    
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// 获取单个反馈
router.get('/:id', async (req, res, next) => {
  try {
    const id = parseInt(req.params.id);
    
    if (!id || id <= 0) {
      return res.status(400).json({
        success: false,
        message: '无效的反馈ID'
      });
    }

    const result = await Feedback.getById(id);
    
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// 更新反馈状态（管理员功能）
router.patch('/:id/status', validateStatusUpdate, handleValidationErrors, async (req, res, next) => {
  try {
    const id = parseInt(req.params.id);
    const { status, admin_notes } = req.body;
    
    if (!id || id <= 0) {
      return res.status(400).json({
        success: false,
        message: '无效的反馈ID'
      });
    }

    const result = await Feedback.updateStatus(id, status, admin_notes);
    
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// 删除反馈（管理员功能）
router.delete('/:id', async (req, res, next) => {
  try {
    const id = parseInt(req.params.id);
    
    if (!id || id <= 0) {
      return res.status(400).json({
        success: false,
        message: '无效的反馈ID'
      });
    }

    const result = await Feedback.delete(id);
    
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// 获取统计数据
router.get('/stats/overview', async (req, res, next) => {
  try {
    const result = await Feedback.getStats();
    
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// 获取最近反馈
router.get('/recent/list', async (req, res, next) => {
  try {
    const limit = parseInt(req.query.limit) || 5;
    const result = await Feedback.getRecent(limit);
    
    res.json(result);
  } catch (error) {
    next(error);
  }
});

module.exports = router;
