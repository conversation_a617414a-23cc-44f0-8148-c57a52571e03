const express = require('express');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const { 
  authenticateToken, 
  requireSuperAdmin, 
  requireAdmin,
  checkUserStatus,
  logApiAccess 
} = require('../middleware/auth');

const router = express.Router();

// 登录限流 - 每15分钟最多5次尝试
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 每个IP最多5次登录尝试
  message: {
    success: false,
    message: '登录尝试过于频繁，请15分钟后再试'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // 使用IP和用户名组合作为限流key
    return `${req.clientIP}_${req.body.username || 'unknown'}`;
  }
});

// 注册限流 - 每小时最多3次
const registerLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 3,
  message: {
    success: false,
    message: '注册请求过于频繁，请1小时后再试'
  }
});

// 登录验证规则
const validateLogin = [
  body('username')
    .notEmpty()
    .withMessage('用户名不能为空')
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度必须在3-50字符之间'),
  
  body('password')
    .notEmpty()
    .withMessage('密码不能为空')
    .isLength({ min: 6 })
    .withMessage('密码长度至少6位')
];

// 注册验证规则
const validateRegister = [
  body('username')
    .notEmpty()
    .withMessage('用户名不能为空')
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度必须在3-50字符之间')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名只能包含字母、数字和下划线'),
  
  body('email')
    .isEmail()
    .withMessage('邮箱格式不正确')
    .normalizeEmail(),
  
  body('password')
    .isLength({ min: 8 })
    .withMessage('密码长度至少8位')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('密码必须包含大小写字母和数字'),
  
  body('full_name')
    .optional()
    .isLength({ max: 100 })
    .withMessage('全名长度不能超过100字符'),
  
  body('role')
    .optional()
    .isIn(['admin', 'viewer'])
    .withMessage('角色必须是admin或viewer')
];

// 处理验证错误
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: '输入数据验证失败',
      errors: errors.array().map(error => ({
        field: error.path,
        message: error.msg,
        value: error.value
      }))
    });
  }
  
  next();
};

// 用户登录
router.post('/login', loginLimiter, validateLogin, handleValidationErrors, async (req, res) => {
  try {
    const { username, password } = req.body;
    const ip_address = req.clientIP;
    const user_agent = req.get('User-Agent');

    const result = await User.authenticate(username, password, ip_address, user_agent);

    res.json({
      success: true,
      message: '登录成功',
      data: result
    });
  } catch (error) {
    console.error('登录失败:', error);
    res.status(401).json({
      success: false,
      message: error.message || '登录失败'
    });
  }
});

// 刷新token
router.post('/refresh', async (req, res) => {
  try {
    const { refresh_token } = req.body;
    
    if (!refresh_token) {
      return res.status(400).json({
        success: false,
        message: '刷新token不能为空'
      });
    }

    const ip_address = req.clientIP;
    const user_agent = req.get('User-Agent');

    const result = await User.refreshToken(refresh_token, ip_address, user_agent);

    res.json({
      success: true,
      message: 'Token刷新成功',
      data: result
    });
  } catch (error) {
    console.error('Token刷新失败:', error);
    res.status(401).json({
      success: false,
      message: error.message || 'Token刷新失败'
    });
  }
});

// 用户登出
router.post('/logout', authenticateToken, async (req, res) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      await User.logout(token);
    }

    res.json({
      success: true,
      message: '登出成功'
    });
  } catch (error) {
    console.error('登出失败:', error);
    res.status(500).json({
      success: false,
      message: '登出失败'
    });
  }
});

// 获取当前用户信息
router.get('/me', authenticateToken, checkUserStatus, (req, res) => {
  res.json({
    success: true,
    data: {
      user: req.user,
      session: {
        expires_at: req.session.expires_at
      }
    }
  });
});

// 创建新用户（仅超级管理员）
router.post('/register', 
  authenticateToken, 
  requireSuperAdmin, 
  registerLimiter,
  validateRegister, 
  handleValidationErrors, 
  logApiAccess,
  async (req, res) => {
    try {
      const { username, email, password, full_name, role } = req.body;
      const created_by = req.user.id;

      const result = await User.create({
        username,
        email,
        password,
        full_name,
        role: role || 'admin',
        created_by
      });

      res.status(201).json({
        success: true,
        message: '用户创建成功',
        data: {
          id: result.id
        }
      });
    } catch (error) {
      console.error('用户创建失败:', error);
      res.status(400).json({
        success: false,
        message: error.message || '用户创建失败'
      });
    }
  }
);

// 验证token有效性（用于前端检查）
router.get('/verify', authenticateToken, checkUserStatus, (req, res) => {
  res.json({
    success: true,
    message: 'Token有效',
    data: {
      user: req.user,
      expires_at: req.session.expires_at
    }
  });
});

// 获取用户列表（管理员权限）
router.get('/users', 
  authenticateToken, 
  requireAdmin, 
  checkUserStatus,
  logApiAccess,
  async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const offset = (page - 1) * limit;

      const sql = `
        SELECT 
          id, username, email, full_name, role, status, 
          last_login, login_attempts, created_at, updated_at
        FROM admin_users 
        ORDER BY created_at DESC 
        LIMIT ? OFFSET ?
      `;

      const countSql = 'SELECT COUNT(*) as total FROM admin_users';
      
      const { executeQuery } = require('../config/database');
      const users = await executeQuery(sql, [limit, offset]);
      const countResult = await executeQuery(countSql);
      const total = countResult[0].total;

      res.json({
        success: true,
        data: {
          users,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit)
          }
        }
      });
    } catch (error) {
      console.error('获取用户列表失败:', error);
      res.status(500).json({
        success: false,
        message: '获取用户列表失败'
      });
    }
  }
);

// 修改密码验证规则
const validateChangePassword = [
  body('current_password')
    .notEmpty()
    .withMessage('当前密码不能为空'),

  body('new_password')
    .isLength({ min: 8 })
    .withMessage('新密码长度至少8位')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('新密码必须包含大小写字母和数字'),

  body('confirm_password')
    .custom((value, { req }) => {
      if (value !== req.body.new_password) {
        throw new Error('确认密码与新密码不匹配');
      }
      return true;
    })
];

// 修改密码
router.post('/change-password',
  authenticateToken,
  checkUserStatus,
  validateChangePassword,
  handleValidationErrors,
  logApiAccess,
  async (req, res) => {
    try {
      const { current_password, new_password } = req.body;
      const userId = req.user.id;

      const result = await User.changePassword(userId, current_password, new_password);

      res.json({
        success: true,
        message: '密码修改成功'
      });
    } catch (error) {
      console.error('修改密码失败:', error);
      res.status(400).json({
        success: false,
        message: error.message || '密码修改失败'
      });
    }
  }
);

// 清理过期sessions（系统维护接口）
router.post('/cleanup-sessions',
  authenticateToken,
  requireSuperAdmin,
  logApiAccess,
  async (req, res) => {
    try {
      const cleanedCount = await User.cleanupExpiredSessions();

      res.json({
        success: true,
        message: `清理了 ${cleanedCount} 个过期session`,
        data: {
          cleaned_count: cleanedCount
        }
      });
    } catch (error) {
      console.error('清理过期sessions失败:', error);
      res.status(500).json({
        success: false,
        message: '清理过期sessions失败'
      });
    }
  }
);

module.exports = router;
