const express = require('express');
const router = express.Router();
const Analytics = require('../models/Analytics');

// 记录工具使用
router.post('/tool-usage', async (req, res, next) => {
  try {
    const toolData = {
      ...req.body,
      user_agent: req.get('User-Agent'),
      user_ip: req.clientIP
    };

    const result = await Analytics.recordToolUsage(toolData);
    res.status(201).json(result);
  } catch (error) {
    next(error);
  }
});

// 记录页面访问
router.post('/page-view', async (req, res, next) => {
  try {
    const pageData = {
      ...req.body,
      user_agent: req.get('User-Agent'),
      user_ip: req.clientIP,
      referrer: req.get('Referer')
    };

    const result = await Analytics.recordPageView(pageData);
    res.status(201).json(result);
  } catch (error) {
    next(error);
  }
});

// 更新用户会话
router.post('/session', async (req, res, next) => {
  try {
    const sessionData = {
      ...req.body,
      user_agent: req.get('User-Agent'),
      user_ip: req.clientIP
    };

    const result = await Analytics.updateUserSession(sessionData);
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// 获取工具使用统计
router.get('/tool-usage-stats', async (req, res, next) => {
  try {
    const result = await Analytics.getToolUsageStats();
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// 获取页面访问统计
router.get('/page-view-stats', async (req, res, next) => {
  try {
    const result = await Analytics.getPageViewStats();
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// 获取总体运营统计
router.get('/overall', async (req, res, next) => {
  try {
    const result = await Analytics.getOverallAnalytics();
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// 获取热门工具排行
router.get('/top-tools', async (req, res, next) => {
  try {
    const limit = parseInt(req.query.limit) || 10;
    const result = await Analytics.getTopTools(limit);
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// 获取使用趋势
router.get('/usage-trend', async (req, res, next) => {
  try {
    const days = parseInt(req.query.days) || 7;
    const result = await Analytics.getUsageTrend(days);
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// 获取用户地理分布
router.get('/geography', async (req, res, next) => {
  try {
    const result = await Analytics.getUserGeography();
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// 获取实时统计
router.get('/realtime', async (req, res, next) => {
  try {
    const result = await Analytics.getRealTimeStats();
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// 批量记录数据（用于前端批量上报）
router.post('/batch', async (req, res, next) => {
  try {
    const { events } = req.body;
    const results = [];

    for (const event of events) {
      const eventData = {
        ...event,
        user_agent: req.get('User-Agent'),
        user_ip: req.clientIP
      };

      let result;
      switch (event.type) {
        case 'tool_usage':
          result = await Analytics.recordToolUsage(eventData);
          break;
        case 'page_view':
          eventData.referrer = req.get('Referer');
          result = await Analytics.recordPageView(eventData);
          break;
        case 'session':
          result = await Analytics.updateUserSession(eventData);
          break;
        default:
          result = { success: false, message: '未知事件类型' };
      }
      
      results.push(result);
    }

    res.json({
      success: true,
      results,
      processed: results.length
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
