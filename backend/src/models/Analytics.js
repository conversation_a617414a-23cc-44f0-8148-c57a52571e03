const { executeQuery } = require('../config/database');

class Analytics {
  // 记录工具使用
  static async recordToolUsage(toolData) {
    const {
      tool_id,
      tool_name,
      user_ip,
      user_agent,
      session_id,
      usage_type = 'view',
      usage_data = null
    } = toolData;

    const sql = `
      INSERT INTO tool_usage 
      (tool_id, tool_name, user_ip, user_agent, session_id, usage_type, usage_data) 
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      tool_id,
      tool_name,
      user_ip || null,
      user_agent || null,
      session_id || null,
      usage_type,
      usage_data ? JSON.stringify(usage_data) : null
    ];

    try {
      const result = await executeQuery(sql, params);
      return {
        success: true,
        id: result.insertId
      };
    } catch (error) {
      console.error('记录工具使用失败:', error);
      throw new Error('记录工具使用失败');
    }
  }

  // 记录页面访问
  static async recordPageView(pageData) {
    const {
      page_path,
      page_title,
      user_ip,
      user_agent,
      session_id,
      referrer
    } = pageData;

    const sql = `
      INSERT INTO page_views 
      (page_path, page_title, user_ip, user_agent, session_id, referrer) 
      VALUES (?, ?, ?, ?, ?, ?)
    `;

    const params = [
      page_path,
      page_title || null,
      user_ip || null,
      user_agent || null,
      session_id || null,
      referrer || null
    ];

    try {
      const result = await executeQuery(sql, params);
      return {
        success: true,
        id: result.insertId
      };
    } catch (error) {
      console.error('记录页面访问失败:', error);
      throw new Error('记录页面访问失败');
    }
  }

  // 更新或创建用户会话
  static async updateUserSession(sessionData) {
    const {
      session_id,
      user_ip,
      user_agent,
      page_views = 0,
      tool_uses = 0
    } = sessionData;

    const sql = `
      INSERT INTO user_sessions (session_id, user_ip, user_agent, page_views, tool_uses)
      VALUES (?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        last_visit = CURRENT_TIMESTAMP,
        page_views = page_views + VALUES(page_views),
        tool_uses = tool_uses + VALUES(tool_uses)
    `;

    const params = [
      session_id,
      user_ip || null,
      user_agent || null,
      page_views,
      tool_uses
    ];

    try {
      const result = await executeQuery(sql, params);
      return {
        success: true,
        id: result.insertId
      };
    } catch (error) {
      console.error('更新用户会话失败:', error);
      throw new Error('更新用户会话失败');
    }
  }

  // 获取工具使用统计
  static async getToolUsageStats() {
    const sql = 'SELECT * FROM tool_usage_stats ORDER BY total_uses DESC';
    
    try {
      const result = await executeQuery(sql);
      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('获取工具使用统计失败:', error);
      throw new Error('获取工具使用统计失败');
    }
  }

  // 获取页面访问统计
  static async getPageViewStats() {
    const sql = 'SELECT * FROM page_view_stats ORDER BY total_views DESC';
    
    try {
      const result = await executeQuery(sql);
      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('获取页面访问统计失败:', error);
      throw new Error('获取页面访问统计失败');
    }
  }

  // 获取总体运营统计
  static async getOverallAnalytics() {
    const sql = 'SELECT * FROM overall_analytics';
    
    try {
      const result = await executeQuery(sql);
      return {
        success: true,
        data: result[0] || {}
      };
    } catch (error) {
      console.error('获取总体运营统计失败:', error);
      throw new Error('获取总体运营统计失败');
    }
  }

  // 获取热门工具排行
  static async getTopTools(limit = 10) {
    const sql = `
      SELECT tool_id, tool_name, total_uses, unique_users, daily_uses, weekly_uses
      FROM tool_usage_stats 
      ORDER BY total_uses DESC 
      LIMIT ?
    `;
    
    try {
      const result = await executeQuery(sql, [parseInt(limit)]);
      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('获取热门工具排行失败:', error);
      throw new Error('获取热门工具排行失败');
    }
  }

  // 获取时间段内的使用趋势
  static async getUsageTrend(days = 7) {
    const sql = `
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as total_uses,
        COUNT(DISTINCT tool_id) as unique_tools,
        COUNT(DISTINCT user_ip) as unique_users
      FROM tool_usage 
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `;
    
    try {
      const result = await executeQuery(sql, [parseInt(days)]);
      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('获取使用趋势失败:', error);
      throw new Error('获取使用趋势失败');
    }
  }

  // 获取用户地理分布（如果有地理信息）
  static async getUserGeography() {
    const sql = `
      SELECT 
        country,
        city,
        COUNT(*) as session_count,
        COUNT(DISTINCT user_ip) as unique_users
      FROM user_sessions 
      WHERE country IS NOT NULL
      GROUP BY country, city
      ORDER BY session_count DESC
      LIMIT 20
    `;
    
    try {
      const result = await executeQuery(sql);
      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('获取用户地理分布失败:', error);
      throw new Error('获取用户地理分布失败');
    }
  }

  // 获取实时统计（最近24小时）
  static async getRealTimeStats() {
    const sql = `
      SELECT 
        (SELECT COUNT(*) FROM tool_usage WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)) as hourly_tool_uses,
        (SELECT COUNT(*) FROM page_views WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)) as hourly_page_views,
        (SELECT COUNT(DISTINCT session_id) FROM page_views WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)) as hourly_active_sessions,
        (SELECT COUNT(*) FROM tool_usage WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as daily_tool_uses,
        (SELECT COUNT(*) FROM page_views WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as daily_page_views,
        (SELECT COUNT(DISTINCT session_id) FROM page_views WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as daily_active_sessions
    `;
    
    try {
      const result = await executeQuery(sql);
      return {
        success: true,
        data: result[0] || {}
      };
    } catch (error) {
      console.error('获取实时统计失败:', error);
      throw new Error('获取实时统计失败');
    }
  }
}

module.exports = Analytics;
