const { executeQuery } = require('../config/database');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');

class User {
  // 创建新用户
  static async create(userData) {
    const {
      username,
      email,
      password,
      full_name,
      role = 'admin',
      created_by = null
    } = userData;

    // 检查用户名和邮箱是否已存在
    const existingUser = await this.findByUsernameOrEmail(username, email);
    if (existingUser) {
      throw new Error('用户名或邮箱已存在');
    }

    // 加密密码
    const saltRounds = 10;
    const password_hash = await bcrypt.hash(password, saltRounds);

    const sql = `
      INSERT INTO admin_users 
      (username, email, password_hash, full_name, role, created_by) 
      VALUES (?, ?, ?, ?, ?, ?)
    `;

    const params = [
      username,
      email,
      password_hash,
      full_name || null,
      role,
      created_by
    ];

    try {
      const result = await executeQuery(sql, params);
      
      // 记录操作日志
      await this.logAction(created_by, 'create_user', 'admin_users', result.insertId, {
        username,
        email,
        role
      });

      return {
        success: true,
        id: result.insertId,
        message: '用户创建成功'
      };
    } catch (error) {
      console.error('创建用户失败:', error);
      throw new Error('用户创建失败');
    }
  }

  // 根据用户名或邮箱查找用户
  static async findByUsernameOrEmail(username, email) {
    const sql = `
      SELECT * FROM admin_users 
      WHERE username = ? OR email = ? 
      LIMIT 1
    `;
    
    const result = await executeQuery(sql, [username, email]);
    return result.length > 0 ? result[0] : null;
  }

  // 根据ID查找用户
  static async findById(id) {
    const sql = 'SELECT * FROM admin_users WHERE id = ? AND status = "active"';
    const result = await executeQuery(sql, [id]);
    return result.length > 0 ? result[0] : null;
  }

  // 验证用户登录
  static async authenticate(username, password, ip_address, user_agent) {
    try {
      // 查找用户
      const user = await this.findByUsernameOrEmail(username, username);
      if (!user) {
        throw new Error('用户名或密码错误');
      }

      // 检查用户状态
      if (user.status !== 'active') {
        throw new Error('账户已被禁用');
      }

      // 检查是否被锁定
      if (user.locked_until && new Date() < new Date(user.locked_until)) {
        const lockTime = Math.ceil((new Date(user.locked_until) - new Date()) / 1000 / 60);
        throw new Error(`账户已被锁定，请${lockTime}分钟后再试`);
      }

      // 验证密码
      const isValidPassword = await bcrypt.compare(password, user.password_hash);
      
      if (!isValidPassword) {
        // 增加登录失败次数
        await this.incrementLoginAttempts(user.id);
        throw new Error('用户名或密码错误');
      }

      // 重置登录尝试次数并更新最后登录时间
      await this.resetLoginAttempts(user.id);

      // 生成JWT token
      const tokenData = await this.generateTokens(user, ip_address, user_agent);

      // 记录登录日志
      await this.logAction(user.id, 'login', 'admin_users', user.id, {
        ip_address,
        user_agent: user_agent ? user_agent.substring(0, 200) : null
      }, ip_address, user_agent);

      return {
        success: true,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          full_name: user.full_name,
          role: user.role
        },
        ...tokenData
      };
    } catch (error) {
      console.error('用户认证失败:', error);
      throw error;
    }
  }

  // 生成JWT tokens
  static async generateTokens(user, ip_address, user_agent) {
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      throw new Error('JWT密钥未配置');
    }

    // 生成访问token（1小时有效）
    const accessToken = jwt.sign(
      {
        id: user.id,
        username: user.username,
        role: user.role
      },
      jwtSecret,
      { expiresIn: '1h' }
    );

    // 生成刷新token（7天有效）
    const refreshToken = jwt.sign(
      {
        id: user.id,
        type: 'refresh'
      },
      jwtSecret,
      { expiresIn: '7d' }
    );

    // 计算token哈希
    const tokenHash = crypto.createHash('sha256').update(accessToken).digest('hex');
    const refreshTokenHash = crypto.createHash('sha256').update(refreshToken).digest('hex');

    // 保存session到数据库
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1小时后过期
    
    const sql = `
      INSERT INTO admin_sessions 
      (user_id, token_hash, refresh_token_hash, ip_address, user_agent, expires_at) 
      VALUES (?, ?, ?, ?, ?, ?)
    `;

    await executeQuery(sql, [
      user.id,
      tokenHash,
      refreshTokenHash,
      ip_address,
      user_agent ? user_agent.substring(0, 500) : null,
      expiresAt
    ]);

    return {
      access_token: accessToken,
      refresh_token: refreshToken,
      expires_in: 3600, // 1小时
      token_type: 'Bearer'
    };
  }

  // 验证token
  static async verifyToken(token) {
    try {
      const jwtSecret = process.env.JWT_SECRET;
      if (!jwtSecret) {
        throw new Error('JWT密钥未配置');
      }

      // 验证JWT token
      const decoded = jwt.verify(token, jwtSecret);
      
      // 计算token哈希
      const tokenHash = crypto.createHash('sha256').update(token).digest('hex');

      // 检查session是否存在且未过期
      const sql = `
        SELECT s.*, u.username, u.email, u.full_name, u.role, u.status
        FROM admin_sessions s
        JOIN admin_users u ON s.user_id = u.id
        WHERE s.token_hash = ? AND s.expires_at > NOW() AND u.status = 'active'
      `;

      const result = await executeQuery(sql, [tokenHash]);
      
      if (result.length === 0) {
        throw new Error('Token无效或已过期');
      }

      const session = result[0];

      // 更新session最后使用时间
      await executeQuery(
        'UPDATE admin_sessions SET last_used = NOW() WHERE id = ?',
        [session.id]
      );

      return {
        success: true,
        user: {
          id: session.user_id,
          username: session.username,
          email: session.email,
          full_name: session.full_name,
          role: session.role
        },
        session: {
          id: session.id,
          expires_at: session.expires_at
        }
      };
    } catch (error) {
      console.error('Token验证失败:', error);
      throw new Error('Token无效');
    }
  }

  // 刷新token
  static async refreshToken(refreshToken, ip_address, user_agent) {
    try {
      const jwtSecret = process.env.JWT_SECRET;
      const decoded = jwt.verify(refreshToken, jwtSecret);

      if (decoded.type !== 'refresh') {
        throw new Error('无效的刷新token');
      }

      // 查找用户
      const user = await this.findById(decoded.id);
      if (!user) {
        throw new Error('用户不存在');
      }

      // 计算刷新token哈希
      const refreshTokenHash = crypto.createHash('sha256').update(refreshToken).digest('hex');

      // 验证刷新token
      const sql = `
        SELECT * FROM admin_sessions 
        WHERE user_id = ? AND refresh_token_hash = ? AND expires_at > NOW()
      `;
      
      const sessions = await executeQuery(sql, [user.id, refreshTokenHash]);
      
      if (sessions.length === 0) {
        throw new Error('刷新token无效或已过期');
      }

      // 删除旧的session
      await executeQuery('DELETE FROM admin_sessions WHERE id = ?', [sessions[0].id]);

      // 生成新的tokens
      const tokenData = await this.generateTokens(user, ip_address, user_agent);

      return {
        success: true,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          full_name: user.full_name,
          role: user.role
        },
        ...tokenData
      };
    } catch (error) {
      console.error('刷新token失败:', error);
      throw new Error('刷新token失败');
    }
  }

  // 登出
  static async logout(token) {
    try {
      const tokenHash = crypto.createHash('sha256').update(token).digest('hex');
      
      // 删除session
      const result = await executeQuery(
        'DELETE FROM admin_sessions WHERE token_hash = ?',
        [tokenHash]
      );

      return {
        success: true,
        message: '登出成功'
      };
    } catch (error) {
      console.error('登出失败:', error);
      throw new Error('登出失败');
    }
  }

  // 增加登录尝试次数
  static async incrementLoginAttempts(userId) {
    const sql = `
      UPDATE admin_users 
      SET login_attempts = login_attempts + 1,
          locked_until = CASE 
            WHEN login_attempts >= 4 THEN DATE_ADD(NOW(), INTERVAL 30 MINUTE)
            ELSE locked_until
          END
      WHERE id = ?
    `;
    
    await executeQuery(sql, [userId]);
  }

  // 重置登录尝试次数
  static async resetLoginAttempts(userId) {
    const sql = `
      UPDATE admin_users 
      SET login_attempts = 0, 
          locked_until = NULL, 
          last_login = NOW() 
      WHERE id = ?
    `;
    
    await executeQuery(sql, [userId]);
  }

  // 记录操作日志
  static async logAction(userId, action, resource, resourceId, details, ipAddress, userAgent) {
    try {
      const sql = `
        INSERT INTO admin_logs 
        (user_id, action, resource, resource_id, details, ip_address, user_agent) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `;

      await executeQuery(sql, [
        userId,
        action,
        resource || null,
        resourceId || null,
        details ? JSON.stringify(details) : null,
        ipAddress || null,
        userAgent ? userAgent.substring(0, 500) : null
      ]);
    } catch (error) {
      console.error('记录操作日志失败:', error);
      // 不抛出错误，避免影响主要功能
    }
  }

  // 修改密码
  static async changePassword(userId, currentPassword, newPassword) {
    try {
      // 获取用户信息
      const user = await this.findById(userId);
      if (!user) {
        throw new Error('用户不存在');
      }

      // 验证当前密码
      const isValidPassword = await bcrypt.compare(currentPassword, user.password_hash);
      if (!isValidPassword) {
        throw new Error('当前密码错误');
      }

      // 检查新密码是否与当前密码相同
      const isSamePassword = await bcrypt.compare(newPassword, user.password_hash);
      if (isSamePassword) {
        throw new Error('新密码不能与当前密码相同');
      }

      // 加密新密码
      const saltRounds = 10;
      const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

      // 更新密码
      const sql = `
        UPDATE admin_users
        SET password_hash = ?, updated_at = NOW()
        WHERE id = ?
      `;

      await executeQuery(sql, [newPasswordHash, userId]);

      // 记录操作日志
      await this.logAction(userId, 'change_password', 'admin_users', userId, {
        message: '用户修改密码'
      });

      // 清除该用户的所有session（强制重新登录）
      await executeQuery('DELETE FROM admin_sessions WHERE user_id = ?', [userId]);

      return {
        success: true,
        message: '密码修改成功，请重新登录'
      };
    } catch (error) {
      console.error('修改密码失败:', error);
      throw error;
    }
  }

  // 清理过期的sessions
  static async cleanupExpiredSessions() {
    try {
      const result = await executeQuery(
        'DELETE FROM admin_sessions WHERE expires_at < NOW()'
      );

      console.log(`清理了 ${result.affectedRows} 个过期session`);
      return result.affectedRows;
    } catch (error) {
      console.error('清理过期session失败:', error);
      return 0;
    }
  }
}

module.exports = User;
