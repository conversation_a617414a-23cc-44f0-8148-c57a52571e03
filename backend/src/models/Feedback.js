const { executeQuery } = require('../config/database');

class Feedback {
  // 创建新反馈
  static async create(feedbackData) {
    const {
      feedback_type,
      rating,
      message,
      email,
      user_agent,
      ip_address
    } = feedbackData;

    const sql = `
      INSERT INTO user_feedback 
      (feedback_type, rating, message, email, user_agent, ip_address) 
      VALUES (?, ?, ?, ?, ?, ?)
    `;

    const params = [
      feedback_type,
      rating,
      message,
      email || null,
      user_agent || null,
      ip_address || null
    ];

    try {
      const result = await executeQuery(sql, params);
      return {
        success: true,
        id: result.insertId,
        message: '反馈提交成功'
      };
    } catch (error) {
      console.error('创建反馈失败:', error);
      throw new Error('反馈提交失败');
    }
  }

  // 获取所有反馈（分页）
  static async getAll(page = 1, limit = 10, filters = {}) {
    const offset = (page - 1) * limit;
    let whereClause = '';
    let filterParams = [];

    // 构建筛选条件
    if (filters.feedback_type) {
      whereClause += ' WHERE feedback_type = ?';
      filterParams.push(filters.feedback_type);
    }

    if (filters.status) {
      whereClause += whereClause ? ' AND status = ?' : ' WHERE status = ?';
      filterParams.push(filters.status);
    }

    if (filters.rating) {
      whereClause += whereClause ? ' AND rating = ?' : ' WHERE rating = ?';
      filterParams.push(filters.rating);
    }

    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM user_feedback${whereClause}`;
    const countResult = await executeQuery(countSql, filterParams);
    const total = countResult[0].total;

    // 获取数据 - 使用新的参数数组
    const sql = `
      SELECT * FROM user_feedback
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
    `;

    const feedbacks = await executeQuery(sql, filterParams);

    return {
      success: true,
      data: feedbacks,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  // 根据ID获取反馈
  static async getById(id) {
    const sql = 'SELECT * FROM user_feedback WHERE id = ?';
    const result = await executeQuery(sql, [id]);
    
    if (result.length === 0) {
      throw new Error('反馈不存在');
    }

    return {
      success: true,
      data: result[0]
    };
  }

  // 更新反馈状态
  static async updateStatus(id, status, adminNotes = null) {
    const sql = `
      UPDATE user_feedback 
      SET status = ?, admin_notes = ?, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `;

    const result = await executeQuery(sql, [status, adminNotes, id]);

    if (result.affectedRows === 0) {
      throw new Error('反馈不存在');
    }

    return {
      success: true,
      message: '状态更新成功'
    };
  }

  // 删除反馈
  static async delete(id) {
    const sql = 'DELETE FROM user_feedback WHERE id = ?';
    const result = await executeQuery(sql, [id]);

    if (result.affectedRows === 0) {
      throw new Error('反馈不存在');
    }

    return {
      success: true,
      message: '反馈删除成功'
    };
  }

  // 获取统计数据
  static async getStats() {
    const overallStatsSql = 'SELECT * FROM overall_stats';
    const feedbackStatsSql = 'SELECT * FROM feedback_stats';

    const [overallStats, feedbackStats] = await Promise.all([
      executeQuery(overallStatsSql),
      executeQuery(feedbackStatsSql)
    ]);

    return {
      success: true,
      data: {
        overall: overallStats[0] || {},
        byType: feedbackStats || []
      }
    };
  }

  // 获取最近的反馈
  static async getRecent(limit = 5) {
    const sql = `
      SELECT id, feedback_type, rating, message, created_at, status
      FROM user_feedback 
      ORDER BY created_at DESC 
      LIMIT ?
    `;

    const result = await executeQuery(sql, [limit]);

    return {
      success: true,
      data: result
    };
  }
}

module.exports = Feedback;
