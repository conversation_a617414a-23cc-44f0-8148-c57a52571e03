const { executeQuery } = require('../config/database');

class Announcement {
  // 获取所有公告（支持筛选和分页）
  static async getAll(filters = {}) {
    // 简化版本，先不使用复杂的筛选
    const sql = `
      SELECT
        a.*,
        au_created.username as created_by_username,
        au_updated.username as updated_by_username
      FROM system_announcements a
      LEFT JOIN admin_users au_created ON a.created_by = au_created.id
      LEFT JOIN admin_users au_updated ON a.updated_by = au_updated.id
      ORDER BY a.priority DESC, a.created_at DESC
      LIMIT 50
    `;

    return await executeQuery(sql, []);
  }

  // 根据ID获取单个公告
  static async getById(id) {
    const sql = `
      SELECT 
        a.*,
        au_created.username as created_by_username,
        au_updated.username as updated_by_username
      FROM system_announcements a
      LEFT JOIN admin_users au_created ON a.created_by = au_created.id
      LEFT JOIN admin_users au_updated ON a.updated_by = au_updated.id
      WHERE a.id = ?
    `;
    const results = await executeQuery(sql, [id]);
    return results[0] || null;
  }

  // 获取公告的翻译
  static async getTranslations(announcementId, languageCode = null) {
    let sql = `
      SELECT * FROM announcement_translations 
      WHERE announcement_id = ?
    `;
    const params = [announcementId];

    if (languageCode) {
      sql += ' AND language_code = ?';
      params.push(languageCode);
    }

    sql += ' ORDER BY language_code';
    return await executeQuery(sql, params);
  }

  // 获取带翻译的公告（用于前端显示）
  static async getWithTranslations(filters = {}) {
    const announcements = await this.getAll(filters);
    
    for (let announcement of announcements) {
      announcement.translations = await this.getTranslations(announcement.id);
    }
    
    return announcements;
  }

  // 创建新公告
  static async create(data, createdBy) {
    const {
      title, content, type = 'info', priority = 0, status = 'draft',
      start_time, end_time, target_audience = 'all', display_position = 'header',
      is_closable = true, click_action, translations = []
    } = data;

    const sql = `
      INSERT INTO system_announcements 
      (title, content, type, priority, status, start_time, end_time, 
       target_audience, display_position, is_closable, click_action, created_by)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      title, content, type, priority, status, start_time || null, end_time || null,
      target_audience, display_position, is_closable, click_action || null, createdBy
    ];

    const result = await executeQuery(sql, params);
    const announcementId = result.insertId;

    // 添加翻译
    if (translations && translations.length > 0) {
      await this.addTranslations(announcementId, translations);
    }

    return await this.getById(announcementId);
  }

  // 更新公告
  static async update(id, data, updatedBy) {
    const {
      title, content, type, priority, status, start_time, end_time,
      target_audience, display_position, is_closable, click_action, translations
    } = data;

    const sql = `
      UPDATE system_announcements 
      SET title = ?, content = ?, type = ?, priority = ?, status = ?,
          start_time = ?, end_time = ?, target_audience = ?, display_position = ?,
          is_closable = ?, click_action = ?, updated_by = ?
      WHERE id = ?
    `;

    const params = [
      title, content, type, priority, status, start_time || null, end_time || null,
      target_audience, display_position, is_closable, click_action || null, updatedBy, id
    ];

    await executeQuery(sql, params);

    // 更新翻译
    if (translations && translations.length > 0) {
      // 先删除现有翻译
      await executeQuery('DELETE FROM announcement_translations WHERE announcement_id = ?', [id]);
      // 添加新翻译
      await this.addTranslations(id, translations);
    }

    return await this.getById(id);
  }

  // 删除公告
  static async delete(id) {
    // 由于外键约束，翻译和查看记录会自动删除
    const sql = 'DELETE FROM system_announcements WHERE id = ?';
    const result = await executeQuery(sql, [id]);
    return result.affectedRows > 0;
  }

  // 添加翻译
  static async addTranslations(announcementId, translations) {
    if (!translations || translations.length === 0) return;

    const sql = `
      INSERT INTO announcement_translations (announcement_id, language_code, title, content)
      VALUES (?, ?, ?, ?)
    `;

    for (const translation of translations) {
      await executeQuery(sql, [
        announcementId,
        translation.language_code,
        translation.title,
        translation.content
      ]);
    }
  }

  // 记录用户查看
  static async recordView(announcementId, userSession, ipAddress, userAgent) {
    const sql = `
      INSERT INTO user_announcement_views 
      (announcement_id, user_session, ip_address, user_agent)
      VALUES (?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE viewed_at = CURRENT_TIMESTAMP
    `;

    await executeQuery(sql, [announcementId, userSession, ipAddress, userAgent]);

    // 更新公告的查看次数
    await executeQuery(
      'UPDATE system_announcements SET view_count = view_count + 1 WHERE id = ?',
      [announcementId]
    );
  }

  // 记录用户关闭公告
  static async recordClose(announcementId, userSession) {
    const sql = `
      UPDATE user_announcement_views 
      SET is_closed = TRUE, closed_at = CURRENT_TIMESTAMP
      WHERE announcement_id = ? AND user_session = ?
    `;

    const result = await executeQuery(sql, [announcementId, userSession]);
    return result.affectedRows > 0;
  }

  // 获取公告统计
  static async getStats(announcementId = null) {
    let sql = 'SELECT * FROM announcement_stats';
    const params = [];

    if (announcementId) {
      sql += ' WHERE id = ?';
      params.push(announcementId);
    }

    sql += ' ORDER BY priority DESC, total_views DESC';
    return await executeQuery(sql, params);
  }

  // 获取用户已关闭的公告列表
  static async getUserClosedAnnouncements(userSession) {
    const sql = `
      SELECT announcement_id 
      FROM user_announcement_views 
      WHERE user_session = ? AND is_closed = TRUE
    `;
    const results = await executeQuery(sql, [userSession]);
    return results.map(row => row.announcement_id);
  }
}

module.exports = Announcement;
