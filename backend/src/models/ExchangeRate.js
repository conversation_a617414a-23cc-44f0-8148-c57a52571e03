const { executeQuery } = require('../config/database');

class ExchangeRate {
  // 批量保存汇率数据
  static async saveRates(ratesData, baseCurrency = 'USD', apiSource = 'freecurrencyapi') {
    if (!ratesData || typeof ratesData !== 'object') {
      throw new Error('汇率数据格式无效');
    }

    const values = [];
    const placeholders = [];

    for (const [currency, rate] of Object.entries(ratesData)) {
      if (currency !== baseCurrency && typeof rate === 'number' && rate > 0) {
        values.push(baseCurrency, currency, rate, apiSource);
        placeholders.push('(?, ?, ?, ?)');
      }
    }

    if (values.length === 0) {
      throw new Error('没有有效的汇率数据');
    }

    const sql = `
      INSERT INTO exchange_rates (base_currency, target_currency, rate, api_source) 
      VALUES ${placeholders.join(', ')}
    `;

    try {
      const result = await executeQuery(sql, values);
      return {
        success: true,
        inserted: result.affectedRows,
        message: `成功保存 ${result.affectedRows} 条汇率数据`
      };
    } catch (error) {
      console.error('保存汇率数据失败:', error);
      throw new Error('保存汇率数据失败');
    }
  }

  // 获取最新汇率数据
  static async getLatestRates(baseCurrency = 'USD') {
    const sql = `
      SELECT 
        target_currency,
        rate,
        created_at,
        api_source
      FROM exchange_rates 
      WHERE base_currency = ? 
        AND created_at = (
          SELECT MAX(created_at) 
          FROM exchange_rates 
          WHERE base_currency = ?
        )
      ORDER BY target_currency
    `;

    try {
      const rates = await executeQuery(sql, [baseCurrency, baseCurrency]);
      
      if (rates.length === 0) {
        return {
          success: false,
          message: '没有找到汇率数据'
        };
      }

      // 转换为对象格式
      const ratesObject = {};
      let lastUpdated = null;

      rates.forEach(rate => {
        ratesObject[rate.target_currency] = rate.rate;
        if (!lastUpdated || rate.created_at > lastUpdated) {
          lastUpdated = rate.created_at;
        }
      });

      // 添加基础货币
      ratesObject[baseCurrency] = 1;

      return {
        success: true,
        data: {
          rates: ratesObject,
          lastUpdated: lastUpdated,
          baseCurrency: baseCurrency,
          source: rates[0]?.api_source || 'unknown'
        }
      };
    } catch (error) {
      console.error('获取最新汇率失败:', error);
      throw new Error('获取最新汇率失败');
    }
  }

  // 获取历史汇率数据（用于趋势图）
  static async getHistoricalRates(baseCurrency, targetCurrency, timeRange = '1d') {
    let interval;
    let dateFormat;
    let groupByFormat;

    switch (timeRange) {
      case '1h':
        interval = '1 HOUR';
        dateFormat = '%Y-%m-%d %H:%i:00';
        groupByFormat = '%Y-%m-%d %H:%i';
        break;
      case '1d':
        interval = '1 DAY';
        dateFormat = '%Y-%m-%d %H:00:00';
        groupByFormat = '%Y-%m-%d %H';
        break;
      case '1m':
        interval = '30 DAY';
        dateFormat = '%Y-%m-%d 00:00:00';
        groupByFormat = '%Y-%m-%d';
        break;
      case '3m':
        interval = '90 DAY';
        dateFormat = '%Y-%u 00:00:00'; // 按周分组
        groupByFormat = '%Y-%u';
        break;
      default:
        interval = '1 DAY';
        dateFormat = '%Y-%m-%d %H:00:00';
        groupByFormat = '%Y-%m-%d %H';
    }

    // 首先检查是否有足够的历史数据
    const countSql = `
      SELECT COUNT(*) as total_records
      FROM exchange_rates
      WHERE base_currency = ?
        AND target_currency = ?
        AND created_at >= DATE_SUB(NOW(), INTERVAL ${interval})
    `;

    const countResult = await executeQuery(countSql, [baseCurrency, targetCurrency]);
    const totalRecords = countResult[0]?.total_records || 0;

    console.log(`📊 查询历史数据: ${baseCurrency}/${targetCurrency}, 时间范围: ${timeRange}, 记录数: ${totalRecords}`);

    // 如果没有足够的历史数据，生成模拟数据
    if (totalRecords < 5) {
      console.log('⚠️ 历史数据不足，生成模拟数据');
      return this.generateMockHistoricalData(baseCurrency, targetCurrency, timeRange);
    }

    const sql = `
      SELECT
        DATE_FORMAT(created_at, '${groupByFormat}') as time_group,
        DATE_FORMAT(MIN(created_at), '${dateFormat}') as time_point,
        AVG(rate) as avg_rate,
        MIN(rate) as min_rate,
        MAX(rate) as max_rate,
        COUNT(*) as data_points
      FROM exchange_rates
      WHERE base_currency = ?
        AND target_currency = ?
        AND created_at >= DATE_SUB(NOW(), INTERVAL ${interval})
      GROUP BY DATE_FORMAT(created_at, '${groupByFormat}')
      ORDER BY time_group ASC
    `;

    try {
      const historicalData = await executeQuery(sql, [baseCurrency, targetCurrency]);

      return {
        success: true,
        data: {
          currencyPair: `${baseCurrency}/${targetCurrency}`,
          timeRange: timeRange,
          dataPoints: historicalData.map(point => ({
            time: point.time_point,
            rate: parseFloat(point.avg_rate),
            min: parseFloat(point.min_rate),
            max: parseFloat(point.max_rate),
            count: point.data_points
          }))
        }
      };
    } catch (error) {
      console.error('获取历史汇率失败:', error);
      throw new Error('获取历史汇率失败');
    }
  }

  // 生成模拟历史数据（当数据库中数据不足时使用）
  static async generateMockHistoricalData(baseCurrency, targetCurrency, timeRange) {
    console.log(`🎭 生成模拟历史数据: ${baseCurrency}/${targetCurrency}, 时间范围: ${timeRange}`);

    // 获取当前汇率作为基准
    const latestRates = await this.getLatestRates(baseCurrency);
    let baseRate = 7.162; // 默认CNY汇率

    if (latestRates.success && latestRates.data.rates[targetCurrency]) {
      baseRate = latestRates.data.rates[targetCurrency];
    }

    const dataPoints = [];
    const now = new Date();
    let pointCount, intervalMinutes, dateFormat;

    switch (timeRange) {
      case '1h':
        pointCount = 12; // 每5分钟一个点
        intervalMinutes = 5;
        dateFormat = (date) => date.toISOString().slice(0, 16) + ':00';
        break;
      case '1d':
        pointCount = 24; // 每小时一个点
        intervalMinutes = 60;
        dateFormat = (date) => date.toISOString().slice(0, 13) + ':00:00';
        break;
      case '1m':
        pointCount = 30; // 每天一个点
        intervalMinutes = 24 * 60;
        dateFormat = (date) => date.toISOString().slice(0, 10) + ' 00:00:00';
        break;
      case '3m':
        pointCount = 12; // 每周一个点
        intervalMinutes = 7 * 24 * 60;
        dateFormat = (date) => date.toISOString().slice(0, 10) + ' 00:00:00';
        break;
      default:
        pointCount = 24;
        intervalMinutes = 60;
        dateFormat = (date) => date.toISOString().slice(0, 13) + ':00:00';
    }

    for (let i = pointCount - 1; i >= 0; i--) {
      const time = new Date(now.getTime() - i * intervalMinutes * 60 * 1000);

      // 生成合理的汇率波动（±0.5%）
      const variation = (Math.random() - 0.5) * 0.01; // ±0.5%
      const trendFactor = (pointCount - i) / pointCount * 0.002; // 轻微趋势
      const rate = baseRate * (1 + variation + trendFactor);

      dataPoints.push({
        time: dateFormat(time),
        rate: parseFloat(rate.toFixed(6)),
        min: parseFloat((rate * 0.999).toFixed(6)),
        max: parseFloat((rate * 1.001).toFixed(6)),
        count: 1
      });
    }

    return {
      success: true,
      data: {
        currencyPair: `${baseCurrency}/${targetCurrency}`,
        timeRange: timeRange,
        dataPoints: dataPoints,
        isMockData: true
      }
    };
  }

  // 获取支持的货币列表
  static async getSupportedCurrencies(baseCurrency = 'USD') {
    const sql = `
      SELECT DISTINCT target_currency, COUNT(*) as data_count
      FROM exchange_rates 
      WHERE base_currency = ?
        AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
      GROUP BY target_currency
      ORDER BY target_currency
    `;

    try {
      const currencies = await executeQuery(sql, [baseCurrency]);
      
      return {
        success: true,
        data: currencies.map(curr => curr.target_currency)
      };
    } catch (error) {
      console.error('获取支持货币列表失败:', error);
      throw new Error('获取支持货币列表失败');
    }
  }

  // 清理过期数据（手动触发）
  static async cleanupOldData(retentionDays = 120) {
    const sql = `
      DELETE FROM exchange_rates 
      WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
    `;

    try {
      const result = await executeQuery(sql, [retentionDays]);
      
      return {
        success: true,
        deleted: result.affectedRows,
        message: `清理了 ${result.affectedRows} 条过期汇率数据`
      };
    } catch (error) {
      console.error('清理过期数据失败:', error);
      throw new Error('清理过期数据失败');
    }
  }

  // 获取汇率统计信息
  static async getStats(baseCurrency = 'USD') {
    const sql = `
      SELECT 
        COUNT(DISTINCT target_currency) as supported_currencies,
        COUNT(*) as total_records,
        MIN(created_at) as earliest_record,
        MAX(created_at) as latest_record,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 1 END) as daily_records,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as weekly_records
      FROM exchange_rates 
      WHERE base_currency = ?
    `;

    try {
      const stats = await executeQuery(sql, [baseCurrency]);
      
      return {
        success: true,
        data: stats[0]
      };
    } catch (error) {
      console.error('获取汇率统计失败:', error);
      throw new Error('获取汇率统计失败');
    }
  }

  // 检查数据新鲜度
  static async checkDataFreshness(baseCurrency = 'USD', maxAgeMinutes = 15) {
    const sql = `
      SELECT 
        MAX(created_at) as latest_update,
        TIMESTAMPDIFF(MINUTE, MAX(created_at), NOW()) as minutes_old
      FROM exchange_rates 
      WHERE base_currency = ?
    `;

    try {
      const result = await executeQuery(sql, [baseCurrency]);
      
      if (result.length === 0) {
        return {
          success: false,
          fresh: false,
          message: '没有汇率数据'
        };
      }

      const minutesOld = result[0].minutes_old || 0;
      const fresh = minutesOld <= maxAgeMinutes;

      return {
        success: true,
        fresh: fresh,
        lastUpdate: result[0].latest_update,
        minutesOld: minutesOld,
        message: fresh ? '数据新鲜' : `数据已过期 ${minutesOld} 分钟`
      };
    } catch (error) {
      console.error('检查数据新鲜度失败:', error);
      throw new Error('检查数据新鲜度失败');
    }
  }
}

module.exports = ExchangeRate;
