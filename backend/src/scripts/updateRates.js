// 汇率更新脚本 - 从FreeCurrency API获取真实汇率数据
const ExchangeRate = require('../models/ExchangeRate');
const https = require('https');
const { URL } = require('url');
// 在Docker容器中，环境变量已经通过docker-compose传递，无需加载.env文件
// require('dotenv').config({ path: '../../.env' });

// FreeCurrency API配置
const API_KEY = process.env.FREECURRENCY_API_KEY;
const API_BASE_URL = 'https://api.freecurrencyapi.com/v1';

// 支持的货币列表
const SUPPORTED_CURRENCIES = [
  'CNY', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF', 'HKD', 'SGD',
  'KRW', 'INR', 'THB', 'MYR', 'PHP', 'IDR', 'NZD', 'ZAR', 'BRL', 'MXN', 'RUB'
];

// 使用https模块进行API请求
const httpsRequest = (url) => {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);

    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname + urlObj.search,
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'SellerBox/1.0'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            ok: res.statusCode >= 200 && res.statusCode < 300,
            status: res.statusCode,
            statusText: res.statusMessage,
            headers: res.headers,
            data: jsonData
          });
        } catch (error) {
          reject(new Error(`JSON解析失败: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(new Error(`请求失败: ${error.message}`));
    });

    req.end();
  });
};

// 从FreeCurrency API获取真实汇率数据
const fetchRealRates = async () => {
  if (!API_KEY) {
    throw new Error('API密钥未配置，请检查.env文件中的FREECURRENCY_API_KEY');
  }

  const currencies = SUPPORTED_CURRENCIES.join(',');
  const url = `${API_BASE_URL}/latest?apikey=${API_KEY}&currencies=${currencies}`;

  console.log('📡 正在从FreeCurrency API获取数据...');
  console.log('🔗 API URL:', url.replace(API_KEY, 'API_KEY_HIDDEN'));

  const response = await httpsRequest(url);

  if (!response.ok) {
    throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
  }

  const data = response.data;

  if (!data.data) {
    throw new Error('API响应格式异常，缺少data字段');
  }

  // 显示API限制信息
  const headers = response.headers;
  console.log('📊 API限制信息:');
  console.log(`   月度限制: ${headers['x-ratelimit-limit-quota-month'] || 'N/A'}`);
  console.log(`   月度剩余: ${headers['x-ratelimit-remaining-quota-month'] || 'N/A'}`);
  console.log(`   分钟限制: ${headers['x-ratelimit-limit-quota-minute'] || 'N/A'}`);
  console.log(`   分钟剩余: ${headers['x-ratelimit-remaining-quota-minute'] || 'N/A'}`);

  return data.data;
};

// 模拟汇率数据（作为备用方案）
const generateMockRates = () => {
  const baseRates = {
    CNY: 7.24,
    EUR: 0.857,
    GBP: 0.784,
    JPY: 149.8,
    CAD: 1.35,
    AUD: 1.48,
    CHF: 0.92,
    HKD: 7.8,
    SGD: 1.35,
    KRW: 1300,
    INR: 83.2,
    THB: 36.5,
    MYR: 4.7,
    PHP: 56.8,
    IDR: 15200,
    NZD: 1.62,
    ZAR: 18.5,
    BRL: 5.1,
    MXN: 17.8,
    RUB: 88.5
  };

  // 添加小幅随机波动（±0.5%）
  const rates = {};
  for (const [currency, rate] of Object.entries(baseRates)) {
    const variation = (Math.random() - 0.5) * 0.01; // ±0.5%
    rates[currency] = rate * (1 + variation);
  }

  return rates;
};

// 更新汇率数据
async function updateExchangeRates(useRealAPI = true) {
  try {
    console.log('🔄 开始更新汇率数据...');

    let rates;
    let apiSource;

    if (useRealAPI) {
      try {
        rates = await fetchRealRates();
        apiSource = 'freecurrencyapi';
        console.log('✅ 成功从FreeCurrency API获取数据');
      } catch (apiError) {
        console.warn('⚠️ FreeCurrency API调用失败，使用模拟数据:', apiError.message);
        rates = generateMockRates();
        apiSource = 'mock_api';
      }
    } else {
      rates = generateMockRates();
      apiSource = 'mock_api';
      console.log('📊 使用模拟汇率数据');
    }

    const result = await ExchangeRate.saveRates(rates, 'USD', apiSource);

    if (result.success) {
      console.log(`✅ ${result.message}`);
      console.log('📊 更新的汇率:', Object.keys(rates).join(', '));
      console.log('🔗 数据源:', apiSource);
    } else {
      console.error('❌ 汇率更新失败:', result.message);
    }

  } catch (error) {
    console.error('❌ 汇率更新异常:', error.message);
  }
}

// 清理过期数据
async function cleanupOldData() {
  try {
    console.log('🧹 开始清理过期数据...');
    
    const result = await ExchangeRate.cleanupOldData(120);
    
    if (result.success) {
      console.log(`✅ ${result.message}`);
    } else {
      console.error('❌ 数据清理失败:', result.message);
    }
    
  } catch (error) {
    console.error('❌ 数据清理异常:', error.message);
  }
}

// 获取汇率统计
async function showStats() {
  try {
    const stats = await ExchangeRate.getStats();
    
    if (stats.success) {
      console.log('📈 汇率数据统计:');
      console.log(`   支持货币数: ${stats.data.supported_currencies}`);
      console.log(`   总记录数: ${stats.data.total_records}`);
      console.log(`   今日记录: ${stats.data.daily_records}`);
      console.log(`   本周记录: ${stats.data.weekly_records}`);
      console.log(`   最早记录: ${stats.data.earliest_record}`);
      console.log(`   最新记录: ${stats.data.latest_record}`);
    }
    
  } catch (error) {
    console.error('❌ 获取统计失败:', error.message);
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'update';
  
  console.log(`🚀 汇率管理脚本启动 - 命令: ${command}`);
  console.log(`⏰ 时间: ${new Date().toLocaleString('zh-CN')}`);
  console.log('─'.repeat(50));
  
  switch (command) {
    case 'update':
      await updateExchangeRates();
      break;
      
    case 'cleanup':
      await cleanupOldData();
      break;
      
    case 'stats':
      await showStats();
      break;
      
    case 'all':
      await updateExchangeRates();
      await showStats();
      break;
      
    case 'auto':
      console.log('🔄 启动自动更新模式（每10分钟更新一次）...');
      
      // 立即执行一次
      await updateExchangeRates();
      await showStats();
      
      // 设置定时器
      setInterval(async () => {
        console.log('\n' + '─'.repeat(50));
        console.log(`⏰ 定时更新 - ${new Date().toLocaleString('zh-CN')}`);
        await updateExchangeRates();
      }, 10 * 60 * 1000); // 10分钟
      
      // 每小时清理一次过期数据
      setInterval(async () => {
        console.log('\n' + '─'.repeat(50));
        console.log(`🧹 定时清理 - ${new Date().toLocaleString('zh-CN')}`);
        await cleanupOldData();
      }, 60 * 60 * 1000); // 1小时
      
      console.log('✅ 自动更新模式已启动，按 Ctrl+C 停止');
      break;
      
    default:
      console.log('❓ 未知命令，可用命令:');
      console.log('   update  - 更新汇率数据');
      console.log('   cleanup - 清理过期数据');
      console.log('   stats   - 显示统计信息');
      console.log('   all     - 更新数据并显示统计');
      console.log('   auto    - 启动自动更新模式');
      break;
  }
  
  if (command !== 'auto') {
    console.log('─'.repeat(50));
    console.log('✨ 脚本执行完成');
    process.exit(0);
  }
}

// 优雅退出处理
process.on('SIGINT', () => {
  console.log('\n🛑 收到退出信号，正在优雅关闭...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 收到终止信号，正在优雅关闭...');
  process.exit(0);
});

// 启动脚本
main().catch(error => {
  console.error('💥 脚本执行失败:', error);
  process.exit(1);
});
