// 生成历史汇率数据脚本 - 用于测试汇率趋势图
const { executeQuery } = require('../config/database');
require('dotenv').config({ path: '../../.env' });

// 基础汇率数据（相对于USD）
const BASE_RATES = {
  CNY: 7.24,
  EUR: 0.857,
  GBP: 0.784,
  JPY: 149.8,
  CAD: 1.35,
  AUD: 1.48,
  CHF: 0.92,
  HKD: 7.8,
  SGD: 1.35,
  KRW: 1300,
  INR: 83.2,
  THB: 36.5,
  MYR: 4.7,
  PHP: 56.8,
  IDR: 15200,
  NZD: 1.62,
  ZAR: 18.5,
  BRL: 5.1,
  MXN: 17.8,
  RUB: 88.5
};

// 生成带有趋势的汇率数据
function generateRateWithTrend(baseRate, timeIndex, totalPoints, trendFactor = 0.02) {
  // 添加长期趋势
  const trend = (timeIndex / totalPoints - 0.5) * trendFactor;
  
  // 添加随机波动（±1%）
  const randomVariation = (Math.random() - 0.5) * 0.02;
  
  // 添加周期性波动（模拟市场周期）
  const cyclicVariation = Math.sin(timeIndex * 0.1) * 0.005;
  
  return baseRate * (1 + trend + randomVariation + cyclicVariation);
}

// 生成历史数据点
async function generateHistoricalData() {
  try {
    console.log('🔄 开始生成历史汇率数据...');
    
    const currencies = Object.keys(BASE_RATES);
    const now = new Date();
    const totalInserted = [];
    
    // 生成过去7天的数据（每小时一个数据点）
    for (let day = 6; day >= 0; day--) {
      for (let hour = 0; hour < 24; hour++) {
        const timestamp = new Date(now);
        timestamp.setDate(timestamp.getDate() - day);
        timestamp.setHours(hour, Math.floor(Math.random() * 60), 0, 0);
        
        const values = [];
        const placeholders = [];
        
        currencies.forEach((currency, index) => {
          const timeIndex = (6 - day) * 24 + hour;
          const totalPoints = 7 * 24;
          const rate = generateRateWithTrend(BASE_RATES[currency], timeIndex, totalPoints);
          
          values.push('USD', currency, rate, 'historical_mock', timestamp);
          placeholders.push('(?, ?, ?, ?, ?)');
        });
        
        const sql = `
          INSERT INTO exchange_rates (base_currency, target_currency, rate, api_source, created_at) 
          VALUES ${placeholders.join(', ')}
        `;
        
        const result = await executeQuery(sql, values);
        totalInserted.push(result.affectedRows);
        
        if (day === 0 && hour % 6 === 0) {
          console.log(`📊 已生成 ${timestamp.toLocaleString('zh-CN')} 的数据`);
        }
      }
    }
    
    const total = totalInserted.reduce((sum, count) => sum + count, 0);
    console.log(`✅ 成功生成 ${total} 条历史汇率数据`);
    console.log(`📈 时间范围: ${new Date(now.getTime() - 6 * 24 * 60 * 60 * 1000).toLocaleDateString()} - ${now.toLocaleDateString()}`);
    console.log(`💱 货币数量: ${currencies.length}`);
    
  } catch (error) {
    console.error('❌ 生成历史数据失败:', error.message);
    throw error;
  }
}

// 清理现有历史数据
async function cleanExistingData() {
  try {
    console.log('🧹 清理现有历史数据...');
    
    const sql = `DELETE FROM exchange_rates WHERE api_source = 'historical_mock'`;
    const result = await executeQuery(sql);
    
    console.log(`✅ 清理了 ${result.affectedRows} 条历史数据`);
    
  } catch (error) {
    console.error('❌ 清理数据失败:', error.message);
    throw error;
  }
}

// 检查数据库表结构
async function checkTableStructure() {
  try {
    console.log('🔍 检查数据库表结构...');
    
    const sql = `DESCRIBE exchange_rates`;
    const result = await executeQuery(sql);
    
    console.log('📋 表结构:');
    result.forEach(column => {
      console.log(`   ${column.Field}: ${column.Type} ${column.Null === 'NO' ? 'NOT NULL' : ''} ${column.Key ? `(${column.Key})` : ''}`);
    });
    
  } catch (error) {
    console.error('❌ 检查表结构失败:', error.message);
    
    // 如果表不存在，创建表
    console.log('🔨 尝试创建exchange_rates表...');
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS exchange_rates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        base_currency VARCHAR(3) NOT NULL,
        target_currency VARCHAR(3) NOT NULL,
        rate DECIMAL(15,8) NOT NULL,
        api_source VARCHAR(50) DEFAULT 'freecurrencyapi',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_base_target (base_currency, target_currency),
        INDEX idx_created_at (created_at),
        INDEX idx_base_created (base_currency, created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;
    
    await executeQuery(createTableSQL);
    console.log('✅ 表创建成功');
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'generate';
  
  console.log(`🚀 历史数据生成脚本启动 - 命令: ${command}`);
  console.log(`⏰ 时间: ${new Date().toLocaleString('zh-CN')}`);
  console.log('─'.repeat(50));
  
  try {
    // 检查表结构
    await checkTableStructure();
    
    switch (command) {
      case 'generate':
        await cleanExistingData();
        await generateHistoricalData();
        break;
        
      case 'clean':
        await cleanExistingData();
        break;
        
      case 'check':
        // 只检查表结构，已经在上面执行了
        break;
        
      default:
        console.log('❓ 未知命令，可用命令:');
        console.log('   generate - 生成历史数据（默认）');
        console.log('   clean    - 清理历史数据');
        console.log('   check    - 检查表结构');
        break;
    }
    
  } catch (error) {
    console.error('💥 脚本执行失败:', error);
    process.exit(1);
  }
  
  console.log('─'.repeat(50));
  console.log('✨ 脚本执行完成');
  process.exit(0);
}

// 启动脚本
main().catch(error => {
  console.error('💥 脚本启动失败:', error);
  process.exit(1);
});
