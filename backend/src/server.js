const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');
require('dotenv').config({ path: process.env.NODE_ENV === 'production' ? '../.env.production' : '../.env' });

const { testConnection } = require('./config/database');
const feedbackRoutes = require('./routes/feedback');
const analyticsRoutes = require('./routes/analytics');
const exchangeRatesRoutes = require('./routes/exchangeRates');
const authRoutes = require('./routes/auth');
const announcementRoutes = require('./routes/announcements');
const {
  errorHandler,
  requestLogger,
  getClientIP
} = require('./middleware/validation');

const app = express();
const PORT = process.env.API_PORT || 3001;

// 安全中间件
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// CORS配置
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:3000',
    'https://sellerbox.asia'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// 请求限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每个IP最多100个请求
  message: {
    success: false,
    message: '请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

app.use(limiter);

// 反馈提交特殊限制
const feedbackSubmitLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5分钟
  max: 5, // 每个IP最多5个反馈
  message: {
    success: false,
    message: '反馈提交过于频繁，请5分钟后再试'
  }
});

// 统计数据查询限制（更宽松）
const statsLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 30, // 每个IP最多30个请求
  message: {
    success: false,
    message: '请求过于频繁，请稍后再试'
  }
});

// 汇率查询限制（较宽松，因为需要频繁查询）
const exchangeRatesLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 60, // 每个IP最多60个请求
  message: {
    success: false,
    message: '汇率查询过于频繁，请稍后再试'
  }
});

// 基础中间件
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(morgan('combined'));
app.use(requestLogger);
app.use(getClientIP);

// 健康检查端点
app.get('/health', async (req, res) => {
  try {
    const dbStatus = await testConnection();
    
    res.json({
      success: true,
      message: 'SellerBox API 服务正常运行',
      timestamp: new Date().toISOString(),
      database: dbStatus ? '连接正常' : '连接异常',
      version: '1.0.0'
    });
  } catch (error) {
    res.status(503).json({
      success: false,
      message: '服务异常',
      error: error.message
    });
  }
});

// 认证限流
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 20, // 每个IP最多20个认证请求
  message: {
    success: false,
    message: '认证请求过于频繁，请稍后再试'
  }
});

// API路由 - 为不同接口应用不同的限制
app.use('/api/auth', authLimiter, authRoutes);
app.use('/api/feedback/stats', statsLimiter);
app.use('/api/feedback', feedbackRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/exchange-rates', exchangeRatesLimiter, exchangeRatesRoutes);
app.use('/api/announcements', announcementRoutes);

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在',
    path: req.originalUrl
  });
});

// 错误处理中间件
app.use(errorHandler);

// 启动服务器
async function startServer() {
  try {
    // 测试数据库连接
    const dbConnected = await testConnection();
    
    if (!dbConnected) {
      console.error('❌ 数据库连接失败，服务器启动中止');
      process.exit(1);
    }

    app.listen(PORT, () => {
      console.log(`
🚀 SellerBox API 服务器启动成功！
📍 端口: ${PORT}
🌐 环境: ${process.env.NODE_ENV || 'development'}
📊 健康检查: http://localhost:${PORT}/health
📝 反馈API: http://localhost:${PORT}/api/feedback
⏰ 启动时间: ${new Date().toLocaleString('zh-CN')}
      `);
    });

  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在优雅关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在优雅关闭服务器...');
  process.exit(0);
});

// 启动服务器
startServer();
