const User = require('../models/User');

// JWT认证中间件
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '访问被拒绝，需要认证token'
      });
    }

    // 验证token
    const result = await User.verifyToken(token);
    
    if (!result.success) {
      return res.status(401).json({
        success: false,
        message: 'Token无效'
      });
    }

    // 将用户信息添加到请求对象
    req.user = result.user;
    req.session = result.session;
    
    next();
  } catch (error) {
    console.error('Token认证失败:', error);
    return res.status(401).json({
      success: false,
      message: 'Token无效或已过期'
    });
  }
};

// 角色权限检查中间件
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '未认证用户'
      });
    }

    // 如果roles是字符串，转换为数组
    const allowedRoles = Array.isArray(roles) ? roles : [roles];

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    next();
  };
};

// 超级管理员权限检查
const requireSuperAdmin = requireRole('super_admin');

// 管理员权限检查（包括super_admin和admin）
const requireAdmin = requireRole(['super_admin', 'admin']);

// 任何已认证用户（包括viewer）
const requireAuth = requireRole(['super_admin', 'admin', 'viewer']);

// 可选认证中间件（不强制要求认证，但如果有token会验证）
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      try {
        const result = await User.verifyToken(token);
        if (result.success) {
          req.user = result.user;
          req.session = result.session;
        }
      } catch (error) {
        // 忽略token验证错误，继续处理请求
        console.log('可选认证token验证失败:', error.message);
      }
    }

    next();
  } catch (error) {
    // 忽略所有错误，继续处理请求
    next();
  }
};

// 登录限流中间件
const loginRateLimit = (req, res, next) => {
  // 这里可以实现更复杂的登录限流逻辑
  // 目前使用express-rate-limit在路由层面处理
  next();
};

// 检查用户状态中间件
const checkUserStatus = async (req, res, next) => {
  try {
    if (!req.user) {
      return next();
    }

    // 重新查询用户状态（防止用户在session期间被禁用）
    const currentUser = await User.findById(req.user.id);
    
    if (!currentUser || currentUser.status !== 'active') {
      return res.status(401).json({
        success: false,
        message: '用户账户已被禁用'
      });
    }

    // 更新请求中的用户信息
    req.user = {
      id: currentUser.id,
      username: currentUser.username,
      email: currentUser.email,
      full_name: currentUser.full_name,
      role: currentUser.role
    };

    next();
  } catch (error) {
    console.error('检查用户状态失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 记录API访问日志中间件
const logApiAccess = async (req, res, next) => {
  try {
    if (req.user) {
      // 记录API访问日志
      await User.logAction(
        req.user.id,
        `api_${req.method.toLowerCase()}`,
        'api_endpoint',
        null,
        {
          path: req.originalUrl,
          method: req.method,
          query: req.query,
          body: req.method === 'POST' || req.method === 'PUT' ? req.body : undefined
        },
        req.clientIP,
        req.get('User-Agent')
      );
    }
    next();
  } catch (error) {
    // 不影响主要功能，只记录错误
    console.error('记录API访问日志失败:', error);
    next();
  }
};

// 错误处理中间件
const authErrorHandler = (err, req, res, next) => {
  if (err.name === 'JsonWebTokenError') {
    return res.status(401).json({
      success: false,
      message: 'Token格式错误'
    });
  }

  if (err.name === 'TokenExpiredError') {
    return res.status(401).json({
      success: false,
      message: 'Token已过期'
    });
  }

  if (err.name === 'NotBeforeError') {
    return res.status(401).json({
      success: false,
      message: 'Token尚未生效'
    });
  }

  // 传递给下一个错误处理中间件
  next(err);
};

module.exports = {
  authenticateToken,
  requireRole,
  requireSuperAdmin,
  requireAdmin,
  requireAuth,
  optionalAuth,
  loginRateLimit,
  checkUserStatus,
  logApiAccess,
  authErrorHandler
};
