const { body, validationResult } = require('express-validator');

// 反馈创建验证规则
const validateFeedback = [
  body('feedback_type')
    .isIn(['general', 'bug', 'feature', 'improvement'])
    .withMessage('反馈类型必须是: general, bug, feature, improvement 之一'),
  
  body('rating')
    .optional()
    .isInt({ min: 1, max: 5 })
    .withMessage('评分必须是1-5之间的整数'),
  
  body('message')
    .notEmpty()
    .withMessage('反馈内容不能为空')
    .isLength({ min: 5, max: 1000 })
    .withMessage('反馈内容长度必须在5-1000字符之间'),
  
  body('email')
    .optional()
    .isEmail()
    .withMessage('邮箱格式不正确')
    .normalizeEmail()
];

// 状态更新验证规则
const validateStatusUpdate = [
  body('status')
    .isIn(['new', 'in_progress', 'resolved', 'closed'])
    .withMessage('状态必须是: new, in_progress, resolved, closed 之一'),
  
  body('admin_notes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('管理员备注不能超过500字符')
];

// 分页参数验证
const validatePagination = [
  body('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是大于0的整数'),
  
  body('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须是1-100之间的整数')
];

// 处理验证错误
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: '输入数据验证失败',
      errors: errors.array().map(error => ({
        field: error.path,
        message: error.msg,
        value: error.value
      }))
    });
  }
  
  next();
};

// 错误处理中间件
const errorHandler = (err, req, res, next) => {
  console.error('API错误:', err);

  // 数据库错误
  if (err.code === 'ER_DUP_ENTRY') {
    return res.status(409).json({
      success: false,
      message: '数据重复',
      error: err.message
    });
  }

  // 数据库连接错误
  if (err.code === 'ECONNREFUSED') {
    return res.status(503).json({
      success: false,
      message: '数据库连接失败',
      error: '服务暂时不可用'
    });
  }

  // 自定义错误
  if (err.message) {
    return res.status(400).json({
      success: false,
      message: err.message
    });
  }

  // 默认错误
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? err.stack : '请稍后重试'
  });
};

// 请求日志中间件
const requestLogger = (req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log(`${req.method} ${req.originalUrl} - ${res.statusCode} - ${duration}ms`);
  });
  
  next();
};

// IP地址获取中间件
const getClientIP = (req, res, next) => {
  req.clientIP = req.headers['x-forwarded-for'] || 
                 req.headers['x-real-ip'] || 
                 req.connection.remoteAddress || 
                 req.socket.remoteAddress ||
                 (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
                 req.ip;
  
  next();
};

module.exports = {
  validateFeedback,
  validateStatusUpdate,
  validatePagination,
  handleValidationErrors,
  errorHandler,
  requestLogger,
  getClientIP
};
