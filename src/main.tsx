import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import App from './App.tsx';
import './index.css';

// 调试信息
console.log('🚀 main.tsx: 应用程序入口点启动');

// 在开发环境中导入测试
// if (import.meta.env.DEV) {
//   import('./test/currencyConverter.test.ts');
//   import('./test/keywordCombiner.test.ts');
// }

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <HelmetProvider>
      <BrowserRouter>
        <App />
      </BrowserRouter>
    </HelmetProvider>
  </StrictMode>
);
