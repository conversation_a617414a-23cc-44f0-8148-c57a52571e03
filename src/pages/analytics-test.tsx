import React, { useEffect } from 'react';
import Head from 'next/head';
import { SEO_CONFIG } from '../config/seo';

declare global {
  interface Window {
    gtag: (...args: any[]) => void;
  }
}

const AnalyticsTestPage: React.FC = () => {
  useEffect(() => {
    // 测试 GA4 事件
    const testGA4 = () => {
      if (typeof window !== 'undefined' && window.gtag) {
        console.log('发送测试事件到 GA4...');
        
        // 发送页面浏览事件
        window.gtag('event', 'page_view', {
          page_title: 'Analytics Test Page',
          page_location: window.location.href,
          custom_parameter_1: 'test_value'
        });

        // 发送自定义事件
        window.gtag('event', 'analytics_test', {
          event_category: 'test',
          event_label: 'manual_test',
          value: 1
        });

        console.log('GA4 测试事件已发送');
      } else {
        console.error('GA4 未加载或 gtag 函数不可用');
      }
    };

    // 延迟执行，确保 GA4 已加载
    const timer = setTimeout(testGA4, 2000);
    return () => clearTimeout(timer);
  }, []);

  const handleManualTest = () => {
    if (window.gtag) {
      window.gtag('event', 'button_click', {
        event_category: 'engagement',
        event_label: 'manual_test_button',
        value: 1
      });
      alert('测试事件已发送到 GA4！请检查控制台和 GA4 实时报告。');
    } else {
      alert('GA4 未加载！');
    }
  };

  return (
    <>
      <Head>
        <title>Analytics 测试页面 - {SEO_CONFIG.site.name}</title>
        <meta name="description" content="测试 Google Analytics 4 集成" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <div style={{ 
        maxWidth: '800px', 
        margin: '0 auto', 
        padding: '40px 20px',
        fontFamily: 'Arial, sans-serif'
      }}>
        <h1>🔍 Analytics 测试页面</h1>
        
        <div style={{ 
          background: '#f5f5f5', 
          padding: '20px', 
          borderRadius: '8px',
          marginBottom: '30px'
        }}>
          <h2>📊 当前配置</h2>
          <p><strong>GA4 ID:</strong> {SEO_CONFIG.analytics.googleAnalytics}</p>
          <p><strong>网站名称:</strong> {SEO_CONFIG.site.name}</p>
          <p><strong>当前 URL:</strong> {typeof window !== 'undefined' ? window.location.href : '加载中...'}</p>
        </div>

        <div style={{ marginBottom: '30px' }}>
          <h2>🧪 测试功能</h2>
          <button 
            onClick={handleManualTest}
            style={{
              background: '#0070f3',
              color: 'white',
              border: 'none',
              padding: '12px 24px',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '16px',
              marginRight: '10px'
            }}
          >
            发送测试事件
          </button>
          
          <button 
            onClick={() => window.location.reload()}
            style={{
              background: '#666',
              color: 'white',
              border: 'none',
              padding: '12px 24px',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '16px'
            }}
          >
            刷新页面
          </button>
        </div>

        <div style={{ 
          background: '#e8f4fd', 
          padding: '20px', 
          borderRadius: '8px',
          marginBottom: '30px'
        }}>
          <h2>📋 检查清单</h2>
          <ol>
            <li>打开浏览器开发者工具 (F12)</li>
            <li>查看 Console 标签页，确认 GA4 初始化消息</li>
            <li>查看 Network 标签页，确认对 googletagmanager.com 的请求</li>
            <li>点击"发送测试事件"按钮</li>
            <li>在 GA4 实时报告中查看数据（可能需要几分钟）</li>
          </ol>
        </div>

        <div style={{ 
          background: '#fff3cd', 
          padding: '20px', 
          borderRadius: '8px',
          border: '1px solid #ffeaa7'
        }}>
          <h2>⚠️ 注意事项</h2>
          <ul>
            <li>GA4 数据可能需要 24-48 小时才能在标准报告中显示</li>
            <li>实时报告通常在几分钟内显示数据</li>
            <li>确保没有广告拦截器阻止 GA4 脚本</li>
            <li>检查浏览器是否启用了"不跟踪"设置</li>
          </ul>
        </div>

        <div style={{ marginTop: '40px', textAlign: 'center' }}>
          <a 
            href="/"
            style={{
              color: '#0070f3',
              textDecoration: 'none',
              fontSize: '16px'
            }}
          >
            ← 返回首页
          </a>
        </div>
      </div>
    </>
  );
};

export default AnalyticsTestPage;
