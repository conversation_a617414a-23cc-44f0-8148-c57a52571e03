import React, { useState } from 'react';
import { X, Download, Smartphone } from 'lucide-react';
import { usePWA } from '../hooks/usePWA';
import { useLanguage } from '../hooks/useLanguage';
import { getTranslation } from '../locales';

export const PWAInstallPrompt: React.FC = () => {
  const { isInstallable, isInstalled, installPWA, getInstallInstructions } = usePWA();
  const { currentLanguage } = useLanguage();
  const [isDismissed, setIsDismissed] = useState(false);

  // 如果已安装、不可安装或已被用户关闭，则不显示
  if (isInstalled || !isInstallable || isDismissed) {
    return null;
  }

  const handleInstall = async () => {
    await installPWA();
    setIsDismissed(true);
  };

  const handleDismiss = () => {
    setIsDismissed(true);
  };

  const instructions = getInstallInstructions();

  return (
    <div className="fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:max-w-sm bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-50">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-2">
          <Smartphone className="w-5 h-5 text-orange-600" />
          <h3 className="font-semibold text-gray-900">
            {currentLanguage === 'zh' ? '安装应用' : 'Install App'}
          </h3>
        </div>
        <button
          onClick={handleDismiss}
          className="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
      
      <p className="text-sm text-gray-600 mb-4">
        {currentLanguage === 'zh'
          ? '将 AmzOva 爱麦蛙 安装到您的设备，享受更快的访问速度和离线使用功能。'
          : 'Install AmzOva on your device for faster access and offline functionality.'
        }
      </p>
      
      <div className="flex space-x-2">
        <button
          onClick={handleInstall}
          className="flex-1 bg-orange-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-orange-600 transition-colors flex items-center justify-center space-x-1"
        >
          <Download className="w-4 h-4" />
          <span>{currentLanguage === 'zh' ? '安装' : 'Install'}</span>
        </button>
        <button
          onClick={handleDismiss}
          className="px-4 py-2 text-gray-600 text-sm hover:text-gray-800 transition-colors"
        >
          {currentLanguage === 'zh' ? '稍后' : 'Later'}
        </button>
      </div>
      
      {/* 安装说明（可选显示） */}
      <details className="mt-3">
        <summary className="text-xs text-gray-500 cursor-pointer hover:text-gray-700">
          {currentLanguage === 'zh' ? '手动安装说明' : 'Manual installation guide'}
        </summary>
        <div className="mt-2 text-xs text-gray-600">
          <p className="font-medium mb-1">{instructions.browser}:</p>
          <ol className="list-decimal list-inside space-y-1">
            {instructions.steps.map((step, index) => (
              <li key={index}>{step}</li>
            ))}
          </ol>
        </div>
      </details>
    </div>
  );
};

// 离线状态提示组件
export const OfflineIndicator: React.FC = () => {
  const { isOffline } = usePWA();
  const { currentLanguage } = useLanguage();

  if (!isOffline) return null;

  return (
    <div className="fixed top-0 left-0 right-0 bg-yellow-500 text-white text-center py-2 text-sm z-50">
      {currentLanguage === 'zh' 
        ? '您当前处于离线状态，某些功能可能不可用'
        : 'You are currently offline. Some features may not be available.'
      }
    </div>
  );
};

// PWA 更新提示组件
export const PWAUpdatePrompt: React.FC = () => {
  const { currentLanguage } = useLanguage();
  const [showUpdate, setShowUpdate] = useState(false);
  const [isFirstLoad, setIsFirstLoad] = useState(true);

  // 监听 Service Worker 更新
  React.useEffect(() => {
    if ('serviceWorker' in navigator) {
      // 检查是否已经有控制器（表示不是首次加载）
      const hasController = !!navigator.serviceWorker.controller;
      setIsFirstLoad(!hasController);

      navigator.serviceWorker.addEventListener('controllerchange', () => {
        // 只有在非首次加载时才显示更新提示
        if (!isFirstLoad) {
          setShowUpdate(true);
        }
      });

      // 监听 Service Worker 注册更新
      navigator.serviceWorker.ready.then(registration => {
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;
          if (newWorker && hasController) {
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed') {
                setShowUpdate(true);
              }
            });
          }
        });
      });
    }
  }, [isFirstLoad]);

  const handleUpdate = () => {
    window.location.reload();
  };

  const handleDismiss = () => {
    setShowUpdate(false);
  };

  if (!showUpdate) return null;

  return (
    <div className="fixed top-4 left-4 right-4 md:left-auto md:right-4 md:max-w-sm bg-green-600 text-white rounded-lg shadow-lg p-4 z-50">
      <div className="flex items-start justify-between mb-2">
        <h3 className="font-semibold">
          {currentLanguage === 'zh' ? '应用更新' : 'App Update'}
        </h3>
        <button
          onClick={handleDismiss}
          className="text-green-200 hover:text-white transition-colors"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
      
      <p className="text-sm mb-4">
        {currentLanguage === 'zh' 
          ? '新版本已准备就绪，点击更新以获得最新功能。'
          : 'A new version is ready. Click update to get the latest features.'
        }
      </p>
      
      <div className="flex space-x-2">
        <button
          onClick={handleUpdate}
          className="flex-1 bg-white text-green-600 px-4 py-2 rounded text-sm font-medium hover:bg-green-50 transition-colors"
        >
          {currentLanguage === 'zh' ? '立即更新' : 'Update Now'}
        </button>
        <button
          onClick={handleDismiss}
          className="px-4 py-2 text-green-200 text-sm hover:text-white transition-colors"
        >
          {currentLanguage === 'zh' ? '稍后' : 'Later'}
        </button>
      </div>
    </div>
  );
};
