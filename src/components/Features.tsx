import React from 'react';
import { Search, Target, Package, DollarSign, TrendingUp, Users, BarChart3, Settings } from 'lucide-react';
import { useLanguage } from '../hooks/useLanguage';
import { getTranslation } from '../utils/translations';

const Features = () => {
  const { currentLanguage } = useLanguage();

  const features = [
    {
      icon: <Search className="h-8 w-8" />,
      title: getTranslation(currentLanguage, 'productResearch'),
      description: getTranslation(currentLanguage, 'productResearchDesc'),
      color: "bg-emerald-500"
    },
    {
      icon: <Target className="h-8 w-8" />,
      title: getTranslation(currentLanguage, 'keywordOptimization'),
      description: getTranslation(currentLanguage, 'keywordOptimizationDesc'),
      color: "bg-teal-500"
    },
    {
      icon: <Package className="h-8 w-8" />,
      title: getTranslation(currentLanguage, 'inventoryManagement'),
      description: getTranslation(currentLanguage, 'inventoryManagementDesc'),
      color: "bg-cyan-500"
    },
    {
      icon: <DollarSign className="h-8 w-8" />,
      title: getTranslation(currentLanguage, 'ppcCampaignManager'),
      description: getTranslation(currentLanguage, 'ppcCampaignManagerDesc'),
      color: "bg-amber-500"
    },
    {
      icon: <TrendingUp className="h-8 w-8" />,
      title: getTranslation(currentLanguage, 'profitAnalytics'),
      description: getTranslation(currentLanguage, 'profitAnalyticsDesc'),
      color: "bg-orange-500"
    },
    {
      icon: <Users className="h-8 w-8" />,
      title: getTranslation(currentLanguage, 'competitorAnalysis'),
      description: getTranslation(currentLanguage, 'competitorAnalysisDesc'),
      color: "bg-slate-500"
    },
    {
      icon: <BarChart3 className="h-8 w-8" />,
      title: getTranslation(currentLanguage, 'salesForecasting'),
      description: getTranslation(currentLanguage, 'salesForecastingDesc'),
      color: "bg-gray-500"
    },
    {
      icon: <Settings className="h-8 w-8" />,
      title: getTranslation(currentLanguage, 'listingOptimization'),
      description: getTranslation(currentLanguage, 'listingOptimizationDesc'),
      color: "bg-stone-500"
    }
  ];

  return (
    <section id="features" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            {getTranslation(currentLanguage, 'featuresTitle')}
            <span className="bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent"> {getTranslation(currentLanguage, 'featuresTitleHighlight')} </span>
            {getTranslation(currentLanguage, 'featuresTitleEnd')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {getTranslation(currentLanguage, 'featuresDescription')}
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div 
              key={index}
              className="group bg-white p-8 rounded-2xl border border-gray-100 hover:border-gray-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
            >
              <div className={`${feature.color} text-white p-3 rounded-xl w-fit mb-6 group-hover:scale-110 transition-transform duration-200`}>
                {feature.icon}
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">{feature.title}</h3>
              <p className="text-gray-600 leading-relaxed">{feature.description}</p>
            </div>
          ))}
        </div>

        <div className="mt-16 text-center">
          <a 
            href="#tools"
            className="bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl inline-flex items-center"
          >
            {getTranslation(currentLanguage, 'exploreAllFeatures')}
          </a>
        </div>
      </div>
    </section>
  );
};

export default Features;