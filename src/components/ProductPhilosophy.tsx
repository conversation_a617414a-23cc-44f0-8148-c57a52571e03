import React from 'react';
import { Heart, Shield, Zap, Globe, Users, TrendingUp, Award, CheckCircle } from 'lucide-react';
import { useLanguage } from '../hooks/useLanguage';
import { getTranslation } from '../utils/translations';

const ProductPhilosophy: React.FC = () => {
  const { currentLanguage } = useLanguage();

  const coreValues = [
    {
      icon: <Heart className="h-8 w-8" />,
      title: getTranslation(currentLanguage, 'freeForeverTitle'),
      description: getTranslation(currentLanguage, 'freeForeverDesc'),
      color: 'bg-emerald-500'
    },
    {
      icon: <Shield className="h-8 w-8" />,
      title: getTranslation(currentLanguage, 'noRegistrationTitle'),
      description: getTranslation(currentLanguage, 'noRegistrationDesc'),
      color: 'bg-blue-500'
    },
    {
      icon: <Zap className="h-8 w-8" />,
      title: getTranslation(currentLanguage, 'aiDrivenTitle'),
      description: getTranslation(currentLanguage, 'aiDrivenDesc'),
      color: 'bg-purple-500'
    },
    {
      icon: <Globe className="h-8 w-8" />,
      title: getTranslation(currentLanguage, 'globalSupportTitle'),
      description: getTranslation(currentLanguage, 'globalSupportDesc'),
      color: 'bg-orange-500'
    }
  ];

  const stats = [
    {
      icon: <Users className="h-6 w-6" />,
      label: getTranslation(currentLanguage, 'dailyActiveUsers'),
      value: getTranslation(currentLanguage, 'dailyActiveUsersCount'),
      color: 'text-emerald-600'
    },
    {
      icon: <TrendingUp className="h-6 w-6" />,
      label: getTranslation(currentLanguage, 'toolUsageCount'),
      value: getTranslation(currentLanguage, 'toolUsageCountNumber'),
      color: 'text-blue-600'
    },
    {
      icon: <Globe className="h-6 w-6" />,
      label: getTranslation(currentLanguage, 'countriesServed'),
      value: getTranslation(currentLanguage, 'countriesServedCount'),
      color: 'text-purple-600'
    },
    {
      icon: <Award className="h-6 w-6" />,
      label: getTranslation(currentLanguage, 'averageRating'),
      value: getTranslation(currentLanguage, 'averageRatingScore'),
      color: 'text-orange-600'
    }
  ];

  return (
    <section className="py-20" style={{ backgroundColor: '#F8F9FA' }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 使命部分 */}
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold mb-6" style={{ color: '#232F3E' }}>
            {getTranslation(currentLanguage, 'ourMissionTitle')}
          </h2>
          <p className="text-xl font-semibold mb-8" style={{ color: '#FF9900' }}>
            {getTranslation(currentLanguage, 'ourMissionSubtitle')}
          </p>
          <p className="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed">
            {getTranslation(currentLanguage, 'missionDescription')}
          </p>
        </div>

        {/* 核心价值观 */}
        <div className="mb-20">
          <h3 className="text-2xl lg:text-3xl font-bold text-center mb-12" style={{ color: '#232F3E' }}>
            {getTranslation(currentLanguage, 'coreValuesTitle')}
          </h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {coreValues.map((value, index) => (
              <div
                key={index}
                className="group bg-white p-6 rounded-xl border transition-all duration-300 hover:shadow-lg"
                style={{ borderColor: '#E5E5E5' }}
              >
                <div className={`${value.color} text-white p-3 rounded-xl w-fit mb-6 group-hover:scale-110 transition-transform duration-200`}>
                  {value.icon}
                </div>
                <h4 className="text-xl font-bold mb-4" style={{ color: '#232F3E' }}>{value.title}</h4>
                <p className="text-gray-600 leading-relaxed">{value.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* 使用统计 */}
        <div className="bg-white rounded-xl p-8 lg:p-12 shadow-sm border" style={{ borderColor: '#E5E5E5' }}>
          <h3 className="text-2xl lg:text-3xl font-bold text-center mb-12" style={{ color: '#232F3E' }}>
            {getTranslation(currentLanguage, 'usageStatsTitle')}
          </h3>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className={`${stat.color} mx-auto mb-4 p-3 rounded-full w-fit`} style={{ backgroundColor: '#FFF5E6' }}>
                  {stat.icon}
                </div>
                <div className="text-3xl lg:text-4xl font-bold mb-2" style={{ color: '#FF9900' }}>
                  {stat.value}
                </div>
                <div className="text-gray-600 font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProductPhilosophy;
