import React, { useState, useEffect } from 'react';
import { MessageSquare, Send, Star, ThumbsUp, ThumbsDown, Lightbulb, Bug, Heart, CheckCircle, AlertCircle } from 'lucide-react';
import { useLanguage } from '../hooks/useLanguage';
import { getTranslation } from '../utils/translations';
import { feedbackService, FeedbackData, handleApiError } from '../services/feedbackService';

const UserFeedback = () => {
  const { currentLanguage } = useLanguage();
  const [feedbackType, setFeedbackType] = useState<'general' | 'bug' | 'feature' | 'improvement'>('general');
  const [rating, setRating] = useState(0);
  const [message, setMessage] = useState('');
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<any>(null);

  const feedbackTypes = [
    {
      id: 'general' as const,
      icon: <MessageSquare className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'generalFeedback'),
      description: getTranslation(currentLanguage, 'generalFeedbackDesc'),
      color: 'bg-blue-500'
    },
    {
      id: 'bug' as const,
      icon: <Bug className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'bugReport'),
      description: getTranslation(currentLanguage, 'bugReportDesc'),
      color: 'bg-red-500'
    },
    {
      id: 'feature' as const,
      icon: <Lightbulb className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'featureRequest'),
      description: getTranslation(currentLanguage, 'featureRequestDesc'),
      color: 'bg-amber-500'
    },
    {
      id: 'improvement' as const,
      icon: <ThumbsUp className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'improvement'),
      description: getTranslation(currentLanguage, 'improvementDesc'),
      color: 'bg-emerald-500'
    }
  ];

  // 加载统计数据
  useEffect(() => {
    let isMounted = true; // 防止组件卸载后设置状态

    const loadStats = async () => {
      try {
        const statsData = await feedbackService.getStats();
        if (statsData.success && isMounted) {
          setStats(statsData.data);
        }
      } catch (error) {
        console.error('加载统计数据失败:', error);
        // 如果是频率限制错误，使用默认数据而不显示错误
        if (isMounted) {
          setStats({
            overall: {
              total_feedback: 0,
              overall_avg_rating: 0,
              weekly_count: 0,
              monthly_count: 0
            },
            byType: []
          });
        }
      }
    };

    // 添加延迟以避免开发模式下的重复请求
    const timer = setTimeout(loadStats, 100);

    return () => {
      isMounted = false;
      clearTimeout(timer);
    };
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      const feedbackData: FeedbackData = {
        feedback_type: feedbackType,
        rating: rating > 0 ? rating : undefined,
        message: message.trim(),
        email: email.trim() || undefined,
      };

      const result = await feedbackService.submitFeedback(feedbackData);

      if (result.success) {
        setIsSubmitted(true);
        // 重置表单
        setTimeout(() => {
          setIsSubmitted(false);
          setMessage('');
          setEmail('');
          setRating(0);
          setFeedbackType('general');
        }, 3000);
      } else {
        setError(result.message || '提交失败，请重试');
      }
    } catch (error) {
      setError(handleApiError(error));
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <section id="feedback" className="py-20 bg-gradient-to-br from-emerald-50 to-teal-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-2xl shadow-lg p-12 text-center">
            <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="h-8 w-8 text-emerald-600" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              {getTranslation(currentLanguage, 'feedbackSubmitted')}
            </h3>
            <p className="text-gray-600 text-lg">
              {getTranslation(currentLanguage, 'feedbackThankYou')}
            </p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="feedback" className="py-20 bg-gradient-to-br from-emerald-50 to-teal-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center bg-emerald-100 text-emerald-700 px-6 py-3 rounded-full text-sm font-medium mb-6 shadow-sm">
            <Heart className="h-4 w-4 mr-2" />
            {getTranslation(currentLanguage, 'weValueYourFeedback')}
          </div>
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 tracking-tight">
            📱 {getTranslation(currentLanguage, 'userFeedbackTitle')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {getTranslation(currentLanguage, 'userFeedbackDescription')}
          </p>
        </div>

        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
          <div className="grid lg:grid-cols-2">
            {/* Feedback Types */}
            <div className="p-8 bg-gray-50 border-r border-gray-200">
              <h3 className="text-xl font-bold text-gray-900 mb-6">
                {getTranslation(currentLanguage, 'selectFeedbackType')}
              </h3>
              <div className="space-y-4">
                {feedbackTypes.map((type) => (
                  <button
                    key={type.id}
                    onClick={() => setFeedbackType(type.id)}
                    className={`w-full p-4 rounded-xl border-2 transition-all duration-200 text-left ${
                      feedbackType === type.id
                        ? 'border-emerald-500 bg-emerald-50 shadow-md'
                        : 'border-gray-200 hover:border-gray-300 bg-white hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className={`${type.color} text-white p-2 rounded-lg flex-shrink-0`}>
                        {type.icon}
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-1">{type.title}</h4>
                        <p className="text-sm text-gray-600">{type.description}</p>
                      </div>
                    </div>
                  </button>
                ))}
              </div>

              {/* Quick Stats */}
              <div className="mt-8 p-6 bg-white rounded-xl border border-gray-200">
                <h4 className="font-semibold text-gray-900 mb-4">
                  {getTranslation(currentLanguage, 'feedbackStats')}
                </h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-emerald-600">
                      {stats?.overall?.total_feedback || 0}
                    </div>
                    <div className="text-sm text-gray-600">{getTranslation(currentLanguage, 'totalFeedback')}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {(() => {
                        const rating = stats?.overall?.overall_avg_rating;
                        if (!rating) return '0.0';
                        const numRating = typeof rating === 'string' ? parseFloat(rating) : rating;
                        return isNaN(numRating) ? '0.0' : numRating.toFixed(1);
                      })()}
                    </div>
                    <div className="text-sm text-gray-600">{getTranslation(currentLanguage, 'avgRating')}</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Feedback Form */}
            <div className="p-8">
              {/* 错误提示 */}
              {error && (
                <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center">
                  <AlertCircle className="h-5 w-5 text-red-500 mr-3 flex-shrink-0" />
                  <div className="text-red-700">
                    <p className="font-medium">提交失败</p>
                    <p className="text-sm">{error}</p>
                  </div>
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Rating */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    {getTranslation(currentLanguage, 'overallRating')}
                  </label>
                  <div className="flex items-center space-x-2">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <button
                        key={star}
                        type="button"
                        onClick={() => setRating(star)}
                        className={`p-1 transition-colors duration-200 ${
                          star <= rating ? 'text-amber-400' : 'text-gray-300 hover:text-amber-300'
                        }`}
                      >
                        <Star className="h-8 w-8 fill-current" />
                      </button>
                    ))}
                    {rating > 0 && (
                      <span className="ml-3 text-sm text-gray-600">
                        {rating === 5 && getTranslation(currentLanguage, 'excellent')}
                        {rating === 4 && getTranslation(currentLanguage, 'good')}
                        {rating === 3 && getTranslation(currentLanguage, 'average')}
                        {rating === 2 && getTranslation(currentLanguage, 'poor')}
                        {rating === 1 && getTranslation(currentLanguage, 'terrible')}
                      </span>
                    )}
                  </div>
                </div>

                {/* Message */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {getTranslation(currentLanguage, 'yourMessage')}
                  </label>
                  <textarea
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    required
                    rows={6}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200 resize-none"
                    placeholder={getTranslation(currentLanguage, 'messagePlaceholder')}
                  />
                  <div className="text-sm text-gray-500 mt-1">
                    {message.length}/500 {getTranslation(currentLanguage, 'characters')}
                  </div>
                </div>

                {/* Email */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {getTranslation(currentLanguage, 'emailOptional')}
                  </label>
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                    placeholder={getTranslation(currentLanguage, 'emailPlaceholder')}
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    {getTranslation(currentLanguage, 'emailNote')}
                  </p>
                </div>

                {/* Submit Button */}
                <button
                  type="submit"
                  disabled={!message.trim() || isSubmitting}
                  className="w-full bg-emerald-600 hover:bg-emerald-700 disabled:bg-emerald-400 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 transform hover:scale-105 disabled:transform-none flex items-center justify-center shadow-lg"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      {getTranslation(currentLanguage, 'submitting')}
                    </>
                  ) : (
                    <>
                      <Send className="h-5 w-5 mr-2" />
                      {getTranslation(currentLanguage, 'submitFeedback')}
                    </>
                  )}
                </button>
              </form>

              {/* Privacy Note */}
              <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                <p className="text-sm text-gray-600 leading-relaxed">
                  🔒 {getTranslation(currentLanguage, 'privacyNote')}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Feedback Highlights */}
        <div className="mt-16">
          <h3 className="text-2xl font-bold text-gray-900 text-center mb-8">
            {getTranslation(currentLanguage, 'recentFeedbackHighlights')}
          </h3>
          <div className="grid md:grid-cols-3 gap-6">
            {[
              {
                type: 'feature',
                message: getTranslation(currentLanguage, 'feedbackExample1'),
                status: getTranslation(currentLanguage, 'implemented'),
                color: 'bg-green-100 text-green-800'
              },
              {
                type: 'improvement',
                message: getTranslation(currentLanguage, 'feedbackExample2'),
                status: getTranslation(currentLanguage, 'inProgress'),
                color: 'bg-blue-100 text-blue-800'
              },
              {
                type: 'bug',
                message: getTranslation(currentLanguage, 'feedbackExample3'),
                status: getTranslation(currentLanguage, 'fixed'),
                color: 'bg-green-100 text-green-800'
              }
            ].map((item, index) => (
              <div key={index} className="bg-white p-6 rounded-xl border border-gray-200 shadow-sm">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-sm font-medium text-gray-500">
                    {feedbackTypes.find(t => t.id === item.type)?.title}
                  </span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${item.color}`}>
                    {item.status}
                  </span>
                </div>
                <p className="text-gray-700 text-sm leading-relaxed">"{item.message}"</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default UserFeedback;