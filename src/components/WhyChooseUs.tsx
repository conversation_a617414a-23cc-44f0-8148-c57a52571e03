import React from 'react';
import { Wrench, Database, MousePointer, RefreshCw, ArrowRight } from 'lucide-react';
import { useLanguage } from '../hooks/useLanguage';
import { getTranslation } from '../utils/translations';

const WhyChooseUs: React.FC = () => {
  const { currentLanguage } = useLanguage();

  const reasons = [
    {
      icon: <Wrench className="h-8 w-8" />,
      title: getTranslation(currentLanguage, 'professionalToolsTitle'),
      description: getTranslation(currentLanguage, 'professionalToolsDesc'),
      color: 'bg-emerald-500',
      features: ['12个专业工具', 'AI智能分析', '实时数据更新', '多语言支持']
    },
    {
      icon: <Database className="h-8 w-8" />,
      title: getTranslation(currentLanguage, 'realTimeDataTitle'),
      description: getTranslation(currentLanguage, 'realTimeDataDesc'),
      color: 'bg-blue-500',
      features: ['10分钟更新', 'Amazon政策同步', '准确计算', '可靠数据源']
    },
    {
      icon: <MousePointer className="h-8 w-8" />,
      title: getTranslation(currentLanguage, 'easyToUseTitle'),
      description: getTranslation(currentLanguage, 'easyToUseDesc'),
      color: 'bg-purple-500',
      features: ['直观界面', '无需学习', '移动友好', '即开即用']
    },
    {
      icon: <RefreshCw className="h-8 w-8" />,
      title: getTranslation(currentLanguage, 'continuousUpdateTitle'),
      description: getTranslation(currentLanguage, 'continuousUpdateDesc'),
      color: 'bg-orange-500',
      features: ['持续优化', '用户反馈', '免费更新', '最新版本']
    }
  ];

  const scrollToTools = () => {
    const toolsSection = document.getElementById('tools');
    if (toolsSection) {
      toolsSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="py-20" style={{ backgroundColor: '#F8F9FA' }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 标题 */}
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold mb-6" style={{ color: '#232F3E' }}>
            {getTranslation(currentLanguage, 'whyChooseUsTitle')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {getTranslation(currentLanguage, 'whyChooseUsSubtitle')}
          </p>
        </div>

        {/* 原因网格 */}
        <div className="grid md:grid-cols-2 gap-8 mb-16">
          {reasons.map((reason, index) => (
            <div 
              key={index}
              className="group bg-gradient-to-br from-gray-50 to-white p-8 rounded-2xl border border-gray-100 hover:border-gray-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
            >
              {/* 图标和标题 */}
              <div className="flex items-start mb-6">
                <div className={`${reason.color} text-white p-3 rounded-xl mr-4 group-hover:scale-110 transition-transform duration-200`}>
                  {reason.icon}
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-gray-900 mb-3">
                    {reason.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {reason.description}
                  </p>
                </div>
              </div>

              {/* 特性列表 */}
              <div className="grid grid-cols-2 gap-3">
                {reason.features.map((feature, featureIndex) => (
                  <div 
                    key={featureIndex}
                    className="flex items-center text-sm text-gray-700"
                  >
                    <div className={`w-2 h-2 rounded-full ${reason.color} mr-2`}></div>
                    {feature}
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* CTA部分 */}
        <div className="rounded-xl p-8 lg:p-12 text-center text-white" style={{ background: 'linear-gradient(135deg, #FF9900 0%, #E6890A 100%)' }}>
          <h3 className="text-2xl lg:text-3xl font-bold mb-6">
            {getTranslation(currentLanguage, 'getStartedTitle')}
          </h3>
          <p className="text-xl mb-4 opacity-90">
            {getTranslation(currentLanguage, 'getStartedSubtitle')}
          </p>
          <p className="text-lg mb-8 opacity-80 max-w-3xl mx-auto">
            {getTranslation(currentLanguage, 'getStartedDescription')}
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={scrollToTools}
              className="bg-white px-8 py-3 rounded-lg font-medium text-lg transition-all duration-200 hover:shadow-md inline-flex items-center justify-center"
              style={{ color: '#232F3E' }}
            >
              {getTranslation(currentLanguage, 'startUsingToolsNow')}
              <ArrowRight className="ml-2 h-5 w-5" />
            </button>
            <button
              onClick={scrollToTools}
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-medium text-lg transition-all duration-200 hover:bg-white inline-flex items-center justify-center"
              style={{
                borderColor: 'white',
                ':hover': { color: '#FF9900' }
              }}
            >
              {getTranslation(currentLanguage, 'viewAllTools')}
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUs;
