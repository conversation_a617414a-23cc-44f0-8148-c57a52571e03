import React from 'react';
import { Check, Star, Zap } from 'lucide-react';
import { useLanguage } from '../hooks/useLanguage';
import { getTranslation } from '../utils/translations';

const Pricing = () => {
  const { currentLanguage } = useLanguage();

  const plans = [
    {
      name: getTranslation(currentLanguage, 'starter'),
      price: 29,
      description: getTranslation(currentLanguage, 'starterDesc'),
      features: [
        getTranslation(currentLanguage, 'productResearchLimit'),
        getTranslation(currentLanguage, 'basicKeywordResearch'),
        getTranslation(currentLanguage, 'inventoryTracking50'),
        getTranslation(currentLanguage, 'emailSupport'),
        getTranslation(currentLanguage, 'basicProfitAnalytics')
      ],
      color: "border-gray-200",
      buttonColor: "bg-gray-600 hover:bg-gray-700"
    },
    {
      name: getTranslation(currentLanguage, 'professional'),
      price: 79,
      description: getTranslation(currentLanguage, 'professionalDesc'),
      features: [
        getTranslation(currentLanguage, 'productResearchLimit500'),
        getTranslation(currentLanguage, 'advancedKeywordOptimization'),
        getTranslation(currentLanguage, 'inventoryTracking250'),
        getTranslation(currentLanguage, 'ppcCampaignManagement'),
        getTranslation(currentLanguage, 'advancedProfitAnalytics'),
        getTranslation(currentLanguage, 'competitorMonitoring'),
        getTranslation(currentLanguage, 'prioritySupport')
      ],
      color: "border-blue-500 ring-2 ring-blue-500",
      buttonColor: "bg-blue-600 hover:bg-blue-700",
      popular: true
    },
    {
      name: getTranslation(currentLanguage, 'enterprise'),
      price: 199,
      description: getTranslation(currentLanguage, 'enterpriseDesc'),
      features: [
        getTranslation(currentLanguage, 'unlimitedProductResearch'),
        getTranslation(currentLanguage, 'fullKeywordSuite'),
        getTranslation(currentLanguage, 'unlimitedInventoryTracking'),
        getTranslation(currentLanguage, 'advancedPpcAutomation'),
        getTranslation(currentLanguage, 'customProfitReports'),
        getTranslation(currentLanguage, 'fullCompetitorIntelligence'),
        getTranslation(currentLanguage, 'dedicatedAccountManager'),
        getTranslation(currentLanguage, 'apiAccess'),
        getTranslation(currentLanguage, 'customIntegrations')
      ],
      color: "border-purple-500",
      buttonColor: "bg-purple-600 hover:bg-purple-700"
    }
  ];

  return (
    <section id="pricing" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            {getTranslation(currentLanguage, 'pricingTitle')}
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"> {getTranslation(currentLanguage, 'pricingTitleHighlight')} </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {getTranslation(currentLanguage, 'pricingDescription')}
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {plans.map((plan, index) => (
            <div 
              key={index}
              className={`relative bg-white rounded-2xl p-8 ${plan.color} hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-semibold flex items-center">
                    <Star className="h-4 w-4 mr-1" />
                    {getTranslation(currentLanguage, 'mostPopular')}
                  </div>
                </div>
              )}
              
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                <p className="text-gray-600 mb-6">{plan.description}</p>
                <div className="flex items-center justify-center mb-6">
                  <span className="text-5xl font-bold text-gray-900">${plan.price}</span>
                  <span className="text-gray-600 ml-2">{getTranslation(currentLanguage, 'month')}</span>
                </div>
              </div>

              <ul className="space-y-4 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-start">
                    <Check className="h-5 w-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>

              <button className={`w-full ${plan.buttonColor} text-white py-4 rounded-xl font-semibold text-lg transition-all duration-200 transform hover:scale-105 flex items-center justify-center`}>
                {plan.popular && <Zap className="h-5 w-5 mr-2" />}
                {getTranslation(currentLanguage, 'startFreeTrial')}
              </button>
            </div>
          ))}
        </div>

        <div className="mt-16 text-center">
          <p className="text-gray-600 mb-6">
            {getTranslation(currentLanguage, 'pricingFooterText')}
          </p>
          <div className="flex flex-wrap justify-center items-center gap-8 text-sm text-gray-500">
            <div className="flex items-center">
              <Check className="h-4 w-4 text-green-500 mr-2" />
              {getTranslation(currentLanguage, 'noSetupFees')}
            </div>
            <div className="flex items-center">
              <Check className="h-4 w-4 text-green-500 mr-2" />
              {getTranslation(currentLanguage, 'cancelAnytime')}
            </div>
            <div className="flex items-center">
              <Check className="h-4 w-4 text-green-500 mr-2" />
              {getTranslation(currentLanguage, 'support247')}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Pricing;