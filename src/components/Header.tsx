import React, { useState, useEffect } from 'react';
import { Menu, X } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../hooks/useLanguage';
import { getTranslation } from '../utils/translations';
import { getAssetUrl } from '../utils/assetLoader';
import LanguageSwitcher from './LanguageSwitcher';
import AnnouncementBanner from './AnnouncementBanner';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { currentLanguage } = useLanguage();
  const navigate = useNavigate();

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMenuOpen && !(event.target as Element).closest('.mobile-menu')) {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [isMenuOpen]);



  return (
    <>
      <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50 backdrop-blur-sm bg-white/95">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div
              className="flex items-center cursor-pointer hover:opacity-80 transition-opacity duration-200"
              onClick={() => navigate('/')}
            >
              <img
                src={getAssetUrl(currentLanguage === 'zh' || currentLanguage === 'zh-TW' ? "/amzova_cn.png" : "/amzova.png")}
                alt={currentLanguage === 'zh' || currentLanguage === 'zh-TW' ? "AmzOva 爱麦蛙 Logo" : "AmzOva Logo"}
                className="h-10 w-auto max-w-[200px] object-contain"
              />
              <div className="hidden sm:block ml-3 badge badge-primary">
                {getTranslation(currentLanguage, 'freeTools')}
              </div>
            </div>

            {/* 中间公告区域 */}
            <div className="flex-1 mx-8 hidden md:block">
              <AnnouncementBanner />
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-6">
              <LanguageSwitcher />

              <button
                onClick={() => navigate('/feedback')}
                className="btn-primary btn-sm"
              >
                {getTranslation(currentLanguage, 'feedbackButton')}
              </button>
            </nav>

          {/* Mobile Menu Button */}
          <div className="md:hidden flex items-center space-x-2">
            <LanguageSwitcher />
            <button
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-200 mobile-menu bg-white">
            <div className="flex flex-col space-y-3">
              <button
                onClick={() => {
                  setIsMenuOpen(false);
                  navigate('/feedback');
                }}
                className="btn-primary w-full text-center"
              >
                {getTranslation(currentLanguage, 'feedbackButton')}
              </button>
            </div>
          </div>
        )}
      </div>
    </header>
    </>
  );
};

export default Header;