import React from 'react';
import { Helmet } from 'react-helmet-async';
import { SEO_CONFIG } from '../config/seo';

export const AnalyticsScripts: React.FC = () => {
  return (
    <Helmet>
      {/* Google AdSense */}
      <script
        async
        src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-6352731815227894"
        crossOrigin="anonymous"
      />

      {/* Google Analytics 4 */}
      {SEO_CONFIG.analytics.googleAnalytics && (
        <>
          <script
            async
            src={`https://www.googletagmanager.com/gtag/js?id=${SEO_CONFIG.analytics.googleAnalytics}`}
          />
          <script>
            {`
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', '${SEO_CONFIG.analytics.googleAnalytics}', {
                page_title: document.title,
                page_location: window.location.href,
                send_page_view: true,
                anonymize_ip: true,
                allow_google_signals: false,
                allow_ad_personalization_signals: false
              });

              // 调试信息
              console.log('GA4 已初始化:', '${SEO_CONFIG.analytics.googleAnalytics}');
              console.log('当前页面:', window.location.href);

              // 自定义事件追踪
              window.gtag = gtag;
            `}
          </script>
        </>
      )}

      {/* 百度统计 */}
      {SEO_CONFIG.analytics.baiduAnalytics && (
        <script>
          {`
            var _hmt = _hmt || [];
            (function() {
              var hm = document.createElement("script");
              hm.src = "https://hm.baidu.com/hm.js?${SEO_CONFIG.analytics.baiduAnalytics}";
              var s = document.getElementsByTagName("script")[0]; 
              s.parentNode.insertBefore(hm, s);
            })();
          `}
        </script>
      )}

      {/* Microsoft Clarity - 暂时禁用，避免占位符 ID 错误 */}
      {SEO_CONFIG.analytics.microsoftClarity && SEO_CONFIG.analytics.microsoftClarity !== 'your-clarity-id' && (
        <script>
          {`
            (function(c,l,a,r,i,t,y){
                c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
            })(window, document, "clarity", "script", "${SEO_CONFIG.analytics.microsoftClarity}");
          `}
        </script>
      )}

      {/* 百度自动推送 */}
      <script>
        {`
          (function(){
              var bp = document.createElement('script');
              var curProtocol = window.location.protocol.split(':')[0];
              if (curProtocol === 'https') {
                  bp.src = 'https://zz.bdstatic.com/linksubmit/push.js';
              }
              else {
                  bp.src = 'http://push.zhanzhang.baidu.com/push.js';
              }
              var s = document.getElementsByTagName("script")[0];
              s.parentNode.insertBefore(bp, s);
          })();
        `}
      </script>

      {/* 性能监控 */}
      <script>
        {`
          // Core Web Vitals 监控
          function sendToAnalytics(metric) {
            if (window.gtag) {
              gtag('event', metric.name, {
                event_category: 'Web Vitals',
                value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
                event_label: metric.id,
                non_interaction: true,
              });
            }
          }

          // 动态导入 web-vitals
          import('https://unpkg.com/web-vitals@3/dist/web-vitals.js').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
            getCLS(sendToAnalytics);
            getFID(sendToAnalytics);
            getFCP(sendToAnalytics);
            getLCP(sendToAnalytics);
            getTTFB(sendToAnalytics);
          }).catch(err => {
            console.log('Web Vitals loading failed:', err);
          });
        `}
      </script>

      {/* 错误监控 */}
      <script>
        {`
          window.addEventListener('error', function(e) {
            if (window.gtag) {
              gtag('event', 'exception', {
                description: e.error ? e.error.toString() : e.message,
                fatal: false,
                event_category: 'JavaScript Error'
              });
            }
          });

          window.addEventListener('unhandledrejection', function(e) {
            if (window.gtag) {
              gtag('event', 'exception', {
                description: e.reason ? e.reason.toString() : 'Unhandled Promise Rejection',
                fatal: false,
                event_category: 'Promise Rejection'
              });
            }
          });
        `}
      </script>

      {/* 用户交互追踪 */}
      <script>
        {`
          // 工具使用追踪
          window.trackToolUsage = function(toolName, action = 'use') {
            if (window.gtag) {
              gtag('event', 'tool_usage', {
                event_category: 'Tools',
                event_label: toolName,
                value: 1,
                custom_parameter_1: action
              });
            }
            
            // 百度统计事件追踪
            if (window._hmt) {
              _hmt.push(['_trackEvent', 'Tools', action, toolName]);
            }
          };

          // 页面停留时间追踪
          let startTime = Date.now();
          window.addEventListener('beforeunload', function() {
            const timeSpent = Math.round((Date.now() - startTime) / 1000);
            if (window.gtag && timeSpent > 10) { // 只追踪停留超过10秒的
              gtag('event', 'page_view_duration', {
                event_category: 'Engagement',
                value: timeSpent,
                event_label: window.location.pathname
              });
            }
          });

          // 滚动深度追踪
          let maxScroll = 0;
          window.addEventListener('scroll', function() {
            const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
            if (scrollPercent > maxScroll && scrollPercent % 25 === 0) {
              maxScroll = scrollPercent;
              if (window.gtag) {
                gtag('event', 'scroll_depth', {
                  event_category: 'Engagement',
                  value: scrollPercent,
                  event_label: window.location.pathname
                });
              }
            }
          });
        `}
      </script>
    </Helmet>
  );
};

export default AnalyticsScripts;
