import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useLanguage } from '../hooks/useLanguage';
import { getTranslation } from '../locales';

// 中文搜索引擎优化组件
export const ChineseSearchEngineOptimization: React.FC = () => {
  const { currentLanguage } = useLanguage();
  
  // 只在中文环境下加载
  if (!['zh', 'zh-TW'].includes(currentLanguage)) {
    return null;
  }

  return (
    <Helmet>
      {/* 百度搜索引擎优化 */}
      <meta name="baidu-site-verification" content="codeva-G83azNsIXv" />
      <meta name="applicable-device" content="pc,mobile" />
      <meta name="MobileOptimized" content="width" />
      <meta name="HandheldFriendly" content="true" />
      
      {/* 360搜索优化 */}
      <meta name="360-site-verification" content="your-360-verification-code" />
      
      {/* 搜狗搜索优化 */}
      <meta name="sogou_site_verification" content="your-sogou-verification-code" />
      
      {/* 神马搜索优化 */}
      <meta name="shenma-site-verification" content="your-shenma-verification-code" />
      
      {/* 中文特定的Open Graph */}
      <meta property="og:locale" content={currentLanguage === 'zh' ? 'zh_CN' : 'zh_TW'} />
      
      {/* 百度特定的结构化数据 */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "WebApplication",
          "name": getTranslation(currentLanguage, 'siteTitle'),
          "description": getTranslation(currentLanguage, 'siteDescription'),
          "url": "https://amzova.com",
          "applicationCategory": "BusinessApplication",
          "operatingSystem": "Web Browser",
          "inLanguage": currentLanguage === 'zh' ? 'zh-CN' : 'zh-TW',
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "CNY"
          },
          "author": {
            "@type": "Organization",
            "name": "爱麦蛙 AmzOva",
            "url": "https://amzova.com"
          },
          "publisher": {
            "@type": "Organization",
            "name": "爱麦蛙 AmzOva",
            "url": "https://amzova.com"
          }
        })}
      </script>
      
      {/* 百度统计代码占位符 */}
      {process.env.NODE_ENV === 'production' && (
        <script>
          {`
            var _hmt = _hmt || [];
            (function() {
              var hm = document.createElement("script");
              hm.src = "https://hm.baidu.com/hm.js?your-baidu-analytics-id";
              var s = document.getElementsByTagName("script")[0]; 
              s.parentNode.insertBefore(hm, s);
            })();
          `}
        </script>
      )}
      
      {/* 360统计代码 - 已禁用 */}
      {/*
      {process.env.NODE_ENV === 'production' && process.env.VITE_360_ANALYTICS_ID && (
        <script>
          {`
            (function(i,s,o,g,r,a,m){i['_360_analytics_object']=r;i[r]=i[r]||function(){
            (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
            m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
            })(window,document,'script','//s.360.cn/so/analytics.js','_360_analytics');
            _360_analytics('create', '${process.env.VITE_360_ANALYTICS_ID}');
            _360_analytics('send', 'pageview');
          `}
        </script>
      )}
      */}
    </Helmet>
  );
};

// 国际搜索引擎优化组件
export const InternationalSearchEngineOptimization: React.FC = () => {
  const { currentLanguage } = useLanguage();
  
  return (
    <Helmet>
      {/* Google Search Console */}
      <meta name="google-site-verification" content="your-google-verification-code" />

      {/* Bing Webmaster Tools */}
      <meta name="msvalidate.01" content="your-bing-verification-code" />

      {/* Yandex Webmaster */}
      <meta name="yandex-verification" content="your-yandex-verification-code" />

      {/* 核心 Web Vitals 优化 */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="dns-prefetch" href="https://api.amzova.com" />

      {/* 结构化数据 - 网站 */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "WebSite",
          "name": "爱麦蛙 - 亚马逊卖家工具",
          "alternateName": "AmzOva - Amazon Seller Tools",
          "url": "https://amzova.com",
          "description": "专业的亚马逊卖家工具平台，提供标题优化、利润计算、汇率换算等免费工具",
          "inLanguage": ["zh-CN", "en-US", "ja-JP", "id-ID", "vi-VN", "zh-TW"],
          "potentialAction": {
            "@type": "SearchAction",
            "target": "https://amzova.com/tools?q={search_term_string}",
            "query-input": "required name=search_term_string"
          },
          "publisher": {
            "@type": "Organization",
            "name": "爱麦蛙 AmzOva",
            "url": "https://amzova.com",
            "logo": {
              "@type": "ImageObject",
              "url": "https://amzova.com/amzova.png",
              "width": 512,
              "height": 512
            }
          }
        })}
      </script>

      {/* 结构化数据 - 软件应用 */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "SoftwareApplication",
          "name": "爱麦蛙 - 亚马逊卖家工具",
          "description": "专业的亚马逊卖家工具平台，提供标题优化、利润计算、汇率换算等免费工具",
          "url": "https://amzova.com",
          "applicationCategory": "BusinessApplication",
          "operatingSystem": "Web Browser",
          "browserRequirements": "Requires JavaScript. Requires HTML5.",
          "softwareVersion": "2.0",
          "datePublished": "2025-01-01",
          "dateModified": "2025-07-21",
          "inLanguage": ["zh-CN", "en-US", "ja-JP", "id-ID", "vi-VN", "zh-TW"],
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "author": {
            "@type": "Organization",
            "name": "爱麦蛙 AmzOva",
            "url": "https://amzova.com"
          },
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.8",
            "ratingCount": "1250",
            "bestRating": "5",
            "worstRating": "1"
          },
          "featureList": [
            "标题分析器",
            "标题修复器",
            "利润计算器",
            "实时汇率换算",
            "装箱计算器",
            "单位转换器",
            "关键词密度检查",
            "世界时钟",
            "二维码生成器"
          ]
        })}
      </script>
      
      {/* 针对不同语言的特定优化 */}
      {currentLanguage === 'ja' && (
        <>
          <meta name="format-detection" content="telephone=no" />
          <meta name="format-detection" content="email=no" />
          <meta name="format-detection" content="address=no" />
        </>
      )}
      
      {currentLanguage === 'id' && (
        <>
          <meta name="geo.region" content="ID" />
          <meta name="geo.placename" content="Indonesia" />
        </>
      )}
      
      {currentLanguage === 'vi' && (
        <>
          <meta name="geo.region" content="VN" />
          <meta name="geo.placename" content="Vietnam" />
        </>
      )}
      
      {/* Google Analytics 4 */}
      {process.env.NODE_ENV === 'production' && (
        <>
          <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
          <script>
            {`
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'GA_MEASUREMENT_ID');
            `}
          </script>
        </>
      )}
      
      {/* Microsoft Clarity - 暂时禁用直到配置真实ID */}
      {false && process.env.NODE_ENV === 'production' && (
        <script>
          {`
            (function(c,l,a,r,i,t,y){
                c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
            })(window, document, "clarity", "script", "your-clarity-id");
          `}
        </script>
      )}
    </Helmet>
  );
};
