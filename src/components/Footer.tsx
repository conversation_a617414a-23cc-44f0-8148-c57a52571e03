import React from 'react';
import { Code, Heart, Users, Mail, MessageCircle, MessageSquare } from 'lucide-react';
import { useLanguage } from '../hooks/useLanguage';
import { getTranslation } from '../utils/translations';

const Footer = () => {
  const { currentLanguage } = useLanguage();

  return (
    <footer className="bg-white border-t border-gray-200">
      {/* 主要内容区域 */}
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center">
          {/* 个人介绍 */}
          <div className="mb-16">
            <div className="flex items-center justify-center mb-8">
              <div className="bg-gradient-to-r from-orange-500 to-orange-600 p-4 rounded-2xl shadow-lg">
                <Code className="h-10 w-10 text-white" />
              </div>
            </div>

            <h3 className="text-3xl font-bold text-gray-900 mb-8">
              {getTranslation(currentLanguage, 'aboutAuthor')}
            </h3>

            <div className="max-w-3xl mx-auto space-y-6 text-gray-700 text-lg leading-relaxed">
              <p dangerouslySetInnerHTML={{ __html: getTranslation(currentLanguage, 'authorIntro1') }} />
              <p dangerouslySetInnerHTML={{ __html: getTranslation(currentLanguage, 'authorIntro2') }} />
              <p dangerouslySetInnerHTML={{ __html: getTranslation(currentLanguage, 'authorIntro3') }} />
            </div>
          </div>

          {/* 核心价值观 */}
          <div className="grid md:grid-cols-3 gap-12 mb-16">
            <div className="text-center group">
              <div className="bg-blue-50 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-blue-100 transition-colors duration-300">
                <Heart className="h-10 w-10 text-blue-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-3">{getTranslation(currentLanguage, 'freeAndOpen')}</h4>
              <p className="text-gray-600" style={{ whiteSpace: 'pre-line' }}>
                {getTranslation(currentLanguage, 'freeAndOpenDesc')}
              </p>
            </div>

            <div className="text-center group">
              <div className="bg-green-50 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-green-100 transition-colors duration-300">
                <Users className="h-10 w-10 text-green-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-3">{getTranslation(currentLanguage, 'growTogether')}</h4>
              <p className="text-gray-600" style={{ whiteSpace: 'pre-line' }}>
                {getTranslation(currentLanguage, 'growTogetherDesc')}
              </p>
            </div>

            <div className="text-center group">
              <div className="bg-orange-50 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-orange-100 transition-colors duration-300">
                <Code className="h-10 w-10 text-orange-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-3">{getTranslation(currentLanguage, 'techDriven')}</h4>
              <p className="text-gray-600" style={{ whiteSpace: 'pre-line' }}>
                {getTranslation(currentLanguage, 'techDrivenDesc')}
              </p>
            </div>
          </div>

          {/* 联系方式 */}
          <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-3xl p-10">
            <h4 className="text-2xl font-semibold text-gray-900 mb-4">{getTranslation(currentLanguage, 'communicationAndFeedback')}</h4>
            <p className="text-gray-600 mb-8 text-lg">
              {getTranslation(currentLanguage, 'communicationDesc')}
            </p>

            <div className="flex flex-col sm:flex-row items-center justify-center gap-8">
              <a
                href="mailto:<EMAIL>"
                className="flex items-center space-x-3 bg-white px-6 py-4 rounded-xl shadow-sm hover:shadow-md text-gray-700 hover:text-orange-600 transition-all duration-300 group"
              >
                <div className="bg-orange-50 p-2 rounded-lg group-hover:bg-orange-100 transition-colors duration-300">
                  <Mail className="h-5 w-5 text-orange-600" />
                </div>
                <span className="font-medium">{getTranslation(currentLanguage, 'emailContact')}</span>
              </a>

              <a
                href="#"
                className="flex items-center space-x-3 bg-white px-6 py-4 rounded-xl shadow-sm hover:shadow-md text-gray-700 hover:text-blue-600 transition-all duration-300 group"
                onClick={(e) => {
                  e.preventDefault();
                  alert(getTranslation(currentLanguage, 'forumComingSoon'));
                }}
              >
                <div className="bg-blue-50 p-2 rounded-lg group-hover:bg-blue-100 transition-colors duration-300">
                  <MessageCircle className="h-5 w-5 text-blue-600" />
                </div>
                <span className="font-medium">{getTranslation(currentLanguage, 'forumDiscussion')}</span>
              </a>

              <a
                href="/feedback"
                className="flex items-center space-x-3 bg-white px-6 py-4 rounded-xl shadow-sm hover:shadow-md text-gray-700 hover:text-green-600 transition-all duration-300 group"
              >
                <div className="bg-green-50 p-2 rounded-lg group-hover:bg-green-100 transition-colors duration-300">
                  <MessageSquare className="h-5 w-5 text-green-600" />
                </div>
                <span className="font-medium">{getTranslation(currentLanguage, 'onlineFeedback')}</span>
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* 版权信息 */}
      <div className="border-t border-gray-100 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col items-center text-sm text-gray-500">
            {/* 版权信息 */}
            <div className="text-center">
              <p>{getTranslation(currentLanguage, 'copyright')}</p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;