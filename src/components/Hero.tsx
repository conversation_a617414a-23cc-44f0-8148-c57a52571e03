import React from 'react';
import { ArrowRight, Play, Star, CheckCircle, Zap, Users, FileText, Target, Calculator, DollarSign, ArrowRightLeft } from 'lucide-react';
import { useLanguage } from '../hooks/useLanguage';
import { getTranslation } from '../utils/translations';

interface HeroProps {
  onToolSelect?: (toolId: string) => void;
}

const Hero: React.FC<HeroProps> = ({ onToolSelect }) => {
  const { currentLanguage } = useLanguage();

  // 热门工具数据
  const hotTools = [
    {
      id: 'title-analyzer',
      icon: <FileText className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'titleAnalyzer'),
      description: getTranslation(currentLanguage, 'titleAnalyzerDesc'),
      color: 'bg-emerald-500',
      href: '#tools'
    },
    {
      id: 'unit-converter',
      icon: <ArrowRightLeft className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'unitConverter'),
      description: getTranslation(currentLanguage, 'unitConverterDesc'),
      color: 'bg-indigo-500',
      href: '#tools'
    },
    {
      id: 'keyword-density',
      icon: <Target className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'keywordDensityChecker'),
      description: getTranslation(currentLanguage, 'keywordDensityDesc'),
      color: 'bg-teal-500',
      href: '#tools'
    },
    {
      id: 'profit-calculator',
      icon: <Calculator className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'profitCalculator'),
      description: getTranslation(currentLanguage, 'profitCalculatorDesc'),
      color: 'bg-amber-500',
      href: '#tools'
    },
    {
      id: 'fba-calculator',
      icon: <DollarSign className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'fbaCalculator'),
      description: getTranslation(currentLanguage, 'fbaCalculatorDesc'),
      color: 'bg-orange-500',
      href: '#tools'
    }
  ];

  const handleToolClick = (toolId: string) => {
    if (onToolSelect) {
      onToolSelect(toolId);
    } else {
      // 如果没有传入回调，则滚动到工具区域
      const toolsSection = document.getElementById('tools');
      if (toolsSection) {
        toolsSection.scrollIntoView({ behavior: 'smooth' });
      }
    }
  };

  const scrollToTools = () => {
    const toolsSection = document.getElementById('tools');
    if (toolsSection) {
      toolsSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="py-12 lg:py-20" style={{
      background: 'linear-gradient(135deg, #FFFFFF 0%, #FFF5E6 50%, #FFEBCC 100%)'
    }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 主要内容区域 */}
        <div className="text-center space-y-6 mb-12">
          {/* Main Headline */}
          <h1 className="text-3xl lg:text-5xl font-bold leading-tight tracking-tight max-w-4xl mx-auto" style={{ color: '#232F3E' }}>
            <span style={{
              background: 'linear-gradient(135deg, #FF9900 0%, #E6890A 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text'
            }}>{getTranslation(currentLanguage, 'heroTitleHighlight')}</span>
            {getTranslation(currentLanguage, 'heroTitleEnd')}
          </h1>

          {/* Free Badge */}
          <div className="inline-flex items-center badge badge-primary px-6 py-3 text-base shadow-sm">
            <CheckCircle className="h-5 w-5 mr-2" />
            {getTranslation(currentLanguage, 'completelyFree')}
          </div>
        </div>

        {/* CTA按钮 */}
        <div className="text-center">
          <button
            onClick={() => scrollToTools()}
            className="btn-primary btn-lg transform hover:scale-105 shadow-lg hover:shadow-xl"
          >
            {getTranslation(currentLanguage, 'startFreeToolsCta')}
            <ArrowRight className="ml-2 h-5 w-5" />
          </button>
        </div>
      </div>
    </section>
  );
};

export default Hero;