import React, { useEffect, useRef, useState } from 'react';
import { Shield, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';

interface TurnstileVerificationProps {
  onVerify: (token: string) => void;
  onError?: (error: string) => void;
  onExpire?: () => void;
  theme?: 'light' | 'dark' | 'auto';
  size?: 'normal' | 'compact';
  className?: string;
  title?: string;
  description?: string;
}

declare global {
  interface Window {
    turnstile: {
      render: (element: string | HTMLElement, options: any) => string;
      reset: (widgetId?: string) => void;
      remove: (widgetId?: string) => void;
      getResponse: (widgetId?: string) => string;
    };
  }
}

const TurnstileVerification: React.FC<TurnstileVerificationProps> = ({
  onVerify,
  onError,
  onExpire,
  theme = 'auto',
  size = 'normal',
  className = '',
  title = '安全验证',
  description = '为了防止滥用，我们需要验证您是真实用户'
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const widgetIdRef = useRef<string | null>(null);
  const [status, setStatus] = useState<'loading' | 'ready' | 'verifying' | 'success' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = useState<string>('');

  const siteKey = import.meta.env.VITE_TURNSTILE_SITE_KEY;

  useEffect(() => {
    if (!siteKey) {
      setStatus('error');
      setErrorMessage('Turnstile配置错误');
      return;
    }

    // 加载Turnstile脚本
    const loadTurnstile = () => {
      if (window.turnstile) {
        console.log('Turnstile already loaded, initializing...');
        initializeTurnstile();
        return;
      }

      // 检查是否已经有脚本在加载中
      const existingScript = document.querySelector('script[src*="turnstile"]');
      if (existingScript) {
        console.log('Turnstile script already exists, waiting for load...');
        existingScript.addEventListener('load', initializeTurnstile);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js';
      script.async = true;
      script.defer = true;
      
      // 增加超时处理
      const timeout = setTimeout(() => {
        setStatus('error');
        setErrorMessage('验证服务加载超时，请刷新页面重试');
        console.error('Turnstile script loading timeout');
      }, 15000); // 15秒超时
      
      script.onload = () => {
        clearTimeout(timeout);
        console.log('Turnstile script loaded successfully');
        // 给Turnstile一些时间来完全初始化
        setTimeout(initializeTurnstile, 200);
      };
      
      script.onerror = () => {
        clearTimeout(timeout);
        setStatus('error');
        setErrorMessage('无法加载验证服务，请检查网络连接');
        console.error('Failed to load Turnstile script');
      };
      
      document.head.appendChild(script);
    };

    const initializeTurnstile = () => {
      if (!containerRef.current || !window.turnstile) {
        console.warn('Turnstile not ready, retrying...');
        setTimeout(initializeTurnstile, 500);
        return;
      }

      try {
        // 清除之前的widget如果存在
        if (widgetIdRef.current) {
          try {
            window.turnstile.remove(widgetIdRef.current);
          } catch (e) {
            console.warn('Failed to remove previous widget:', e);
          }
        }

        widgetIdRef.current = window.turnstile.render(containerRef.current, {
          sitekey: siteKey,
          theme,
          size,
          callback: (token: string) => {
            console.log('Turnstile verification successful');
            setStatus('success');
            onVerify(token);
          },
          'error-callback': (error: string) => {
            console.error('Turnstile verification failed:', error);
            setStatus('error');
            setErrorMessage('验证失败，请重试');
            onError?.(error);
          },
          'expired-callback': () => {
            console.log('Turnstile token expired');
            setStatus('ready');
            onExpire?.();
          },
          'before-interactive-callback': () => {
            setStatus('verifying');
          },
          'after-interactive-callback': () => {
            setStatus('ready');
          }
        });
        
        console.log('Turnstile widget initialized with ID:', widgetIdRef.current);
        setStatus('ready');
      } catch (error) {
        console.error('Turnstile initialization error:', error);
        setStatus('error');
        setErrorMessage('初始化验证失败');
      }
    };

    loadTurnstile();

    return () => {
      if (widgetIdRef.current && window.turnstile) {
        try {
          window.turnstile.remove(widgetIdRef.current);
        } catch (error) {
          console.error('Error removing Turnstile widget:', error);
        }
      }
    };
  }, [siteKey, theme, size, onVerify, onError, onExpire]);

  const resetVerification = () => {
    if (widgetIdRef.current && window.turnstile) {
      window.turnstile.reset(widgetIdRef.current);
      setStatus('ready');
      setErrorMessage('');
    }
  };

  const renderStatus = () => {
    switch (status) {
      case 'loading':
        return (
          <div className="flex items-center justify-center p-4 text-gray-600">
            <Loader2 className="h-5 w-5 animate-spin mr-2" />
            <span>正在加载验证...</span>
          </div>
        );
      case 'verifying':
        return (
          <div className="flex items-center justify-center p-4 text-blue-600">
            <Loader2 className="h-5 w-5 animate-spin mr-2" />
            <span>正在验证...</span>
          </div>
        );
      case 'success':
        return (
          <div className="flex items-center justify-center p-4 text-green-600">
            <CheckCircle className="h-5 w-5 mr-2" />
            <span>验证成功！</span>
          </div>
        );
      case 'error':
        return (
          <div className="text-center p-4">
            <div className="flex items-center justify-center text-red-600 mb-2">
              <AlertCircle className="h-5 w-5 mr-2" />
              <span>{errorMessage}</span>
            </div>
            <button
              onClick={resetVerification}
              className="text-sm text-blue-600 hover:text-blue-800 underline"
            >
              重新验证
            </button>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className={`turnstile-container ${className}`}>
      <div className="text-center mb-4">
        <div className="flex items-center justify-center mb-2">
          <Shield className="h-6 w-6 text-blue-600 mr-2" />
          <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        </div>
        <p className="text-sm text-gray-600">{description}</p>
      </div>
      
      <div className="flex justify-center">
        {status === 'ready' ? (
          <div ref={containerRef} />
        ) : (
          renderStatus()
        )}
      </div>
    </div>
  );
};

export default TurnstileVerification;
