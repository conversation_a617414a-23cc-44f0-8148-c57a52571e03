import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useLanguage } from '../hooks/useLanguage';
import { useSEOLanguage } from '../hooks/useSEOLanguage';
import { getTranslation, Language, languageConfig } from '../locales';
import {
  detectSearchEngine,
  optimizeTitleForSearchEngine,
  optimizeDescriptionForSearchEngine,
  generateSearchEngineSpecificMeta,
  generateSearchEngineStructuredData
} from '../utils/searchEngineOptimization';

interface SEOHeadProps {
  page?: 'home' | 'tools' | 'tool-specific';
  toolId?: string;
  customTitle?: string;
  customDescription?: string;
  customKeywords?: string;
}

export const SEOHead: React.FC<SEOHeadProps> = ({
  page = 'home',
  toolId,
  customTitle,
  customDescription,
  customKeywords
}) => {
  const { currentLanguage } = useLanguage();
  const { getLanguageUrls, getCanonicalUrl } = useSEOLanguage();
  
  // 获取基础SEO信息
  const getPageSEO = () => {
    if (customTitle && customDescription) {
      return {
        title: customTitle,
        description: customDescription,
        keywords: customKeywords || getTranslation(currentLanguage, 'siteKeywords')
      };
    }

    switch (page) {
      case 'tools':
        return {
          title: getTranslation(currentLanguage, 'toolsTitle'),
          description: getTranslation(currentLanguage, 'toolsDescription'),
          keywords: getTranslation(currentLanguage, 'siteKeywords')
        };
      
      case 'tool-specific':
        if (toolId) {
          // 将工具ID转换为驼峰命名
          const camelCaseToolId = toolId.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
          const titleKey = `${camelCaseToolId}SeoTitle`;
          const descKey = `${camelCaseToolId}SeoDescription`;
          const keywordsKey = `${camelCaseToolId}SeoKeywords`;

          const title = getTranslation(currentLanguage, titleKey);
          const description = getTranslation(currentLanguage, descKey);
          const keywords = getTranslation(currentLanguage, keywordsKey);

          // 如果找到了工具特定的SEO信息，使用它
          if (title !== titleKey) {
            return { title, description, keywords };
          }
        }
        return {
          title: getTranslation(currentLanguage, 'toolsTitle'),
          description: getTranslation(currentLanguage, 'toolsDescription'),
          keywords: getTranslation(currentLanguage, 'siteKeywords')
        };
      
      default: // home
        return {
          title: getTranslation(currentLanguage, 'homeTitle'),
          description: getTranslation(currentLanguage, 'homeDescription'),
          keywords: getTranslation(currentLanguage, 'siteKeywords')
        };
    }
  };

  const seo = getPageSEO();
  const currentLangConfig = languageConfig[currentLanguage];
  const canonicalUrl = getCanonicalUrl();
  const languageUrls = getLanguageUrls();

  // 检测搜索引擎并优化内容
  const searchEngine = detectSearchEngine(navigator.userAgent || '');
  const optimizedTitle = optimizeTitleForSearchEngine(seo.title, searchEngine);
  const optimizedDescription = optimizeDescriptionForSearchEngine(seo.description, searchEngine);
  const searchEngineMetaTags = generateSearchEngineSpecificMeta(searchEngine);

  // 生成hreflang标签
  const generateHrefLangs = () => {
    const hreflangs = [];
    Object.keys(languageConfig).forEach((lang) => {
      const langCode = getTranslation(lang as Language, 'languageCode');
      const url = languageUrls[lang as Language];
      hreflangs.push(
        <link key={lang} rel="alternate" hrefLang={langCode} href={url} />
      );
    });

    // 添加x-default
    hreflangs.push(
      <link key="x-default" rel="alternate" hrefLang="x-default" href={canonicalUrl} />
    );

    return hreflangs;
  };

  // 生成结构化数据
  const generateStructuredData = () => {
    const organizationData = {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": getTranslation(currentLanguage, 'organizationName'),
      "description": getTranslation(currentLanguage, 'organizationDescription'),
      "url": "https://amzova.com",
      "logo": "https://amzova.com/amzova.png",
      "sameAs": [
        "https://amzova.com"
      ]
    };

    const websiteData = {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": getTranslation(currentLanguage, 'siteTitle'),
      "description": getTranslation(currentLanguage, 'siteDescription'),
      "url": "https://amzova.com",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://amzova.com/tools?q={search_term_string}",
        "query-input": "required name=search_term_string"
      }
    };

    const webApplicationData = generateSearchEngineStructuredData(searchEngine, {
      "@context": "https://schema.org",
      "@type": getTranslation(currentLanguage, 'websiteType'),
      "name": getTranslation(currentLanguage, 'siteTitle'),
      "description": optimizedDescription,
      "url": canonicalUrl,
      "applicationCategory": getTranslation(currentLanguage, 'applicationCategory'),
      "operatingSystem": "Web Browser",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
      }
    }) || {
      "@context": "https://schema.org",
      "@type": getTranslation(currentLanguage, 'websiteType'),
      "name": getTranslation(currentLanguage, 'siteTitle'),
      "description": optimizedDescription,
      "url": canonicalUrl,
      "applicationCategory": getTranslation(currentLanguage, 'applicationCategory'),
      "operatingSystem": "Web Browser",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
      }
    };

    return [organizationData, websiteData, webApplicationData];
  };

  const structuredData = generateStructuredData();

  return (
    <Helmet>
      {/* 基础meta标签 */}
      <title>{optimizedTitle}</title>
      <meta name="description" content={optimizedDescription} />
      <meta name="keywords" content={seo.keywords} />

      {/* 搜索引擎特定meta标签 */}
      {searchEngineMetaTags.map((tag, index) => (
        <meta key={index} name={tag.name} content={tag.content} />
      ))}
      
      {/* 语言和地区 */}
      <html lang={getTranslation(currentLanguage, 'languageCode')} />
      <meta name="language" content={getTranslation(currentLanguage, 'languageCode')} />
      <meta name="geo.region" content={getTranslation(currentLanguage, 'countryCode')} />
      
      {/* 规范链接 */}
      <link rel="canonical" href={canonicalUrl} />
      
      {/* hreflang标签 */}
      {generateHrefLangs()}
      
      {/* Open Graph */}
      <meta property="og:type" content="website" />
      <meta property="og:title" content={optimizedTitle} />
      <meta property="og:description" content={optimizedDescription} />
      <meta property="og:url" content={canonicalUrl} />
      <meta property="og:image" content="https://amzova.com/amzova.png" />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:locale" content={getTranslation(currentLanguage, 'locale')} />
      <meta property="og:site_name" content="SellerBox" />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={optimizedTitle} />
      <meta name="twitter:description" content={optimizedDescription} />
      <meta name="twitter:image" content="https://amzova.com/amzova.png" />
      
      {/* 移动端优化 */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="SellerBox" />
      
      {/* 搜索引擎指令 */}
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
      <meta name="googlebot" content="index, follow" />
      <meta name="bingbot" content="index, follow" />
      
      {/* 结构化数据 */}
      {structuredData.map((data, index) => (
        <script key={index} type="application/ld+json">
          {JSON.stringify(data)}
        </script>
      ))}
    </Helmet>
  );
};
