import React, { useState, useEffect } from 'react';
import { BarChart3, TrendingUp, Users, Clock, RefreshCw, Download } from 'lucide-react';
import { getToolUsageReport, getHotToolsByWeeklyClicks, resetAllStats } from '../../utils/analytics';

interface ToolUsageData {
  totalClicks: number;
  topTools: Array<{ toolId: string; clicks: number; weeklyClicks: number }>;
  weeklyTrend: Array<{ week: string; totalClicks: number }>;
}

const Analytics = () => {
  const [usageData, setUsageData] = useState<ToolUsageData>({
    totalClicks: 0,
    topTools: [],
    weeklyTrend: []
  });
  const [hotToolIds, setHotToolIds] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // 工具名称映射
  const toolNames: { [key: string]: string } = {
    'title-fixer': '标题修复器',
    'unit-converter': '单位换算工具',
    'title-analyzer': '标题优化器',
    'fba-calculator': 'FBA计算器',
    'currency-converter': '实时汇率换算器',
    'keyword-density': '关键词密度检查器',
    'listing-optimizer': 'Listing优化器',
    'profit-calculator': '利润计算器',
    'packing-calculator': '装箱计算器',
    'keyword-researcher': '关键词研究器',
    'competitor-analyzer': '竞争对手分析器',
    'review-analyzer': '评论分析器'
  };

  // 加载数据
  const loadData = () => {
    setIsLoading(true);
    try {
      const report = getToolUsageReport();
      const hotTools = getHotToolsByWeeklyClicks(4);
      
      setUsageData(report);
      setHotToolIds(hotTools);
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 重置数据
  const handleResetStats = () => {
    if (window.confirm('确定要重置所有统计数据吗？此操作不可恢复。')) {
      resetAllStats();
      loadData();
    }
  };

  // 导出数据
  const handleExportData = () => {
    const dataToExport = {
      exportTime: new Date().toISOString(),
      usageData,
      hotToolIds
    };
    
    const blob = new Blob([JSON.stringify(dataToExport, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `amztools-analytics-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  useEffect(() => {
    loadData();
  }, []);

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* 页面标题 */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl">
              <BarChart3 className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">工具使用分析</h1>
              <p className="text-gray-600">查看工具点击量统计和热门工具排序</p>
            </div>
          </div>
          
          <div className="flex gap-3">
            <button
              onClick={loadData}
              disabled={isLoading}
              className="btn btn-secondary flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              刷新数据
            </button>
            <button
              onClick={handleExportData}
              className="btn btn-primary flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              导出数据
            </button>
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="card">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-blue-100 rounded-lg">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">总点击量</p>
              <p className="text-2xl font-bold text-gray-900">{usageData.totalClicks.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-green-100 rounded-lg">
              <TrendingUp className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">活跃工具数</p>
              <p className="text-2xl font-bold text-gray-900">{usageData.topTools.length}</p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-purple-100 rounded-lg">
              <Clock className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">统计周数</p>
              <p className="text-2xl font-bold text-gray-900">{usageData.weeklyTrend.length}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 热门工具排行 */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">热门工具排行</h3>
          <div className="space-y-3">
            {usageData.topTools.slice(0, 10).map((tool, index) => (
              <div key={tool.toolId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                    index < 3 ? 'bg-orange-500 text-white' : 'bg-gray-300 text-gray-700'
                  }`}>
                    {index + 1}
                  </div>
                  <span className="font-medium text-gray-900">
                    {toolNames[tool.toolId] || tool.toolId}
                  </span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-gray-900">{tool.clicks}</div>
                  <div className="text-xs text-gray-500">本周: {tool.weeklyClicks}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 当前热门工具（基于周点击量） */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">当前热门工具（首页显示）</h3>
          <div className="space-y-3">
            {hotToolIds.length > 0 ? (
              hotToolIds.map((toolId, index) => (
                <div key={toolId} className="flex items-center gap-3 p-3 bg-orange-50 rounded-lg border border-orange-200">
                  <div className="w-6 h-6 rounded-full bg-orange-500 text-white flex items-center justify-center text-xs font-bold">
                    {index + 1}
                  </div>
                  <span className="font-medium text-orange-900">
                    {toolNames[toolId] || toolId}
                  </span>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>暂无热门工具数据</p>
                <p className="text-sm">用户开始使用工具后会显示热门排行</p>
              </div>
            )}
          </div>
          
          <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
            <p className="text-sm text-blue-700">
              <strong>说明：</strong>热门工具基于最近一周的点击量排序，每周自动更新首页显示的4个热门工具。
            </p>
          </div>
        </div>
      </div>

      {/* 周趋势图 */}
      {usageData.weeklyTrend.length > 0 && (
        <div className="card mt-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">周点击量趋势</h3>
          <div className="space-y-2">
            {usageData.weeklyTrend.map((week) => (
              <div key={week.week} className="flex items-center gap-4">
                <div className="w-16 text-sm text-gray-600">{week.week}</div>
                <div className="flex-1 bg-gray-200 rounded-full h-4 relative">
                  <div 
                    className="bg-blue-500 h-4 rounded-full transition-all duration-500"
                    style={{ 
                      width: `${Math.max(5, (week.totalClicks / Math.max(...usageData.weeklyTrend.map(w => w.totalClicks))) * 100)}%` 
                    }}
                  ></div>
                </div>
                <div className="w-12 text-sm font-medium text-gray-900">{week.totalClicks}</div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 管理操作 */}
      <div className="card mt-6 border-red-200 bg-red-50">
        <h3 className="text-lg font-semibold text-red-900 mb-4">数据管理</h3>
        <div className="flex gap-3">
          <button
            onClick={handleResetStats}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            重置所有统计数据
          </button>
        </div>
        <p className="text-sm text-red-700 mt-2">
          注意：重置操作将清除所有点击量统计数据，此操作不可恢复。
        </p>
      </div>
    </div>
  );
};

export default Analytics;
