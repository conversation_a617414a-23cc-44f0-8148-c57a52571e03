import React, { useState, useEffect } from 'react';
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  AlertCircle,
  CheckCircle,
  Info,
  AlertTriangle,
  Settings,
  Calendar,
  Users,
  BarChart3,
  X
} from 'lucide-react';
import AnnouncementForm from './AnnouncementForm';
import { buildApiUrl, getAuthHeaders } from '../../utils/apiConfig';

interface Announcement {
  id: number;
  title: string;
  content: string;
  type: 'info' | 'warning' | 'success' | 'error' | 'maintenance';
  priority: number;
  status: 'draft' | 'active' | 'inactive' | 'expired';
  start_time: string | null;
  end_time: string | null;
  target_audience: 'all' | 'admin' | 'user';
  display_position: 'header' | 'banner' | 'popup';
  is_closable: boolean;
  click_action: string | null;
  view_count: number;
  created_at: string;
  updated_at: string;
  created_by_username: string;
  updated_by_username: string;
  translations?: AnnouncementTranslation[];
}

interface AnnouncementTranslation {
  id: number;
  language_code: string;
  title: string;
  content: string;
}

const AnnouncementAdmin: React.FC = () => {
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingAnnouncement, setEditingAnnouncement] = useState<Announcement | null>(null);
  const [selectedAnnouncements, setSelectedAnnouncements] = useState<number[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  const [previewAnnouncement, setPreviewAnnouncement] = useState<Announcement | null>(null);
  const [filters, setFilters] = useState({
    status: '',
    type: '',
    target_audience: ''
  });

  // 获取公告列表
  const fetchAnnouncements = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams();

      Object.entries(filters).forEach(([key, value]) => {
        if (value) queryParams.append(key, value);
      });

      const response = await fetch(buildApiUrl(`/api/v1/admin/announcements?${queryParams}`), {
        headers: getAuthHeaders()
      });

      if (response.ok) {
        const data = await response.json();
        setAnnouncements(data.data || []);
      } else {
        console.error('获取公告列表失败');
      }
    } catch (error) {
      console.error('获取公告列表错误:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnnouncements();
  }, [filters]);

  // 删除公告
  const handleDelete = async (id: number) => {
    const announcement = announcements.find(a => a.id === id);
    const confirmMessage = `确定要删除公告"${announcement?.title}"吗？\n\n此操作不可撤销。`;

    if (!confirm(confirmMessage)) return;

    try {
      const response = await fetch(buildApiUrl(`/api/v1/admin/announcements/${id}`), {
        method: 'DELETE',
        headers: getAuthHeaders()
      });

      if (response.ok) {
        setAnnouncements(announcements.filter(a => a.id !== id));
        // 显示成功提示
        const successDiv = document.createElement('div');
        successDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
        successDiv.textContent = '公告删除成功';
        document.body.appendChild(successDiv);
        setTimeout(() => document.body.removeChild(successDiv), 3000);
      } else {
        const errorData = await response.json();
        alert(`删除失败: ${errorData.message || '未知错误'}`);
      }
    } catch (error) {
      console.error('删除公告错误:', error);
      alert('删除失败: 网络错误');
    }
  };

  // 批量删除公告
  const handleBatchDelete = async () => {
    if (selectedAnnouncements.length === 0) {
      alert('请选择要删除的公告');
      return;
    }

    const selectedTitles = selectedAnnouncements
      .map(id => announcements.find(a => a.id === id)?.title)
      .filter(Boolean)
      .slice(0, 3)
      .join('、');

    const displayTitles = selectedAnnouncements.length > 3
      ? `${selectedTitles} 等${selectedAnnouncements.length}个公告`
      : selectedTitles;

    const confirmMessage = `确定要删除以下公告吗？\n\n${displayTitles}\n\n此操作不可撤销。`;

    if (!confirm(confirmMessage)) return;

    setLoading(true);
    try {
      const promises = selectedAnnouncements.map(id =>
        fetch(buildApiUrl(`/api/v1/admin/announcements/${id}`), {
          method: 'DELETE',
          headers: getAuthHeaders()
        })
      );

      const results = await Promise.all(promises);
      const failedCount = results.filter(r => !r.ok).length;

      setSelectedAnnouncements([]);
      fetchAnnouncements();

      // 显示结果提示
      const message = failedCount === 0
        ? `成功删除 ${selectedAnnouncements.length} 个公告`
        : `删除完成，${selectedAnnouncements.length - failedCount} 个成功，${failedCount} 个失败`;

      const alertClass = failedCount === 0 ? 'bg-green-500' : 'bg-yellow-500';
      const successDiv = document.createElement('div');
      successDiv.className = `fixed top-4 right-4 ${alertClass} text-white px-4 py-2 rounded-lg shadow-lg z-50`;
      successDiv.textContent = message;
      document.body.appendChild(successDiv);
      setTimeout(() => document.body.removeChild(successDiv), 4000);

    } catch (error) {
      console.error('批量删除失败:', error);
      alert('批量删除失败: 网络错误');
    } finally {
      setLoading(false);
    }
  };

  // 批量更新状态
  const handleBatchStatusUpdate = async (status: string) => {
    if (selectedAnnouncements.length === 0) {
      alert('请选择要更新的公告');
      return;
    }

    const statusText = {
      'active': '激活',
      'inactive': '停用',
      'draft': '设为草稿',
      'expired': '设为过期'
    }[status] || status;

    if (!confirm(`确定要将选中的 ${selectedAnnouncements.length} 个公告${statusText}吗？`)) return;

    setLoading(true);
    try {
      const promises = selectedAnnouncements.map(id => {
        const announcement = announcements.find(a => a.id === id);
        if (!announcement) return Promise.resolve({ ok: false });

        return fetch(buildApiUrl(`/api/v1/admin/announcements/${id}`), {
          method: 'PUT',
          headers: getAuthHeaders(),
          body: JSON.stringify({
            ...announcement,
            status
          })
        });
      });

      const results = await Promise.all(promises);
      const failedCount = results.filter(r => !r?.ok).length;

      setSelectedAnnouncements([]);
      fetchAnnouncements();

      // 显示结果提示
      const message = failedCount === 0
        ? `成功${statusText} ${selectedAnnouncements.length} 个公告`
        : `操作完成，${selectedAnnouncements.length - failedCount} 个成功，${failedCount} 个失败`;

      const alertClass = failedCount === 0 ? 'bg-green-500' : 'bg-yellow-500';
      const successDiv = document.createElement('div');
      successDiv.className = `fixed top-4 right-4 ${alertClass} text-white px-4 py-2 rounded-lg shadow-lg z-50`;
      successDiv.textContent = message;
      document.body.appendChild(successDiv);
      setTimeout(() => document.body.removeChild(successDiv), 4000);

    } catch (error) {
      console.error('批量更新失败:', error);
      alert(`批量${statusText}失败: 网络错误`);
    } finally {
      setLoading(false);
    }
  };

  // 预览公告
  const handlePreview = (announcement: Announcement) => {
    setPreviewAnnouncement(announcement);
    setShowPreview(true);
  };

  // 选择/取消选择公告
  const handleSelectAnnouncement = (id: number) => {
    setSelectedAnnouncements(prev =>
      prev.includes(id)
        ? prev.filter(announcementId => announcementId !== id)
        : [...prev, id]
    );
  };

  // 全选/取消全选
  const handleSelectAll = () => {
    if (selectedAnnouncements.length === announcements.length) {
      setSelectedAnnouncements([]);
    } else {
      setSelectedAnnouncements(announcements.map(a => a.id));
    }
  };

  // 获取类型图标
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'info': return <Info className="w-4 h-4 text-blue-500" />;
      case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'success': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error': return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'maintenance': return <Settings className="w-4 h-4 text-purple-500" />;
      default: return <Info className="w-4 h-4 text-gray-500" />;
    }
  };

  // 获取状态样式
  const getStatusStyle = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200';
      case 'draft': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'inactive': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'expired': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '活跃';
      case 'draft': return '草稿';
      case 'inactive': return '停用';
      case 'expired': return '过期';
      default: return status;
    }
  };

  // 获取类型文本
  const getTypeText = (type: string) => {
    switch (type) {
      case 'info': return '信息';
      case 'warning': return '警告';
      case 'success': return '成功';
      case 'error': return '错误';
      case 'maintenance': return '维护';
      default: return type;
    }
  };

  // 格式化日期
  const formatDate = (dateString: string | null) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleString('zh-CN');
  };

  if (loading && announcements.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <p className="text-gray-600">正在加载公告数据...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">公告管理</h1>
          <p className="text-gray-600 mt-1">管理系统公告，支持多语言和定时发布</p>
        </div>
        <button
          onClick={() => {
            setEditingAnnouncement(null);
            setShowForm(true);
          }}
          className="btn-primary flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>新建公告</span>
        </button>
      </div>

      {/* 筛选器和批量操作 */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        {/* 筛选器 */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">状态筛选</label>
            <select
              value={filters.status}
              onChange={(e) => setFilters({ ...filters, status: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
            >
              <option value="">全部状态</option>
              <option value="active">✅ 活跃</option>
              <option value="draft">📝 草稿</option>
              <option value="inactive">⏸️ 停用</option>
              <option value="expired">⏰ 过期</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">类型筛选</label>
            <select
              value={filters.type}
              onChange={(e) => setFilters({ ...filters, type: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
            >
              <option value="">全部类型</option>
              <option value="info">ℹ️ 信息</option>
              <option value="warning">⚠️ 警告</option>
              <option value="success">✅ 成功</option>
              <option value="error">❌ 错误</option>
              <option value="maintenance">🔧 维护</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">受众筛选</label>
            <select
              value={filters.target_audience}
              onChange={(e) => setFilters({ ...filters, target_audience: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
            >
              <option value="">全部受众</option>
              <option value="all">👥 所有用户</option>
              <option value="admin">👨‍💼 管理员</option>
              <option value="user">👤 普通用户</option>
            </select>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="flex flex-wrap items-center justify-between text-sm text-gray-600 mb-4 gap-2">
          <div className="flex items-center space-x-4">
            <span>共 {announcements.length} 个公告</span>
            {loading && (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span>更新中...</span>
              </div>
            )}
          </div>
          {selectedAnnouncements.length > 0 && (
            <span className="text-blue-600 font-medium">
              已选择 {selectedAnnouncements.length} 项
            </span>
          )}
        </div>

        {/* 批量操作 */}
        {selectedAnnouncements.length > 0 && (
          <div className="border-t pt-4">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-700">批量操作:</span>
                <span className="text-sm text-gray-600">
                  已选择 {selectedAnnouncements.length} 个公告
                </span>
              </div>
              <div className="flex flex-wrap items-center gap-2">
                <button
                  onClick={() => handleBatchStatusUpdate('active')}
                  disabled={loading}
                  className="px-3 py-1.5 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-1"
                >
                  <CheckCircle className="w-3 h-3" />
                  <span>批量激活</span>
                </button>
                <button
                  onClick={() => handleBatchStatusUpdate('inactive')}
                  disabled={loading}
                  className="px-3 py-1.5 text-sm bg-yellow-600 text-white rounded-md hover:bg-yellow-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-1"
                >
                  <AlertTriangle className="w-3 h-3" />
                  <span>批量停用</span>
                </button>
                <button
                  onClick={handleBatchDelete}
                  disabled={loading}
                  className="px-3 py-1.5 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-1"
                >
                  <Trash2 className="w-3 h-3" />
                  <span>批量删除</span>
                </button>
                <button
                  onClick={() => setSelectedAnnouncements([])}
                  className="px-3 py-1.5 text-sm bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
                >
                  取消选择
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 公告列表 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {announcements.length === 0 ? (
          <div className="text-center py-16">
            <AlertCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无公告数据</h3>
            <p className="text-gray-500 mb-6">还没有创建任何公告，点击上方按钮开始创建</p>
            <button
              onClick={() => {
                setEditingAnnouncement(null);
                setShowForm(true);
              }}
              className="btn-primary"
            >
              创建第一个公告
            </button>
          </div>
        ) : (
          <>
            {/* 桌面端表格视图 */}
            <div className="hidden lg:block overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <input
                        type="checkbox"
                        checked={selectedAnnouncements.length === announcements.length && announcements.length > 0}
                        onChange={handleSelectAll}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      公告信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      类型/状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      优先级
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      时间范围
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      统计
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {announcements.map((announcement) => (
                  <tr key={announcement.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <input
                        type="checkbox"
                        checked={selectedAnnouncements.includes(announcement.id)}
                        onChange={() => handleSelectAnnouncement(announcement.id)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </td>
                    <td className="px-6 py-4">
                      <div>
                        <div className="text-sm font-medium text-gray-900 mb-1">
                          {announcement.title}
                        </div>
                        <div className="text-sm text-gray-500 line-clamp-2">
                          {announcement.content}
                        </div>
                        <div className="text-xs text-gray-400 mt-1">
                          创建者: {announcement.created_by_username} | {formatDate(announcement.created_at)}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          {getTypeIcon(announcement.type)}
                          <span className="text-sm text-gray-600">
                            {getTypeText(announcement.type)}
                          </span>
                        </div>
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full border ${getStatusStyle(announcement.status)}`}>
                          {getStatusText(announcement.status)}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <span className="text-sm font-medium text-gray-900">
                        {announcement.priority}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-600">
                        <div className="flex items-center space-x-1 mb-1">
                          <Calendar className="w-3 h-3" />
                          <span>开始: {formatDate(announcement.start_time)}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-3 h-3" />
                          <span>结束: {formatDate(announcement.end_time)}</span>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-1 text-sm text-gray-600">
                        <BarChart3 className="w-3 h-3" />
                        <span>{announcement.view_count} 次查看</span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handlePreview(announcement)}
                          className="text-green-600 hover:text-green-800"
                          title="预览"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => {
                            setEditingAnnouncement(announcement);
                            setShowForm(true);
                          }}
                          className="text-blue-600 hover:text-blue-800"
                          title="编辑"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(announcement.id)}
                          className="text-red-600 hover:text-red-800"
                          title="删除"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            </div>

            {/* 移动端卡片视图 */}
            <div className="lg:hidden">
              {announcements.map((announcement) => (
                <div key={announcement.id} className="border-b border-gray-200 p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={selectedAnnouncements.includes(announcement.id)}
                        onChange={() => handleSelectAnnouncement(announcement.id)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <div className="flex items-center space-x-2">
                        {getTypeIcon(announcement.type)}
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full border ${getStatusStyle(announcement.status)}`}>
                          {getStatusText(announcement.status)}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handlePreview(announcement)}
                        className="text-green-600 hover:text-green-800 p-1"
                        title="预览"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => {
                          setEditingAnnouncement(announcement);
                          setShowForm(true);
                        }}
                        className="text-blue-600 hover:text-blue-800 p-1"
                        title="编辑"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(announcement.id)}
                        className="text-red-600 hover:text-red-800 p-1"
                        title="删除"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>

                  <h3 className="text-sm font-medium text-gray-900 mb-1">{announcement.title}</h3>
                  <p className="text-sm text-gray-500 line-clamp-2 mb-2">{announcement.content}</p>

                  <div className="grid grid-cols-2 gap-2 text-xs text-gray-600 mt-3">
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-3 h-3" />
                      <span>开始: {formatDate(announcement.start_time)}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-3 h-3" />
                      <span>结束: {formatDate(announcement.end_time)}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <BarChart3 className="w-3 h-3" />
                      <span>查看: {announcement.view_count}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="w-3 h-3" />
                      <span>优先级: {announcement.priority}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}
      </div>

      {/* 公告表单模态框 */}
      {showForm && (
        <AnnouncementForm
          announcement={editingAnnouncement}
          onClose={() => {
            setShowForm(false);
            setEditingAnnouncement(null);
          }}
          onSuccess={() => {
            setShowForm(false);
            setEditingAnnouncement(null);
            fetchAnnouncements();
          }}
        />
      )}

      {/* 公告预览模态框 */}
      {showPreview && previewAnnouncement && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b">
              <h3 className="text-lg font-semibold text-gray-900">公告预览</h3>
              <button
                onClick={() => setShowPreview(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="p-6">
              {/* 模拟公告显示效果 */}
              <div className={`border rounded-lg p-4 ${
                previewAnnouncement.type === 'info' ? 'bg-blue-50 border-blue-200' :
                previewAnnouncement.type === 'warning' ? 'bg-yellow-50 border-yellow-200' :
                previewAnnouncement.type === 'success' ? 'bg-green-50 border-green-200' :
                previewAnnouncement.type === 'error' ? 'bg-red-50 border-red-200' :
                previewAnnouncement.type === 'maintenance' ? 'bg-purple-50 border-purple-200' :
                'bg-gray-50 border-gray-200'
              }`}>
                <div className="flex items-center space-x-3">
                  <div className={`flex-shrink-0 ${
                    previewAnnouncement.type === 'info' ? 'text-blue-500' :
                    previewAnnouncement.type === 'warning' ? 'text-yellow-500' :
                    previewAnnouncement.type === 'success' ? 'text-green-500' :
                    previewAnnouncement.type === 'error' ? 'text-red-500' :
                    previewAnnouncement.type === 'maintenance' ? 'text-purple-500' :
                    'text-gray-500'
                  }`}>
                    {previewAnnouncement.type === 'info' && <Info className="w-5 h-5" />}
                    {previewAnnouncement.type === 'warning' && <AlertTriangle className="w-5 h-5" />}
                    {previewAnnouncement.type === 'success' && <CheckCircle className="w-5 h-5" />}
                    {previewAnnouncement.type === 'error' && <AlertCircle className="w-5 h-5" />}
                    {previewAnnouncement.type === 'maintenance' && <Settings className="w-5 h-5" />}
                  </div>

                  <div className="flex-1">
                    <div className={`text-sm font-medium ${
                      previewAnnouncement.type === 'info' ? 'text-blue-800' :
                      previewAnnouncement.type === 'warning' ? 'text-yellow-800' :
                      previewAnnouncement.type === 'success' ? 'text-green-800' :
                      previewAnnouncement.type === 'error' ? 'text-red-800' :
                      previewAnnouncement.type === 'maintenance' ? 'text-purple-800' :
                      'text-gray-800'
                    }`}>
                      {previewAnnouncement.title}
                    </div>
                    {previewAnnouncement.content && (
                      <div className={`text-sm mt-1 ${
                        previewAnnouncement.type === 'info' ? 'text-blue-700' :
                        previewAnnouncement.type === 'warning' ? 'text-yellow-700' :
                        previewAnnouncement.type === 'success' ? 'text-green-700' :
                        previewAnnouncement.type === 'error' ? 'text-red-700' :
                        previewAnnouncement.type === 'maintenance' ? 'text-purple-700' :
                        'text-gray-700'
                      }`}>
                        {previewAnnouncement.content}
                      </div>
                    )}
                  </div>

                  {previewAnnouncement.is_closable && (
                    <button className={`flex-shrink-0 ${
                      previewAnnouncement.type === 'info' ? 'text-blue-600' :
                      previewAnnouncement.type === 'warning' ? 'text-yellow-600' :
                      previewAnnouncement.type === 'success' ? 'text-green-600' :
                      previewAnnouncement.type === 'error' ? 'text-red-600' :
                      previewAnnouncement.type === 'maintenance' ? 'text-purple-600' :
                      'text-gray-600'
                    }`}>
                      <X className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>

              {/* 公告详细信息 */}
              <div className="mt-6 space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">类型：</span>
                    <span className="text-gray-600">{getTypeText(previewAnnouncement.type)}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">状态：</span>
                    <span className="text-gray-600">{getStatusText(previewAnnouncement.status)}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">优先级：</span>
                    <span className="text-gray-600">{previewAnnouncement.priority}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">目标受众：</span>
                    <span className="text-gray-600">
                      {previewAnnouncement.target_audience === 'all' ? '所有用户' :
                       previewAnnouncement.target_audience === 'admin' ? '管理员' : '普通用户'}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">开始时间：</span>
                    <span className="text-gray-600">{formatDate(previewAnnouncement.start_time)}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">结束时间：</span>
                    <span className="text-gray-600">{formatDate(previewAnnouncement.end_time)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};



export default AnnouncementAdmin;
