import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  BarChart3,
  MessageSquare,
  Users,
  Settings,
  TrendingUp,
  Database,
  Shield,
  Activity,
  LogOut,
  User,
  ChevronDown,
  Lock
} from 'lucide-react';
import AuthService from '../../services/authService';
import ChangePassword from './ChangePassword';
import { analyticsService } from '../../services/analyticsService';

const AdminDashboard = () => {
  const navigate = useNavigate();
  const authService = AuthService.getInstance();
  const [user, setUser] = useState(authService.getUser());
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showChangePassword, setShowChangePassword] = useState(false);
  const [statsData, setStatsData] = useState({
    daily_page_views: 0,
    daily_tool_uses: 0,
    total_feedback: 0,
    daily_active_sessions: 0
  });
  const [loading, setLoading] = useState(true);

  // 获取统计数据
  const loadStatsData = async () => {
    try {
      setLoading(true);
      const response = await analyticsService.getOverallAnalytics();
      if (response.success) {
        setStatsData(response.data);
      }
    } catch (error) {
      console.error('Failed to load stats data:', error);
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时加载数据
  useEffect(() => {
    loadStatsData();
  }, []);

  const adminModules = [
    {
      id: 'analytics',
      title: '数据分析',
      description: '查看网站运营数据、工具使用统计和用户行为分析',
      icon: BarChart3,
      color: 'bg-gradient-to-r from-blue-500 to-blue-600',
      path: '/admin/analytics',
      features: ['实时数据', '工具使用统计', '用户行为分析', '趋势图表']
    },
    {
      id: 'feedback',
      title: '反馈管理',
      description: '管理用户反馈、处理问题和跟踪解决状态',
      icon: MessageSquare,
      color: 'bg-gradient-to-r from-green-500 to-green-600',
      path: '/admin/feedback',
      features: ['反馈列表', '状态管理', '分类筛选', '统计报告']
    },
    {
      id: 'announcements',
      title: '公告管理',
      description: '发布和管理系统公告，支持多语言和定时发布',
      icon: Settings,
      color: 'bg-gradient-to-r from-orange-500 to-orange-600',
      path: '/admin/announcements',
      features: ['公告发布', '多语言支持', '定时发布', '查看统计']
    },
    {
      id: 'users',
      title: '用户管理',
      description: '用户会话管理、访问统计和用户画像分析',
      icon: Users,
      color: 'bg-purple-500',
      path: '/admin/users',
      features: ['会话管理', '访问统计', '地理分布', '用户画像'],
      disabled: true
    },
    {
      id: 'tools',
      title: '工具管理',
      description: '工具配置、性能监控和使用优化',
      icon: Settings,
      color: 'bg-orange-500',
      path: '/admin/tools',
      features: ['工具配置', '性能监控', '使用优化', '错误追踪'],
      disabled: true
    }
  ];

  const quickStats = [
    {
      label: '今日访问',
      value: loading ? '...' : statsData.daily_page_views?.toLocaleString() || '0',
      change: '',
      icon: Activity,
      color: 'text-blue-600'
    },
    {
      label: '工具使用',
      value: loading ? '...' : statsData.daily_tool_uses?.toLocaleString() || '0',
      change: '',
      icon: TrendingUp,
      color: 'text-green-600'
    },
    {
      label: '新反馈',
      value: loading ? '...' : statsData.total_feedback?.toLocaleString() || '0',
      change: '',
      icon: MessageSquare,
      color: 'text-purple-600'
    },
    {
      label: '活跃用户',
      value: loading ? '...' : statsData.daily_active_sessions?.toLocaleString() || '0',
      change: '',
      icon: Users,
      color: 'text-orange-600'
    }
  ];

  // 处理登出
  const handleLogout = async () => {
    try {
      await authService.logout();
      navigate('/admin/login');
    } catch (error) {
      console.error('登出失败:', error);
      // 即使登出失败也清除本地状态
      navigate('/admin/login');
    }
  };

  // 获取用户角色显示文本
  const getRoleText = (role: string) => {
    switch (role) {
      case 'super_admin':
        return '超级管理员';
      case 'admin':
        return '管理员';
      case 'viewer':
        return '查看者';
      default:
        return '未知角色';
    }
  };

  return (
    <div className="min-h-screen" style={{ backgroundColor: '#F8F9FA' }}>
      {/* 顶部导航 */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <div className="bg-gradient-to-r from-orange-500 to-orange-600 p-2 rounded-xl shadow-sm">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <div className="ml-3">
                <h1 className="text-xl font-bold" style={{ color: '#232F3E' }}>AmzOva 爱麦蛙 管理后台</h1>
                <p className="text-sm text-gray-500">系统管理与数据分析</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              {/* 用户信息 */}
              <div className="relative">
                <button
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                >
                  <User className="h-4 w-4" />
                  <span>{user?.full_name || user?.username}</span>
                  <ChevronDown className="h-4 w-4" />
                </button>

                {/* 用户菜单 */}
                {showUserMenu && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                    <div className="py-1">
                      <div className="px-4 py-2 text-sm text-gray-500 border-b">
                        <div className="font-medium">{user?.full_name || user?.username}</div>
                        <div className="text-xs">{getRoleText(user?.role || '')}</div>
                        <div className="text-xs">{user?.email}</div>
                      </div>
                      <button
                        onClick={() => {
                          setShowChangePassword(true);
                          setShowUserMenu(false);
                        }}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <Lock className="h-4 w-4 mr-2" />
                        修改密码
                      </button>
                      <button
                        onClick={handleLogout}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <LogOut className="h-4 w-4 mr-2" />
                        登出
                      </button>
                    </div>
                  </div>
                )}
              </div>

              <button
                onClick={() => navigate('/')}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"
              >
                返回首页
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 快速统计 */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">快速概览</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickStats.map((stat, index) => (
              <div key={index} className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200">
                <div className="flex items-center">
                  <div className="p-3 rounded-xl bg-gray-50">
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                    <div className="flex items-center">
                      <p className="text-2xl font-bold" style={{ color: '#232F3E' }}>{stat.value}</p>
                      {stat.change && (
                        <span className="ml-2 text-sm font-medium text-green-600">{stat.change}</span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 管理模块 */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">管理模块</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {adminModules.map((module) => (
              <div
                key={module.id}
                className={`bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-lg hover:border-orange-200 transition-all duration-300 ${
                  module.disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'
                }`}
                onClick={() => !module.disabled && navigate(module.path)}
              >
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <div className={`p-3 rounded-xl ${module.color} shadow-sm`}>
                      <module.icon className="h-6 w-6 text-white" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                        {module.title}
                        {module.disabled && (
                          <span className="ml-2 px-2 py-1 text-xs bg-gray-200 text-gray-600 rounded">
                            开发中
                          </span>
                        )}
                      </h3>
                      <p className="text-sm text-gray-600">{module.description}</p>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-700">主要功能：</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {module.features.map((feature, index) => (
                        <div key={index} className="flex items-center text-sm text-gray-600">
                          <div className="w-1.5 h-1.5 bg-gray-400 rounded-full mr-2"></div>
                          {feature}
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  {!module.disabled && (
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <button className="text-sm font-medium text-orange-600 hover:text-orange-700 transition-colors duration-200">
                        进入管理 →
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 系统信息 */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Database className="h-5 w-5 mr-2 text-gray-600" />
            系统信息
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">数据库状态</h3>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                <span className="text-sm text-gray-600">MySQL 连接正常</span>
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">API 服务</h3>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                <span className="text-sm text-gray-600">服务运行正常</span>
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">数据同步</h3>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                <span className="text-sm text-gray-600">实时同步中</span>
              </div>
            </div>
          </div>
        </div>

        {/* 快速操作 */}
        <div className="mt-8 bg-orange-50 rounded-xl border border-orange-100 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">快速操作</h2>
          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => navigate('/admin/analytics')}
              className="px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 text-sm font-medium transition-all duration-200 shadow-sm"
            >
              查看实时数据
            </button>
            <button
              onClick={() => navigate('/admin/feedback')}
              className="px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 text-sm font-medium transition-all duration-200 shadow-sm"
            >
              处理用户反馈
            </button>
            <button
              onClick={() => navigate('/admin/announcements')}
              className="px-6 py-3 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-lg hover:from-orange-600 hover:to-orange-700 text-sm font-medium transition-all duration-200 shadow-sm"
            >
              公告管理
            </button>
            <button
              onClick={() => navigate('/feedback')}
              className="px-6 py-3 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-lg hover:from-gray-600 hover:to-gray-700 text-sm font-medium transition-all duration-200 shadow-sm"
            >
              用户反馈页面
            </button>
          </div>
        </div>
      </div>

      {/* 修改密码弹窗 */}
      {showChangePassword && (
        <ChangePassword
          onClose={() => setShowChangePassword(false)}
          onSuccess={() => setShowChangePassword(false)}
        />
      )}
    </div>
  );
};

export default AdminDashboard;
