import React, { useState, useEffect } from 'react';
import { X, Plus, Trash2, Globe, AlertCircle } from 'lucide-react';
import { forceRefreshAnnouncements } from '../../utils/cacheUtils';
import { buildApiUrl, getAuthHeaders } from '../../utils/apiConfig';

interface Announcement {
  id: number;
  title: string;
  content: string;
  type: 'info' | 'warning' | 'success' | 'error' | 'maintenance';
  priority: number;
  status: 'draft' | 'active' | 'inactive' | 'expired';
  start_time: string | null;
  end_time: string | null;
  target_audience: 'all' | 'admin' | 'user';
  display_position: 'header' | 'banner' | 'popup';
  is_closable: boolean;
  click_action: string | null;
  translations?: AnnouncementTranslation[];
}

interface AnnouncementTranslation {
  language_code: string;
  title: string;
  content: string;
}

interface AnnouncementFormProps {
  announcement: Announcement | null;
  onClose: () => void;
  onSuccess: () => void;
}

const AnnouncementForm: React.FC<AnnouncementFormProps> = ({
  announcement,
  onClose,
  onSuccess
}) => {
  const [formData, setFormData] = useState<{
    title: string;
    content: string;
    type: 'info' | 'warning' | 'success' | 'error' | 'maintenance';
    priority: number;
    status: 'draft' | 'active' | 'inactive' | 'expired';
    start_time: string;
    end_time: string;
    target_audience: 'all' | 'admin' | 'user';
    display_position: 'header' | 'banner' | 'popup';
    is_closable: boolean;
    click_action: string;
  }>({
    title: '',
    content: '',
    type: 'info',
    priority: 0,
    status: 'draft',
    start_time: '',
    end_time: '',
    target_audience: 'all',
    display_position: 'header',
    is_closable: true,
    click_action: ''
  });

  const [translations, setTranslations] = useState<AnnouncementTranslation[]>([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 支持的语言列表
  const supportedLanguages = [
    { code: 'en', name: 'English' },
    { code: 'ja', name: '日本語' },
    { code: 'id', name: 'Bahasa Indonesia' },
    { code: 'vi', name: 'Tiếng Việt' },
    { code: 'zh-TW', name: '繁體中文' }
  ];

  useEffect(() => {
    if (announcement) {
      setFormData({
        title: announcement.title,
        content: announcement.content,
        type: announcement.type,
        priority: announcement.priority,
        status: announcement.status,
        start_time: announcement.start_time ? announcement.start_time.slice(0, 16) : '',
        end_time: announcement.end_time ? announcement.end_time.slice(0, 16) : '',
        target_audience: announcement.target_audience,
        display_position: announcement.display_position,
        is_closable: announcement.is_closable,
        click_action: announcement.click_action || ''
      });

      if (announcement.translations) {
        setTranslations(announcement.translations);
      }
    }
  }, [announcement]);

  // 验证表单
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = '公告标题不能为空';
    }

    if (!formData.content.trim()) {
      newErrors.content = '公告内容不能为空';
    }

    if (formData.priority < 0 || formData.priority > 100) {
      newErrors.priority = '优先级必须在0-100之间';
    }

    if (formData.start_time && formData.end_time) {
      if (new Date(formData.start_time) >= new Date(formData.end_time)) {
        newErrors.end_time = '结束时间必须晚于开始时间';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 显示成功提示
  const showSuccessMessage = (message: string) => {
    const successDiv = document.createElement('div');
    successDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
    successDiv.textContent = message;
    document.body.appendChild(successDiv);
    setTimeout(() => {
      if (document.body.contains(successDiv)) {
        document.body.removeChild(successDiv);
      }
    }, 3000);
  };

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      // 滚动到第一个错误字段
      const firstErrorField = document.querySelector('.border-red-500');
      if (firstErrorField) {
        firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      return;
    }

    setLoading(true);

    try {
      const submitData = {
        ...formData,
        start_time: formData.start_time || null,
        end_time: formData.end_time || null,
        click_action: formData.click_action || null,
        translations: translations.filter(t => t.title.trim() && t.content.trim())
      };

      const endpoint = announcement
        ? `/api/v1/admin/announcements/${announcement.id}`
        : '/api/v1/admin/announcements';

      const method = announcement ? 'PUT' : 'POST';

      const response = await fetch(buildApiUrl(endpoint), {
        method,
        headers: getAuthHeaders(),
        body: JSON.stringify(submitData)
      });

      if (response.ok) {
        // 清除公告缓存，确保新公告立即显示
        await forceRefreshAnnouncements();
        showSuccessMessage(announcement ? '公告更新成功' : '公告创建成功');
        onSuccess();
      } else {
        const errorData = await response.json();
        alert(errorData.message || '保存失败');
      }
    } catch (error) {
      console.error('保存公告错误:', error);
      alert('保存失败: 网络错误，请检查网络连接后重试');
    } finally {
      setLoading(false);
    }
  };

  // 添加翻译
  const addTranslation = () => {
    setTranslations([...translations, { language_code: 'en', title: '', content: '' }]);
  };

  // 删除翻译
  const removeTranslation = (index: number) => {
    setTranslations(translations.filter((_, i) => i !== index));
  };

  // 更新翻译
  const updateTranslation = (index: number, field: keyof AnnouncementTranslation, value: string) => {
    const newTranslations = [...translations];
    newTranslations[index] = { ...newTranslations[index], [field]: value };
    setTranslations(newTranslations);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-4xl max-h-[95vh] overflow-hidden shadow-2xl">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200 bg-gray-50">
          <div>
            <h2 className="text-xl font-bold text-gray-900">
              {announcement ? '编辑公告' : '新建公告'}
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              {announcement ? '修改现有公告的内容和设置' : '创建新的系统公告'}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-200 transition-colors"
            disabled={loading}
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* 表单内容 */}
        <div className="p-4 sm:p-6 overflow-y-auto max-h-[calc(95vh-180px)]">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* 基本信息 */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">基本信息</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    公告标题 *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                      errors.title ? 'border-red-500 bg-red-50' : 'border-gray-300'
                    }`}
                    placeholder="请输入公告标题"
                    disabled={loading}
                  />
                  {errors.title && <p className="text-red-500 text-sm mt-1 flex items-center space-x-1">
                    <AlertCircle className="w-4 h-4" />
                    <span>{errors.title}</span>
                  </p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    公告类型
                  </label>
                  <select
                    value={formData.type}
                    onChange={(e) => setFormData({ ...formData, type: e.target.value as any })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                    disabled={loading}
                  >
                    <option value="info">ℹ️ 信息</option>
                    <option value="warning">⚠️ 警告</option>
                    <option value="success">✅ 成功</option>
                    <option value="error">❌ 错误</option>
                    <option value="maintenance">🔧 维护</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    优先级 (0-100)
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="100"
                    value={formData.priority}
                    onChange={(e) => setFormData({ ...formData, priority: parseInt(e.target.value) || 0 })}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                      errors.priority ? 'border-red-500 bg-red-50' : 'border-gray-300'
                    }`}
                    disabled={loading}
                  />
                  {errors.priority && <p className="text-red-500 text-sm mt-1 flex items-center space-x-1">
                    <AlertCircle className="w-4 h-4" />
                    <span>{errors.priority}</span>
                  </p>}
                  <p className="text-xs text-gray-500 mt-1">数字越大优先级越高</p>
                </div>
              </div>
            </div>

            {/* 公告内容 */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">公告内容</h3>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  公告内容 *
                </label>
                <textarea
                  value={formData.content}
                  onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                  rows={4}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                    errors.content ? 'border-red-500 bg-red-50' : 'border-gray-300'
                  }`}
                  placeholder="请输入公告内容，支持简单的HTML标签"
                  disabled={loading}
                />
                {errors.content && <p className="text-red-500 text-sm mt-1 flex items-center space-x-1">
                  <AlertCircle className="w-4 h-4" />
                  <span>{errors.content}</span>
                </p>}
                <p className="text-xs text-gray-500 mt-1">
                  字符数: {formData.content.length} | 建议控制在200字以内以获得最佳显示效果
                </p>
              </div>
            </div>

            {/* 发布设置 */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">发布设置</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    状态
                  </label>
                  <select
                    value={formData.status}
                    onChange={(e) => setFormData({ ...formData, status: e.target.value as any })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                    disabled={loading}
                  >
                    <option value="draft">📝 草稿</option>
                    <option value="active">✅ 活跃</option>
                    <option value="inactive">⏸️ 停用</option>
                    <option value="expired">⏰ 过期</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    目标受众
                  </label>
                  <select
                    value={formData.target_audience}
                    onChange={(e) => setFormData({ ...formData, target_audience: e.target.value as any })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                    disabled={loading}
                  >
                    <option value="all">👥 所有用户</option>
                    <option value="admin">👨‍💼 管理员</option>
                    <option value="user">👤 普通用户</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    显示位置
                  </label>
                  <select
                    value={formData.display_position}
                    onChange={(e) => setFormData({ ...formData, display_position: e.target.value as any })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                    disabled={loading}
                  >
                    <option value="header">📍 顶部导航栏</option>
                    <option value="banner">🎯 横幅</option>
                    <option value="popup">💬 弹窗</option>
                  </select>
                </div>
              </div>
            </div>

            {/* 时间设置 */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">时间设置</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    开始时间
                  </label>
                  <input
                    type="datetime-local"
                    value={formData.start_time}
                    onChange={(e) => setFormData({ ...formData, start_time: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                    disabled={loading}
                  />
                  <p className="text-xs text-gray-500 mt-1">留空表示立即生效</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    结束时间
                  </label>
                  <input
                    type="datetime-local"
                    value={formData.end_time}
                    onChange={(e) => setFormData({ ...formData, end_time: e.target.value })}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                      errors.end_time ? 'border-red-500 bg-red-50' : 'border-gray-300'
                    }`}
                    disabled={loading}
                  />
                  {errors.end_time && <p className="text-red-500 text-sm mt-1 flex items-center space-x-1">
                    <AlertCircle className="w-4 h-4" />
                    <span>{errors.end_time}</span>
                  </p>}
                  <p className="text-xs text-gray-500 mt-1">留空表示永不过期</p>
                </div>
              </div>
            </div>

            {/* 高级设置 */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">高级设置</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    点击动作 (URL)
                  </label>
                  <input
                    type="url"
                    value={formData.click_action}
                    onChange={(e) => setFormData({ ...formData, click_action: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                    placeholder="https://example.com"
                    disabled={loading}
                  />
                  <p className="text-xs text-gray-500 mt-1">用户点击公告时跳转的链接，留空则无跳转</p>
                </div>

                <div className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    id="is_closable"
                    checked={formData.is_closable}
                    onChange={(e) => setFormData({ ...formData, is_closable: e.target.checked })}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mt-1"
                    disabled={loading}
                  />
                  <div>
                    <label htmlFor="is_closable" className="text-sm font-medium text-gray-700 cursor-pointer">
                      允许用户关闭此公告
                    </label>
                    <p className="text-xs text-gray-500 mt-1">
                      勾选后用户可以手动关闭公告，取消勾选则公告将持续显示直到过期
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* 多语言翻译 */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900 flex items-center space-x-2">
                  <Globe className="w-5 h-5" />
                  <span>多语言翻译</span>
                </h3>
                <button
                  type="button"
                  onClick={addTranslation}
                  className="btn-secondary btn-sm flex items-center space-x-1"
                >
                  <Plus className="w-4 h-4" />
                  <span>添加翻译</span>
                </button>
              </div>

              {translations.map((translation, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4 mb-4">
                  <div className="flex items-center justify-between mb-3">
                    <select
                      value={translation.language_code}
                      onChange={(e) => updateTranslation(index, 'language_code', e.target.value)}
                      className="px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      {supportedLanguages.map(lang => (
                        <option key={lang.code} value={lang.code}>
                          {lang.name}
                        </option>
                      ))}
                    </select>
                    <button
                      type="button"
                      onClick={() => removeTranslation(index)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                  <div className="space-y-3">
                    <input
                      type="text"
                      value={translation.title}
                      onChange={(e) => updateTranslation(index, 'title', e.target.value)}
                      placeholder="翻译标题"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <textarea
                      value={translation.content}
                      onChange={(e) => updateTranslation(index, 'content', e.target.value)}
                      placeholder="翻译内容"
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              ))}
            </div>
          </form>
        </div>

        {/* 底部按钮 */}
        <div className="flex flex-col sm:flex-row justify-between items-center gap-3 p-4 sm:p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600 order-2 sm:order-1">
            {loading && (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span>正在保存...</span>
              </div>
            )}
          </div>
          <div className="flex space-x-3 order-1 sm:order-2">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary min-w-[80px]"
              disabled={loading}
            >
              取消
            </button>
            <button
              onClick={handleSubmit}
              className="btn-primary min-w-[80px] flex items-center justify-center space-x-2"
              disabled={loading}
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>保存中</span>
                </>
              ) : (
                <span>{announcement ? '更新公告' : '创建公告'}</span>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnnouncementForm;
