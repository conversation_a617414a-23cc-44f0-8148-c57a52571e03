import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>3,
  TrendingU<PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Globe,
  Zap,
  RefreshCw,
  Calendar,
  Activity
} from 'lucide-react';
import { analyticsService } from '../../services/analyticsService';

interface OverallStats {
  total_sessions: number;
  unique_visitors: number;
  total_page_views: number;
  total_tool_uses: number;
  active_tools: number;
  total_feedback: number;
  daily_page_views: number;
  daily_tool_uses: number;
  daily_active_sessions: number;
  weekly_page_views: number;
  weekly_tool_uses: number;
  weekly_active_sessions: number;
}

interface ToolStats {
  tool_id: string;
  tool_name: string;
  total_uses: number;
  unique_users: number;
  daily_uses: number;
  weekly_uses: number;
}

interface UsageTrend {
  date: string;
  total_uses: number;
  unique_tools: number;
  unique_users: number;
}

interface RealTimeStats {
  hourly_tool_uses: number;
  hourly_page_views: number;
  hourly_active_sessions: number;
  daily_tool_uses: number;
  daily_page_views: number;
  daily_active_sessions: number;
}

const AnalyticsDashboard = () => {
  const [overallStats, setOverallStats] = useState<OverallStats | null>(null);
  const [toolStats, setToolStats] = useState<ToolStats[]>([]);
  const [usageTrend, setUsageTrend] = useState<UsageTrend[]>([]);
  const [realTimeStats, setRealTimeStats] = useState<RealTimeStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [overallRes, toolRes, trendRes, realtimeRes] = await Promise.all([
        analyticsService.getOverallAnalytics(),
        analyticsService.getTopTools(10),
        analyticsService.getUsageTrend(7),
        analyticsService.getRealTimeStats()
      ]);

      if (overallRes.success) setOverallStats(overallRes.data);
      if (toolRes.success) setToolStats(toolRes.data);
      if (trendRes.success) setUsageTrend(trendRes.data);
      if (realtimeRes.success) setRealTimeStats(realtimeRes.data);

    } catch (error) {
      setError('加载数据失败，请稍后重试');
      console.error('Analytics data loading failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadAnalyticsData();
    setRefreshing(false);
  };

  useEffect(() => {
    loadAnalyticsData();
    
    // 每5分钟自动刷新实时数据
    const interval = setInterval(() => {
      analyticsService.getRealTimeStats().then(res => {
        if (res.success) setRealTimeStats(res.data);
      });
    }, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">加载数据中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 页面标题 */}
        <div className="mb-8 flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">数据分析</h1>
            <p className="mt-2 text-gray-600">网站运营数据和用户行为分析</p>
          </div>
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            刷新数据
          </button>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
            {error}
          </div>
        )}

        {/* 实时统计卡片 */}
        {realTimeStats && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
              <Activity className="h-5 w-5 mr-2 text-green-500" />
              实时数据
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
              <div className="bg-white p-4 rounded-lg shadow border-l-4 border-green-500">
                <div className="flex items-center">
                  <Clock className="h-6 w-6 text-green-500" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-600">过去1小时</p>
                    <p className="text-lg font-bold text-gray-900">{realTimeStats.hourly_tool_uses}</p>
                    <p className="text-xs text-gray-500">工具使用</p>
                  </div>
                </div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow border-l-4 border-blue-500">
                <div className="flex items-center">
                  <Eye className="h-6 w-6 text-blue-500" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-600">过去1小时</p>
                    <p className="text-lg font-bold text-gray-900">{realTimeStats.hourly_page_views}</p>
                    <p className="text-xs text-gray-500">页面浏览</p>
                  </div>
                </div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow border-l-4 border-purple-500">
                <div className="flex items-center">
                  <Users className="h-6 w-6 text-purple-500" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-600">过去1小时</p>
                    <p className="text-lg font-bold text-gray-900">{realTimeStats.hourly_active_sessions}</p>
                    <p className="text-xs text-gray-500">活跃会话</p>
                  </div>
                </div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow border-l-4 border-orange-500">
                <div className="flex items-center">
                  <Calendar className="h-6 w-6 text-orange-500" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-600">今日</p>
                    <p className="text-lg font-bold text-gray-900">{realTimeStats.daily_tool_uses}</p>
                    <p className="text-xs text-gray-500">工具使用</p>
                  </div>
                </div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow border-l-4 border-teal-500">
                <div className="flex items-center">
                  <Eye className="h-6 w-6 text-teal-500" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-600">今日</p>
                    <p className="text-lg font-bold text-gray-900">{realTimeStats.daily_page_views}</p>
                    <p className="text-xs text-gray-500">页面浏览</p>
                  </div>
                </div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow border-l-4 border-indigo-500">
                <div className="flex items-center">
                  <Users className="h-6 w-6 text-indigo-500" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-600">今日</p>
                    <p className="text-lg font-bold text-gray-900">{realTimeStats.daily_active_sessions}</p>
                    <p className="text-xs text-gray-500">活跃会话</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 总体统计卡片 */}
        {overallStats && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
              <BarChart3 className="h-5 w-5 mr-2 text-blue-500" />
              总体统计
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <Users className="h-8 w-8 text-blue-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">总访客数</p>
                    <p className="text-2xl font-bold text-gray-900">{overallStats.unique_visitors.toLocaleString()}</p>
                  </div>
                </div>
              </div>
              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <Eye className="h-8 w-8 text-green-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">总页面浏览</p>
                    <p className="text-2xl font-bold text-gray-900">{overallStats.total_page_views.toLocaleString()}</p>
                  </div>
                </div>
              </div>
              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <MousePointer className="h-8 w-8 text-purple-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">工具使用次数</p>
                    <p className="text-2xl font-bold text-gray-900">{overallStats.total_tool_uses.toLocaleString()}</p>
                  </div>
                </div>
              </div>
              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <Globe className="h-8 w-8 text-orange-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">总会话数</p>
                    <p className="text-2xl font-bold text-gray-900">{overallStats.total_sessions.toLocaleString()}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 热门工具排行 */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <TrendingUp className="h-5 w-5 mr-2 text-green-500" />
            热门工具排行
          </h2>
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      排名
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      工具名称
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      总使用次数
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      独立用户
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      今日使用
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      本周使用
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {toolStats.map((tool, index) => (
                    <tr key={tool.tool_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <span className={`inline-flex items-center justify-center w-6 h-6 rounded-full text-xs font-bold text-white ${
                            index === 0 ? 'bg-yellow-500' : 
                            index === 1 ? 'bg-gray-400' : 
                            index === 2 ? 'bg-orange-500' : 'bg-gray-300'
                          }`}>
                            {index + 1}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{tool.tool_name}</div>
                        <div className="text-sm text-gray-500">{tool.tool_id}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {tool.total_uses.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {tool.unique_users.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {tool.daily_uses.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {tool.weekly_uses.toLocaleString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* 使用趋势图 */}
        {usageTrend.length > 0 && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
              <BarChart3 className="h-5 w-5 mr-2 text-purple-500" />
              7天使用趋势
            </h2>
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="space-y-4">
                {usageTrend.map((day, index) => {
                  const maxUses = Math.max(...usageTrend.map(d => d.total_uses));
                  const percentage = maxUses > 0 ? (day.total_uses / maxUses) * 100 : 0;
                  
                  return (
                    <div key={index} className="flex items-center">
                      <div className="w-20 text-sm text-gray-600">
                        {new Date(day.date).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}
                      </div>
                      <div className="flex-1 mx-4">
                        <div className="bg-gray-200 rounded-full h-4 relative">
                          <div 
                            className="bg-purple-500 h-4 rounded-full transition-all duration-300"
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                      </div>
                      <div className="w-16 text-sm text-gray-900 text-right">
                        {day.total_uses}
                      </div>
                      <div className="w-16 text-xs text-gray-500 text-right ml-2">
                        {day.unique_users}人
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
