import React, { Suspense, lazy } from 'react';

// 懒加载工具组件
const TitleAnalyzer = lazy(() => import('./tools/TitleAnalyzer'));
const TitleFixer = lazy(() => import('./tools/TitleFixer'));
const UnitConverter = lazy(() => import('./tools/UnitConverter'));
const CurrencyConverter = lazy(() => import('./tools/CurrencyConverter'));
const FBACalculator = lazy(() => import('./tools/FBACalculator'));
const KeywordDensityChecker = lazy(() => import('./tools/KeywordDensityChecker'));
const ListingOptimizer = lazy(() => import('./tools/ListingOptimizer'));
const ProfitCalculator = lazy(() => import('./tools/ProfitCalculator'));
const KeywordResearcher = lazy(() => import('./tools/KeywordResearcher'));
const CompetitorAnalyzer = lazy(() => import('./tools/CompetitorAnalyzer'));
const ReviewAnalyzer = lazy(() => import('./tools/ReviewAnalyzer'));
const PackingCalculator = lazy(() => import('./tools/PackingCalculator'));
const WorldClock = lazy(() => import('./tools/WorldClock'));
const AmzSellerCalendar = lazy(() => import('./tools/AmzSellerCalendar'));
const ReferenceTable = lazy(() => import('./tools/ReferenceTable'));
const CaseConverter = lazy(() => import('./tools/CaseConverter'));
const KeywordCombiner = lazy(() => import('./tools/KeywordCombiner'));
const WordFrequencyAnalyzer = lazy(() => import('./tools/WordFrequencyAnalyzer'));
const QRCodeGenerator = lazy(() => import('./tools/QRCodeGenerator'));
const DesignPatentSearch = lazy(() => import('./tools/DesignPatentSearch'));

// 加载中组件
const LoadingSpinner: React.FC = () => (
  <div className="flex items-center justify-center min-h-[400px]">
    <div className="flex flex-col items-center space-y-4">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      <p className="text-gray-600">Loading tool...</p>
    </div>
  </div>
);

// 错误边界组件
class ToolErrorBoundary extends React.Component<
  { children: React.ReactNode; toolId: string },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode; toolId: string }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error(`Error loading tool ${this.props.toolId}:`, error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="text-red-500 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Tool Loading Error</h3>
            <p className="text-gray-600 mb-4">Failed to load the {this.props.toolId} tool.</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Reload Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// 工具映射
const toolComponents: Record<string, React.LazyExoticComponent<React.ComponentType<any>>> = {
  'title-analyzer': TitleAnalyzer,
  'title-fixer': TitleFixer,
  'unit-converter': UnitConverter,
  'currency-converter': CurrencyConverter,
  'fba-calculator': FBACalculator,
  'keyword-density-checker': KeywordDensityChecker,
  'listing-optimizer': ListingOptimizer,
  'profit-calculator': ProfitCalculator,
  'keyword-researcher': KeywordResearcher,
  'competitor-analyzer': CompetitorAnalyzer,
  'review-analyzer': ReviewAnalyzer,
  'packing-calculator': PackingCalculator,
  'world-clock': WorldClock,
  'amz-seller-calendar': AmzSellerCalendar,
  'reference-table': ReferenceTable,
  'case-converter': CaseConverter,
  'keyword-combiner': KeywordCombiner,
  'word-frequency-analyzer': WordFrequencyAnalyzer,
  'qr-code-generator': QRCodeGenerator,
  'design-patent-search': DesignPatentSearch,
};

interface LazyToolLoaderProps {
  toolId: string;
}

export const LazyToolLoader: React.FC<LazyToolLoaderProps> = ({ toolId }) => {
  const ToolComponent = toolComponents[toolId];

  if (!ToolComponent) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="text-yellow-500 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Tool Not Found</h3>
          <p className="text-gray-600">The requested tool "{toolId}" was not found.</p>
        </div>
      </div>
    );
  }

  return (
    <ToolErrorBoundary toolId={toolId}>
      <Suspense fallback={<LoadingSpinner />}>
        <ToolComponent />
      </Suspense>
    </ToolErrorBoundary>
  );
};

// 预加载工具函数
export const preloadTool = (toolId: string) => {
  const ToolComponent = toolComponents[toolId];
  if (ToolComponent) {
    // 预加载组件
    ToolComponent();
  }
};

// 预加载热门工具
export const preloadPopularTools = () => {
  const popularTools = ['title-analyzer', 'title-fixer', 'currency-converter', 'fba-calculator'];
  popularTools.forEach(toolId => {
    setTimeout(() => preloadTool(toolId), 100);
  });
};
