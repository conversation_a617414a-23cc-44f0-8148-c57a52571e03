import React, { useState, useEffect } from 'react';
import {
  AlertCircle,
  CheckCircle,
  Info,
  AlertTriangle,
  Settings,
  ExternalLink
} from 'lucide-react';
import { useLanguage } from '../hooks/useLanguage';
import { getCacheBustParam } from '../utils/cacheUtils';
import { buildApiUrl } from '../utils/apiConfig';

interface Announcement {
  id: number;
  title: string;
  content: string;
  type: 'info' | 'warning' | 'success' | 'error' | 'maintenance';
  priority: number;
  display_position: 'header' | 'banner' | 'popup';
  is_closable: boolean;
  click_action: string | null;
  start_time: string | null;
  end_time: string | null;
}

const AnnouncementBanner: React.FC = () => {
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const { currentLanguage } = useLanguage();

  const MAX_RETRY_COUNT = 3;
  const RETRY_DELAY = 1000; // 1秒

  // 生成用户会话标识
  const getUserSession = () => {
    let session = localStorage.getItem('user_session');
    if (!session) {
      session = 'user_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
      localStorage.setItem('user_session', session);
    }
    return session;
  };



  // 获取活跃公告（带重试机制）
  const fetchAnnouncements = async (isRetry = false) => {
    try {
      if (!isRetry) {
        setLoading(true);
        setError(null);
      }

      const userSession = getUserSession();
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

      const cacheBustParam = getCacheBustParam();
      const endpoint = `/api/v1/announcements/active?language=${currentLanguage}&user_session=${userSession}&${cacheBustParam}`;
      const url = buildApiUrl(endpoint);

      const response = await fetch(url, {
        signal: controller.signal,
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      clearTimeout(timeoutId);



      if (response.ok) {
        const data = await response.json();
        const activeAnnouncements = data.data || [];

        // 不过滤公告，显示所有活跃公告
        setAnnouncements(activeAnnouncements);
        setError(null);
        setRetryCount(0);
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '网络错误';
      setError(errorMessage);

      // 重试逻辑
      if (retryCount < MAX_RETRY_COUNT && !isRetry) {
        setTimeout(() => {
          setRetryCount(prev => prev + 1);
          fetchAnnouncements(true);
        }, RETRY_DELAY * (retryCount + 1)); // 递增延迟
      }
    } finally {
      if (!isRetry) {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchAnnouncements();
  }, [currentLanguage]);

  // 页面可见性变化时重新获取公告
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && !loading) {
        fetchAnnouncements();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [loading]);

  // 监听缓存清除事件
  useEffect(() => {
    const handleCacheCleared = () => {
      console.log('收到缓存清除事件，重新获取公告');
      fetchAnnouncements();
    };

    window.addEventListener('announcementCacheCleared', handleCacheCleared);
    return () => window.removeEventListener('announcementCacheCleared', handleCacheCleared);
  }, []);

  // 自动滚动到下一条公告
  useEffect(() => {
    if (announcements.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % announcements.length);
    }, 5000); // 每5秒切换一次

    return () => clearInterval(interval);
  }, [announcements.length]);

  // 记录公告查看
  const recordView = async (announcementId: number) => {
    try {
      const userSession = getUserSession();
      const url = buildApiUrl(`/api/v1/announcements/${announcementId}/view`);
      await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ user_session: userSession })
      });
    } catch (error) {
      console.error('记录查看失败:', error);
    }
  };

  // 处理公告点击
  const handleClick = (announcement: Announcement) => {
    recordView(announcement.id);
    
    if (announcement.click_action) {
      if (announcement.click_action.startsWith('http')) {
        window.open(announcement.click_action, '_blank');
      } else {
        window.location.href = announcement.click_action;
      }
    }
  };

  // 获取类型图标
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'info': return <Info className="w-4 h-4" />;
      case 'warning': return <AlertTriangle className="w-4 h-4" />;
      case 'success': return <CheckCircle className="w-4 h-4" />;
      case 'error': return <AlertCircle className="w-4 h-4" />;
      case 'maintenance': return <Settings className="w-4 h-4" />;
      default: return <Info className="w-4 h-4" />;
    }
  };

  // 获取类型样式
  const getTypeStyle = (type: string) => {
    switch (type) {
      case 'info': 
        return {
          bg: 'bg-blue-50',
          border: 'border-blue-200',
          text: 'text-blue-800',
          icon: 'text-blue-500'
        };
      case 'warning': 
        return {
          bg: 'bg-yellow-50',
          border: 'border-yellow-200',
          text: 'text-yellow-800',
          icon: 'text-yellow-500'
        };
      case 'success': 
        return {
          bg: 'bg-green-50',
          border: 'border-green-200',
          text: 'text-green-800',
          icon: 'text-green-500'
        };
      case 'error': 
        return {
          bg: 'bg-red-50',
          border: 'border-red-200',
          text: 'text-red-800',
          icon: 'text-red-500'
        };
      case 'maintenance': 
        return {
          bg: 'bg-purple-50',
          border: 'border-purple-200',
          text: 'text-purple-800',
          icon: 'text-purple-500'
        };
      default: 
        return {
          bg: 'bg-gray-50',
          border: 'border-gray-200',
          text: 'text-gray-800',
          icon: 'text-gray-500'
        };
    }
  };

  if (loading || announcements.length === 0) {
    return null;
  }

  // 如果有错误且重试次数已达上限，静默失败
  if (error && retryCount >= MAX_RETRY_COUNT) {
    return null;
  }

  // 按优先级排序公告
  const sortedAnnouncements = announcements.sort((a, b) => b.priority - a.priority);
  const currentAnnouncement = sortedAnnouncements[currentIndex] || sortedAnnouncements[0];

  if (!currentAnnouncement) {
    return null;
  }

  const style = getTypeStyle(currentAnnouncement.type);

  return (
    <div className="relative overflow-hidden">
      <div className="flex items-center justify-center space-x-2 py-1">
        <div className={`${style.icon} flex-shrink-0`}>
          {getTypeIcon(currentAnnouncement.type)}
        </div>

        <div className="flex-1 min-w-0 text-center">
          <div className={`text-sm font-medium ${style.text} truncate`}>
            {currentAnnouncement.title}
            {currentAnnouncement.content && (
              <span className="ml-2 font-normal opacity-90">
                - {currentAnnouncement.content}
              </span>
            )}
          </div>
        </div>

        {currentAnnouncement.click_action && (
          <button
            onClick={() => handleClick(currentAnnouncement)}
            className={`${style.text} hover:opacity-80 flex items-center space-x-1 text-xs font-medium flex-shrink-0`}
          >
            <ExternalLink className="w-3 h-3" />
          </button>
        )}

        {/* 显示公告指示器（如果有多条公告） */}
        {sortedAnnouncements.length > 1 && (
          <div className="flex items-center space-x-1 flex-shrink-0">
            {sortedAnnouncements.map((_, index) => (
              <div
                key={index}
                className={`w-1.5 h-1.5 rounded-full transition-colors ${
                  index === currentIndex ? 'bg-blue-500' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default AnnouncementBanner;
