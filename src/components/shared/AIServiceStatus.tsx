import React, { useState, useEffect } from 'react';
import {
  <PERSON>ap,
  CheckCircle,
  Refresh<PERSON><PERSON>,
  <PERSON>ting<PERSON>,
  AlertCircle,
  Clock
} from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
import { getTranslation } from '../../utils/translations';

interface AIServiceStatusProps {
  className?: string;
  showDetails?: boolean;
}

interface ServiceStatus {
  isHealthy: boolean;
  responseTime?: number;
  lastChecked?: Date;
  statusMessage?: string;
}

const AIServiceStatus: React.FC<AIServiceStatusProps> = ({
  className = '',
  showDetails = false
}) => {
  const { currentLanguage } = useLanguage();
  const [serviceStatus, setServiceStatus] = useState<ServiceStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // 检查AI服务状态
  const checkServiceStatus = async () => {
    setIsLoading(true);
    try {
      const startTime = Date.now();
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'https://amzova-backend-service.sellerbox.workers.dev';
      const response = await fetch(`${apiBaseUrl}/api/ai/health`);
      const responseTime = Date.now() - startTime;
      
      if (response.ok) {
        const data = await response.json();
        setServiceStatus({
          isHealthy: data.success && data.status === 'healthy',
          responseTime,
          lastChecked: new Date(),
          statusMessage: data.success ? 
            getTranslation(currentLanguage, 'statusHealthyDesc') : 
            getTranslation(currentLanguage, 'statusUnhealthyDesc')
        });
      } else {
        setServiceStatus({
          isHealthy: false,
          responseTime,
          lastChecked: new Date(),
          statusMessage: getTranslation(currentLanguage, 'statusUnhealthyDesc')
        });
      }
    } catch (error) {
      setServiceStatus({
        isHealthy: false,
        lastChecked: new Date(),
        statusMessage: getTranslation(currentLanguage, 'connectionError')
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 组件加载时检查状态
  useEffect(() => {
    checkServiceStatus();
  }, []);

  const getStatusIcon = () => {
    if (isLoading) {
      return <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />;
    }
    
    if (!serviceStatus) {
      return <AlertCircle className="h-4 w-4 text-gray-400" />;
    }
    
    return serviceStatus.isHealthy ? 
      <CheckCircle className="h-4 w-4 text-green-500" /> : 
      <AlertCircle className="h-4 w-4 text-red-500" />;
  };

  const getStatusColor = () => {
    if (isLoading) return 'bg-blue-500';
    if (!serviceStatus) return 'bg-gray-400';
    return serviceStatus.isHealthy ? 'bg-green-500' : 'bg-red-500';
  };

  const getStatusText = () => {
    if (isLoading) return getTranslation(currentLanguage, 'checking');
    if (!serviceStatus) return getTranslation(currentLanguage, 'serviceUnavailable');
    return serviceStatus.isHealthy ? 
      getTranslation(currentLanguage, 'serviceHealthy') : 
      getTranslation(currentLanguage, 'serviceUnhealthy');
  };

  const formatResponseTime = (time?: number) => {
    if (!time) return '--';
    return time < 1000 ? `${time}ms` : `${(time / 1000).toFixed(1)}s`;
  };

  const formatLastChecked = (date?: Date) => {
    if (!date) return '--';
    return date.toLocaleTimeString(currentLanguage === 'zh' ? 'zh-CN' : currentLanguage);
  };

  if (isLoading && !serviceStatus) {
    return (
      <div className={`flex items-center justify-center p-4 bg-white rounded-xl border border-gray-200 shadow-sm ${className}`}>
        <RefreshCw className="h-4 w-4 animate-spin mr-2 text-gray-400" />
        <span className="text-sm text-gray-600">
          {getTranslation(currentLanguage, 'loading')}
        </span>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-xl border border-gray-200 shadow-sm p-4 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-gray-900 flex items-center">
          <Settings className="h-4 w-4 mr-2" />
          {getTranslation(currentLanguage, 'aiServiceStatus')}
        </h3>
        <button
          onClick={checkServiceStatus}
          disabled={isLoading}
          className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
          title={getTranslation(currentLanguage, 'refreshStatus')}
        >
          <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      {/* 通用AI服务状态 - 不显示具体提供商 */}
      <div className="p-3 bg-gray-50 rounded-lg border border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Zap className="h-4 w-4 mr-2 text-blue-500" />
            <span className="text-sm font-medium text-gray-900">
              {getTranslation(currentLanguage, 'aiServiceName')}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <div
              className={`w-3 h-3 rounded-full ${getStatusColor()}`}
              title={getStatusText()}
            />
          </div>
        </div>
        
        <div className="mt-2">
          <span className="text-xs text-gray-600">
            {getStatusText()}
          </span>
        </div>

        {showDetails && serviceStatus && (
          <div className="mt-3 pt-2 border-t border-gray-200">
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex items-center text-gray-600">
                <Clock className="h-3 w-3 mr-1" />
                <span>{getTranslation(currentLanguage, 'responseTime')}: {formatResponseTime(serviceStatus.responseTime)}</span>
              </div>
              <div className="text-gray-600">
                <span>{getTranslation(currentLanguage, 'lastChecked')}: {formatLastChecked(serviceStatus.lastChecked)}</span>
              </div>
            </div>
            {serviceStatus.statusMessage && (
              <p className="text-xs text-gray-500 mt-2">
                {serviceStatus.statusMessage}
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default AIServiceStatus;
