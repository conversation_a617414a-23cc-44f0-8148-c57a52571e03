import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Calendar, Calculator, Search, Target, FileText, BarChart3, DollarSign, TrendingUp, Zap, Package, Clock, ArrowRight, ArrowRightLeft, ChevronLeft, ChevronRight, Wrench, Globe, BookOpen, Type, Shuffle, QrCode, Shield, Image as ImageIcon } from 'lucide-react';
import { useLanguage } from '../hooks/useLanguage';
import { getTranslation } from '../utils/translations';
import { recordToolClick } from '../utils/analytics';

import TitleAnalyzer from './tools/TitleAnalyzer';
import TitleFixer from './tools/TitleFixer';
import KeywordDensityChecker from './tools/KeywordDensityChecker';
import ListingOptimizer from './tools/ListingOptimizer';
import ProfitCalculator from './tools/ProfitCalculator';
import FBACalculator from './tools/FBACalculator';
import UnitConverter from './tools/UnitConverter';
import CurrencyConverter from './tools/CurrencyConverter';
import KeywordResearcher from './tools/KeywordResearcher';
import CompetitorAnalyzer from './tools/CompetitorAnalyzer';
import ReviewAnalyzer from './tools/ReviewAnalyzer';
import PackingCalculator from './tools/PackingCalculator';
import WorldClock from './tools/WorldClock';
import AmzSellerCalendar from './tools/AmzSellerCalendar';
import ReferenceTable from './tools/ReferenceTable';
import CaseConverter from './tools/CaseConverter';
import KeywordCombiner from './tools/KeywordCombiner';
import WordFrequencyAnalyzer from './tools/WordFrequencyAnalyzer';
import QRCodeGenerator from './tools/QRCodeGenerator';
import DesignPatentSearch from './tools/DesignPatentSearch';
import ProductImageGenerator from './tools/ProductImageGenerator';

interface ToolsProps {
  selectedTool?: string | null;
  onToolChange?: (toolId: string | null) => void;
  showAsHomepage?: boolean;
}

const Tools: React.FC<ToolsProps> = ({ selectedTool, onToolChange, showAsHomepage = false }) => {
  const { currentLanguage } = useLanguage();
  const { toolId } = useParams<{ toolId: string }>();
  const navigate = useNavigate();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Get currently active tool from URL parameters
  const activeTool = toolId || null;

  // Tool switching handler
  const handleToolSelect = (newToolId: string) => {
    recordToolClick(newToolId);
    navigate(`/tools/${newToolId}`);
  };

  // Back to home handler
  const handleBackToHome = () => {
    navigate('/');
  };



  // Hot tools - Top 4 based on click count (currently default settings)
  const hotTools = [
    {
      id: 'title-fixer',
      icon: <Wrench className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'titleFixer'),
      description: getTranslation(currentLanguage, 'titleFixerShortDesc'),
      color: 'bg-gradient-to-br from-orange-500 to-red-500',
      iconBg: '#F97316',
      component: TitleFixer,
      category: 'optimization',
      status: 'ready',
      clickCount: 0 // For future click statistics
    },
    {
      id: 'unit-converter',
      icon: <ArrowRightLeft className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'unitConverter'),
      description: getTranslation(currentLanguage, 'unitConverterDesc'),
      color: 'bg-gradient-to-br from-emerald-500 to-emerald-600',
      iconBg: '#10B981',
      component: UnitConverter,
      category: 'management',
      status: 'ready',
      clickCount: 0
    },
    {
      id: 'title-analyzer',
      icon: <FileText className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'titleAnalyzer'),
      description: getTranslation(currentLanguage, 'titleAnalyzerDesc'),
      color: 'bg-gradient-to-br from-blue-500 to-blue-600',
      iconBg: '#3B82F6',
      component: TitleAnalyzer,
      category: 'optimization',
      status: 'ready',
      clickCount: 0
    },
    {
      id: 'fba-calculator',
      icon: <DollarSign className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'fbaCalculator'),
      description: getTranslation(currentLanguage, 'fbaCalculatorDesc'),
      color: 'bg-gradient-to-br from-indigo-500 to-indigo-600',
      iconBg: '#6366F1',
      component: FBACalculator,
      category: 'financial',
      status: 'ready',
      clickCount: 0
    }
  ];

  // Other tools - moved to developing or all tools list
  const otherTools = [
    {
      id: 'currency-converter',
      icon: <DollarSign className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'currencyConverter'),
      description: getTranslation(currentLanguage, 'currencyConverterDesc'),
      color: 'bg-gradient-to-br from-green-500 to-green-600',
      iconBg: '#22C55E',
      component: CurrencyConverter,
      category: 'financial',
      status: 'ready',
      clickCount: 0
    },
    {
      id: 'keyword-density',
      icon: <Target className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'keywordDensityChecker'),
      description: getTranslation(currentLanguage, 'keywordDensityDesc'),
      color: 'bg-gradient-to-br from-purple-500 to-purple-600',
      iconBg: '#8B5CF6',
      component: KeywordDensityChecker,
      category: 'optimization',
      status: 'ready',
      clickCount: 0
    },
    {
      id: 'keyword-combiner',
      icon: <Shuffle className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'keywordCombiner'),
      description: getTranslation(currentLanguage, 'keywordCombinerDesc'),
      color: 'bg-gradient-to-br from-purple-500 to-pink-500',
      iconBg: '#A855F7',
      component: KeywordCombiner,
      category: 'optimization',
      status: 'ready',
      clickCount: 0
    },
    {
      id: 'profit-calculator',
      icon: <Calculator className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'profitCalculator'),
      description: getTranslation(currentLanguage, 'profitCalculatorDesc'),
      color: 'bg-gradient-to-br from-rose-500 to-rose-600',
      iconBg: '#F43F5E',
      component: ProfitCalculator,
      category: 'financial',
      status: 'ready',
      clickCount: 0
    },
    {
      id: 'world-clock',
      icon: <Globe className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'worldClock'),
      description: getTranslation(currentLanguage, 'worldClockShortDesc'),
      color: 'bg-gradient-to-br from-blue-500 to-cyan-500',
      iconBg: '#3B82F6',
      component: WorldClock,
      category: 'management',
      status: 'ready',
      clickCount: 0
    },
    {
      id: 'amz-seller-calendar',
      icon: <Calendar className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'amzSellerCalendar'),
      description: getTranslation(currentLanguage, 'amzSellerCalendarDesc'),
      color: 'bg-gradient-to-br from-purple-500 to-pink-500',
      iconBg: '#A855F7',
      component: AmzSellerCalendar,
      category: 'management',
      status: 'ready',
      clickCount: 0
    },
    {
      id: 'reference-table',
      icon: <BookOpen className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'referenceTable'),
      description: getTranslation(currentLanguage, 'referenceTableShortDesc'),
      color: 'bg-gradient-to-br from-indigo-500 to-purple-500',
      iconBg: '#6366F1',
      component: ReferenceTable,
      category: 'management',
      status: 'ready',
      clickCount: 0
    },
    {
      id: 'case-converter',
      icon: <Type className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'caseConverter'),
      description: getTranslation(currentLanguage, 'caseConverterDesc'),
      color: 'bg-gradient-to-br from-blue-500 to-purple-600',
      iconBg: '#3B82F6',
      component: CaseConverter,
      category: 'management',
      status: 'ready',
      clickCount: 0
    },
    {
      id: 'word-frequency-analyzer',
      icon: <BarChart3 className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'wordFrequencyAnalyzer'),
      description: getTranslation(currentLanguage, 'wordFrequencyAnalyzerShortDesc'),
      color: 'bg-gradient-to-br from-blue-500 to-purple-600',
      iconBg: '#3B82F6',
      component: WordFrequencyAnalyzer,
      category: 'optimization',
      status: 'ready',
      clickCount: 0
    },
    {
      id: 'qr-code-generator',
      icon: <QrCode className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'qrCodeGenerator'),
      description: getTranslation(currentLanguage, 'qrCodeGeneratorShortDesc'),
      color: 'bg-gradient-to-br from-blue-500 to-purple-600',
      iconBg: '#3B82F6',
      component: QRCodeGenerator,
      category: 'management',
      status: 'ready',
      clickCount: 0
    },
    {
      id: 'product-image-generator',
      icon: <ImageIcon className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'toolName'),
      description: getTranslation(currentLanguage, 'shortDescription'),
      color: 'bg-gradient-to-br from-purple-500 to-pink-500',
      iconBg: '#8B5CF6',
      component: ProductImageGenerator,
      category: 'optimization',
      status: 'ready',
      clickCount: 0
    },

  ];

  // Developing tools - minimalist gray theme
  const developingTools = [
    {
      id: 'listing-optimizer',
      icon: <Zap className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'listingOptimizer'),
      description: getTranslation(currentLanguage, 'listingOptimizerDesc'),
      color: 'bg-gradient-to-br from-slate-400 to-slate-500',
      iconBg: '#64748B',
      component: ListingOptimizer,
      category: 'optimization',
      status: 'developing'
    },
    {
      id: 'keyword-researcher',
      icon: <Search className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'keywordResearcher'),
      description: getTranslation(currentLanguage, 'keywordResearcherDesc'),
      color: 'bg-gradient-to-br from-slate-400 to-slate-500',
      iconBg: '#64748B',
      component: KeywordResearcher,
      category: 'research',
      status: 'developing'
    },
    {
      id: 'competitor-analyzer',
      icon: <BarChart3 className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'competitorAnalyzer'),
      description: getTranslation(currentLanguage, 'competitorAnalyzerDesc'),
      color: 'bg-gradient-to-br from-gray-400 to-gray-500',
      iconBg: '#6B7280',
      component: CompetitorAnalyzer,
      category: 'research',
      status: 'developing'
    },
    {
      id: 'review-analyzer',
      icon: <TrendingUp className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'reviewAnalyzer'),
      description: getTranslation(currentLanguage, 'reviewAnalyzerDesc'),
      color: 'bg-gradient-to-br from-zinc-400 to-zinc-500',
      iconBg: '#71717A',
      component: ReviewAnalyzer,
      category: 'research',
      status: 'developing'
    },
    {
      id: 'design-patent-search',
      icon: <Shield className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'designPatentSearch'),
      description: getTranslation(currentLanguage, 'designPatentSearchShortDesc'),
      color: 'bg-gradient-to-br from-slate-400 to-slate-500',
      iconBg: '#64748B',
      component: DesignPatentSearch,
      category: 'research',
      status: 'developing'
    },
    {
      id: 'packing-calculator',
      icon: <Package className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'packingCalculator'),
      description: getTranslation(currentLanguage, 'packingCalculatorDesc'),
      color: 'bg-gradient-to-br from-slate-400 to-slate-500',
      iconBg: '#64748B',
      component: PackingCalculator,
      category: 'management',
      status: 'developing'
    }
  ];

  const allTools = [...hotTools, ...otherTools, ...developingTools];

  const categories = [
    { id: 'all', name: getTranslation(currentLanguage, 'allTools') },
    { id: 'optimization', name: getTranslation(currentLanguage, 'optimizationTools') },
    { id: 'financial', name: getTranslation(currentLanguage, 'financialTools') },
    { id: 'research', name: getTranslation(currentLanguage, 'researchTools') },
    { id: 'management', name: getTranslation(currentLanguage, 'managementTools') }
  ];

  const filteredHotTools = selectedCategory === 'all'
    ? hotTools
    : hotTools.filter(tool => tool.category === selectedCategory);

  const filteredOtherTools = selectedCategory === 'all'
    ? otherTools
    : otherTools.filter(tool => tool.category === selectedCategory);

  const filteredDevelopingTools = selectedCategory === 'all'
    ? developingTools
    : developingTools.filter(tool => tool.category === selectedCategory);

  // Only show tool details page when not in homepage mode and has active tool
  if (activeTool && !showAsHomepage) {
    const tool = allTools.find(t => t.id === activeTool);
    if (tool && tool.component) {
      const ToolComponent = tool.component;
      return (
        <div className="fixed inset-0 top-16 bg-gray-50 z-40">
          <div className="flex h-full">
            {/* Left sidebar */}
            <div className={`${sidebarCollapsed ? 'w-16' : 'w-80'} sidebar transition-all duration-300`}>
              {/* Sidebar header */}
              <div className="sidebar-header">
                {!sidebarCollapsed && (
                  <h3 className="font-bold text-gray-900">{getTranslation(currentLanguage, 'toolsList')}</h3>
                )}
                <button
                  onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
                  className="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                >
                  {sidebarCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
                </button>
              </div>

              {/* Back button */}
              <div className="p-4 border-b border-gray-200">
                <button
                  onClick={handleBackToHome}
                  className={`${sidebarCollapsed ? 'w-8 h-8 p-0 justify-center' : 'w-full px-4 py-2'} inline-flex items-center text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg font-medium transition-all duration-200`}
                  title={sidebarCollapsed ? getTranslation(currentLanguage, 'backToTools') : ''}
                >
                  <ArrowRight className="h-4 w-4 rotate-180" />
                  {!sidebarCollapsed && <span className="ml-2">{getTranslation(currentLanguage, 'backToTools')}</span>}
                </button>
              </div>

              {/* {getTranslation(currentLanguage, 'toolsList')} */}
              <div className="flex-1 overflow-y-auto">
                {/* {getTranslation(currentLanguage, 'optimizationToolsSection')} */}
                <div className="p-4">
                  {!sidebarCollapsed && (
                    <h4 className="text-sm font-medium text-gray-500 mb-3">{getTranslation(currentLanguage, 'optimizationToolsSection')}</h4>
                  )}
                  <div className="space-y-2">
                    {[...hotTools, ...otherTools].filter(t => t.category === 'optimization').map((t) => (
                      <button
                        key={t.id}
                        onClick={() => handleToolSelect(t.id)}
                        className={`${sidebarCollapsed ? 'w-8 h-8 p-0 justify-center' : 'sidebar-item'} inline-flex items-center ${
                          t.id === activeTool
                            ? 'sidebar-item-active'
                            : 'sidebar-item-inactive'
                        }`}
                        title={sidebarCollapsed ? t.title : ''}
                      >
                        <div
                          className={`text-white p-1 rounded ${sidebarCollapsed ? 'w-6 h-6' : 'w-5 h-5 mr-3'} flex items-center justify-center`}
                          style={{ backgroundColor: t.iconBg }}
                        >
                          {React.cloneElement(t.icon, { className: 'h-3 w-3' })}
                        </div>
                        {!sidebarCollapsed && (
                          <div className="flex items-center">
                            <span className="truncate">{t.title}</span>
                            {hotTools.includes(t) && (
                              <span className="ml-2 text-xs bg-red-500 text-white px-1.5 py-0.5 rounded">{getTranslation(currentLanguage, 'hot')}</span>
                            )}
                          </div>
                        )}
                      </button>
                    ))}
                    {/* Developing optimization tools */}
                    {developingTools.filter(t => t.category === 'optimization').map((t) => (
                      <div
                        key={t.id}
                        className={`${sidebarCollapsed ? 'w-8 h-8 p-0 justify-center' : 'w-full px-3 py-2 justify-start'} inline-flex items-center rounded-lg text-sm font-medium opacity-60 cursor-not-allowed`}
                        title={sidebarCollapsed ? `${t.title} (${getTranslation(currentLanguage, 'developing')})` : ''}
                      >
                        <div
                          className={`text-white p-1 rounded ${sidebarCollapsed ? 'w-6 h-6' : 'w-5 h-5 mr-3'} flex items-center justify-center opacity-75`}
                          style={{ backgroundColor: t.iconBg }}
                        >
                          {React.cloneElement(t.icon, { className: 'h-3 w-3' })}
                        </div>
                        {!sidebarCollapsed && (
                          <div className="flex items-center">
                            <span className="truncate">{t.title}</span>
                            <span className="ml-2 text-xs bg-gray-400 text-white px-1.5 py-0.5 rounded">{getTranslation(currentLanguage, 'developing')}</span>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* {getTranslation(currentLanguage, 'financialToolsSection')} */}
                <div className="p-4 border-t border-gray-100">
                  {!sidebarCollapsed && (
                    <h4 className="text-sm font-medium text-gray-500 mb-3">{getTranslation(currentLanguage, 'financialToolsSection')}</h4>
                  )}
                  <div className="space-y-2">
                    {[...hotTools, ...otherTools].filter(t => t.category === 'financial').map((t) => (
                      <button
                        key={t.id}
                        onClick={() => handleToolSelect(t.id)}
                        className={`${sidebarCollapsed ? 'w-8 h-8 p-0 justify-center' : 'sidebar-item'} inline-flex items-center ${
                          t.id === activeTool
                            ? 'sidebar-item-active'
                            : 'sidebar-item-inactive'
                        }`}
                        title={sidebarCollapsed ? t.title : ''}
                      >
                        <div
                          className={`text-white p-1 rounded ${sidebarCollapsed ? 'w-6 h-6' : 'w-5 h-5 mr-3'} flex items-center justify-center`}
                          style={{ backgroundColor: t.iconBg }}
                        >
                          {React.cloneElement(t.icon, { className: 'h-3 w-3' })}
                        </div>
                        {!sidebarCollapsed && (
                          <div className="flex items-center">
                            <span className="truncate">{t.title}</span>
                            {hotTools.includes(t) && (
                              <span className="ml-2 text-xs bg-red-500 text-white px-1.5 py-0.5 rounded">{getTranslation(currentLanguage, 'hot')}</span>
                            )}
                          </div>
                        )}
                      </button>
                    ))}
                  </div>
                </div>

                {/* {getTranslation(currentLanguage, 'managementToolsSection')} */}
                <div className="p-4 border-t border-gray-100">
                  {!sidebarCollapsed && (
                    <h4 className="text-sm font-medium text-gray-500 mb-3">{getTranslation(currentLanguage, 'managementToolsSection')}</h4>
                  )}
                  <div className="space-y-2">
                    {[...hotTools, ...otherTools].filter(t => t.category === 'management').map((t) => (
                      <button
                        key={t.id}
                        onClick={() => handleToolSelect(t.id)}
                        className={`${sidebarCollapsed ? 'w-8 h-8 p-0 justify-center' : 'sidebar-item'} inline-flex items-center ${
                          t.id === activeTool
                            ? 'sidebar-item-active'
                            : 'sidebar-item-inactive'
                        }`}
                        title={sidebarCollapsed ? t.title : ''}
                      >
                        <div
                          className={`text-white p-1 rounded ${sidebarCollapsed ? 'w-6 h-6' : 'w-5 h-5 mr-3'} flex items-center justify-center`}
                          style={{ backgroundColor: t.iconBg }}
                        >
                          {React.cloneElement(t.icon, { className: 'h-3 w-3' })}
                        </div>
                        {!sidebarCollapsed && (
                          <div className="flex items-center">
                            <span className="truncate">{t.title}</span>
                            {hotTools.includes(t) && (
                              <span className="ml-2 text-xs bg-red-500 text-white px-1.5 py-0.5 rounded">{getTranslation(currentLanguage, 'hot')}</span>
                            )}
                          </div>
                        )}
                      </button>
                    ))}
                  </div>
                </div>

                {/* {getTranslation(currentLanguage, 'researchToolsSection')} */}
                <div className="p-4 border-t border-gray-100">
                  {!sidebarCollapsed && (
                    <h4 className="text-sm font-medium text-gray-500 mb-3">{getTranslation(currentLanguage, 'researchToolsSection')}</h4>
                  )}
                  <div className="space-y-2">
                    {developingTools.filter(t => t.category === 'research').map((t) => (
                      <div
                        key={t.id}
                        className={`${sidebarCollapsed ? 'w-8 h-8 p-0 justify-center' : 'w-full px-3 py-2 justify-start'} inline-flex items-center rounded-lg text-sm font-medium opacity-60 cursor-not-allowed`}
                        title={sidebarCollapsed ? `${t.title} (${getTranslation(currentLanguage, 'developing')})` : ''}
                      >
                        <div
                          className={`text-white p-1 rounded ${sidebarCollapsed ? 'w-6 h-6' : 'w-5 h-5 mr-3'} flex items-center justify-center opacity-75`}
                          style={{ backgroundColor: t.iconBg }}
                        >
                          {React.cloneElement(t.icon, { className: 'h-3 w-3' })}
                        </div>
                        {!sidebarCollapsed && (
                          <div className="flex items-center">
                            <span className="truncate">{t.title}</span>
                            <span className="ml-2 text-xs bg-gray-400 text-white px-1.5 py-0.5 rounded">{getTranslation(currentLanguage, 'developing')}</span>
                          </div>
                        )}
                      </div>
                    ))}
                    {/* Packing calculator is under management tools category but displayed here */}
                    {developingTools.filter(t => t.category === 'management').map((t) => (
                      <div
                        key={t.id}
                        className={`${sidebarCollapsed ? 'w-8 h-8 p-0 justify-center' : 'w-full px-3 py-2 justify-start'} inline-flex items-center rounded-lg text-sm font-medium opacity-60 cursor-not-allowed`}
                        title={sidebarCollapsed ? `${t.title} (${getTranslation(currentLanguage, 'developing')})` : ''}
                      >
                        <div
                          className={`text-white p-1 rounded ${sidebarCollapsed ? 'w-6 h-6' : 'w-5 h-5 mr-3'} flex items-center justify-center opacity-75`}
                          style={{ backgroundColor: t.iconBg }}
                        >
                          {React.cloneElement(t.icon, { className: 'h-3 w-3' })}
                        </div>
                        {!sidebarCollapsed && (
                          <div className="flex items-center">
                            <span className="truncate">{t.title}</span>
                            <span className="ml-2 text-xs bg-gray-400 text-white px-1.5 py-0.5 rounded">{getTranslation(currentLanguage, 'developing')}</span>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Main content area */}
            <div className="flex-1 p-6 overflow-y-auto">
              <ToolComponent key={activeTool} />
            </div>
          </div>
        </div>
      );
    }
  }

  return (
    <section id="tools" className="py-8 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

        {/* {getTranslation(currentLanguage, 'hotTools')} */}
        <div className="mb-16">
          <div className="card p-8 lg:p-10 shadow-lg border-0" style={{ background: 'linear-gradient(135deg, #FFFFFF 0%, #FAFAFA 100%)' }}>
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-xl flex items-center justify-center mr-4" style={{ background: 'linear-gradient(135deg, #FF9900 0%, #E6890A 100%)' }}>
                  <span className="text-white text-lg">🔥</span>
                </div>
                <div>
                  <h3 className="text-2xl font-bold" style={{ color: '#232F3E' }}>
                    {getTranslation(currentLanguage, 'hotTools')}
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    {getTranslation(currentLanguage, 'toolsCount').replace('{count}', hotTools.length.toString())}
                  </p>
                </div>
              </div>
              <div className="hidden sm:block">
                <span className="badge badge-primary text-xs font-medium px-3 py-1">
                  {getTranslation(currentLanguage, 'freeToUse')}
                </span>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {hotTools.map((tool) => (
                <button
                  key={tool.id}
                  onClick={() => handleToolSelect(tool.id)}
                  className="group bg-white p-6 rounded-xl border border-gray-200 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-[1.02] text-left h-full flex flex-col"
                  style={{
                    borderColor: 'transparent',
                    boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px 0 rgb(0 0 0 / 0.06)'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = tool.iconBg;
                    e.currentTarget.style.boxShadow = '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = 'transparent';
                    e.currentTarget.style.boxShadow = '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px 0 rgb(0 0 0 / 0.06)';
                  }}
                >
                  <div className="flex items-start justify-between mb-4">
                    <div
                      className="w-14 h-14 text-white rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg"
                      style={{ backgroundColor: tool.iconBg }}
                    >
                      {React.cloneElement(tool.icon, { className: 'h-6 w-6' })}
                    </div>
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="w-2 h-2 rounded-full" style={{ backgroundColor: tool.iconBg }}></div>
                    </div>
                  </div>
                  <div className="flex-1 flex flex-col">
                    <h3 className="text-lg font-bold mb-2 leading-tight" style={{ color: '#232F3E' }}>
                      {tool.title}
                    </h3>
                    <p className="text-sm text-gray-600 leading-relaxed mb-4 flex-1">
                      {tool.description}
                    </p>
                    <div className="flex items-center text-sm font-semibold group-hover:text-orange-600 transition-colors duration-300" style={{ color: '#FF9900' }}>
                      <span>{getTranslation(currentLanguage, 'useToolNow')}</span>
                      <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* {getTranslation(currentLanguage, 'allToolsSection')} */}
        <div className="card p-6 lg:p-8 shadow-lg">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-gray-900 flex items-center">
              {getTranslation(currentLanguage, 'allToolsSection')}
              <span className="ml-3 badge badge-secondary">
                {allTools.length}{getTranslation(currentLanguage, 'toolsCountSuffix')}
              </span>
            </h3>
          </div>

          {/* Category filter */}
          <div className="flex flex-wrap gap-2 mb-6">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                  selectedCategory === category.id
                    ? 'btn-primary'
                    : 'btn-outline'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>

          {/* Tools grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {/* {getTranslation(currentLanguage, 'hotTools')} */}
            {filteredHotTools.map((tool) => (
              <button
                key={tool.id}
                onClick={() => handleToolSelect(tool.id)}
                className="group bg-white p-4 rounded-xl border border-gray-200 hover:shadow-md transition-all duration-300 text-left relative"
                style={{
                  background: 'linear-gradient(135deg, #FFFFFF 0%, #FAFAFA 100%)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.borderColor = tool.iconBg;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.borderColor = '#E5E7EB';
                }}
              >
                <div className="absolute top-2 right-2">
                  <span className="badge badge-error text-xs">{getTranslation(currentLanguage, 'hot')}</span>
                </div>
                <div
                  className="text-white p-2.5 rounded-xl w-fit mb-3"
                  style={{ backgroundColor: tool.iconBg }}
                >
                  {tool.icon}
                </div>
                <h4 className="font-bold mb-2 text-sm" style={{ color: '#232F3E' }}>{tool.title}</h4>
                <p className="text-gray-600 text-xs leading-relaxed mb-3">{tool.description}</p>
                <div className="flex items-center text-xs font-medium" style={{ color: '#FF9900' }}>
                  <span>{getTranslation(currentLanguage, 'useToolNow')}</span>
                  <ArrowRight className="h-3 w-3 ml-1" />
                </div>
              </button>
            ))}

            {/* Other tools */}
            {filteredOtherTools.map((tool) => (
              <button
                key={tool.id}
                onClick={() => handleToolSelect(tool.id)}
                className="group bg-white p-4 rounded-xl border border-gray-200 hover:shadow-md transition-all duration-300 text-left relative"
                style={{
                  background: 'linear-gradient(135deg, #FFFFFF 0%, #FAFAFA 100%)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.borderColor = tool.iconBg;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.borderColor = '#E5E7EB';
                }}
              >
                <div
                  className="text-white p-2.5 rounded-xl w-fit mb-3"
                  style={{ backgroundColor: tool.iconBg }}
                >
                  {tool.icon}
                </div>
                <h4 className="font-bold mb-2 text-sm" style={{ color: '#232F3E' }}>{tool.title}</h4>
                <p className="text-gray-600 text-xs leading-relaxed mb-3">{tool.description}</p>
                <div className="flex items-center text-xs font-medium" style={{ color: '#FF9900' }}>
                  <span>{getTranslation(currentLanguage, 'useToolNow')}</span>
                  <ArrowRight className="h-3 w-3 ml-1" />
                </div>
              </button>
            ))}

            {/* {getTranslation(currentLanguage, 'developingTools')} */}
            {filteredDevelopingTools.map((tool) => (
              <div
                key={tool.id}
                className="group bg-white p-4 rounded-xl border border-gray-200 text-left relative opacity-75 cursor-not-allowed"
                style={{
                  background: 'linear-gradient(135deg, #F9FAFB 0%, #F3F4F6 100%)'
                }}
              >
                <div className="absolute top-2 right-2">
                  <span className="badge badge-secondary text-xs">{getTranslation(currentLanguage, 'developing')}</span>
                </div>
                <div
                  className="text-white p-2.5 rounded-xl w-fit mb-3 opacity-75"
                  style={{ backgroundColor: tool.iconBg }}
                >
                  {tool.icon}
                </div>
                <h4 className="font-bold mb-2 text-sm" style={{ color: '#232F3E' }}>{tool.title}</h4>
                <p className="text-gray-600 text-xs leading-relaxed mb-3">{tool.description}</p>
                <div className="flex items-center text-gray-500 text-xs font-medium">
                  <Clock className="h-3 w-3 mr-1" />
                  <span>{getTranslation(currentLanguage, 'comingSoon')}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Tools;
