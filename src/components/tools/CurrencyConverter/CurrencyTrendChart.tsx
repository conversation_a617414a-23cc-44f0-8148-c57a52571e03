import React, { useRef, useEffect, useState, useCallback } from 'react';
import { useLanguage } from '../../../hooks/useLanguage';
import { getTranslation } from '../../../utils/translations';

interface TrendDataPoint {
  timestamp: number;
  rate: number;
  date: string;
}

interface Props {
  fromCurrency: string;
  toCurrency: string;
}

const CurrencyTrendChart: React.FC<Props> = ({ fromCurrency, toCurrency }) => {
  const { currentLanguage } = useLanguage();
  const chartCanvasRef = useRef<HTMLCanvasElement>(null);
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState(false);
  const [chartData, setChartData] = useState<TrendDataPoint[]>([]);
  const [selectedRange, setSelectedRange] = useState('1h');

  const timeRanges = [
    { value: '1h', label: 'ccLast1Hour' },
    { value: '12h', label: 'ccLast12Hours' },
    { value: '24h', label: 'ccLast24Hours' }
  ];

  // 获取历史汇率数据
  const fetchTrendData = useCallback(async (from: string, to: string, range: string) => {
    if (from === to) {
      setChartData([]);
      return;
    }

    setLoading(true);

    try {
      // 调用实际的API来获取D1数据库中的历史数据
      const apiUrl = `${import.meta.env.VITE_API_BASE_URL || 'https://api.amzova.com'}/api/v1/exchange-rates/historical`;

      const response = await fetch(`${apiUrl}?base=${from}&target=${to}&range=${range}`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success && result.data && result.data.rates) {
        // 转换API数据格式为图表需要的格式
        const chartPoints = result.data.rates.map((point: any) => ({
          timestamp: new Date(point.timestamp).getTime(),
          rate: point.rate,
          date: point.timestamp
        }));
        setChartData(chartPoints);
      } else {
        console.warn('API返回的历史数据格式不正确或暂无数据');
        setChartData([]);
      }
    } catch (error) {
      console.error('获取历史汇率数据失败:', error);
      // 如果API调用失败，不使用模拟数据，显示无数据状态
      setChartData([]);
    } finally {
      setLoading(false);
    }
  }, []);



  // 绘制图表
  const drawChart = useCallback(() => {
    if (!chartCanvasRef.current || !chartData.length) return;
    
    const canvas = chartCanvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // 设置canvas尺寸
    const container = chartContainerRef.current;
    if (!container) return;
    
    const rect = container.getBoundingClientRect();
    canvas.width = rect.width - 40;
    canvas.height = 160; // 缩短图表高度
    
    const { width, height } = canvas;
    const padding = { top: 20, right: 20, bottom: 40, left: 60 };
    const chartWidth = width - padding.left - padding.right;
    const chartHeight = height - padding.top - padding.bottom;
    
    // 清空画布
    ctx.clearRect(0, 0, width, height);
    
    // 获取数据范围并优化Y轴显示
    const rates = chartData.map(d => d.rate);
    const minRate = Math.min(...rates);
    const maxRate = Math.max(...rates);
    const rateRange = maxRate - minRate;

    // 如果汇率变化很小，扩展Y轴范围以便更好地显示变化
    let displayMinRate, displayMaxRate;
    if (rateRange < 0.01) {
      // 变化很小时，以平均值为中心，扩展±0.5%的范围
      const avgRate = (minRate + maxRate) / 2;
      const expandedRange = Math.max(avgRate * 0.005, 0.001); // 至少0.1%的变化范围
      displayMinRate = avgRate - expandedRange;
      displayMaxRate = avgRate + expandedRange;
    } else {
      // 正常情况下，在实际范围基础上增加10%的边距
      const margin = rateRange * 0.1;
      displayMinRate = minRate - margin;
      displayMaxRate = maxRate + margin;
    }
    const displayRange = displayMaxRate - displayMinRate;
    
    // 绘制网格线
    ctx.strokeStyle = '#f0f0f0';
    ctx.lineWidth = 1;
    
    // 水平网格线
    for (let i = 0; i <= 4; i++) {
      const y = padding.top + (chartHeight / 4) * i;
      ctx.beginPath();
      ctx.moveTo(padding.left, y);
      ctx.lineTo(padding.left + chartWidth, y);
      ctx.stroke();
    }
    
    // 垂直网格线
    const timePoints = Math.min(chartData.length, 6);
    for (let i = 0; i <= timePoints; i++) {
      const x = padding.left + (chartWidth / timePoints) * i;
      ctx.beginPath();
      ctx.moveTo(x, padding.top);
      ctx.lineTo(x, padding.top + chartHeight);
      ctx.stroke();
    }
    
    // 绘制汇率线
    ctx.strokeStyle = '#ff8c00';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    chartData.forEach((point, index) => {
      const x = padding.left + (chartWidth / (chartData.length - 1)) * index;
      const y = padding.top + chartHeight - ((point.rate - displayMinRate) / displayRange) * chartHeight;

      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });

    ctx.stroke();

    // 绘制数据点
    ctx.fillStyle = '#ff8c00';
    chartData.forEach((point, index) => {
      const x = padding.left + (chartWidth / (chartData.length - 1)) * index;
      const y = padding.top + chartHeight - ((point.rate - displayMinRate) / displayRange) * chartHeight;

      ctx.beginPath();
      ctx.arc(x, y, 3, 0, 2 * Math.PI);
      ctx.fill();
    });
    
    // 绘制Y轴标签（汇率）
    ctx.fillStyle = '#666';
    ctx.font = '12px Arial';
    ctx.textAlign = 'right';
    
    for (let i = 0; i <= 4; i++) {
      const rate = displayMinRate + (displayRange / 4) * (4 - i);
      const y = padding.top + (chartHeight / 4) * i + 4;
      ctx.fillText(rate.toFixed(4), padding.left - 10, y);
    }
    
    // 绘制X轴标签（时间）
    ctx.textAlign = 'center';
    const labelCount = Math.min(chartData.length, 6);
    for (let i = 0; i < labelCount; i++) {
      const dataIndex = Math.floor((chartData.length - 1) * i / (labelCount - 1));
      const point = chartData[dataIndex];
      const x = padding.left + (chartWidth / (labelCount - 1)) * i;
      const y = height - 10;
      
      const date = new Date(point.timestamp);
      let label = '';
      
      if (selectedRange === '1d') {
        label = date.toLocaleTimeString(currentLanguage === 'zh' ? 'zh-CN' : 'en-US', {
          hour: '2-digit',
          minute: '2-digit'
        });
      } else {
        label = date.toLocaleDateString(currentLanguage === 'zh' ? 'zh-CN' : 'en-US', {
          month: 'short',
          day: 'numeric'
        });
      }
      
      ctx.fillText(label, x, y);
    }
  }, [chartData, selectedRange, currentLanguage]);

  const selectTimeRange = (range: string) => {
    setSelectedRange(range);
    fetchTrendData(fromCurrency, toCurrency, range);
  };

  // 监听货币对变化
  useEffect(() => {
    if (fromCurrency && toCurrency) {
      fetchTrendData(fromCurrency, toCurrency, selectedRange);
    }
  }, [fromCurrency, toCurrency, selectedRange, fetchTrendData]);

  // 绘制图表
  useEffect(() => {
    if (chartData.length > 0) {
      const timer = setTimeout(() => {
        drawChart();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [chartData, drawChart]);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      if (chartData.length > 0) {
        setTimeout(() => drawChart(), 100);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [chartData, drawChart]);

  return (
    <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-100 mt-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 gap-4">
        <h3 className="text-lg font-bold text-gray-800">
          {getTranslation(currentLanguage, 'ccRateTrendChart')}
        </h3>
        <div className="flex gap-2">
          {timeRanges.map((range) => (
            <button
              key={range.value}
              onClick={() => selectTimeRange(range.value)}
              className={`px-3 py-1.5 text-sm rounded-lg border transition-all duration-200 ${
                selectedRange === range.value
                  ? 'bg-orange-500 text-white border-orange-500'
                  : 'bg-white text-gray-600 border-gray-300 hover:border-orange-400 hover:text-orange-600'
              }`}
            >
              {getTranslation(currentLanguage, range.label)}
            </button>
          ))}
        </div>
      </div>
      
      <div
        ref={chartContainerRef}
        className="relative h-48 flex items-center justify-center"
      >
        {loading ? (
          <div className="flex flex-col items-center justify-center text-gray-500 gap-3">
            <div className="w-6 h-6 border-2 border-gray-300 border-t-orange-500 rounded-full animate-spin"></div>
            <p className="text-sm">{getTranslation(currentLanguage, 'ccLoadingTrend')}</p>
          </div>
        ) : !chartData || chartData.length === 0 ? (
          <div className="flex flex-col items-center justify-center text-gray-500 gap-3">
            <div className="text-4xl opacity-50">📈</div>
            <p className="text-sm">{getTranslation(currentLanguage, 'ccNoTrendData')}</p>
          </div>
        ) : (
          <canvas 
            ref={chartCanvasRef}
            className="w-full h-full"
            style={{ maxHeight: '160px' }}
          />
        )}
      </div>
    </div>
  );
};

export default CurrencyTrendChart;
