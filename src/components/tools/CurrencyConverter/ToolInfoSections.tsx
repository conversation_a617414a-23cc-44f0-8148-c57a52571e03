import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Info, BookOpen, HelpCircle, Lightbulb } from 'lucide-react';
import { useLanguage } from '../../../hooks/useLanguage';
import { getTranslation } from '../../../utils/translations';

const ToolInfoSections: React.FC = () => {
  const { currentLanguage } = useLanguage();
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({});

  const toggleSection = (sectionId: string) => {
    setOpenSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  const sections = [
    {
      id: 'intro',
      icon: <Info className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'ccToolIntroTitle'),
      content: (
        <div className="space-y-4">
          <p className="text-gray-700 leading-relaxed">
            {getTranslation(currentLanguage, 'ccToolIntroContent')}
          </p>
          
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <h4 className="font-semibold text-orange-800 mb-3">
              {getTranslation(currentLanguage, 'ccToolFeaturesTitle')}
            </h4>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <p className="font-medium text-orange-800">
                    {getTranslation(currentLanguage, 'ccFeatureRealTimeRates')}
                  </p>
                  <p className="text-sm text-orange-700">
                    {getTranslation(currentLanguage, 'ccFeatureRealTimeRatesDesc')}
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <p className="font-medium text-orange-800">
                    {getTranslation(currentLanguage, 'ccFeatureTrendChart')}
                  </p>
                  <p className="text-sm text-orange-700">
                    {getTranslation(currentLanguage, 'ccFeatureTrendChartDesc')}
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <p className="font-medium text-orange-800">
                    {getTranslation(currentLanguage, 'ccFeatureMultiCurrency')}
                  </p>
                  <p className="text-sm text-orange-700">
                    {getTranslation(currentLanguage, 'ccFeatureMultiCurrencyDesc')}
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <p className="font-medium text-orange-800">
                    {getTranslation(currentLanguage, 'ccFeatureTimezone')}
                  </p>
                  <p className="text-sm text-orange-700">
                    {getTranslation(currentLanguage, 'ccFeatureTimezoneDesc')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'guide',
      icon: <BookOpen className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'ccUsageGuideTitle'),
      content: (
        <div className="space-y-6">
          {[1, 2, 3, 4, 5].map(step => (
            <div key={step} className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center font-semibold text-sm">
                {step}
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-gray-900 mb-2">
                  {getTranslation(currentLanguage, `ccStep${step}Title`)}
                </h4>
                <p className="text-gray-700 leading-relaxed">
                  {getTranslation(currentLanguage, `ccStep${step}Content`)}
                </p>
              </div>
            </div>
          ))}
        </div>
      )
    },
    {
      id: 'faq',
      icon: <HelpCircle className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'ccFaqTitle'),
      content: (
        <div className="space-y-6">
          {[1, 2, 3, 4, 5, 6].map(faq => {
            const questionKey = `ccFaq${faq}Question`;
            const answerKey = `ccFaq${faq}Answer`;
            const question = getTranslation(currentLanguage, questionKey);
            const answer = getTranslation(currentLanguage, answerKey);
            
            // 只显示有翻译的FAQ
            if (question === questionKey || answer === answerKey) {
              return null;
            }
            
            return (
              <div key={faq} className="border-b border-gray-200 pb-4 last:border-b-0">
                <h4 className="font-semibold text-gray-900 mb-2">
                  Q{faq}: {question}
                </h4>
                <p className="text-gray-700 leading-relaxed">
                  {answer}
                </p>
              </div>
            );
          })}
        </div>
      )
    },
    {
      id: 'best-practices',
      icon: <Lightbulb className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'ccBestPracticesTitle'),
      content: (
        <div className="space-y-3">
          {[1, 2, 3, 4, 5].map(practice => {
            const practiceKey = `ccBestPractice${practice}`;
            const practiceText = getTranslation(currentLanguage, practiceKey);
            
            // 只显示有翻译的最佳实践
            if (practiceText === practiceKey) {
              return null;
            }
            
            return (
              <div key={practice} className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-gray-700 leading-relaxed">
                  {practiceText}
                </p>
              </div>
            );
          })}
        </div>
      )
    }
  ];

  return (
    <div className="mt-16 border-t border-gray-200 pt-12">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {getTranslation(currentLanguage, 'ccToolDetailInfoTitle')}
          </h2>
          <p className="text-gray-600">
            {getTranslation(currentLanguage, 'ccToolDetailInfoSubtitle')}
          </p>
        </div>

        <div className="space-y-4">
          {sections.map(section => (
            <div key={section.id} className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
              <button
                onClick={() => toggleSection(section.id)}
                className="w-full px-6 py-4 bg-gray-50 hover:bg-gray-100 flex items-center justify-between transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-inset"
              >
                <div className="flex items-center space-x-3">
                  <div className="text-orange-600 flex-shrink-0">
                    {section.icon}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 text-left">
                    {section.title}
                  </h3>
                </div>
                <div className="text-gray-500 flex-shrink-0">
                  {openSections[section.id] ? (
                    <ChevronUp className="h-5 w-5" />
                  ) : (
                    <ChevronDown className="h-5 w-5" />
                  )}
                </div>
              </button>

              {openSections[section.id] && (
                <div className="px-6 py-6 bg-white border-t border-gray-200 animate-fadeIn">
                  {section.content}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ToolInfoSections;
