import React, { useState } from 'react';
import { BarChart3, TrendingUp, Star, DollarSign, Package, Users } from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
import { getTranslation } from '../../utils/translations';

interface CompetitorData {
  asin: string;
  title: string;
  price: number;
  rating: number;
  reviewCount: number;
  salesRank: number;
  estimatedSales: number;
  revenue: number;
  fbaFees: number;
  profitMargin: number;
}

const CompetitorAnalyzer = () => {
  const { currentLanguage } = useLanguage();
  const [asinInput, setAsinInput] = useState('');
  const [marketplace, setMarketplace] = useState('US');
  const [isLoading, setIsLoading] = useState(false);
  const [competitors, setCompetitors] = useState<CompetitorData[]>([]);

  const marketplaces = [
    { code: 'US', name: 'Amazon.com (US)', currency: '$' },
    { code: 'UK', name: 'Amazon.co.uk (UK)', currency: '£' },
    { code: 'DE', name: 'Amazon.de (Germany)', currency: '€' },
    { code: 'CA', name: 'Amazon.ca (Canada)', currency: 'C$' }
  ];

  const currentMarketplace = marketplaces.find(m => m.code === marketplace) || marketplaces[0];

  // Mock data generator
  const generateMockCompetitors = (asin: string): CompetitorData[] => {
    const mockTitles = [
      'Premium Wireless Bluetooth Headphones with Noise Cancelling',
      'Professional Gaming Headset with Microphone',
      'Sports Wireless Earbuds with Charging Case',
      'Studio Quality Over-Ear Headphones',
      'Compact Bluetooth Speaker with Bass Boost',
      'True Wireless Earbuds with Active Noise Cancellation',
      'Gaming Headphones with RGB Lighting',
      'Portable Bluetooth Headphones for Travel'
    ];

    return mockTitles.map((title, index) => ({
      asin: `B0${Math.random().toString(36).substr(2, 8).toUpperCase()}`,
      title,
      price: Math.random() * 100 + 20,
      rating: Math.random() * 2 + 3,
      reviewCount: Math.floor(Math.random() * 5000) + 100,
      salesRank: Math.floor(Math.random() * 10000) + 1,
      estimatedSales: Math.floor(Math.random() * 1000) + 50,
      revenue: 0,
      fbaFees: 0,
      profitMargin: 0
    })).map(item => {
      item.revenue = item.estimatedSales * item.price;
      item.fbaFees = item.price * 0.15;
      item.profitMargin = ((item.price - item.fbaFees - (item.price * 0.4)) / item.price) * 100;
      return item;
    }).sort((a, b) => a.salesRank - b.salesRank);
  };

  const handleAnalyze = async () => {
    if (!asinInput.trim()) return;
    
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      const mockCompetitors = generateMockCompetitors(asinInput);
      setCompetitors(mockCompetitors);
      setIsLoading(false);
    }, 2000);
  };

  const getRatingColor = (rating: number) => {
    if (rating >= 4.5) return 'text-green-600';
    if (rating >= 4.0) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getProfitColor = (margin: number) => {
    if (margin >= 20) return 'text-green-600';
    if (margin >= 10) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="max-w-7xl mx-auto">
      <div className="bg-white rounded-2xl shadow-lg p-8">
        <div className="flex items-center mb-8">
          <div className="bg-teal-500 text-white p-3 rounded-xl mr-4">
            <BarChart3 className="h-8 w-8" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {getTranslation(currentLanguage, 'competitorAnalyzer')}
            </h1>
            <p className="text-gray-600 mt-2">
              {getTranslation(currentLanguage, 'competitorAnalyzerDesc')}
            </p>
          </div>
        </div>

        {/* Search Form */}
        <div className="bg-gray-50 rounded-xl p-6 mb-8">
          <div className="grid md:grid-cols-3 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {getTranslation(currentLanguage, 'marketplace')}
              </label>
              <select
                value={marketplace}
                onChange={(e) => setMarketplace(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
              >
                {marketplaces.map((mp) => (
                  <option key={mp.code} value={mp.code}>
                    {mp.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {getTranslation(currentLanguage, 'productAsin')}
              </label>
              <input
                type="text"
                value={asinInput}
                onChange={(e) => setAsinInput(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleAnalyze()}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                placeholder="B08N5WRWNW"
              />
            </div>
          </div>

          <button
            onClick={handleAnalyze}
            disabled={!asinInput.trim() || isLoading}
            className="bg-teal-600 hover:bg-teal-700 disabled:bg-teal-400 text-white px-8 py-3 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 disabled:transform-none flex items-center"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                {getTranslation(currentLanguage, 'analyzing')}
              </>
            ) : (
              <>
                <BarChart3 className="h-5 w-5 mr-2" />
                {getTranslation(currentLanguage, 'analyzeCompetitors')}
              </>
            )}
          </button>
        </div>

        {/* Results */}
        {competitors.length > 0 && (
          <div>
            {/* Summary Stats */}
            <div className="grid md:grid-cols-4 gap-6 mb-8">
              <div className="bg-blue-50 rounded-xl p-6">
                <div className="text-blue-600 text-sm font-medium mb-2">
                  {getTranslation(currentLanguage, 'avgPrice')}
                </div>
                <div className="text-2xl font-bold text-blue-900">
                  {currentMarketplace.currency}{(competitors.reduce((sum, c) => sum + c.price, 0) / competitors.length).toFixed(2)}
                </div>
              </div>

              <div className="bg-green-50 rounded-xl p-6">
                <div className="text-green-600 text-sm font-medium mb-2">
                  {getTranslation(currentLanguage, 'avgRating')}
                </div>
                <div className="text-2xl font-bold text-green-900">
                  {(competitors.reduce((sum, c) => sum + c.rating, 0) / competitors.length).toFixed(1)}
                </div>
              </div>

              <div className="bg-purple-50 rounded-xl p-6">
                <div className="text-purple-600 text-sm font-medium mb-2">
                  {getTranslation(currentLanguage, 'avgSales')}
                </div>
                <div className="text-2xl font-bold text-purple-900">
                  {Math.round(competitors.reduce((sum, c) => sum + c.estimatedSales, 0) / competitors.length)}
                </div>
              </div>

              <div className="bg-orange-50 rounded-xl p-6">
                <div className="text-orange-600 text-sm font-medium mb-2">
                  {getTranslation(currentLanguage, 'avgMargin')}
                </div>
                <div className="text-2xl font-bold text-orange-900">
                  {(competitors.reduce((sum, c) => sum + c.profitMargin, 0) / competitors.length).toFixed(1)}%
                </div>
              </div>
            </div>

            {/* Competitor Table */}
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-4 px-4 font-semibold text-gray-900">
                      {getTranslation(currentLanguage, 'product')}
                    </th>
                    <th className="text-left py-4 px-4 font-semibold text-gray-900">
                      {getTranslation(currentLanguage, 'price')}
                    </th>
                    <th className="text-left py-4 px-4 font-semibold text-gray-900">
                      {getTranslation(currentLanguage, 'rating')}
                    </th>
                    <th className="text-left py-4 px-4 font-semibold text-gray-900">
                      {getTranslation(currentLanguage, 'salesRank')}
                    </th>
                    <th className="text-left py-4 px-4 font-semibold text-gray-900">
                      {getTranslation(currentLanguage, 'estSales')}
                    </th>
                    <th className="text-left py-4 px-4 font-semibold text-gray-900">
                      {getTranslation(currentLanguage, 'revenue')}
                    </th>
                    <th className="text-left py-4 px-4 font-semibold text-gray-900">
                      {getTranslation(currentLanguage, 'margin')}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {competitors.map((competitor, index) => (
                    <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-4 px-4">
                        <div>
                          <div className="font-medium text-gray-900 text-sm">
                            {competitor.title.length > 50 
                              ? competitor.title.substring(0, 50) + '...' 
                              : competitor.title}
                          </div>
                          <div className="text-xs text-gray-500">{competitor.asin}</div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center">
                          <DollarSign className="h-4 w-4 text-gray-400 mr-1" />
                          {currentMarketplace.currency}{competitor.price.toFixed(2)}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center">
                          <Star className={`h-4 w-4 mr-1 ${getRatingColor(competitor.rating)}`} />
                          <span className={getRatingColor(competitor.rating)}>
                            {competitor.rating.toFixed(1)}
                          </span>
                          <span className="text-gray-500 text-sm ml-1">
                            ({competitor.reviewCount})
                          </span>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center">
                          <TrendingUp className="h-4 w-4 text-gray-400 mr-1" />
                          #{competitor.salesRank.toLocaleString()}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center">
                          <Package className="h-4 w-4 text-gray-400 mr-1" />
                          {competitor.estimatedSales}/month
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="font-medium text-gray-900">
                          {currentMarketplace.currency}{competitor.revenue.toLocaleString()}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className={`font-medium ${getProfitColor(competitor.profitMargin)}`}>
                          {competitor.profitMargin.toFixed(1)}%
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Insights */}
            <div className="mt-8 bg-teal-50 rounded-xl p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Users className="h-5 w-5 mr-2 text-teal-600" />
                {getTranslation(currentLanguage, 'competitorInsights')}
              </h4>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h5 className="font-medium text-gray-900 mb-2">
                    {getTranslation(currentLanguage, 'marketOpportunities')}
                  </h5>
                  <div className="space-y-2 text-sm text-gray-700">
                    <p>• {getTranslation(currentLanguage, 'opportunity1')}</p>
                    <p>• {getTranslation(currentLanguage, 'opportunity2')}</p>
                    <p>• {getTranslation(currentLanguage, 'opportunity3')}</p>
                  </div>
                </div>
                <div>
                  <h5 className="font-medium text-gray-900 mb-2">
                    {getTranslation(currentLanguage, 'competitiveAdvantages')}
                  </h5>
                  <div className="space-y-2 text-sm text-gray-700">
                    <p>• {getTranslation(currentLanguage, 'advantage1')}</p>
                    <p>• {getTranslation(currentLanguage, 'advantage2')}</p>
                    <p>• {getTranslation(currentLanguage, 'advantage3')}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Tips */}
        {competitors.length === 0 && !isLoading && (
          <div className="bg-teal-50 rounded-xl p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">
              {getTranslation(currentLanguage, 'competitorAnalysisTips')}
            </h4>
            <div className="space-y-2 text-sm text-gray-700">
              <p>• {getTranslation(currentLanguage, 'competitorTip1')}</p>
              <p>• {getTranslation(currentLanguage, 'competitorTip2')}</p>
              <p>• {getTranslation(currentLanguage, 'competitorTip3')}</p>
              <p>• {getTranslation(currentLanguage, 'competitorTip4')}</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CompetitorAnalyzer;