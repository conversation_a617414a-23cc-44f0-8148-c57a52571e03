import React from 'react';
import { AlertTriangle, Info, Clock, Lightbulb, Heart, CheckCircle } from 'lucide-react';
import { useLanguage } from '../../../hooks/useLanguage';
import { getTranslation } from '../../../utils/translations';
import { UsageStats, formatTimeUntilReset } from '../../../utils/aiUsageLimit';

interface UsageLimitInfoProps {
  usageStats: UsageStats;
  isVisible: boolean;
}

const UsageLimitInfo: React.FC<UsageLimitInfoProps> = ({
  usageStats,
  isVisible
}) => {
  const { currentLanguage } = useLanguage();

  if (!isVisible) return null;

  return (
    <div className="space-y-4">
      {/* 限制说明卡片 */}
      {usageStats.isLimited && (
        <div className="bg-amber-50 border border-amber-200 rounded-xl p-4">
          <div className="flex items-start">
            <AlertTriangle className="h-4 w-4 text-amber-600 mr-3 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <h4 className="text-amber-900 font-medium text-sm mb-2">
                {getTranslation(currentLanguage, 'taUsageLimitExceeded')}
              </h4>
              <p className="text-amber-800 text-xs mb-3">
                {getTranslation(currentLanguage, 'taDailyLimitReached')}
              </p>
              
              <div className="flex items-center text-xs text-amber-700 mb-3">
                <Clock className="h-3 w-3 mr-1" />
                <span>{getTranslation(currentLanguage, 'taNextResetTime')}: {getTranslation(currentLanguage, 'taResetTomorrow')}</span>
              </div>

              <div className="bg-green-100 border border-green-200 rounded-lg p-3">
                <div className="flex items-center text-green-800 text-xs mb-1">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  <span className="font-medium">{getTranslation(currentLanguage, 'taBasicAnalysisAvailable')}</span>
                </div>
                <p className="text-green-700 text-xs">
                  {getTranslation(currentLanguage, 'taBasicAnalysisDescription')}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 使用统计 */}
      <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
        <div className="flex items-center mb-3">
          <Info className="h-4 w-4 text-blue-600 mr-2" />
          <h4 className="text-blue-900 font-medium text-sm">
            {getTranslation(currentLanguage, 'taAiUsageStatus')}
          </h4>
        </div>

        <div className="grid grid-cols-2 gap-3 mb-4">
          <div className="text-center">
            <div className="text-lg font-bold text-blue-900">
              {Math.max(0, 10 - usageStats.totalUsage)}
            </div>
            <div className="text-xs text-blue-700">
              {getTranslation(currentLanguage, 'taRemainingUsage')}
            </div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-blue-900">
              {usageStats.totalUsage}/10
            </div>
            <div className="text-xs text-blue-700">
              {getTranslation(currentLanguage, 'taTodayUsed')}
            </div>
          </div>
        </div>

        {/* 进度条 */}
        <div className="mb-4">
          <div className="flex justify-between text-xs text-blue-700 mb-1">
            <span>{getTranslation(currentLanguage, 'taTodayUsageProgress')}</span>
            <span>{usageStats.totalUsage}/10</span>
          </div>
          <div className="w-full bg-blue-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${
                (10 - usageStats.totalUsage) <= 1 ? 'bg-red-500' :
                (10 - usageStats.totalUsage) <= 2 ? 'bg-amber-500' : 'bg-blue-500'
              }`}
              style={{ width: `${Math.min((usageStats.totalUsage / 10) * 100, 100)}%` }}
            />
          </div>
        </div>
      </div>

      {/* 为什么有限制 */}
      <div className="bg-gray-50 border border-gray-200 rounded-xl p-4">
        <div className="flex items-center mb-3">
          <Lightbulb className="h-4 w-4 text-gray-600 mr-2" />
          <h4 className="text-gray-900 font-medium text-sm">
            {getTranslation(currentLanguage, 'taLimitReason')}
          </h4>
        </div>

        <div className="space-y-2 mb-4">
          <div className="text-xs text-gray-700">
            {getTranslation(currentLanguage, 'taLimitExplanation1')}
          </div>
          <div className="text-xs text-gray-700">
            {getTranslation(currentLanguage, 'taLimitExplanation2')}
          </div>
          <div className="text-xs text-gray-700">
            {getTranslation(currentLanguage, 'taLimitExplanation3')}
          </div>
          <div className="text-xs text-gray-700">
            {getTranslation(currentLanguage, 'taLimitExplanation4')}
          </div>
        </div>

        {/* 优化建议 */}
        <div className="border-t border-gray-200 pt-3">
          <h5 className="text-xs font-medium text-gray-700 mb-2">
            {getTranslation(currentLanguage, 'taOptimizationTips')}
          </h5>
          <div className="space-y-1">
            <div className="flex items-start text-xs text-gray-600">
              <span className="text-blue-500 mr-1">1.</span>
              <span>{getTranslation(currentLanguage, 'taOptimizationTip1')}</span>
            </div>
            <div className="flex items-start text-xs text-gray-600">
              <span className="text-blue-500 mr-1">2.</span>
              <span>{getTranslation(currentLanguage, 'taOptimizationTip2')}</span>
            </div>
            <div className="flex items-start text-xs text-gray-600">
              <span className="text-blue-500 mr-1">3.</span>
              <span>{getTranslation(currentLanguage, 'taOptimizationTip3')}</span>
            </div>
          </div>
        </div>

        {/* 感谢理解 */}
        <div className="mt-4 pt-3 border-t border-gray-200">
          <div className="flex items-center text-xs text-gray-600">
            <Heart className="h-3 w-3 mr-1 text-red-500" />
            <span>{getTranslation(currentLanguage, 'taUnderstanding')}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UsageLimitInfo;
