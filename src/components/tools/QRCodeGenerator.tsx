import React, { useState, useRef } from 'react';
import QRCode from 'react-qr-code';
import { 
  QrCode, 
  Download, 
  Co<PERSON>, 
  Printer, 
  Link, 
  Palette, 
  Settings,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
import { getTranslation } from '../../locales';

const QRCodeGenerator: React.FC = () => {
  const { currentLanguage } = useLanguage();
  const [url, setUrl] = useState('');
  const [qrValue, setQrValue] = useState('');
  const [size, setSize] = useState(256);
  const [fgColor, setFgColor] = useState('#000000');
  const [bgColor, setBgColor] = useState('#FFFFFF');
  const [errorLevel, setErrorLevel] = useState<'L' | 'M' | 'Q' | 'H'>('M');
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null);
  const qrRef = useRef<HTMLDivElement>(null);

  // URL validation
  const isValidUrl = (string: string) => {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  };

  // Generate QR Code
  const generateQR = () => {
    if (!url.trim()) {
      setMessage({ type: 'error', text: getTranslation(currentLanguage, 'urlRequired') });
      return;
    }

    if (!isValidUrl(url)) {
      setMessage({ type: 'error', text: getTranslation(currentLanguage, 'invalidUrl') });
      return;
    }

    try {
      setQrValue(url);
      setMessage({ type: 'success', text: getTranslation(currentLanguage, 'qrGenerated') });
    } catch (error) {
      setMessage({ type: 'error', text: getTranslation(currentLanguage, 'generateError') });
    }
  };

  // Download QR Code as PNG
  const downloadPNG = () => {
    if (!qrValue) return;

    try {
      const svg = qrRef.current?.querySelector('svg');
      if (!svg) return;

      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      canvas.width = size;
      canvas.height = size;

      const svgData = new XMLSerializer().serializeToString(svg);
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const url = URL.createObjectURL(svgBlob);

      img.onload = () => {
        ctx?.drawImage(img, 0, 0);
        canvas.toBlob((blob) => {
          if (blob) {
            const link = document.createElement('a');
            link.download = 'qrcode.png';
            link.href = URL.createObjectURL(blob);
            link.click();
            setMessage({ type: 'success', text: getTranslation(currentLanguage, 'downloadStarted') });
          }
        });
        URL.revokeObjectURL(url);
      };

      img.src = url;
    } catch (error) {
      setMessage({ type: 'error', text: getTranslation(currentLanguage, 'downloadError') });
    }
  };

  // Download QR Code as SVG
  const downloadSVG = () => {
    if (!qrValue) return;

    try {
      const svg = qrRef.current?.querySelector('svg');
      if (!svg) return;

      const svgData = new XMLSerializer().serializeToString(svg);
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const link = document.createElement('a');
      link.download = 'qrcode.svg';
      link.href = URL.createObjectURL(svgBlob);
      link.click();
      setMessage({ type: 'success', text: getTranslation(currentLanguage, 'downloadStarted') });
    } catch (error) {
      setMessage({ type: 'error', text: getTranslation(currentLanguage, 'downloadError') });
    }
  };

  // Copy QR Code to clipboard
  const copyImage = async () => {
    if (!qrValue) return;

    try {
      const svg = qrRef.current?.querySelector('svg');
      if (!svg) return;

      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      canvas.width = size;
      canvas.height = size;

      const svgData = new XMLSerializer().serializeToString(svg);
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const url = URL.createObjectURL(svgBlob);

      img.onload = async () => {
        ctx?.drawImage(img, 0, 0);
        canvas.toBlob(async (blob) => {
          if (blob) {
            try {
              await navigator.clipboard.write([
                new ClipboardItem({ 'image/png': blob })
              ]);
              setMessage({ type: 'success', text: getTranslation(currentLanguage, 'imageCopied') });
            } catch (err) {
              setMessage({ type: 'error', text: getTranslation(currentLanguage, 'copyError') });
            }
          }
        });
        URL.revokeObjectURL(url);
      };

      img.src = url;
    } catch (error) {
      setMessage({ type: 'error', text: getTranslation(currentLanguage, 'copyError') });
    }
  };

  // Print QR Code
  const printQR = () => {
    if (!qrValue) return;

    const printWindow = window.open('', '_blank');
    if (printWindow) {
      const svg = qrRef.current?.querySelector('svg');
      if (svg) {
        const svgData = new XMLSerializer().serializeToString(svg);
        printWindow.document.write(`
          <html>
            <head><title>QR Code</title></head>
            <body style="display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0;">
              ${svgData}
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
      }
    }
  };

  // Clear message after 3 seconds
  React.useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 3000);
      return () => clearTimeout(timer);
    }
  }, [message]);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-gradient-to-br from-blue-500 to-purple-600 p-3 rounded-2xl">
              <QrCode className="h-8 w-8 text-white" />
            </div>
          </div>
          <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            {getTranslation(currentLanguage, 'qrCodeGenerator')}
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            {getTranslation(currentLanguage, 'qrCodeGeneratorDesc')}
          </p>
        </div>

        {/* Message */}
        {message && (
          <div className={`mb-6 p-4 rounded-xl flex items-center ${
            message.type === 'success' ? 'bg-green-50 text-green-800 border border-green-200' :
            message.type === 'error' ? 'bg-red-50 text-red-800 border border-red-200' :
            'bg-blue-50 text-blue-800 border border-blue-200'
          }`}>
            {message.type === 'success' && <CheckCircle className="h-5 w-5 mr-2" />}
            {message.type === 'error' && <AlertCircle className="h-5 w-5 mr-2" />}
            {message.type === 'info' && <Info className="h-5 w-5 mr-2" />}
            {message.text}
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Input and Settings */}
          <div className="space-y-6">
            {/* URL Input */}
            <div className="card p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <Link className="h-5 w-5 mr-2 text-blue-600" />
                {getTranslation(currentLanguage, 'enterUrl')}
              </h3>
              <div className="space-y-4">
                <div>
                  <input
                    type="url"
                    value={url}
                    onChange={(e) => setUrl(e.target.value)}
                    placeholder={getTranslation(currentLanguage, 'urlPlaceholder')}
                    className="input w-full"
                  />
                </div>
                <button
                  onClick={generateQR}
                  className="btn-primary w-full"
                >
                  {qrValue ? getTranslation(currentLanguage, 'regenerateQR') : getTranslation(currentLanguage, 'generateQR')}
                </button>
              </div>
            </div>

            {/* Customization Options */}
            <div className="card p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <Settings className="h-5 w-5 mr-2 text-purple-600" />
                {getTranslation(currentLanguage, 'customizeOptions')}
              </h3>
              <div className="space-y-4">
                {/* Size */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {getTranslation(currentLanguage, 'qrSize')}
                  </label>
                  <select
                    value={size}
                    onChange={(e) => setSize(Number(e.target.value))}
                    className="select w-full"
                  >
                    <option value={128}>{getTranslation(currentLanguage, 'sizeSmall')}</option>
                    <option value={256}>{getTranslation(currentLanguage, 'sizeMedium')}</option>
                    <option value={512}>{getTranslation(currentLanguage, 'sizeLarge')}</option>
                    <option value={1024}>{getTranslation(currentLanguage, 'sizeXLarge')}</option>
                  </select>
                </div>

                {/* Colors */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {getTranslation(currentLanguage, 'qrColor')}
                    </label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="color"
                        value={fgColor}
                        onChange={(e) => setFgColor(e.target.value)}
                        className="w-12 h-10 rounded border border-gray-300"
                      />
                      <input
                        type="text"
                        value={fgColor}
                        onChange={(e) => setFgColor(e.target.value)}
                        className="input flex-1"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {getTranslation(currentLanguage, 'backgroundColor')}
                    </label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="color"
                        value={bgColor}
                        onChange={(e) => setBgColor(e.target.value)}
                        className="w-12 h-10 rounded border border-gray-300"
                      />
                      <input
                        type="text"
                        value={bgColor}
                        onChange={(e) => setBgColor(e.target.value)}
                        className="input flex-1"
                      />
                    </div>
                  </div>
                </div>

                {/* Error Correction Level */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {getTranslation(currentLanguage, 'errorCorrectionLevel')}
                  </label>
                  <select
                    value={errorLevel}
                    onChange={(e) => setErrorLevel(e.target.value as 'L' | 'M' | 'Q' | 'H')}
                    className="select w-full"
                  >
                    <option value="L">{getTranslation(currentLanguage, 'errorLevelL')}</option>
                    <option value="M">{getTranslation(currentLanguage, 'errorLevelM')}</option>
                    <option value="Q">{getTranslation(currentLanguage, 'errorLevelQ')}</option>
                    <option value="H">{getTranslation(currentLanguage, 'errorLevelH')}</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          {/* QR Code Preview and Actions */}
          <div className="space-y-6">
            {/* Preview */}
            <div className="card p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <Palette className="h-5 w-5 mr-2 text-green-600" />
                {getTranslation(currentLanguage, 'qrPreview')}
              </h3>
              <div className="flex justify-center">
                {qrValue ? (
                  <div ref={qrRef} className="p-4 bg-white rounded-xl border-2 border-gray-200">
                    <QRCode
                      value={qrValue}
                      size={Math.min(size, 300)}
                      fgColor={fgColor}
                      bgColor={bgColor}
                      level={errorLevel}
                    />
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <QrCode className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                    <h4 className="text-lg font-medium text-gray-500 mb-2">
                      {getTranslation(currentLanguage, 'noQRGenerated')}
                    </h4>
                    <p className="text-gray-400">
                      {getTranslation(currentLanguage, 'generateFirst')}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            {qrValue && (
              <div className="card p-6">
                <div className="grid grid-cols-2 gap-4">
                  <button
                    onClick={downloadPNG}
                    className="btn-secondary flex items-center justify-center"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    {getTranslation(currentLanguage, 'downloadPNG')}
                  </button>
                  <button
                    onClick={downloadSVG}
                    className="btn-secondary flex items-center justify-center"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    {getTranslation(currentLanguage, 'downloadSVG')}
                  </button>
                  <button
                    onClick={copyImage}
                    className="btn-outline flex items-center justify-center"
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    {getTranslation(currentLanguage, 'copyImage')}
                  </button>
                  <button
                    onClick={printQR}
                    className="btn-outline flex items-center justify-center"
                  >
                    <Printer className="h-4 w-4 mr-2" />
                    {getTranslation(currentLanguage, 'printQR')}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Tips and Use Cases */}
        <div className="mt-12 grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Usage Tips */}
          <div className="card p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
              <Info className="h-5 w-5 mr-2 text-blue-600" />
              {getTranslation(currentLanguage, 'usageTips')}
            </h3>
            <div className="space-y-3 text-sm text-gray-700">
              <p>• {getTranslation(currentLanguage, 'tip1')}</p>
              <p>• {getTranslation(currentLanguage, 'tip2')}</p>
              <p>• {getTranslation(currentLanguage, 'tip3')}</p>
              <p>• {getTranslation(currentLanguage, 'tip4')}</p>
              <p>• {getTranslation(currentLanguage, 'tip5')}</p>
            </div>
          </div>

          {/* Use Cases */}
          <div className="card p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
              <QrCode className="h-5 w-5 mr-2 text-purple-600" />
              {getTranslation(currentLanguage, 'useCases')}
            </h3>
            <div className="space-y-3 text-sm text-gray-700">
              <p>• {getTranslation(currentLanguage, 'useCase1')}</p>
              <p>• {getTranslation(currentLanguage, 'useCase2')}</p>
              <p>• {getTranslation(currentLanguage, 'useCase3')}</p>
              <p>• {getTranslation(currentLanguage, 'useCase4')}</p>
              <p>• {getTranslation(currentLanguage, 'useCase5')}</p>
            </div>
          </div>
        </div>

        {/* Quality and Format Information */}
        <div className="mt-8 grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="card p-6 bg-blue-50 border-blue-200">
            <h4 className="font-semibold text-blue-900 mb-2">
              {getTranslation(currentLanguage, 'qualityInfo')}
            </h4>
            <p className="text-sm text-blue-800">
              {getTranslation(currentLanguage, 'qualityDesc')}
            </p>
          </div>

          <div className="card p-6 bg-green-50 border-green-200">
            <h4 className="font-semibold text-green-900 mb-2">
              {getTranslation(currentLanguage, 'formatInfo')}
            </h4>
            <div className="text-sm text-green-800 space-y-1">
              <p>{getTranslation(currentLanguage, 'pngFormat')}</p>
              <p>{getTranslation(currentLanguage, 'svgFormat')}</p>
            </div>
          </div>

          <div className="card p-6 bg-orange-50 border-orange-200">
            <h4 className="font-semibold text-orange-900 mb-2">
              {getTranslation(currentLanguage, 'securityTip')}
            </h4>
            <p className="text-sm text-orange-800">
              {getTranslation(currentLanguage, 'securityDesc')}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QRCodeGenerator;
