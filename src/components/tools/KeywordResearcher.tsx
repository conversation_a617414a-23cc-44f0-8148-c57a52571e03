import React, { useState } from 'react';
import { Search, TrendingUp, Target, BarChart3, Star, Eye } from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
import { getTranslation } from '../../utils/translations';

interface KeywordData {
  keyword: string;
  searchVolume: number;
  competition: 'low' | 'medium' | 'high';
  cpc: number;
  difficulty: number;
  trend: 'up' | 'down' | 'stable';
  relevance: number;
}

const KeywordResearcher = () => {
  const { currentLanguage } = useLanguage();
  const [seedKeyword, setSeedKeyword] = useState('');
  const [marketplace, setMarketplace] = useState('US');
  const [category, setCategory] = useState('all');
  const [isLoading, setIsLoading] = useState(false);
  const [keywords, setKeywords] = useState<KeywordData[]>([]);

  const marketplaces = [
    { code: 'US', name: 'Amazon.com (US)' },
    { code: 'UK', name: 'Amazon.co.uk (UK)' },
    { code: 'DE', name: 'Amazon.de (Germany)' },
    { code: 'FR', name: 'Amazon.fr (France)' },
    { code: 'CA', name: 'Amazon.ca (Canada)' }
  ];

  const categories = [
    { id: 'all', name: getTranslation(currentLanguage, 'allCategories') },
    { id: 'electronics', name: getTranslation(currentLanguage, 'electronicsCategory') },
    { id: 'clothing', name: getTranslation(currentLanguage, 'clothingCategory') },
    { id: 'home', name: getTranslation(currentLanguage, 'homeCategory') },
    { id: 'health', name: getTranslation(currentLanguage, 'healthCategory') },
    { id: 'books', name: getTranslation(currentLanguage, 'booksCategory') }
  ];

  // Mock data generator for demonstration
  const generateMockKeywords = (seed: string): KeywordData[] => {
    const variations = [
      `${seed}`,
      `${seed} best`,
      `${seed} cheap`,
      `${seed} reviews`,
      `${seed} for sale`,
      `${seed} amazon`,
      `${seed} buy`,
      `${seed} price`,
      `${seed} deals`,
      `${seed} discount`,
      `best ${seed}`,
      `cheap ${seed}`,
      `${seed} online`,
      `${seed} store`,
      `${seed} shop`
    ];

    return variations.map((keyword, index) => ({
      keyword,
      searchVolume: Math.floor(Math.random() * 50000) + 1000,
      competition: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as 'low' | 'medium' | 'high',
      cpc: Math.random() * 3 + 0.5,
      difficulty: Math.floor(Math.random() * 100) + 1,
      trend: ['up', 'down', 'stable'][Math.floor(Math.random() * 3)] as 'up' | 'down' | 'stable',
      relevance: Math.floor(Math.random() * 40) + 60
    })).sort((a, b) => b.searchVolume - a.searchVolume);
  };

  const handleSearch = async () => {
    if (!seedKeyword.trim()) return;
    
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      const mockKeywords = generateMockKeywords(seedKeyword);
      setKeywords(mockKeywords);
      setIsLoading(false);
    }, 2000);
  };

  const getCompetitionColor = (competition: string) => {
    switch (competition) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getDifficultyColor = (difficulty: number) => {
    if (difficulty <= 30) return 'text-green-600';
    if (difficulty <= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'down': return <TrendingUp className="h-4 w-4 text-red-600 rotate-180" />;
      default: return <BarChart3 className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <div className="max-w-7xl mx-auto">
      <div className="bg-white rounded-2xl shadow-lg p-8">
        <div className="flex items-center mb-8">
          <div className="bg-indigo-500 text-white p-3 rounded-xl mr-4">
            <Search className="h-8 w-8" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {getTranslation(currentLanguage, 'keywordResearcher')}
            </h1>
            <p className="text-gray-600 mt-2">
              {getTranslation(currentLanguage, 'keywordResearcherDesc')}
            </p>
          </div>
        </div>

        {/* Search Form */}
        <div className="bg-gray-50 rounded-xl p-6 mb-8">
          <div className="grid md:grid-cols-3 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {getTranslation(currentLanguage, 'marketplace')}
              </label>
              <select
                value={marketplace}
                onChange={(e) => setMarketplace(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              >
                {marketplaces.map((mp) => (
                  <option key={mp.code} value={mp.code}>
                    {mp.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {getTranslation(currentLanguage, 'category')}
              </label>
              <select
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              >
                {categories.map((cat) => (
                  <option key={cat.id} value={cat.id}>
                    {cat.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {getTranslation(currentLanguage, 'seedKeyword')}
              </label>
              <input
                type="text"
                value={seedKeyword}
                onChange={(e) => setSeedKeyword(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                placeholder={getTranslation(currentLanguage, 'seedKeywordPlaceholder')}
              />
            </div>
          </div>

          <button
            onClick={handleSearch}
            disabled={!seedKeyword.trim() || isLoading}
            className="bg-indigo-600 hover:bg-indigo-700 disabled:bg-indigo-400 text-white px-8 py-3 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 disabled:transform-none flex items-center"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                {getTranslation(currentLanguage, 'researching')}
              </>
            ) : (
              <>
                <Search className="h-5 w-5 mr-2" />
                {getTranslation(currentLanguage, 'findKeywords')}
              </>
            )}
          </button>
        </div>

        {/* Results */}
        {keywords.length > 0 && (
          <div>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-900">
                {getTranslation(currentLanguage, 'keywordResults')} ({keywords.length})
              </h3>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                  {getTranslation(currentLanguage, 'lowCompetition')}
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                  {getTranslation(currentLanguage, 'mediumCompetition')}
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                  {getTranslation(currentLanguage, 'highCompetition')}
                </div>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-4 px-4 font-semibold text-gray-900">
                      {getTranslation(currentLanguage, 'keyword')}
                    </th>
                    <th className="text-left py-4 px-4 font-semibold text-gray-900">
                      {getTranslation(currentLanguage, 'searchVolume')}
                    </th>
                    <th className="text-left py-4 px-4 font-semibold text-gray-900">
                      {getTranslation(currentLanguage, 'competition')}
                    </th>
                    <th className="text-left py-4 px-4 font-semibold text-gray-900">
                      {getTranslation(currentLanguage, 'difficulty')}
                    </th>
                    <th className="text-left py-4 px-4 font-semibold text-gray-900">
                      {getTranslation(currentLanguage, 'trend')}
                    </th>
                    <th className="text-left py-4 px-4 font-semibold text-gray-900">
                      {getTranslation(currentLanguage, 'relevance')}
                    </th>
                    <th className="text-left py-4 px-4 font-semibold text-gray-900">
                      {getTranslation(currentLanguage, 'actions')}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {keywords.map((keyword, index) => (
                    <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-4 px-4">
                        <div className="font-medium text-gray-900">{keyword.keyword}</div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center">
                          <Eye className="h-4 w-4 text-gray-400 mr-2" />
                          {keyword.searchVolume.toLocaleString()}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${getCompetitionColor(keyword.competition)}`}>
                          {keyword.competition}
                        </span>
                      </td>
                      <td className="py-4 px-4">
                        <div className={`font-medium ${getDifficultyColor(keyword.difficulty)}`}>
                          {keyword.difficulty}/100
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        {getTrendIcon(keyword.trend)}
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center">
                          <Star className="h-4 w-4 text-yellow-400 mr-1" />
                          {keyword.relevance}%
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <button className="text-indigo-600 hover:text-indigo-700 font-medium text-sm">
                          {getTranslation(currentLanguage, 'analyze')}
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Summary Stats */}
            <div className="mt-8 grid md:grid-cols-4 gap-6">
              <div className="bg-blue-50 rounded-xl p-6">
                <div className="text-blue-600 text-sm font-medium mb-2">
                  {getTranslation(currentLanguage, 'totalKeywords')}
                </div>
                <div className="text-2xl font-bold text-blue-900">
                  {keywords.length}
                </div>
              </div>

              <div className="bg-green-50 rounded-xl p-6">
                <div className="text-green-600 text-sm font-medium mb-2">
                  {getTranslation(currentLanguage, 'lowCompetitionKeywords')}
                </div>
                <div className="text-2xl font-bold text-green-900">
                  {keywords.filter(k => k.competition === 'low').length}
                </div>
              </div>

              <div className="bg-purple-50 rounded-xl p-6">
                <div className="text-purple-600 text-sm font-medium mb-2">
                  {getTranslation(currentLanguage, 'avgSearchVolume')}
                </div>
                <div className="text-2xl font-bold text-purple-900">
                  {Math.round(keywords.reduce((sum, k) => sum + k.searchVolume, 0) / keywords.length).toLocaleString()}
                </div>
              </div>

              <div className="bg-orange-50 rounded-xl p-6">
                <div className="text-orange-600 text-sm font-medium mb-2">
                  {getTranslation(currentLanguage, 'highRelevanceKeywords')}
                </div>
                <div className="text-2xl font-bold text-orange-900">
                  {keywords.filter(k => k.relevance >= 80).length}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Tips */}
        {keywords.length === 0 && !isLoading && (
          <div className="bg-indigo-50 rounded-xl p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">
              {getTranslation(currentLanguage, 'keywordResearchTips')}
            </h4>
            <div className="space-y-2 text-sm text-gray-700">
              <p>• {getTranslation(currentLanguage, 'keywordTip1')}</p>
              <p>• {getTranslation(currentLanguage, 'keywordTip2')}</p>
              <p>• {getTranslation(currentLanguage, 'keywordTip3')}</p>
              <p>• {getTranslation(currentLanguage, 'keywordTip4')}</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default KeywordResearcher;