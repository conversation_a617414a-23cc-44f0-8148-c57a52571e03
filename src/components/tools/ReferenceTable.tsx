import React, { useState, useMemo, useEffect, useRef } from 'react';
import { 
  Search, 
  Download, 
  Copy, 
  Check, 
  BookOpen, 
  Globe, 
  Ruler, 
  Truck, 
  DollarSign, 
  FileText,
  Filter,
  X
} from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
import { getTranslation } from '../../utils/translations';
import { recordToolCalculation } from '../../utils/analytics';
import {
  allMultilingualReferenceTables,
  referenceCategories,
  MultilingualReferenceTable,
  MultilingualReferenceTableItem
} from '../../data/multilingualReferenceData';
import { Language } from '../../locales';

const ReferenceTableTool = () => {
  const { currentLanguage } = useLanguage();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedTable, setSelectedTable] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [copiedCell, setCopiedCell] = useState<string | null>(null);
  const [isExporting, setIsExporting] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // 键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + K 聚焦搜索框
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        searchInputRef.current?.focus();
      }
      // Escape 清空搜索
      if (event.key === 'Escape' && searchQuery) {
        setSearchQuery('');
        searchInputRef.current?.blur();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [searchQuery]);

  // 获取分类图标
  const getCategoryIcon = (categoryId: string) => {
    switch (categoryId) {
      case 'geography': return <Globe className="h-4 w-4" />;
      case 'sizing': return <Ruler className="h-4 w-4" />;
      case 'logistics': return <Truck className="h-4 w-4" />;
      case 'financial': return <DollarSign className="h-4 w-4" />;
      case 'technical': return <FileText className="h-4 w-4" />;
      default: return <BookOpen className="h-4 w-4" />;
    }
  };

  // 筛选对照表
  const filteredTables = useMemo(() => {
    return allMultilingualReferenceTables.filter(table =>
      selectedCategory === 'all' || table.category === selectedCategory
    );
  }, [selectedCategory]);

  // 获取多语言值
  const getMultilingualValue = (value: any, currentLanguage: Language): string => {
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      return value[currentLanguage] || value.en || String(value);
    }
    return String(value || '');
  };

  // 筛选表格数据
  const getFilteredData = (table: MultilingualReferenceTable): MultilingualReferenceTableItem[] => {
    if (!searchQuery.trim()) return table.data;

    const query = searchQuery.toLowerCase();
    return table.data.filter(item => {
      return table.columns.some(column => {
        if (!column.searchable) return false;
        const value = getMultilingualValue(item[column.key], currentLanguage).toLowerCase();
        return value.includes(query);
      });
    });
  };

  // 复制单元格内容
  const handleCopyCell = async (value: string, cellId: string) => {
    try {
      await navigator.clipboard.writeText(value);
      setCopiedCell(cellId);
      setTimeout(() => setCopiedCell(null), 2000);
      
      recordToolCalculation('reference-table', {
        action: 'copy_cell',
        value: value.substring(0, 50) // 只记录前50个字符
      });
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  // 导出表格数据为CSV
  const handleExportTable = async (table: MultilingualReferenceTable) => {
    setIsExporting(true);
    try {
      const filteredData = getFilteredData(table);
      const headers = table.columns.map(col => getTranslation(currentLanguage, col.labelKey));
      const csvContent = [
        headers.join(','),
        ...filteredData.map(item =>
          table.columns.map(col => `"${getMultilingualValue(item[col.key], currentLanguage)}"`).join(',')
        )
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `${table.id}-${Date.now()}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      recordToolCalculation('reference-table', {
        action: 'export_table',
        table: table.id,
        rows: filteredData.length
      });
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  // 如果选择了特定表格，显示表格详情
  if (selectedTable) {
    const table = allMultilingualReferenceTables.find(t => t.id === selectedTable);
    if (!table) return null;

    const filteredData = getFilteredData(table);

    return (
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-7xl mx-auto">
          {/* 返回按钮 */}
          <div className="mb-6">
            <button
              onClick={() => setSelectedTable(null)}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              <X className="h-4 w-4" />
              {getTranslation(currentLanguage, 'backToTables')}
            </button>
          </div>

          {/* 表格标题和操作 */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div className="p-6 border-b border-gray-200">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 mb-2">
                    {getTranslation(currentLanguage, table.nameKey)}
                  </h1>
                  <p className="text-gray-600">
                    {getTranslation(currentLanguage, table.descriptionKey)}
                  </p>
                </div>
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => handleExportTable(table)}
                    disabled={isExporting}
                    className="flex items-center gap-2 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Download className={`h-4 w-4 ${isExporting ? 'animate-spin' : ''}`} />
                    {isExporting ? getTranslation(currentLanguage, 'exporting') : getTranslation(currentLanguage, 'exportCSV')}
                  </button>
                </div>
              </div>

              {/* 搜索栏 */}
              <div className="mt-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    ref={searchInputRef}
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder={`${getTranslation(currentLanguage, 'searchInTable')} (Ctrl+K)`}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            {/* 表格内容 */}
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    {table.columns.map((column) => (
                      <th
                        key={column.key}
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style={{ width: column.width }}
                      >
                        {getTranslation(currentLanguage, column.labelKey)}
                      </th>
                    ))}
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                      {getTranslation(currentLanguage, 'actions')}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredData.length > 0 ? (
                    filteredData.map((item, index) => (
                      <tr key={item.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                        {table.columns.map((column) => {
                          const cellId = `${item.id}-${column.key}`;
                          const value = getMultilingualValue(item[column.key], currentLanguage);
                          return (
                            <td
                              key={column.key}
                              className={`px-6 py-4 whitespace-nowrap text-sm text-gray-900 cursor-pointer hover:bg-blue-50 transition-colors ${
                                column.isMultilingual ? 'font-medium text-blue-900' : ''
                              }`}
                              onClick={() => handleCopyCell(value, cellId)}
                              title={getTranslation(currentLanguage, 'clickToCopy')}
                            >
                              <div className="flex items-center justify-between">
                                <span>{value}</span>
                                {copiedCell === cellId && (
                                  <Check className="h-3 w-3 text-green-500 ml-2" />
                                )}
                              </div>
                            </td>
                          );
                        })}
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => handleCopyCell(
                              table.columns.map(col => getMultilingualValue(item[col.key], currentLanguage)).join(' | '),
                              `${item.id}-row`
                            )}
                            className="text-orange-600 hover:text-orange-900 transition-colors"
                            title={getTranslation(currentLanguage, 'copyRow')}
                          >
                            <Copy className="h-4 w-4" />
                          </button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={table.columns.length + 1} className="px-6 py-12 text-center">
                        <div className="flex flex-col items-center justify-center text-gray-500">
                          <Search className="h-8 w-8 mb-2" />
                          <p className="text-sm">
                            {searchQuery
                              ? getTranslation(currentLanguage, 'noSearchResults')
                              : getTranslation(currentLanguage, 'noDataAvailable')
                            }
                          </p>
                          {searchQuery && (
                            <button
                              onClick={() => setSearchQuery('')}
                              className="mt-2 text-orange-600 hover:text-orange-800 text-sm"
                            >
                              {getTranslation(currentLanguage, 'clearSearch')}
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {/* 搜索结果统计 */}
            {searchQuery && (
              <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
                <p className="text-sm text-gray-600">
                  {getTranslation(currentLanguage, 'searchResults')}: {filteredData.length} / {table.data.length}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  // 主界面 - 显示所有对照表
  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            {getTranslation(currentLanguage, 'referenceTable')}
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            {getTranslation(currentLanguage, 'referenceTableDesc')}
          </p>
        </div>

        {/* 分类筛选 */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-2 justify-center">
            {referenceCategories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                  selectedCategory === category.id
                    ? 'bg-orange-500 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-200'
                }`}
              >
                {getCategoryIcon(category.id)}
                {getTranslation(currentLanguage, category.nameKey)}
              </button>
            ))}
          </div>
        </div>

        {/* 对照表网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTables.map((table) => (
            <div
              key={table.id}
              onClick={() => setSelectedTable(table.id)}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 cursor-pointer hover:shadow-md hover:border-orange-300 transition-all"
            >
              <div className="flex items-start gap-4">
                <div className="p-3 bg-orange-100 rounded-lg">
                  {getCategoryIcon(table.category)}
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {getTranslation(currentLanguage, table.nameKey)}
                  </h3>
                  <p className="text-gray-600 text-sm mb-3">
                    {getTranslation(currentLanguage, table.descriptionKey)}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">
                      {table.data.length} {getTranslation(currentLanguage, 'entries')}
                    </span>
                    <span className="text-orange-600 text-sm font-medium">
                      {getTranslation(currentLanguage, 'viewTable')} →
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ReferenceTableTool;
