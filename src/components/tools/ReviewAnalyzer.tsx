import React, { useState } from 'react';
import { MessageSquare, TrendingUp, Star, AlertTriangle, ThumbsUp, ThumbsDown } from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
import { getTranslation } from '../../utils/translations';

interface ReviewInsight {
  category: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  frequency: number;
  examples: string[];
}

interface ReviewAnalysis {
  totalReviews: number;
  averageRating: number;
  sentimentBreakdown: {
    positive: number;
    negative: number;
    neutral: number;
  };
  commonThemes: ReviewInsight[];
  keywordMentions: { keyword: string; count: number; sentiment: 'positive' | 'negative' }[];
  competitorComparison: {
    betterThan: string[];
    worseThan: string[];
  };
}

const ReviewAnalyzer = () => {
  const { currentLanguage } = useLanguage();
  const [asinInput, setAsinInput] = useState('');
  const [marketplace, setMarketplace] = useState('US');
  const [isLoading, setIsLoading] = useState(false);
  const [analysis, setAnalysis] = useState<ReviewAnalysis | null>(null);

  const marketplaces = [
    { code: 'US', name: 'Amazon.com (US)' },
    { code: 'UK', name: 'Amazon.co.uk (UK)' },
    { code: 'DE', name: 'Amazon.de (Germany)' },
    { code: 'CA', name: 'Amazon.ca (Canada)' }
  ];

  // Mock data generator
  const generateMockAnalysis = (): ReviewAnalysis => {
    return {
      totalReviews: Math.floor(Math.random() * 5000) + 500,
      averageRating: Math.random() * 2 + 3,
      sentimentBreakdown: {
        positive: Math.floor(Math.random() * 40) + 50,
        negative: Math.floor(Math.random() * 20) + 10,
        neutral: Math.floor(Math.random() * 20) + 10
      },
      commonThemes: [
        {
          category: 'Sound Quality',
          sentiment: 'positive',
          frequency: 85,
          examples: ['Amazing sound quality', 'Crystal clear audio', 'Rich bass']
        },
        {
          category: 'Battery Life',
          sentiment: 'positive',
          frequency: 72,
          examples: ['Long lasting battery', 'All day use', 'Quick charging']
        },
        {
          category: 'Comfort',
          sentiment: 'negative',
          frequency: 45,
          examples: ['Uncomfortable after long use', 'Too tight', 'Hurts ears']
        },
        {
          category: 'Build Quality',
          sentiment: 'positive',
          frequency: 68,
          examples: ['Well built', 'Sturdy construction', 'Premium materials']
        },
        {
          category: 'Connectivity',
          sentiment: 'negative',
          frequency: 32,
          examples: ['Connection drops', 'Pairing issues', 'Bluetooth problems']
        }
      ],
      keywordMentions: [
        { keyword: 'noise cancelling', count: 234, sentiment: 'positive' },
        { keyword: 'comfortable', count: 189, sentiment: 'positive' },
        { keyword: 'battery', count: 156, sentiment: 'positive' },
        { keyword: 'quality', count: 145, sentiment: 'positive' },
        { keyword: 'expensive', count: 89, sentiment: 'negative' },
        { keyword: 'uncomfortable', count: 67, sentiment: 'negative' },
        { keyword: 'breaks', count: 45, sentiment: 'negative' }
      ],
      competitorComparison: {
        betterThan: ['Sound quality vs competitors', 'Battery life advantage', 'Better noise cancellation'],
        worseThan: ['Higher price point', 'Less comfortable fit', 'Fewer color options']
      }
    };
  };

  const handleAnalyze = async () => {
    if (!asinInput.trim()) return;
    
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      const mockAnalysis = generateMockAnalysis();
      setAnalysis(mockAnalysis);
      setIsLoading(false);
    }, 3000);
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return 'text-green-600 bg-green-100';
      case 'negative': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return <ThumbsUp className="h-4 w-4" />;
      case 'negative': return <ThumbsDown className="h-4 w-4" />;
      default: return <MessageSquare className="h-4 w-4" />;
    }
  };

  return (
    <div className="max-w-7xl mx-auto">
      <div className="bg-white rounded-2xl shadow-lg p-8">
        <div className="flex items-center mb-8">
          <div className="bg-pink-500 text-white p-3 rounded-xl mr-4">
            <MessageSquare className="h-8 w-8" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {getTranslation(currentLanguage, 'reviewAnalyzer')}
            </h1>
            <p className="text-gray-600 mt-2">
              {getTranslation(currentLanguage, 'reviewAnalyzerDesc')}
            </p>
          </div>
        </div>

        {/* Search Form */}
        <div className="bg-gray-50 rounded-xl p-6 mb-8">
          <div className="grid md:grid-cols-3 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {getTranslation(currentLanguage, 'marketplace')}
              </label>
              <select
                value={marketplace}
                onChange={(e) => setMarketplace(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
              >
                {marketplaces.map((mp) => (
                  <option key={mp.code} value={mp.code}>
                    {mp.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {getTranslation(currentLanguage, 'productAsin')}
              </label>
              <input
                type="text"
                value={asinInput}
                onChange={(e) => setAsinInput(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleAnalyze()}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                placeholder="B08N5WRWNW"
              />
            </div>
          </div>

          <button
            onClick={handleAnalyze}
            disabled={!asinInput.trim() || isLoading}
            className="bg-pink-600 hover:bg-pink-700 disabled:bg-pink-400 text-white px-8 py-3 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 disabled:transform-none flex items-center"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                {getTranslation(currentLanguage, 'analyzingReviews')}
              </>
            ) : (
              <>
                <MessageSquare className="h-5 w-5 mr-2" />
                {getTranslation(currentLanguage, 'analyzeReviews')}
              </>
            )}
          </button>
        </div>

        {/* Results */}
        {analysis && (
          <div className="space-y-8">
            {/* Overview Stats */}
            <div className="grid md:grid-cols-4 gap-6">
              <div className="bg-blue-50 rounded-xl p-6">
                <div className="text-blue-600 text-sm font-medium mb-2">
                  {getTranslation(currentLanguage, 'totalReviews')}
                </div>
                <div className="text-2xl font-bold text-blue-900">
                  {analysis.totalReviews.toLocaleString()}
                </div>
              </div>

              <div className="bg-green-50 rounded-xl p-6">
                <div className="text-green-600 text-sm font-medium mb-2">
                  {getTranslation(currentLanguage, 'averageRating')}
                </div>
                <div className="text-2xl font-bold text-green-900 flex items-center">
                  {analysis.averageRating.toFixed(1)}
                  <Star className="h-5 w-5 text-yellow-400 ml-1" />
                </div>
              </div>

              <div className="bg-purple-50 rounded-xl p-6">
                <div className="text-purple-600 text-sm font-medium mb-2">
                  {getTranslation(currentLanguage, 'positiveSentiment')}
                </div>
                <div className="text-2xl font-bold text-purple-900">
                  {analysis.sentimentBreakdown.positive}%
                </div>
              </div>

              <div className="bg-orange-50 rounded-xl p-6">
                <div className="text-orange-600 text-sm font-medium mb-2">
                  {getTranslation(currentLanguage, 'negativeSentiment')}
                </div>
                <div className="text-2xl font-bold text-orange-900">
                  {analysis.sentimentBreakdown.negative}%
                </div>
              </div>
            </div>

            {/* Sentiment Breakdown */}
            <div className="bg-gray-50 rounded-xl p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                {getTranslation(currentLanguage, 'sentimentBreakdown')}
              </h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="w-24 text-sm font-medium text-gray-700">
                    {getTranslation(currentLanguage, 'positive')}
                  </div>
                  <div className="flex-1 bg-gray-200 rounded-full h-4 mx-4">
                    <div 
                      className="bg-green-500 h-4 rounded-full" 
                      style={{ width: `${analysis.sentimentBreakdown.positive}%` }}
                    ></div>
                  </div>
                  <div className="w-12 text-sm font-medium text-gray-900">
                    {analysis.sentimentBreakdown.positive}%
                  </div>
                </div>

                <div className="flex items-center">
                  <div className="w-24 text-sm font-medium text-gray-700">
                    {getTranslation(currentLanguage, 'negative')}
                  </div>
                  <div className="flex-1 bg-gray-200 rounded-full h-4 mx-4">
                    <div 
                      className="bg-red-500 h-4 rounded-full" 
                      style={{ width: `${analysis.sentimentBreakdown.negative}%` }}
                    ></div>
                  </div>
                  <div className="w-12 text-sm font-medium text-gray-900">
                    {analysis.sentimentBreakdown.negative}%
                  </div>
                </div>

                <div className="flex items-center">
                  <div className="w-24 text-sm font-medium text-gray-700">
                    {getTranslation(currentLanguage, 'neutral')}
                  </div>
                  <div className="flex-1 bg-gray-200 rounded-full h-4 mx-4">
                    <div 
                      className="bg-gray-500 h-4 rounded-full" 
                      style={{ width: `${analysis.sentimentBreakdown.neutral}%` }}
                    ></div>
                  </div>
                  <div className="w-12 text-sm font-medium text-gray-900">
                    {analysis.sentimentBreakdown.neutral}%
                  </div>
                </div>
              </div>
            </div>

            {/* Common Themes */}
            <div className="bg-white border border-gray-200 rounded-xl p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">
                {getTranslation(currentLanguage, 'commonThemes')}
              </h3>
              <div className="grid md:grid-cols-2 gap-6">
                {analysis.commonThemes.map((theme, index) => (
                  <div key={index} className="border border-gray-100 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-semibold text-gray-900">{theme.category}</h4>
                      <div className="flex items-center">
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${getSentimentColor(theme.sentiment)} flex items-center`}>
                          {getSentimentIcon(theme.sentiment)}
                          <span className="ml-1 capitalize">{theme.sentiment}</span>
                        </span>
                      </div>
                    </div>
                    <div className="mb-3">
                      <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
                        <span>{getTranslation(currentLanguage, 'frequency')}</span>
                        <span>{theme.frequency}%</span>
                      </div>
                      <div className="bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${theme.sentiment === 'positive' ? 'bg-green-500' : theme.sentiment === 'negative' ? 'bg-red-500' : 'bg-gray-500'}`}
                          style={{ width: `${theme.frequency}%` }}
                        ></div>
                      </div>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-700 mb-2">
                        {getTranslation(currentLanguage, 'examples')}:
                      </div>
                      <div className="space-y-1">
                        {theme.examples.map((example, idx) => (
                          <div key={idx} className="text-sm text-gray-600 italic">
                            "{example}"
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Keyword Mentions */}
            <div className="bg-white border border-gray-200 rounded-xl p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">
                {getTranslation(currentLanguage, 'keywordMentions')}
              </h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-green-700 mb-4 flex items-center">
                    <ThumbsUp className="h-5 w-5 mr-2" />
                    {getTranslation(currentLanguage, 'positiveKeywords')}
                  </h4>
                  <div className="space-y-2">
                    {analysis.keywordMentions
                      .filter(k => k.sentiment === 'positive')
                      .map((keyword, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                          <span className="font-medium text-gray-900">{keyword.keyword}</span>
                          <span className="text-green-600 font-semibold">{keyword.count}</span>
                        </div>
                      ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-red-700 mb-4 flex items-center">
                    <ThumbsDown className="h-5 w-5 mr-2" />
                    {getTranslation(currentLanguage, 'negativeKeywords')}
                  </h4>
                  <div className="space-y-2">
                    {analysis.keywordMentions
                      .filter(k => k.sentiment === 'negative')
                      .map((keyword, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                          <span className="font-medium text-gray-900">{keyword.keyword}</span>
                          <span className="text-red-600 font-semibold">{keyword.count}</span>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Competitor Comparison */}
            <div className="bg-pink-50 rounded-xl p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
                <TrendingUp className="h-6 w-6 mr-2 text-pink-600" />
                {getTranslation(currentLanguage, 'competitorComparison')}
              </h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-green-700 mb-4">
                    {getTranslation(currentLanguage, 'strengthsVsCompetitors')}
                  </h4>
                  <div className="space-y-2">
                    {analysis.competitorComparison.betterThan.map((strength, index) => (
                      <div key={index} className="flex items-center p-3 bg-green-50 rounded-lg">
                        <ThumbsUp className="h-4 w-4 text-green-600 mr-3" />
                        <span className="text-gray-900">{strength}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-red-700 mb-4">
                    {getTranslation(currentLanguage, 'weaknessesVsCompetitors')}
                  </h4>
                  <div className="space-y-2">
                    {analysis.competitorComparison.worseThan.map((weakness, index) => (
                      <div key={index} className="flex items-center p-3 bg-red-50 rounded-lg">
                        <AlertTriangle className="h-4 w-4 text-red-600 mr-3" />
                        <span className="text-gray-900">{weakness}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Actionable Insights */}
            <div className="bg-blue-50 rounded-xl p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">
                {getTranslation(currentLanguage, 'actionableInsights')}
              </h4>
              <div className="space-y-2 text-sm text-gray-700">
                <p>• {getTranslation(currentLanguage, 'reviewInsight1')}</p>
                <p>• {getTranslation(currentLanguage, 'reviewInsight2')}</p>
                <p>• {getTranslation(currentLanguage, 'reviewInsight3')}</p>
                <p>• {getTranslation(currentLanguage, 'reviewInsight4')}</p>
              </div>
            </div>
          </div>
        )}

        {/* Tips */}
        {!analysis && !isLoading && (
          <div className="bg-pink-50 rounded-xl p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">
              {getTranslation(currentLanguage, 'reviewAnalysisTips')}
            </h4>
            <div className="space-y-2 text-sm text-gray-700">
              <p>• {getTranslation(currentLanguage, 'reviewTip1')}</p>
              <p>• {getTranslation(currentLanguage, 'reviewTip2')}</p>
              <p>• {getTranslation(currentLanguage, 'reviewTip3')}</p>
              <p>• {getTranslation(currentLanguage, 'reviewTip4')}</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReviewAnalyzer;