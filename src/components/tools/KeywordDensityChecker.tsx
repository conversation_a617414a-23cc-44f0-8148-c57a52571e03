import React, { useState, useEffect } from 'react';
import { Target, BarChart3, TrendingUp, AlertCircle } from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
import { getTranslation } from '../../utils/translations';
import ToolInfoSections from './KeywordDensityChecker/ToolInfoSections';

interface KeywordAnalysis {
  keyword: string;
  count: number;
  density: number;
  status: 'optimal' | 'low' | 'high';
  recommendation: string;
}

const KeywordDensityChecker = () => {
  const { currentLanguage } = useLanguage();
  const [keywords, setKeywords] = useState('');
  const [content, setContent] = useState('');
  const [analysis, setAnalysis] = useState<KeywordAnalysis[]>([]);
  const [contentStats, setContentStats] = useState({
    wordCount: 0,
    characterCount: 0,
    sentenceCount: 0
  });

  useEffect(() => {
    if (content.trim() && keywords.trim()) {
      analyzeKeywords();
    } else {
      setAnalysis([]);
    }
    updateContentStats();
  }, [keywords, content]);

  const updateContentStats = () => {
    const words = content.trim().split(/\s+/).filter(word => word.length > 0);
    const sentences = content.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0);
    
    setContentStats({
      wordCount: words.length,
      characterCount: content.length,
      sentenceCount: sentences.length
    });
  };

  const analyzeKeywords = () => {
    const keywordList = keywords.split(',').map(k => k.trim()).filter(k => k.length > 0);
    const words = content.toLowerCase().split(/\s+/).filter(word => word.length > 0);
    const totalWords = words.length;

    const results: KeywordAnalysis[] = keywordList.map(keyword => {
      const keywordLower = keyword.toLowerCase();
      const count = words.filter(word => 
        word.includes(keywordLower) || keywordLower.includes(word)
      ).length;
      
      const density = totalWords > 0 ? (count / totalWords) * 100 : 0;
      
      let status: 'optimal' | 'low' | 'high' = 'optimal';
      let recommendation = getTranslation(currentLanguage, 'keywordDensityOptimal');
      
      if (density < 1) {
        status = 'low';
        recommendation = getTranslation(currentLanguage, 'increaseKeywordUsage');
      } else if (density > 3) {
        status = 'high';
        recommendation = getTranslation(currentLanguage, 'reduceKeywordUsage');
      }

      return {
        keyword,
        count,
        density,
        status,
        recommendation
      };
    });

    setAnalysis(results);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'optimal': return 'text-green-600 bg-green-100 border-green-200';
      case 'low': return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'high': return 'text-red-600 bg-red-100 border-red-200';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'optimal': return getTranslation(currentLanguage, 'optimal');
      case 'low': return getTranslation(currentLanguage, 'tooLow');
      case 'high': return getTranslation(currentLanguage, 'tooHigh');
      default: return '';
    }
  };

  return (
    <div className="max-w-6xl mx-auto">
      <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
        <div className="flex items-center mb-8">
          <div className="bg-green-500 text-white p-3 rounded-xl mr-4 shadow-sm">
            <Target className="h-8 w-8" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 tracking-tight">
              {getTranslation(currentLanguage, 'keywordDensityChecker')}
            </h1>
            <p className="text-gray-600 mt-2 leading-relaxed">
              {getTranslation(currentLanguage, 'keywordDensityDesc')}
            </p>
          </div>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Input Section */}
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {getTranslation(currentLanguage, 'targetKeywordsLabel')}
              </label>
              <input
                type="text"
                value={keywords}
                onChange={(e) => setKeywords(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200"
                placeholder={getTranslation(currentLanguage, 'keywordsPlaceholder')}
              />
              <p className="text-sm text-gray-500 mt-1">
                {getTranslation(currentLanguage, 'separateKeywordsComma')}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {getTranslation(currentLanguage, 'contentToAnalyze')}
              </label>
              <textarea
                value={content}
                onChange={(e) => setContent(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200"
                rows={12}
                placeholder={getTranslation(currentLanguage, 'contentPlaceholder')}
              />
            </div>

            {/* Content Stats */}
            <div className="grid grid-cols-3 gap-4">
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-gray-900">{contentStats.wordCount}</div>
                <div className="text-sm text-gray-600">{getTranslation(currentLanguage, 'words')}</div>
              </div>
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-gray-900">{contentStats.characterCount}</div>
                <div className="text-sm text-gray-600">{getTranslation(currentLanguage, 'characters')}</div>
              </div>
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-gray-900">{contentStats.sentenceCount}</div>
                <div className="text-sm text-gray-600">{getTranslation(currentLanguage, 'sentences')}</div>
              </div>
            </div>
          </div>

          {/* Analysis Results */}
          <div className="space-y-6">
            {analysis.length > 0 && (
              <>
                <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-xl p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                    <BarChart3 className="h-6 w-6 mr-2 text-green-600" />
                    {getTranslation(currentLanguage, 'keywordAnalysisResults')}
                  </h3>
                </div>

                <div className="space-y-4">
                  {analysis.map((item, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-semibold text-gray-900">{item.keyword}</h4>
                        <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(item.status)}`}>
                          {getStatusText(item.status)}
                        </span>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4 mb-3">
                        <div>
                          <div className="text-sm text-gray-600">{getTranslation(currentLanguage, 'density')}</div>
                          <div className="text-lg font-bold text-gray-900">{item.density.toFixed(2)}%</div>
                        </div>
                        <div>
                          <div className="text-sm text-gray-600">{getTranslation(currentLanguage, 'occurrences')}</div>
                          <div className="text-lg font-bold text-gray-900">{item.count} {getTranslation(currentLanguage, 'times')}</div>
                        </div>
                      </div>

                      <div className="bg-gray-200 rounded-full h-2 mb-3">
                        <div 
                          className={`h-2 rounded-full ${
                            item.status === 'optimal' ? 'bg-green-500' : 
                            item.status === 'low' ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${Math.min(item.density * 10, 100)}%` }}
                        ></div>
                      </div>

                      <div className="text-sm text-gray-700">
                        <span className="font-medium">{getTranslation(currentLanguage, 'recommendation')}:</span> {item.recommendation}
                      </div>
                    </div>
                  ))}
                </div>

                {/* General Recommendations */}
                <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <TrendingUp className="h-5 w-5 mr-2 text-blue-600" />
                    {getTranslation(currentLanguage, 'generalRecommendations')}
                  </h4>
                  <div className="space-y-2 text-sm text-gray-700">
                    <p>• {getTranslation(currentLanguage, 'keywordDensityTip1')}</p>
                    <p>• {getTranslation(currentLanguage, 'keywordDensityTip2')}</p>
                    <p>• {getTranslation(currentLanguage, 'keywordDensityTip3')}</p>
                    <p>• {getTranslation(currentLanguage, 'keywordDensityTip4')}</p>
                  </div>
                </div>
              </>
            )}

            {analysis.length === 0 && (
              <div className="bg-gray-50 border border-gray-200 rounded-xl p-8 text-center">
                <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h4 className="text-lg font-semibold text-gray-900 mb-2">{getTranslation(currentLanguage, 'startKeywordAnalysis')}</h4>
                <p className="text-gray-600">{getTranslation(currentLanguage, 'enterKeywordsAndContent')}</p>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Tool Information Sections */}
      <ToolInfoSections />
    </div>
  );
};

export default KeywordDensityChecker;