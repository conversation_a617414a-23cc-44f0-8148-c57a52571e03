import React, { useState, useEffect } from 'react';
import { Package, Plus, Trash2, Calculator, Box, AlertTriangle, CheckCircle, Info, RotateCcw, Copy, Check, Truck, DollarSign } from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
import { ProductPackage, PackingResult, ShippingCarton } from '../../data/packingData';
import { recommendShippingCarton, optimizePackingInCarton } from '../../utils/packingAlgorithm';
import { STANDARD_SHIPPING_CARTONS, PACKING_TIPS } from '../../data/packingData';

const PackingCalculator = () => {
  const { currentLanguage } = useLanguage();
  const [packages, setPackages] = useState<ProductPackage[]>([
    {
      id: '1',
      name: '商品A',
      length: 12,
      width: 12,
      height: 14,
      weight: 0.5,
      quantity: 10,
      isFragile: false
    }
  ]);

  const [selectedCartonId, setSelectedCartonId] = useState<string>('');
  const [calculationMode, setCalculationMode] = useState<'recommend' | 'optimize'>('recommend');
  const [result, setResult] = useState<PackingResult | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [copiedTip, setCopiedTip] = useState<string>('');

  // 添加商品包装盒
  const addPackage = () => {
    const newPackage: ProductPackage = {
      id: Date.now().toString(),
      name: `商品${String.fromCharCode(65 + packages.length)}`,
      length: 12,
      width: 12,
      height: 14,
      weight: 0.5,
      quantity: 10,
      isFragile: false
    };
    setPackages([...packages, newPackage]);
  };

  // 删除商品包装盒
  const removePackage = (id: string) => {
    setPackages(packages.filter(p => p.id !== id));
  };

  // 更新商品包装盒信息
  const updatePackage = (id: string, field: keyof ProductPackage, value: any) => {
    setPackages(packages.map(p =>
      p.id === id ? { ...p, [field]: value } : p
    ));
  };

  // 计算装箱方案
  const calculatePacking = async () => {
    if (packages.length === 0) return;

    setIsCalculating(true);

    try {
      // 模拟计算延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      let packingResult: PackingResult;

      if (calculationMode === 'recommend') {
        packingResult = recommendShippingCarton(packages);
      } else {
        const selectedCarton = STANDARD_SHIPPING_CARTONS.find(carton => carton.id === selectedCartonId);
        if (!selectedCarton) {
          throw new Error('请选择一个大纸箱');
        }
        packingResult = optimizePackingInCarton(packages, selectedCarton);
      }

      setResult(packingResult);
    } catch (error) {
      console.error('计算错误:', error);
    } finally {
      setIsCalculating(false);
    }
  };

  // 重置表单
  const resetForm = () => {
    setPackages([{
      id: '1',
      name: '商品A',
      length: 12,
      width: 12,
      height: 14,
      weight: 0.5,
      quantity: 10,
      isFragile: false
    }]);
    setResult(null);
    setSelectedCartonId('');
  };

  // 复制建议
  const copyTip = (tip: string) => {
    navigator.clipboard.writeText(tip);
    setCopiedTip(tip);
    setTimeout(() => setCopiedTip(''), 2000);
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* 页面标题 */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl">
            <Package className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">装箱计算器</h1>
            <p className="text-gray-600">智能推荐大纸箱尺寸，优化商品包装盒装箱摆放，降低物流成本</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧：输入区域 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 计算模式选择 */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">计算模式</h3>
            <div className="grid grid-cols-2 gap-4">
              <button
                onClick={() => setCalculationMode('recommend')}
                className={`p-4 rounded-lg border-2 transition-all ${
                  calculationMode === 'recommend'
                    ? 'border-orange-500 bg-orange-50 text-orange-700'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <Box className="h-5 w-5 mx-auto mb-2" />
                <div className="font-medium">推荐大纸箱</div>
                <div className="text-sm text-gray-600">根据商品包装盒推荐最优大纸箱</div>
              </button>
              <button
                onClick={() => setCalculationMode('optimize')}
                className={`p-4 rounded-lg border-2 transition-all ${
                  calculationMode === 'optimize'
                    ? 'border-orange-500 bg-orange-50 text-orange-700'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <Calculator className="h-5 w-5 mx-auto mb-2" />
                <div className="font-medium">优化摆放</div>
                <div className="text-sm text-gray-600">指定大纸箱优化摆放方案</div>
              </button>
            </div>
          </div>

          {/* 大纸箱选择（优化模式） */}
          {calculationMode === 'optimize' && (
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">选择大纸箱</h3>
              <select
                value={selectedCartonId}
                onChange={(e) => setSelectedCartonId(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              >
                <option value="">请选择大纸箱...</option>
                {STANDARD_SHIPPING_CARTONS.map(carton => (
                  <option key={carton.id} value={carton.id}>
                    {carton.name} (外径:{carton.outerLength}×{carton.outerWidth}×{carton.outerHeight}cm, 内径:{carton.innerLength}×{carton.innerWidth}×{carton.innerHeight}cm)
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* 商品包装盒信息输入 */}
          <div className="card">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">商品包装盒信息</h3>
              <button
                onClick={addPackage}
                className="btn btn-secondary flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                添加商品
              </button>
            </div>

            <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center gap-2 mb-2">
                <Info className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-800">说明</span>
              </div>
              <p className="text-sm text-blue-700">
                商品尺寸是指单件商品的包装盒尺寸（如12×12×14cm），数量是指要装入大纸箱的包装盒数量。
                大纸箱用于发往亚马逊仓库，需符合亚马逊入库要求（任一边≤63.5cm，重量≤22.68kg）。
              </p>
            </div>

            <div className="space-y-4">
              {packages.map((pkg, index) => (
                <div key={pkg.id} className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <span className="font-medium text-gray-700">商品 {pkg.name}</span>
                    {packages.length > 1 && (
                      <button
                        onClick={() => removePackage(pkg.id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    )}
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-5 gap-3 mb-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        包装盒长度 (cm)
                      </label>
                      <input
                        type="number"
                        value={pkg.length}
                        onChange={(e) => updatePackage(pkg.id, 'length', parseFloat(e.target.value) || 0)}
                        className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                        min="0"
                        step="0.1"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        包装盒宽度 (cm)
                      </label>
                      <input
                        type="number"
                        value={pkg.width}
                        onChange={(e) => updatePackage(pkg.id, 'width', parseFloat(e.target.value) || 0)}
                        className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                        min="0"
                        step="0.1"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        包装盒高度 (cm)
                      </label>
                      <input
                        type="number"
                        value={pkg.height}
                        onChange={(e) => updatePackage(pkg.id, 'height', parseFloat(e.target.value) || 0)}
                        className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                        min="0"
                        step="0.1"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        单件重量 (kg)
                      </label>
                      <input
                        type="number"
                        value={pkg.weight}
                        onChange={(e) => updatePackage(pkg.id, 'weight', parseFloat(e.target.value) || 0)}
                        className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                        min="0"
                        step="0.01"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        数量 (个)
                      </label>
                      <input
                        type="number"
                        value={pkg.quantity}
                        onChange={(e) => updatePackage(pkg.id, 'quantity', parseInt(e.target.value) || 1)}
                        className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                        min="1"
                        step="1"
                      />
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <input
                      type="text"
                      value={pkg.name}
                      onChange={(e) => updatePackage(pkg.id, 'name', e.target.value)}
                      placeholder="商品名称"
                      className="flex-1 p-2 border border-gray-300 rounded focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    />
                    <label className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={pkg.isFragile || false}
                        onChange={(e) => updatePackage(pkg.id, 'isFragile', e.target.checked)}
                        className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                      />
                      <span className="text-sm text-gray-700">易碎品</span>
                    </label>
                    <div className="text-sm text-gray-600">
                      总重: {(pkg.weight * pkg.quantity).toFixed(2)}kg
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* 操作按钮 */}
            <div className="flex gap-3 mt-6">
              <button
                onClick={calculatePacking}
                disabled={isCalculating || packages.length === 0}
                className="btn btn-primary flex items-center gap-2 flex-1"
              >
                {isCalculating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    计算中...
                  </>
                ) : (
                  <>
                    <Calculator className="h-4 w-4" />
                    开始计算
                  </>
                )}
              </button>
              <button
                onClick={resetForm}
                className="btn btn-secondary flex items-center gap-2"
              >
                <RotateCcw className="h-4 w-4" />
                重置
              </button>
            </div>
          </div>
        </div>

        {/* 右侧：结果展示 */}
        <div className="space-y-6">
          {/* 计算结果 */}
          {result && (
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">计算结果</h3>
              
              {/* 推荐大纸箱 */}
              <div className="mb-4 p-3 bg-green-50 rounded-lg border border-green-200">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="font-medium text-green-800">推荐大纸箱</span>
                </div>
                <div className="text-sm text-green-700">
                  <div>{result.recommendedCarton.name}</div>
                  <div>外径: {result.recommendedCarton.outerLength}×{result.recommendedCarton.outerWidth}×{result.recommendedCarton.outerHeight}cm</div>
                  <div>内径: {result.recommendedCarton.innerLength}×{result.recommendedCarton.innerWidth}×{result.recommendedCarton.innerHeight}cm</div>
                  <div>最大承重: {result.recommendedCarton.maxWeight}kg</div>
                </div>
              </div>

              {/* 空间利用率 */}
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-700">空间利用率</span>
                  <span className="text-sm font-bold text-orange-600">
                    {(result.utilization * 100).toFixed(1)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-orange-500 to-orange-600 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${result.utilization * 100}%` }}
                  ></div>
                </div>
              </div>

              {/* 物流费用信息 */}
              <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center gap-2 mb-2">
                  <Truck className="h-5 w-5 text-blue-600" />
                  <span className="font-medium text-blue-800">物流费用分析</span>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-blue-700">实际重量:</span>
                    <span className="font-medium">{result.shippingCost.actualWeight.toFixed(2)} kg</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-blue-700">体积重量:</span>
                    <span className="font-medium">{result.shippingCost.volumetricWeight.toFixed(2)} kg</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-blue-700">计费重量:</span>
                    <span className="font-medium text-blue-900">{result.shippingCost.chargeableWeight.toFixed(2)} kg</span>
                  </div>
                  <div className="border-t border-blue-300 pt-2 flex justify-between font-semibold">
                    <span className="text-blue-800">预估物流费:</span>
                    <span className="text-blue-900">${result.shippingCost.shippingCost.toFixed(2)}</span>
                  </div>
                  <div className="text-xs text-blue-600 mt-1">
                    * 体积重量 = 长×宽×高(cm) ÷ 6000，计费重量取较大值
                  </div>
                </div>
              </div>

              {/* 警告信息 */}
              {result.warnings.length > 0 && (
                <div className="mb-4 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertTriangle className="h-5 w-5 text-yellow-600" />
                    <span className="font-medium text-yellow-800">注意事项</span>
                  </div>
                  <ul className="text-sm text-yellow-700 space-y-1">
                    {result.warnings.map((warning, index) => (
                      <li key={index}>• {warning}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* 优化建议 */}
              {result.suggestions && result.suggestions.length > 0 && (
                <div className="mb-4 p-3 bg-purple-50 rounded-lg border border-purple-200">
                  <div className="flex items-center gap-2 mb-2">
                    <Info className="h-5 w-5 text-purple-600" />
                    <span className="font-medium text-purple-800">优化建议</span>
                  </div>
                  <ul className="text-sm text-purple-700 space-y-1">
                    {result.suggestions.map((suggestion, index) => (
                      <li key={index}>• {suggestion}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}

          {/* 装箱优化建议 */}
          <div className="card">
            <div className="flex items-center gap-2 mb-4">
              <Info className="h-5 w-5 text-blue-600" />
              <h3 className="text-lg font-semibold text-gray-900">装箱优化建议</h3>
            </div>

            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-800 mb-2">成本优化</h4>
                <ul className="space-y-1">
                  {PACKING_TIPS.COST_OPTIMIZATION.map((tip, index) => (
                    <li key={index} className="text-sm text-gray-600 flex items-start gap-2">
                      <button
                        onClick={() => copyTip(tip)}
                        className="mt-0.5 text-gray-400 hover:text-orange-500 transition-colors"
                      >
                        {copiedTip === tip ? (
                          <Check className="h-3 w-3" />
                        ) : (
                          <Copy className="h-3 w-3" />
                        )}
                      </button>
                      <span>• {tip}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h4 className="font-medium text-gray-800 mb-2">空间优化</h4>
                <ul className="space-y-1">
                  {PACKING_TIPS.SPACE_OPTIMIZATION.map((tip, index) => (
                    <li key={index} className="text-sm text-gray-600 flex items-start gap-2">
                      <button
                        onClick={() => copyTip(tip)}
                        className="mt-0.5 text-gray-400 hover:text-orange-500 transition-colors"
                      >
                        {copiedTip === tip ? (
                          <Check className="h-3 w-3" />
                        ) : (
                          <Copy className="h-3 w-3" />
                        )}
                      </button>
                      <span>• {tip}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h4 className="font-medium text-gray-800 mb-2">亚马逊合规</h4>
                <ul className="space-y-1">
                  {PACKING_TIPS.AMAZON_COMPLIANCE.map((tip, index) => (
                    <li key={index} className="text-sm text-gray-600 flex items-start gap-2">
                      <button
                        onClick={() => copyTip(tip)}
                        className="mt-0.5 text-gray-400 hover:text-orange-500 transition-colors"
                      >
                        {copiedTip === tip ? (
                          <Check className="h-3 w-3" />
                        ) : (
                          <Copy className="h-3 w-3" />
                        )}
                      </button>
                      <span>• {tip}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h4 className="font-medium text-gray-800 mb-2">物流效率</h4>
                <ul className="space-y-1">
                  {PACKING_TIPS.SHIPPING_EFFICIENCY.map((tip, index) => (
                    <li key={index} className="text-sm text-gray-600 flex items-start gap-2">
                      <button
                        onClick={() => copyTip(tip)}
                        className="mt-0.5 text-gray-400 hover:text-orange-500 transition-colors"
                      >
                        {copiedTip === tip ? (
                          <Check className="h-3 w-3" />
                        ) : (
                          <Copy className="h-3 w-3" />
                        )}
                      </button>
                      <span>• {tip}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PackingCalculator;
