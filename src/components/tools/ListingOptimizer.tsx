import { useState, useEffect, useCallback } from 'react';
import { Zap, AlertTriangle, Info, Star } from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
import { getTranslation } from '../../utils/translations';
import ToolInfoSections from './ListingOptimizer/ToolInfoSections';

interface ListingAnalysis {
  overallScore: number;
  titleScore: number;
  bulletScore: number;
  descriptionScore: number;
  keywordScore: number;
  issues: string[];
  suggestions: string[];
}

const ListingOptimizer = () => {
  const { currentLanguage } = useLanguage();
  const [title, setTitle] = useState('');
  const [bulletPoints, setBulletPoints] = useState(['', '', '', '', '']);
  const [description, setDescription] = useState('');
  const [searchTerms, setSearchTerms] = useState('');
  const [marketplace, setMarketplace] = useState('US');
  const [category, setCategory] = useState('general');
  const [analysis, setAnalysis] = useState<ListingAnalysis | null>(null);

  const marketplaces = [
    { code: 'US', name: 'Amazon.com (US)' },
    { code: 'UK', name: 'Amazon.co.uk (UK)' },
    { code: 'DE', name: 'Amazon.de (Germany)' },
    { code: 'CA', name: 'Amazon.ca (Canada)' }
  ];

  const categories = [
    { id: 'general', name: getTranslation(currentLanguage, 'generalCategory') },
    { id: 'electronics', name: getTranslation(currentLanguage, 'electronicsCategory') },
    { id: 'clothing', name: getTranslation(currentLanguage, 'clothingCategory') },
    { id: 'home', name: getTranslation(currentLanguage, 'homeCategory') },
    { id: 'books', name: getTranslation(currentLanguage, 'booksCategory') },
    { id: 'health', name: getTranslation(currentLanguage, 'healthCategory') }
  ];

  const analyzeListing = useCallback(() => {
    const issues: string[] = [];
    const suggestions: string[] = [];
    let titleScore = 100;
    let bulletScore = 100;
    let descriptionScore = 100;
    let keywordScore = 100;

    // Title Analysis
    if (!title.trim()) {
      issues.push(getTranslation(currentLanguage, 'titleRequired'));
      titleScore = 0;
    } else {
      if (title.length < 50) titleScore -= 20;
      if (title.length > 200) titleScore -= 30;
      if (!/^[A-Z]/.test(title)) {
        issues.push(getTranslation(currentLanguage, 'titleNeedsCapitalization'));
        titleScore -= 15;
      }
    }

    // Bullet Points Analysis
    const validBullets = bulletPoints.filter(bp => bp.trim().length > 0);
    if (validBullets.length < 3) {
      issues.push(getTranslation(currentLanguage, 'needMoreBulletPoints'));
      bulletScore -= 40;
    }

    bulletPoints.forEach((bullet, index) => {
      if (bullet.trim().length > 0) {
        if (bullet.length < 50) {
          issues.push(getTranslation(currentLanguage, 'bulletPointTooShort').replace('{index}', (index + 1).toString()));
          bulletScore -= 15;
        }
        if (bullet.length > 255) {
          issues.push(getTranslation(currentLanguage, 'bulletPointTooLong').replace('{index}', (index + 1).toString()));
          bulletScore -= 20;
        }
      }
    });

    // Description Analysis
    if (!description.trim()) {
      issues.push(getTranslation(currentLanguage, 'descriptionRequired'));
      descriptionScore = 0;
    } else {
      if (description.length < 200) {
        issues.push(getTranslation(currentLanguage, 'descriptionTooShort'));
        descriptionScore -= 30;
      }
      if (description.length > 2000) {
        issues.push(getTranslation(currentLanguage, 'descriptionTooLong'));
        descriptionScore -= 20;
      }
      if (!description.includes('<') && description.length > 500) {
        suggestions.push(getTranslation(currentLanguage, 'useHTMLFormatting'));
      }
    }

    // Search Terms Analysis
    if (!searchTerms.trim()) {
      issues.push(getTranslation(currentLanguage, 'searchTermsRequired'));
      keywordScore = 0;
    } else {
      const keywords = searchTerms.split(',').map(k => k.trim()).filter(k => k.length > 0);
      if (keywords.length < 10) {
        issues.push(getTranslation(currentLanguage, 'needMoreKeywords'));
        keywordScore -= 30;
      }
      
      // Check for duplicates
      const uniqueKeywords = [...new Set(keywords.map(k => k.toLowerCase()))];
      if (uniqueKeywords.length < keywords.length) {
        issues.push(getTranslation(currentLanguage, 'duplicateKeywords'));
        keywordScore -= 20;
      }
    }

    // Suggestions
    if (titleScore >= 80) {
      suggestions.push(getTranslation(currentLanguage, 'titleOptimal'));
    } else {
      suggestions.push(getTranslation(currentLanguage, 'improveTitleSuggestion'));
    }

    if (bulletScore >= 80) {
      suggestions.push(getTranslation(currentLanguage, 'bulletPointsOptimal'));
    } else {
      suggestions.push(getTranslation(currentLanguage, 'improveBulletPoints'));
    }

    const overallScore = Math.round((titleScore + bulletScore + descriptionScore + keywordScore) / 4);

    setAnalysis({
      overallScore,
      titleScore: Math.max(0, titleScore),
      bulletScore: Math.max(0, bulletScore),
      descriptionScore: Math.max(0, descriptionScore),
      keywordScore: Math.max(0, keywordScore),
      issues,
      suggestions
    });
  }, [currentLanguage, title, bulletPoints, description, searchTerms]);

  useEffect(() => {
    if (title.trim() || bulletPoints.some(bp => bp.trim()) || description.trim() || searchTerms.trim()) {
      analyzeListing();
    } else {
      setAnalysis(null);
    }
  }, [title, bulletPoints, description, searchTerms, marketplace, category, analyzeListing]);

  const updateBulletPoint = (index: number, value: string) => {
    const newBullets = [...bulletPoints];
    newBullets[index] = value;
    setBulletPoints(newBullets);
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBackground = (score: number) => {
    if (score >= 80) return 'bg-green-100';
    if (score >= 60) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
          <div className="flex items-center mb-8">
          <div className="bg-purple-500 text-white p-3 rounded-xl mr-4">
            <Zap className="h-8 w-8" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {getTranslation(currentLanguage, 'listingOptimizerTool')}
            </h1>
            <p className="text-gray-600 mt-2">
              {getTranslation(currentLanguage, 'listingOptimizerDesc')}
            </p>
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Input Section */}
          <div className="lg:col-span-2 space-y-6">
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {getTranslation(currentLanguage, 'marketplace')}
                </label>
                <select
                  value={marketplace}
                  onChange={(e) => setMarketplace(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  {marketplaces.map((mp) => (
                    <option key={mp.code} value={mp.code}>
                      {mp.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {getTranslation(currentLanguage, 'category')}
                </label>
                <select
                  value={category}
                  onChange={(e) => setCategory(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  {categories.map((cat) => (
                    <option key={cat.id} value={cat.id}>
                      {cat.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {getTranslation(currentLanguage, 'productTitle')}
              </label>
              <textarea
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                rows={3}
                placeholder={getTranslation(currentLanguage, 'titlePlaceholder')}
              />
              <div className="text-sm text-gray-500 mt-1">{title.length}/200 {getTranslation(currentLanguage, 'characters')}</div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {getTranslation(currentLanguage, 'bulletPoints')}
              </label>
              <div className="space-y-3">
                {bulletPoints.map((bullet, index) => (
                  <div key={index}>
                    <input
                      type="text"
                      value={bullet}
                      onChange={(e) => updateBulletPoint(index, e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      placeholder={`${getTranslation(currentLanguage, 'bulletPoint')} ${index + 1}`}
                    />
                    <div className="text-sm text-gray-500 mt-1">{bullet.length}/255 {getTranslation(currentLanguage, 'characters')}</div>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {getTranslation(currentLanguage, 'productDescription')}
              </label>
              <textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                rows={8}
                placeholder={getTranslation(currentLanguage, 'descriptionPlaceholder')}
              />
              <div className="text-sm text-gray-500 mt-1">{description.length}/2000 {getTranslation(currentLanguage, 'characters')}</div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {getTranslation(currentLanguage, 'searchTerms')}
              </label>
              <textarea
                value={searchTerms}
                onChange={(e) => setSearchTerms(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                rows={4}
                placeholder={getTranslation(currentLanguage, 'searchTermsPlaceholder')}
              />
            </div>
          </div>

          {/* Analysis Results */}
          <div className="space-y-6">
            {analysis && (
              <>
                {/* Overall Score */}
                <div className={`rounded-xl p-6 ${getScoreBackground(analysis.overallScore)}`}>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">
                    {getTranslation(currentLanguage, 'listingOptimizationScore')}
                  </h3>
                  <div className="text-center">
                    <div className={`text-4xl font-bold ${getScoreColor(analysis.overallScore)}`}>
                      {analysis.overallScore}
                    </div>
                    <div className="text-sm text-gray-600">
                      {getTranslation(currentLanguage, 'outOf100')}
                    </div>
                    <div className="text-xs text-gray-500 mt-2">
                      {getTranslation(currentLanguage, 'overallListingQuality')}
                    </div>
                  </div>
                </div>

                {/* Individual Scores */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium">{getTranslation(currentLanguage, 'title')}</span>
                    <span className={`font-bold ${getScoreColor(analysis.titleScore)}`}>
                      {analysis.titleScore}
                    </span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium">{getTranslation(currentLanguage, 'bulletPoints')}</span>
                    <span className={`font-bold ${getScoreColor(analysis.bulletScore)}`}>
                      {analysis.bulletScore}
                    </span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium">{getTranslation(currentLanguage, 'description')}</span>
                    <span className={`font-bold ${getScoreColor(analysis.descriptionScore)}`}>
                      {analysis.descriptionScore}
                    </span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium">{getTranslation(currentLanguage, 'keywords')}</span>
                    <span className={`font-bold ${getScoreColor(analysis.keywordScore)}`}>
                      {analysis.keywordScore}
                    </span>
                  </div>
                </div>

                {/* Issues */}
                {analysis.issues.length > 0 && (
                  <div className="bg-red-50 rounded-xl p-4">
                    <h4 className="font-semibold text-red-900 mb-3 flex items-center">
                      <AlertTriangle className="h-4 w-4 mr-2" />
                      {getTranslation(currentLanguage, 'issues')} ({analysis.issues.length})
                    </h4>
                    <div className="space-y-2">
                      {analysis.issues.map((issue, index) => (
                        <div key={index} className="text-sm text-red-800">
                          • {issue}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Suggestions */}
                {analysis.suggestions.length > 0 && (
                  <div className="bg-blue-50 rounded-xl p-4">
                    <h4 className="font-semibold text-blue-900 mb-3 flex items-center">
                      <Info className="h-4 w-4 mr-2" />
                      {getTranslation(currentLanguage, 'suggestions')}
                    </h4>
                    <div className="space-y-2">
                      {analysis.suggestions.map((suggestion, index) => (
                        <div key={index} className="text-sm text-blue-800">
                          • {suggestion}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Best Practices */}
                <div className="bg-purple-50 rounded-xl p-4">
                  <h4 className="font-semibold text-purple-900 mb-3 flex items-center">
                    <Star className="h-4 w-4 mr-2" />
                    {getTranslation(currentLanguage, 'bestPractices')}
                  </h4>
                  <div className="space-y-3">
                    <div>
                      <div className="font-medium text-purple-900 text-sm mb-1">
                        {getTranslation(currentLanguage, 'titleBestPractices')}
                      </div>
                      <div className="text-xs text-purple-800 space-y-1">
                        <div>• {getTranslation(currentLanguage, 'titleTip1')}</div>
                        <div>• {getTranslation(currentLanguage, 'titleTip2')}</div>
                        <div>• {getTranslation(currentLanguage, 'titleTip3')}</div>
                      </div>
                    </div>
                    <div>
                      <div className="font-medium text-purple-900 text-sm mb-1">
                        {getTranslation(currentLanguage, 'bulletPointBestPractices')}
                      </div>
                      <div className="text-xs text-purple-800 space-y-1">
                        <div>• {getTranslation(currentLanguage, 'bulletTip1')}</div>
                        <div>• {getTranslation(currentLanguage, 'bulletTip2')}</div>
                        <div>• {getTranslation(currentLanguage, 'bulletTip3')}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            )}

            {!analysis && (
              <div className="bg-gray-50 rounded-xl p-6 text-center">
                <Zap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h4 className="text-lg font-semibold text-gray-900 mb-2">{getTranslation(currentLanguage, 'startOptimizingListing')}</h4>
                <p className="text-gray-600 text-sm">{getTranslation(currentLanguage, 'fillProductInfo')}</p>
              </div>
            )}
          </div>
        </div>
        </div>

        {/* 工具详细信息部分 */}
        <ToolInfoSections />
      </div>
    </div>
  );
};

export default ListingOptimizer;