import { useState, useEffect, useCallback } from 'react';
import { Calculator, TrendingUp, AlertCircle, Info, RefreshCw, ArrowUpDown } from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
import { getTranslation } from '../../utils/translations';
import CurrencyService from '../../services/currencyService';
import { getCurrencyInfo, POPULAR_CURRENCIES } from '../../data/currencyData';

interface ProfitData {
  sellingPrice: number;
  costOfGoods: number;
  amazonFees: number;
  shippingCost: number;
  advertisingCost: number;
  otherCosts: number;
  fbaFulfillmentFee: number;
  storageFee: number;
  inboundPlacementFee: number;
  returnsProcessingFee: number;
}





const ProfitCalculator = () => {
  const { currentLanguage } = useLanguage();
  const [marketplace, setMarketplace] = useState('US');
  const [category, setCategory] = useState('general');
  const [displayCurrency, setDisplayCurrency] = useState('USD');
  const [currencyService] = useState(() => CurrencyService.getInstance());
  const [isLoadingRates, setIsLoadingRates] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const [data, setData] = useState<ProfitData>({
    sellingPrice: 0,
    costOfGoods: 0,
    amazonFees: 0,
    shippingCost: 0,
    advertisingCost: 0,
    otherCosts: 0,
    fbaFulfillmentFee: 0,
    storageFee: 0,
    inboundPlacementFee: 0,
    returnsProcessingFee: 0
  });



  const [results, setResults] = useState({
    grossProfit: 0,
    netProfit: 0,
    profitMargin: 0,
    roi: 0,
    breakEvenUnits: 0
  });

  // 2025年最新的亚马逊市场配置
  const marketplaces = [
    {
      code: 'US',
      name: 'Amazon.com (US)',
      currency: 'USD',
      symbol: '$',
      storageRate: { nonPeak: 0.87, peak: 2.40 }, // per cubic foot per month
      digitalServicesFee: 0 // No DST in US
    },
    {
      code: 'UK',
      name: 'Amazon.co.uk (UK)',
      currency: 'GBP',
      symbol: '£',
      storageRate: { nonPeak: 0.70, peak: 1.95 },
      digitalServicesFee: 0.02 // 2% DST
    },
    {
      code: 'DE',
      name: 'Amazon.de (Germany)',
      currency: 'EUR',
      symbol: '€',
      storageRate: { nonPeak: 0.75, peak: 2.05 },
      digitalServicesFee: 0
    },
    {
      code: 'FR',
      name: 'Amazon.fr (France)',
      currency: 'EUR',
      symbol: '€',
      storageRate: { nonPeak: 0.75, peak: 2.05 },
      digitalServicesFee: 0.03 // 3% DST
    },
    {
      code: 'IT',
      name: 'Amazon.it (Italy)',
      currency: 'EUR',
      symbol: '€',
      storageRate: { nonPeak: 0.75, peak: 2.05 },
      digitalServicesFee: 0.03 // 3% DST
    },
    {
      code: 'ES',
      name: 'Amazon.es (Spain)',
      currency: 'EUR',
      symbol: '€',
      storageRate: { nonPeak: 0.75, peak: 2.05 },
      digitalServicesFee: 0.03 // 3% DST
    },
    {
      code: 'CA',
      name: 'Amazon.ca (Canada)',
      currency: 'CAD',
      symbol: 'C$',
      storageRate: { nonPeak: 1.15, peak: 3.20 },
      digitalServicesFee: 0.02 // 2% DST
    },
    {
      code: 'JP',
      name: 'Amazon.co.jp (Japan)',
      currency: 'JPY',
      symbol: '¥',
      storageRate: { nonPeak: 95, peak: 265 },
      digitalServicesFee: 0
    }
  ];

  // 2025年最新的亚马逊类目费率
  const categories = [
    {
      id: 'general',
      name: getTranslation(currentLanguage, 'generalCategory'),
      referralFeeRate: 0.15,
      closingFee: 0,
      hasLowPriceFBA: true
    },
    {
      id: 'electronics',
      name: getTranslation(currentLanguage, 'electronicsCategory'),
      referralFeeRate: 0.08,
      closingFee: 0,
      hasLowPriceFBA: true
    },
    {
      id: 'clothing',
      name: getTranslation(currentLanguage, 'clothingCategory'),
      referralFeeRate: 0.17, // 2025年更新：低价服装费率降低
      lowPriceRate: { under15: 0.05, between15and20: 0.10 },
      closingFee: 0,
      hasLowPriceFBA: true,
      hasReturnsProcessingFee: true
    },
    {
      id: 'home',
      name: getTranslation(currentLanguage, 'homeCategory'),
      referralFeeRate: 0.15,
      closingFee: 0,
      hasLowPriceFBA: true
    },
    {
      id: 'books',
      name: getTranslation(currentLanguage, 'booksCategory'),
      referralFeeRate: 0.15,
      closingFee: 1.80, // Books have closing fee
      hasLowPriceFBA: false
    },
    {
      id: 'health',
      name: getTranslation(currentLanguage, 'healthCategory'),
      referralFeeRate: 0.15,
      closingFee: 0,
      hasLowPriceFBA: true
    },
    {
      id: 'automotive',
      name: getTranslation(currentLanguage, 'automotiveCategory'),
      referralFeeRate: 0.12,
      closingFee: 0,
      hasLowPriceFBA: true
    },
    {
      id: 'beauty',
      name: getTranslation(currentLanguage, 'beautyCategory'),
      referralFeeRate: 0.15,
      closingFee: 0,
      hasLowPriceFBA: true
    }
  ];

  const currentMarketplace = marketplaces.find(m => m.code === marketplace) || marketplaces[0];
  const currentCategory = categories.find(c => c.id === category) || categories[0];

  // 计算2025年最新的亚马逊费用
  const calculateAmazonFees = useCallback(() => {
    const { sellingPrice } = data;
    if (!sellingPrice) return 0;

    let referralFee = 0;

    // 计算推荐费 - 2025年服装类目特殊费率
    if (currentCategory.id === 'clothing' && currentCategory.lowPriceRate) {
      if (sellingPrice < 15) {
        referralFee = sellingPrice * currentCategory.lowPriceRate.under15;
      } else if (sellingPrice <= 20) {
        referralFee = sellingPrice * currentCategory.lowPriceRate.between15and20;
      } else {
        referralFee = sellingPrice * currentCategory.referralFeeRate;
      }
    } else {
      referralFee = sellingPrice * currentCategory.referralFeeRate;
    }

    // 添加数字服务费 (DST)
    const digitalServicesFee = referralFee * currentMarketplace.digitalServicesFee;

    // 添加关闭费用
    const closingFee = currentCategory.closingFee;

    return referralFee + digitalServicesFee + closingFee;
  }, [data, currentCategory, currentMarketplace]);

  const amazonFees = calculateAmazonFees();

  // 初始化货币服务
  useEffect(() => {
    const initializeCurrencyService = async () => {
      setIsLoadingRates(true);
      try {
        await currencyService.waitForInitialization();
        setLastUpdated(currencyService.getLastUpdated());
      } catch (error) {
        console.error('货币服务初始化失败:', error);
      } finally {
        setIsLoadingRates(false);
      }
    };

    initializeCurrencyService();

    // 监听汇率更新
    const handleRatesUpdate = () => {
      setLastUpdated(currencyService.getLastUpdated());
    };

    currencyService.addListener(handleRatesUpdate);

    return () => {
      currencyService.removeListener(handleRatesUpdate);
    };
  }, [currencyService]);

  // 货币转换函数
  const convertCurrency = useCallback((amount: number, fromCurrency: string, toCurrency: string): number => {
    if (fromCurrency === toCurrency) return amount;
    const converted = currencyService.convert(amount, fromCurrency, toCurrency);
    return converted || amount;
  }, [currencyService]);

  // 格式化货币显示
  const formatCurrency = useCallback((amount: number, currency: string = displayCurrency): string => {
    const currencyInfo = getCurrencyInfo(currency, currentLanguage);
    const convertedAmount = convertCurrency(amount, currentMarketplace.currency, currency);

    if (currency === 'JPY') {
      return `${currencyInfo.symbol}${Math.round(convertedAmount).toLocaleString()}`;
    }
    return `${currencyInfo.symbol}${convertedAmount.toFixed(2)}`;
  }, [displayCurrency, currentLanguage, convertCurrency, currentMarketplace.currency]);

  useEffect(() => {
    const { sellingPrice, costOfGoods, shippingCost, advertisingCost, otherCosts } = data;

    const totalCosts = costOfGoods + amazonFees + shippingCost + advertisingCost + otherCosts;
    const grossProfit = sellingPrice - costOfGoods;
    const netProfit = sellingPrice - totalCosts;
    const profitMargin = sellingPrice > 0 ? (netProfit / sellingPrice) * 100 : 0;
    const roi = costOfGoods > 0 ? (netProfit / costOfGoods) * 100 : 0;
    const breakEvenUnits = netProfit > 0 ? Math.ceil(totalCosts / netProfit) : 0;

    setResults({
      grossProfit,
      netProfit,
      profitMargin,
      roi,
      breakEvenUnits
    });
  }, [data, amazonFees]);

  const handleInputChange = (field: keyof ProfitData, value: string) => {
    const numValue = parseFloat(value) || 0;
    setData(prev => ({ ...prev, [field]: numValue }));
  };

  const getProfitColor = (profit: number) => {
    if (profit > 0) return 'text-green-600';
    if (profit < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getMarginColor = (margin: number) => {
    if (margin >= 20) return 'text-green-600';
    if (margin >= 10) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="max-w-6xl mx-auto">
      <div className="bg-white rounded-2xl shadow-lg p-8">
        <div className="flex items-center mb-8">
          <div className="bg-orange-500 text-white p-3 rounded-xl mr-4">
            <Calculator className="h-8 w-8" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {getTranslation(currentLanguage, 'profitCalculator')}
            </h1>
            <p className="text-gray-600 mt-2">
              {getTranslation(currentLanguage, 'profitCalculatorDesc')}
            </p>
          </div>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Input Section */}
          <div className="space-y-6">
            <div className="grid md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {getTranslation(currentLanguage, 'marketplace')}
                </label>
                <select
                  value={marketplace}
                  onChange={(e) => setMarketplace(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                >
                  {marketplaces.map((mp) => (
                    <option key={mp.code} value={mp.code}>
                      {mp.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {getTranslation(currentLanguage, 'category')}
                </label>
                <select
                  value={category}
                  onChange={(e) => setCategory(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                >
                  {categories.map((cat) => (
                    <option key={cat.id} value={cat.id}>
                      {cat.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                  {getTranslation(currentLanguage, 'displayCurrency')}
                  <ArrowUpDown className="h-4 w-4 ml-2 text-gray-400" />
                  {isLoadingRates && <RefreshCw className="h-4 w-4 ml-2 text-orange-500 animate-spin" />}
                </label>
                <select
                  value={displayCurrency}
                  onChange={(e) => setDisplayCurrency(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  disabled={isLoadingRates}
                >
                  {POPULAR_CURRENCIES.map((currency) => {
                    const currencyInfo = getCurrencyInfo(currency, currentLanguage);
                    return (
                      <option key={currency} value={currency}>
                        {currencyInfo.flag} {currencyInfo.name} ({currency})
                      </option>
                    );
                  })}
                </select>
                {lastUpdated && (
                  <p className="text-xs text-gray-500 mt-1">
                    {getTranslation(currentLanguage, 'lastUpdated')}: {lastUpdated.toLocaleTimeString()}
                  </p>
                )}
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {getTranslation(currentLanguage, 'sellingPrice')} ({currentMarketplace.currency})
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={data.sellingPrice || ''}
                  onChange={(e) => handleInputChange('sellingPrice', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="29.99"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {getTranslation(currentLanguage, 'costOfGoods')} ({currentMarketplace.currency})
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={data.costOfGoods || ''}
                  onChange={(e) => handleInputChange('costOfGoods', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="10.00"
                />
              </div>

              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                  {getTranslation(currentLanguage, 'amazonFees')} ({currentMarketplace.currency})
                  <Info className="h-4 w-4 ml-2 text-gray-400" />
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={amazonFees.toFixed(2)}
                  readOnly
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50"
                />
                <p className="text-xs text-gray-500 mt-1">
                  {getTranslation(currentLanguage, 'autoCalculated')} ({(currentCategory.referralFeeRate * 100).toFixed(1)}%)
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {getTranslation(currentLanguage, 'shippingCost')} ({currentMarketplace.currency})
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={data.shippingCost || ''}
                  onChange={(e) => handleInputChange('shippingCost', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="2.50"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {getTranslation(currentLanguage, 'advertisingCost')} ({currentMarketplace.currency})
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={data.advertisingCost || ''}
                  onChange={(e) => handleInputChange('advertisingCost', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="3.00"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {getTranslation(currentLanguage, 'otherCosts')} ({currentMarketplace.currency})
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={data.otherCosts || ''}
                  onChange={(e) => handleInputChange('otherCosts', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="1.00"
                />
              </div>
            </div>
          </div>

          {/* Results Section */}
          <div className="space-y-6">
            <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-xl p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                <TrendingUp className="h-6 w-6 mr-2 text-orange-600" />
                {getTranslation(currentLanguage, 'profitAnalysis')}
              </h3>

              <div className="grid grid-cols-2 gap-4">
                <div className="bg-white rounded-lg p-4">
                  <div className="text-sm text-gray-600 mb-1">
                    {getTranslation(currentLanguage, 'grossProfit')}
                  </div>
                  <div className={`text-2xl font-bold ${getProfitColor(results.grossProfit)}`}>
                    {formatCurrency(results.grossProfit)}
                  </div>
                  {displayCurrency !== currentMarketplace.currency && (
                    <div className="text-xs text-gray-500 mt-1">
                      {currentMarketplace.symbol}{results.grossProfit.toFixed(2)}
                    </div>
                  )}
                </div>

                <div className="bg-white rounded-lg p-4">
                  <div className="text-sm text-gray-600 mb-1">
                    {getTranslation(currentLanguage, 'netProfit')}
                  </div>
                  <div className={`text-2xl font-bold ${getProfitColor(results.netProfit)}`}>
                    {formatCurrency(results.netProfit)}
                  </div>
                  {displayCurrency !== currentMarketplace.currency && (
                    <div className="text-xs text-gray-500 mt-1">
                      {currentMarketplace.symbol}{results.netProfit.toFixed(2)}
                    </div>
                  )}
                </div>

                <div className="bg-white rounded-lg p-4">
                  <div className="text-sm text-gray-600 mb-1">
                    {getTranslation(currentLanguage, 'profitMargin')}
                  </div>
                  <div className={`text-2xl font-bold ${getMarginColor(results.profitMargin)}`}>
                    {results.profitMargin.toFixed(1)}%
                  </div>
                </div>

                <div className="bg-white rounded-lg p-4">
                  <div className="text-sm text-gray-600 mb-1">
                    {getTranslation(currentLanguage, 'roi')}
                  </div>
                  <div className={`text-2xl font-bold ${getProfitColor(results.roi)}`}>
                    {results.roi.toFixed(1)}%
                  </div>
                </div>
              </div>
            </div>

            {/* Cost Breakdown */}
            <div className="bg-gray-50 rounded-xl p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">
                {getTranslation(currentLanguage, 'costBreakdown')}
              </h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">{getTranslation(currentLanguage, 'sellingPrice')}</span>
                  <div className="text-right">
                    <span className="font-medium">{formatCurrency(data.sellingPrice)}</span>
                    {displayCurrency !== currentMarketplace.currency && (
                      <div className="text-xs text-gray-500">
                        {currentMarketplace.symbol}{data.sellingPrice.toFixed(2)}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex justify-between text-red-600">
                  <span>- {getTranslation(currentLanguage, 'costOfGoods')}</span>
                  <div className="text-right">
                    <span>-{formatCurrency(data.costOfGoods)}</span>
                    {displayCurrency !== currentMarketplace.currency && (
                      <div className="text-xs text-gray-500">
                        -{currentMarketplace.symbol}{data.costOfGoods.toFixed(2)}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex justify-between text-red-600">
                  <span>- {getTranslation(currentLanguage, 'amazonFees')}</span>
                  <div className="text-right">
                    <span>-{formatCurrency(amazonFees)}</span>
                    {displayCurrency !== currentMarketplace.currency && (
                      <div className="text-xs text-gray-500">
                        -{currentMarketplace.symbol}{amazonFees.toFixed(2)}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex justify-between text-red-600">
                  <span>- {getTranslation(currentLanguage, 'shippingCost')}</span>
                  <div className="text-right">
                    <span>-{formatCurrency(data.shippingCost)}</span>
                    {displayCurrency !== currentMarketplace.currency && (
                      <div className="text-xs text-gray-500">
                        -{currentMarketplace.symbol}{data.shippingCost.toFixed(2)}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex justify-between text-red-600">
                  <span>- {getTranslation(currentLanguage, 'advertisingCost')}</span>
                  <div className="text-right">
                    <span>-{formatCurrency(data.advertisingCost)}</span>
                    {displayCurrency !== currentMarketplace.currency && (
                      <div className="text-xs text-gray-500">
                        -{currentMarketplace.symbol}{data.advertisingCost.toFixed(2)}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex justify-between text-red-600">
                  <span>- {getTranslation(currentLanguage, 'otherCosts')}</span>
                  <div className="text-right">
                    <span>-{formatCurrency(data.otherCosts)}</span>
                    {displayCurrency !== currentMarketplace.currency && (
                      <div className="text-xs text-gray-500">
                        -{currentMarketplace.symbol}{data.otherCosts.toFixed(2)}
                      </div>
                    )}
                  </div>
                </div>
                <div className="border-t pt-3 flex justify-between font-bold">
                  <span>{getTranslation(currentLanguage, 'netProfit')}</span>
                  <div className={`text-right ${getProfitColor(results.netProfit)}`}>
                    <span>{formatCurrency(results.netProfit)}</span>
                    {displayCurrency !== currentMarketplace.currency && (
                      <div className="text-xs text-gray-500">
                        {currentMarketplace.symbol}{results.netProfit.toFixed(2)}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Recommendations */}
            <div className="bg-blue-50 rounded-xl p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <AlertCircle className="h-5 w-5 mr-2 text-blue-600" />
                {getTranslation(currentLanguage, 'recommendations')}
              </h4>
              <div className="space-y-2 text-sm text-gray-700">
                {results.profitMargin < 10 && (
                  <p className="text-red-600">
                    • {getTranslation(currentLanguage, 'lowMarginWarning')}
                  </p>
                )}
                {results.profitMargin >= 20 && (
                  <p className="text-green-600">
                    • {getTranslation(currentLanguage, 'goodMarginMessage')}
                  </p>
                )}
                {results.roi < 50 && (
                  <p className="text-yellow-600">
                    • {getTranslation(currentLanguage, 'lowRoiWarning')}
                  </p>
                )}
                <p>• {getTranslation(currentLanguage, 'profitTip1')}</p>
                <p>• {getTranslation(currentLanguage, 'profitTip2')}</p>
                <p>• {getTranslation(currentLanguage, 'profitTip3')}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfitCalculator;