import React, { useState, useRef, useEffect } from 'react';
import { Upload, Download, Image as ImageIcon, Wand2, Eye, Loader2, AlertCircle, CheckCircle, Shield } from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
import { getTranslation } from '../../utils/translations';
import {
  productImageGenerator,
  type ImageGenerationRequest,
  type ImageGenerationResult,
  AMAZON_IMAGE_SPECS
} from '../../lib/ai/services/imageGenerator';
import { checkAIUsageLimit, recordAIUsage, shouldRequireTurnstileVerification, verifyTurnstileToken } from '../../utils/aiUsageLimit';
import TurnstileVerification from '../common/TurnstileVerification';

// 亚马逊图片类型定义
type AmazonImageType =
  | 'amazonMain' | 'amazonLifestyle' | 'amazonDetail' | 'amazonComparison' | 'amazonInfographic' | 'amazonAplus' | 'amazonBrandStore';

// 亚马逊图片配置映射
const AMAZON_IMAGE_CONFIG: Record<AmazonImageType, {
  aspectRatio: '1:1' | '3:4' | '4:3' | '9:16' | '16:9';
  style: 'product' | 'lifestyle' | 'studio' | 'scene';
  amazonSpec: keyof typeof AMAZON_IMAGE_SPECS;
  promptPrefix: string; // 用于优化prompt的前缀
}> = {
  amazonMain: {
    aspectRatio: '1:1', style: 'studio', amazonSpec: 'main',
    promptPrefix: 'Create a professional Amazon main image with pure white background (RGB 255,255,255), product centered and occupying 85% of the frame, studio lighting, high resolution, clean and minimal, no text or watermarks:'
  },
  amazonLifestyle: {
    aspectRatio: '1:1', style: 'lifestyle', amazonSpec: 'additional',
    promptPrefix: 'Create an Amazon lifestyle image showing the product in real-world usage scenario, natural environment, lifestyle context, appealing to target customers, demonstrating product benefits:'
  },
  amazonDetail: {
    aspectRatio: '1:1', style: 'product', amazonSpec: 'additional',
    promptPrefix: 'Create an Amazon detail image highlighting specific product features, close-up view, clear details, functional aspects, high quality, showing product craftsmanship:'
  },
  amazonComparison: {
    aspectRatio: '1:1', style: 'product', amazonSpec: 'additional',
    promptPrefix: 'Create an Amazon comparison image showing product variations, size comparisons, color options, or feature differences, clear layout, side-by-side comparison:'
  },
  amazonInfographic: {
    aspectRatio: '1:1', style: 'product', amazonSpec: 'additional',
    promptPrefix: 'Create an Amazon infographic image combining product visuals with text callouts, feature highlights, benefits, professional design, informative layout:'
  },
  amazonAplus: {
    aspectRatio: '16:9', style: 'lifestyle', amazonSpec: 'aplus',
    promptPrefix: 'Create a 16:9 Amazon A+ content image for brand storytelling, lifestyle context, premium feel, brand narrative, high-quality lifestyle photography:'
  },
  amazonBrandStore: {
    aspectRatio: '16:9', style: 'lifestyle', amazonSpec: 'brand',
    promptPrefix: 'Create a 16:9 Amazon brand store banner image, brand identity, premium aesthetic, store decoration, professional branding elements:'
  }
};

interface ProductImageGeneratorProps {
  onClose?: () => void;
}

const ProductImageGenerator: React.FC<ProductImageGeneratorProps> = () => {
  const { currentLanguage } = useLanguage();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 状态管理
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [usageStats, setUsageStats] = useState<{
    isLimited: boolean;
    remainingQuota: number;
    limitReason?: string;
    totalUsage: number;
  } | null>(null);

  // Turnstile验证状态
  const [showTurnstile, setShowTurnstile] = useState(false);
  const [turnstileToken, setTurnstileToken] = useState<string | null>(null);
  const [isVerifyingTurnstile, setIsVerifyingTurnstile] = useState(false);

  // 生成参数
  const [prompt, setPrompt] = useState('');
  const [amazonImageType, setAmazonImageType] = useState<AmazonImageType>('amazonMain');

  // 根据亚马逊图片类型获取配置
  const getCurrentConfig = () => AMAZON_IMAGE_CONFIG[amazonImageType];
  const { aspectRatio, style, amazonSpec, promptPrefix } = getCurrentConfig();

  // 获取用户限制配置
  const getUserDailyLimit = () => {
    // 统一使用aiUsageLimit中的配置：每天5次
    return 5;
  };

  // 更新使用统计
  const updateUsageStats = () => {
    const stats = checkAIUsageLimit('product-image-generator');
    setUsageStats(stats);
  };

  // 组件加载时更新使用统计
  useEffect(() => {
    updateUsageStats();
  }, []);

  // 获取翻译文本的辅助函数
  const t = (key: string) => getTranslation(currentLanguage, key);

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      setError(t('fileTypeError'));
      return;
    }

    // 验证文件大小 (最大10MB)
    if (file.size > 10 * 1024 * 1024) {
      setError(t('fileSizeError'));
      return;
    }

    // 读取文件并转换为base64
    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      setUploadedImage(result);
      setError(null);
    };
    reader.onerror = () => {
      setError(t('uploadError'));
    };
    reader.readAsDataURL(file);
  };

  // 生成图片
  const handleGenerateImage = async () => {
    if (!prompt.trim()) {
      setError(t('promptEmpty'));
      return;
    }

    // 检查使用限制 - 统一使用checkAIUsageLimit
    const currentUsageStats = checkAIUsageLimit('product-image-generator');
    if (currentUsageStats.isLimited) {
      setError(currentUsageStats.limitReason === 'daily_limit' ? t('usageExceeded') : t('usageLimited'));
      return;
    }

    // 检查是否需要Turnstile验证
    const needsVerification = shouldRequireTurnstileVerification('product-image-generator', currentUsageStats);

    if (needsVerification && !turnstileToken) {
      setShowTurnstile(true);
      return;
    }

    setIsGenerating(true);
    setError(null);
    setSuccess(null);

    try {
      // 构建优化的prompt
      const optimizedPrompt = `${promptPrefix} ${prompt.trim()}`;

      const request: ImageGenerationRequest = {
        prompt: optimizedPrompt,
        baseImage: uploadedImage || undefined,
        aspectRatio,
        style,
        amazonSpec
      };

      const result: ImageGenerationResult = await productImageGenerator.generateProductImage(request);

      setGeneratedImage(result.imageData);
      setSuccess(t('generateSuccess'));

      // 记录AI使用（成功）
      recordAIUsage('product-image-generator', true);
      
      // 更新使用统计
      updateUsageStats();

    } catch (err) {
      console.error(t('generateError'), err);
      setError(err instanceof Error ? err.message : t('generateError'));
      
      // 记录AI使用（失败）
      recordAIUsage('product-image-generator', false);
    } finally {
      setIsGenerating(false);
    }
  };

  // 处理Turnstile验证
  const handleTurnstileVerify = async (token: string) => {
    setIsVerifyingTurnstile(true);
    try {
      const isValid = await verifyTurnstileToken(token);
      if (isValid) {
        setTurnstileToken(token);
        setShowTurnstile(false);
        // 验证成功后自动继续生成
        setTimeout(() => {
          handleGenerateImage();
        }, 500);
      } else {
        setError(t('verificationFailed') || '验证失败，请重试');
      }
    } catch (error) {
      console.error('Turnstile verification error:', error);
      setError(t('verificationError') || '验证过程出错，请重试');
    } finally {
      setIsVerifyingTurnstile(false);
    }
  };

  const handleTurnstileError = (error: string) => {
    console.error('Turnstile error:', error);
    setError(t('verificationError') || '验证过程出错，请重试');
  };

  const handleTurnstileExpire = () => {
    setTurnstileToken(null);
  };

  // 下载生成的图片
  const handleDownloadImage = () => {
    if (!generatedImage) return;

    try {
      // 创建下载链接
      const link = document.createElement('a');
      link.href = generatedImage;
      link.download = `product-image-${Date.now()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      setSuccess(t('downloadSuccess'));
    } catch {
      setError(t('downloadError'));
    }
  };

  // 清除所有内容
  const handleClear = () => {
    setUploadedImage(null);
    setGeneratedImage(null);
    setPrompt('');
    setError(null);
    setSuccess(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      {/* 标题 */}
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {t('toolName')}
        </h2>
        <p className="text-gray-600">
          {t('toolDescription')}
        </p>
      </div>

      {/* 错误和成功提示 */}
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center">
          <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
          <span className="text-red-700">{error}</span>
        </div>
      )}
      
      {success && (
        <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center">
          <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
          <span className="text-green-700">{success}</span>
        </div>
      )}

      {/* 使用限制显示 */}
      {usageStats && (
        <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Shield className="w-5 h-5 text-blue-500 mr-2" />
              <span className="font-medium text-blue-900">{t('usageLimit')}</span>
            </div>
            <div className="text-sm text-blue-700">
              {t('remainingUses')}: {usageStats.remainingQuota}/{getUserDailyLimit()}
            </div>
          </div>
          {usageStats.isLimited && (
            <div className="mt-2 text-sm text-red-600">
              {t('freeUserLimit')}
            </div>
          )}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 左侧：参数配置 */}
        <div className="space-y-6">
          {/* 图片上传 */}
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
            <div className="text-center">
              {uploadedImage ? (
                <div className="space-y-4">
                  <img
                    src={uploadedImage}
                    alt={t('uploadedImage')}
                    className="max-w-full max-h-48 mx-auto rounded-lg shadow-md"
                  />
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    className="text-blue-600 hover:text-blue-700 font-medium"
                  >
                    {t('changeImage')}
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                  <div>
                    <button
                      onClick={() => fileInputRef.current?.click()}
                      className="text-blue-600 hover:text-blue-700 font-medium"
                    >
                      {t('uploadPrompt')}
                    </button>
                    <p className="text-sm text-gray-500 mt-1">
                      {t('uploadHint')}
                    </p>
                  </div>
                </div>
              )}
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="hidden"
            />
          </div>

          {/* 图片描述 */}
          <div>
            <label className="label">
              {t('promptRequired')}
            </label>
            <textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder={t('promptPlaceholder')}
              className="w-full h-24 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-none"
            />
          </div>

          {/* 亚马逊图片类型选择 */}
          <div>
            <label className="label">
              {t('amazonImageTypeLabel')}
            </label>
            <p className="text-sm text-gray-600 mb-3">
              {t('amazonImageTypeDescription')}
            </p>

            <select
              value={amazonImageType}
              onChange={(e) => setAmazonImageType(e.target.value as AmazonImageType)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            >
              <option value="amazonMain">{t('amazonMain')}</option>
              <option value="amazonLifestyle">{t('amazonLifestyle')}</option>
              <option value="amazonDetail">{t('amazonDetail')}</option>
              <option value="amazonComparison">{t('amazonComparison')}</option>
              <option value="amazonInfographic">{t('amazonInfographic')}</option>
              <option value="amazonAplus">{t('amazonAplus')}</option>
              <option value="amazonBrandStore">{t('amazonBrandStore')}</option>
            </select>

            {/* 当前选择的配置显示 */}
            <div className="mt-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div className="text-sm text-blue-800">
                <span className="font-medium">{t('currentConfig')}：</span>
                <span className="ml-2">
                  {aspectRatio} {t('aspectRatio')} • {style === 'studio' ? t('studioStyle') : style === 'lifestyle' ? t('lifestyleStyle') : style === 'product' ? t('productStyle') : t('sceneStyle')} • {t('amazonSpec')}
                </span>
              </div>
            </div>
          </div>

          {/* Turnstile验证 */}
          {showTurnstile && (
            <div className="mb-6 p-4 border border-blue-200 rounded-lg bg-blue-50">
              <TurnstileVerification
                onVerify={handleTurnstileVerify}
                onError={handleTurnstileError}
                onExpire={handleTurnstileExpire}
                title={t('securityVerification') || '安全验证'}
                description={t('verificationDescription') || '为了防止滥用，我们需要验证您是真实用户'}
                className="max-w-md mx-auto"
              />
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex space-x-3">
            <button
              onClick={handleGenerateImage}
              disabled={isGenerating || !prompt.trim() || (usageStats?.isLimited === true) || isVerifyingTurnstile}
              className="flex-1 btn-primary disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {t('generating')}
                </>
              ) : (
                <>
                  <Wand2 className="w-4 h-4 mr-2" />
                  {t('generateButton')}
                </>
              )}
            </button>

            <button
              onClick={handleClear}
              className="btn-outline"
            >
              {t('clearButton')}
            </button>
          </div>
        </div>

        {/* 右侧：预览区域 */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <Eye className="w-5 h-5 mr-2" />
            {t('previewTitle')}
          </h3>
          
          <div className="border border-gray-300 rounded-lg p-4 min-h-96 flex items-center justify-center bg-gray-50">
            {generatedImage ? (
              <div className="space-y-4 text-center">
                <img
                  src={generatedImage}
                  alt={t('generatedImage')}
                  className="max-w-full max-h-80 mx-auto rounded-lg shadow-md"
                />
                <button
                  onClick={handleDownloadImage}
                  className="btn-secondary flex items-center mx-auto"
                >
                  <Download className="w-4 h-4 mr-2" />
                  {t('downloadButton')}
                </button>
              </div>
            ) : (
              <div className="text-center text-gray-500">
                <ImageIcon className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                <p>{t('previewEmpty')}</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* AI服务说明 */}
      <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h4 className="font-medium text-blue-900 mb-2">🤖 {t('aiServiceTitle')}</h4>
        <p className="text-sm text-blue-800 mb-2">
          {t('aiServiceDescription')}
        </p>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• {t('feature1')}</li>
          <li>• {t('feature2')}</li>
          <li>• {t('feature3')}</li>
          <li>• {t('feature4')}</li>
          <li>• {t('feature5')}</li>
        </ul>
      </div>

      {/* 使用说明 */}
      <div className="mt-4 p-4 bg-blue-50 rounded-lg">
        <h4 className="font-medium text-blue-900 mb-2">{t('instructionsTitle')}</h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>{t('instruction1')}</li>
          <li>{t('instruction2')}</li>
          <li>{t('instruction3')}</li>
          <li>{t('instruction4')}</li>
          <li>{t('instruction5')}</li>
        </ul>
      </div>
    </div>
  );
};

export default ProductImageGenerator;
