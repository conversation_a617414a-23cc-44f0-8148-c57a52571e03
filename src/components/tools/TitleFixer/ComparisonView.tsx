import React, { useState } from 'react';
import { <PERSON>, EyeOff, ArrowRight, Plus, Minus, Edit3, Info } from 'lucide-react';
import { useLanguage } from '../../../hooks/useLanguage';
import { getTranslation } from '../../../utils/translations';
import { generateTextDiff, generateChangeStats, type TextDiff } from '../../../utils/textComparison';

interface ComparisonViewProps {
  originalTitle: string;
  fixedTitle: string;
  className?: string;
}

const ComparisonView: React.FC<ComparisonViewProps> = ({
  originalTitle,
  fixedTitle,
  className = ''
}) => {
  const { currentLanguage } = useLanguage();
  const [isExpanded, setIsExpanded] = useState(false);
  
  const diffs = generateTextDiff(originalTitle, fixedTitle);
  const stats = generateChangeStats(diffs);
  
  const renderDiffText = (diffs: TextDiff[]) => {
    return diffs.map((diff, index) => {
      switch (diff.type) {
        case 'added':
          return (
            <span
              key={index}
              className="bg-green-100 text-green-800 px-1 py-0.5 rounded border border-green-200 font-medium"
              title={getTranslation(currentLanguage, 'tfAddedText')}
            >
              {diff.text}
            </span>
          );
        case 'removed':
          return (
            <span
              key={index}
              className="bg-red-100 text-red-800 px-1 py-0.5 rounded border border-red-200 line-through opacity-75"
              title={getTranslation(currentLanguage, 'tfRemovedText')}
            >
              {diff.text}
            </span>
          );
        case 'modified': {
          const [original, modified] = diff.text.split(' → ');
          return (
            <span key={index} className="inline-flex items-center">
              <span className="bg-red-100 text-red-800 px-1 py-0.5 rounded border border-red-200 line-through opacity-75">
                {original}
              </span>
              <ArrowRight className="h-3 w-3 mx-1 text-gray-400" />
              <span className="bg-green-100 text-green-800 px-1 py-0.5 rounded border border-green-200 font-medium">
                {modified}
              </span>
            </span>
          );
        }
        case 'unchanged':
          return (
            <span key={index} className="text-gray-700">
              {diff.text}
            </span>
          );
        default:
          return null;
      }
    });
  };

  return (
    <div className={`bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-xl shadow-lg ${className}`}>
      {/* Header */}
      <div 
        className="p-4 cursor-pointer hover:bg-blue-100/50 transition-colors rounded-t-xl"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {isExpanded ? (
              <EyeOff className="h-5 w-5 text-blue-600 mr-2" />
            ) : (
              <Eye className="h-5 w-5 text-blue-600 mr-2" />
            )}
            <h4 className="text-lg font-medium text-blue-900">
              {getTranslation(currentLanguage, 'tfComparisonView')}
            </h4>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* Change Statistics */}
            <div className="flex items-center space-x-2 text-sm">
              {stats.added > 0 && (
                <div className="flex items-center bg-green-100 text-green-700 px-2 py-1 rounded">
                  <Plus className="h-3 w-3 mr-1" />
                  {stats.added}
                </div>
              )}
              {stats.removed > 0 && (
                <div className="flex items-center bg-red-100 text-red-700 px-2 py-1 rounded">
                  <Minus className="h-3 w-3 mr-1" />
                  {stats.removed}
                </div>
              )}
              {stats.modified > 0 && (
                <div className="flex items-center bg-yellow-100 text-yellow-700 px-2 py-1 rounded">
                  <Edit3 className="h-3 w-3 mr-1" />
                  {stats.modified}
                </div>
              )}
            </div>
            
            <span className="text-sm text-blue-600">
              {isExpanded ? getTranslation(currentLanguage, 'tfHideComparison') : getTranslation(currentLanguage, 'tfShowComparison')}
            </span>
            
            <svg 
              className={`w-5 h-5 text-blue-600 transition-transform ${isExpanded ? 'rotate-180' : ''}`} 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>
      </div>

      {/* Expanded Content */}
      {isExpanded && (
        <div className="border-t border-blue-200 p-4 space-y-4">
          {/* Legend */}
          <div className="flex flex-wrap items-center gap-4 text-xs bg-white/70 p-3 rounded-lg">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-100 border border-green-200 rounded mr-2"></div>
              <span className="text-green-700">{getTranslation(currentLanguage, 'tfAddedText')}</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-red-100 border border-red-200 rounded mr-2"></div>
              <span className="text-red-700">{getTranslation(currentLanguage, 'tfRemovedText')}</span>
            </div>
            <div className="flex items-center">
              <ArrowRight className="h-3 w-3 text-gray-400 mr-2" />
              <span className="text-gray-600">{getTranslation(currentLanguage, 'tfModifiedText')}</span>
            </div>
          </div>

          {/* Original Title */}
          <div className="space-y-2">
            <h5 className="text-sm font-medium text-gray-700 flex items-center">
              <div className="w-2 h-2 bg-red-400 rounded-full mr-2"></div>
              {getTranslation(currentLanguage, 'tfOriginalTitle')}
            </h5>
            <div className="bg-white border border-gray-200 rounded-lg p-3 text-sm leading-relaxed">
              {originalTitle}
            </div>
          </div>

          {/* Fixed Title */}
          <div className="space-y-2">
            <h5 className="text-sm font-medium text-gray-700 flex items-center">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
              {getTranslation(currentLanguage, 'tfFixedTitles')}
            </h5>
            <div className="bg-white border border-gray-200 rounded-lg p-3 text-sm leading-relaxed">
              {fixedTitle}
            </div>
          </div>

          {/* Diff View */}
          <div className="space-y-2">
            <h5 className="text-sm font-medium text-gray-700 flex items-center">
              <Edit3 className="h-4 w-4 mr-2" />
              {getTranslation(currentLanguage, 'tfChangesHighlight')}
            </h5>
            <div className="bg-white border border-gray-200 rounded-lg p-3 text-sm leading-relaxed">
              {renderDiffText(diffs)}
            </div>
          </div>

          {/* Statistics */}
          <div className="bg-white/70 rounded-lg p-3">
            <h6 className="text-xs font-medium text-gray-600 mb-2 flex items-center">
              <Info className="h-3 w-3 mr-1" />
              {getTranslation(currentLanguage, 'tfChangeStats')}
            </h6>
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 text-xs">
              <div className="text-center">
                <div className="text-green-600 font-bold text-lg">{stats.added}</div>
                <div className="text-gray-500">{getTranslation(currentLanguage, 'tfAdded')}</div>
              </div>
              <div className="text-center">
                <div className="text-red-600 font-bold text-lg">{stats.removed}</div>
                <div className="text-gray-500">{getTranslation(currentLanguage, 'tfRemoved')}</div>
              </div>
              <div className="text-center">
                <div className="text-yellow-600 font-bold text-lg">{stats.modified}</div>
                <div className="text-gray-500">{getTranslation(currentLanguage, 'tfModified')}</div>
              </div>
              <div className="text-center">
                <div className="text-blue-600 font-bold text-lg">{stats.totalChanges}</div>
                <div className="text-gray-500">{getTranslation(currentLanguage, 'tfTotal')}</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ComparisonView;
