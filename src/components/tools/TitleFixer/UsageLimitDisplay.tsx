import React from 'react';
import { Clock, AlertTriangle, Info, RefreshCw, Zap, Database } from 'lucide-react';
import { useLanguage } from '../../../hooks/useLanguage';
import { getTranslation } from '../../../utils/translations';
import { UsageStats, formatTimeUntilReset, getLimitExplanation } from '../../../utils/aiUsageLimit';

interface UsageLimitDisplayProps {
  usageStats: UsageStats;
  isVisible: boolean;
  onRefresh?: () => void;
}

const UsageLimitDisplay: React.FC<UsageLimitDisplayProps> = ({
  usageStats,
  isVisible,
  onRefresh
}) => {
  const { currentLanguage } = useLanguage();

  if (!isVisible) return null;

  const getLimitIcon = () => {
    if (usageStats.limitReason === 'session_limit') return <RefreshCw className="h-4 w-4" />;
    if (usageStats.limitReason === 'cooldown') return <Clock className="h-4 w-4" />;
    return <AlertTriangle className="h-4 w-4" />;
  };

  const getLimitColor = () => {
    if (usageStats.limitReason === 'session_limit') return 'blue';
    if (usageStats.limitReason === 'cooldown') return 'amber';
    return 'red';
  };

  const color = getLimitColor();

  return (
    <div className="space-y-4">
      {/* 限制提醒卡片 */}
      {usageStats.isLimited && (
        <div className={`bg-${color}-50 border border-${color}-200 rounded-xl p-4`}>
          <div className="flex items-start">
            <div className={`text-${color}-600 mr-3 mt-0.5`}>
              {getLimitIcon()}
            </div>
            <div className="flex-1">
              <h4 className={`text-${color}-900 font-medium text-sm mb-1`}>
                {getTranslation(currentLanguage, 'tfUsageLimitExceeded')}
              </h4>
              <p className={`text-${color}-800 text-xs mb-3`}>
                {getLimitExplanation(usageStats.limitReason || '', currentLanguage)}
              </p>
              
              {/* 重置时间 */}
              <div className="flex items-center justify-between text-xs">
                <span className={`text-${color}-700`}>
                  {getTranslation(currentLanguage, 'tfNextResetTime')}:
                </span>
                <span className={`text-${color}-900 font-medium`}>
                  {formatTimeUntilReset(usageStats.nextResetTime, currentLanguage)}
                </span>
              </div>

              {/* 会话限制特殊处理 */}
              {usageStats.limitReason === 'session_limit' && onRefresh && (
                <button
                  onClick={onRefresh}
                  className={`mt-3 w-full flex items-center justify-center px-3 py-2 bg-${color}-600 hover:bg-${color}-700 text-white rounded-lg text-xs font-medium transition-colors`}
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  {getTranslation(currentLanguage, 'tfRefreshToReset')}
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 使用统计卡片 */}
      <div className="bg-gray-50 border border-gray-200 rounded-xl p-4">
        <div className="flex items-center mb-3">
          <Info className="h-4 w-4 text-gray-600 mr-2" />
          <h4 className="text-gray-900 font-medium text-sm">
            {getTranslation(currentLanguage, 'tfUsageStats')}
          </h4>
        </div>

        <div className="grid grid-cols-2 gap-3 mb-4">
          <div className="text-center">
            <div className="text-lg font-bold text-gray-900">
              {usageStats.totalUsage}
            </div>
            <div className="text-xs text-gray-600">
              {getTranslation(currentLanguage, 'tfTodayUsage')}
            </div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-green-600">
              {Math.max(0, 10 - usageStats.totalUsage)}
            </div>
            <div className="text-xs text-gray-600">
              {getTranslation(currentLanguage, 'tfRemainingQuota')}
            </div>
          </div>
        </div>

        {/* 进度条 */}
        <div className="mb-4">
          <div className="flex justify-between text-xs text-gray-600 mb-1">
            <span>{getTranslation(currentLanguage, 'tfDailyUsageProgress')}</span>
            <span>{usageStats.totalUsage}/10</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${
                (10 - usageStats.totalUsage) <= 1 ? 'bg-red-500' :
                (10 - usageStats.totalUsage) <= 2 ? 'bg-amber-500' : 'bg-green-500'
              }`}
              style={{ width: `${Math.min((usageStats.totalUsage / 10) * 100, 100)}%` }}
            />
          </div>
        </div>

        {/* 使用小贴士 */}
        <div className="border-t border-gray-200 pt-3">
          <h5 className="text-xs font-medium text-gray-700 mb-2">
            {getTranslation(currentLanguage, 'tfUsageTips')}
          </h5>
          <div className="space-y-1">
            <div className="flex items-start text-xs text-gray-600">
              <Database className="h-3 w-3 mr-1 mt-0.5 text-green-600 flex-shrink-0" />
              <span>{getTranslation(currentLanguage, 'tfUsageTip1')}</span>
            </div>
            <div className="flex items-start text-xs text-gray-600">
              <Info className="h-3 w-3 mr-1 mt-0.5 text-blue-600 flex-shrink-0" />
              <span>{getTranslation(currentLanguage, 'tfUsageTip2')}</span>
            </div>
            <div className="flex items-start text-xs text-gray-600">
              <Zap className="h-3 w-3 mr-1 mt-0.5 text-purple-600 flex-shrink-0" />
              <span>{getTranslation(currentLanguage, 'tfUsageTip3')}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UsageLimitDisplay;
