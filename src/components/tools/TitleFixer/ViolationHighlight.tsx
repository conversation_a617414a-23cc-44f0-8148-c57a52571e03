import React, { useState } from 'react';
import { AlertTriangle, Eye, EyeOff, Info, X, Zap } from 'lucide-react';
import { useLanguage } from '../../../hooks/useLanguage';
import { getTranslation } from '../../../utils/translations';
import { generateViolationHighlights, splitTextWithHighlights, type ViolationHighlight } from '../../../utils/textComparison';
import { type Violation } from '../../../utils/complianceCheck';

interface ViolationHighlightProps {
  text: string;
  violations: Violation[];
  className?: string;
}

const ViolationHighlightComponent: React.FC<ViolationHighlightProps> = ({
  text,
  violations,
  className = ''
}) => {
  const { currentLanguage } = useLanguage();
  const [isEnabled, setIsEnabled] = useState(true);
  const [selectedViolation, setSelectedViolation] = useState<ViolationHighlight | null>(null);
  
  const highlights = generateViolationHighlights(text, violations);
  const segments = splitTextWithHighlights(text, highlights);
  
  const getViolationColor = (type: string, severity: string) => {
    const baseColors = {
      special_chars: severity === 'error' ? 'bg-red-100 border-red-300 text-red-800' : 'bg-orange-100 border-orange-300 text-orange-800',
      repeated_words: severity === 'error' ? 'bg-purple-100 border-purple-300 text-purple-800' : 'bg-pink-100 border-pink-300 text-pink-800',
      length: 'bg-yellow-100 border-yellow-300 text-yellow-800'
    };
    return baseColors[type as keyof typeof baseColors] || 'bg-gray-100 border-gray-300 text-gray-800';
  };

  const getViolationIcon = (type: string) => {
    switch (type) {
      case 'special_chars':
        return <Zap className="h-3 w-3" />;
      case 'repeated_words':
        return <AlertTriangle className="h-3 w-3" />;
      case 'length':
        return <Info className="h-3 w-3" />;
      default:
        return <AlertTriangle className="h-3 w-3" />;
    }
  };

  const renderHighlightedText = () => {
    if (!isEnabled || highlights.length === 0) {
      return <span className="text-gray-700">{text}</span>;
    }

    return segments.map((segment, index) => {
      if (!segment.isHighlighted || !segment.highlight) {
        return (
          <span key={index} className="text-gray-700">
            {segment.text}
          </span>
        );
      }

      const { highlight } = segment;
      const colorClass = getViolationColor(highlight.type, highlight.severity);

      return (
        <span
          key={index}
          className={`${colorClass} px-1 py-0.5 rounded border cursor-pointer hover:shadow-sm transition-all relative group`}
          onClick={() => setSelectedViolation(highlight)}
          title={getTranslation(currentLanguage, 'tfClickToSeeDetails')}
        >
          {segment.text}
          {/* Tooltip */}
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-10">
            {highlight.message}
          </div>
        </span>
      );
    });
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Control Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <button
            onClick={() => setIsEnabled(!isEnabled)}
            className={`flex items-center px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
              isEnabled
                ? 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                : 'bg-gray-100 text-gray-500 hover:bg-gray-200'
            }`}
          >
            {isEnabled ? (
              <Eye className="h-4 w-4 mr-2" />
            ) : (
              <EyeOff className="h-4 w-4 mr-2" />
            )}
            {getTranslation(currentLanguage, 'tfViolationHighlight')}
          </button>
        </div>

        {highlights.length > 0 && (
          <div className="flex items-center space-x-2 text-xs">
            <span className="text-gray-500">
              {highlights.length} {getTranslation(currentLanguage, 'tfViolationCount')}
            </span>
          </div>
        )}
      </div>

      {/* Highlighted Text */}
      <div className="bg-white border border-gray-200 rounded-lg p-4 min-h-[60px]">
        <div className="text-sm leading-relaxed">
          {renderHighlightedText()}
        </div>
      </div>

      {/* Legend */}
      {isEnabled && highlights.length > 0 && (
        <div className="bg-gray-50 rounded-lg p-3">
          <h6 className="text-xs font-medium text-gray-600 mb-2">{getTranslation(currentLanguage, 'tfViolationHighlight')}</h6>
          <div className="flex flex-wrap gap-3 text-xs">
            {violations.some(v => v.type === 'special_chars') && (
              <div className="flex items-center">
                <div className="w-3 h-3 bg-red-100 border border-red-300 rounded mr-2"></div>
                <span className="text-gray-600">{getTranslation(currentLanguage, 'tfSpecialCharViolation')}</span>
              </div>
            )}
            {violations.some(v => v.type === 'repeated_words') && (
              <div className="flex items-center">
                <div className="w-3 h-3 bg-purple-100 border border-purple-300 rounded mr-2"></div>
                <span className="text-gray-600">{getTranslation(currentLanguage, 'tfRepeatedWordViolation')}</span>
              </div>
            )}
            {violations.some(v => v.type === 'length') && (
              <div className="flex items-center">
                <div className="w-3 h-3 bg-yellow-100 border border-yellow-300 rounded mr-2"></div>
                <span className="text-gray-600">{getTranslation(currentLanguage, 'tfLengthViolation')}</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Violation Detail Modal */}
      {selectedViolation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl max-w-md w-full shadow-xl">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900 flex items-center">
                  {getViolationIcon(selectedViolation.type)}
                  <span className="ml-2">{getTranslation(currentLanguage, 'tfViolationDetails')}</span>
                </h3>
                <button
                  onClick={() => setSelectedViolation(null)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
            </div>
            
            <div className="p-4 space-y-3">
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-1">{getTranslation(currentLanguage, 'tfViolationHighlight')}</h4>
                <div className={`inline-flex items-center px-2 py-1 rounded text-xs ${getViolationColor(selectedViolation.type, selectedViolation.severity)}`}>
                  {getViolationIcon(selectedViolation.type)}
                  <span className="ml-1">
                    {selectedViolation.type === 'special_chars' && getTranslation(currentLanguage, 'tfSpecialCharViolation')}
                    {selectedViolation.type === 'repeated_words' && getTranslation(currentLanguage, 'tfRepeatedWordViolation')}
                    {selectedViolation.type === 'length' && getTranslation(currentLanguage, 'tfLengthViolation')}
                  </span>
                </div>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-1">详细说明</h4>
                <p className="text-sm text-gray-600">{selectedViolation.message}</p>
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-1">违规文本</h4>
                <div className="bg-gray-50 rounded p-2 text-sm font-mono">
                  "{text.slice(selectedViolation.start, selectedViolation.end)}"
                </div>
              </div>

              {selectedViolation.violationData && (
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-1">额外信息</h4>
                  <div className="bg-gray-50 rounded p-2 text-xs">
                    <pre className="whitespace-pre-wrap">
                      {JSON.stringify(selectedViolation.violationData, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>
            
            <div className="p-4 border-t border-gray-200">
              <button
                onClick={() => setSelectedViolation(null)}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                {getTranslation(currentLanguage, 'close') || '关闭'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ViolationHighlightComponent;
