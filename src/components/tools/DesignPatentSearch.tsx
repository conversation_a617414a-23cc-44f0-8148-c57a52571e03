import React, { useState } from 'react';
import { Search, FileText, Eye, Download, AlertCircle, Info, ExternalLink } from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
import { getTranslation } from '../../locales';

interface PatentResult {
  id: string;
  title: string;
  patentNumber: string;
  applicant: string;
  filingDate: string;
  publicationDate: string;
  status: 'granted' | 'pending' | 'expired';
  description: string;
  imageUrl?: string;
  classification: string;
}

const DesignPatentSearch: React.FC = () => {
  const { currentLanguage } = useLanguage();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchType, setSearchType] = useState<'keyword' | 'number' | 'applicant'>('keyword');
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<PatentResult[]>([]);
  const [selectedPatent, setSelectedPatent] = useState<PatentResult | null>(null);
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null);

  // Mock search function - in real implementation, this would call actual patent API
  const searchPatents = async () => {
    if (!searchQuery.trim()) {
      setMessage({ type: 'error', text: getTranslation(currentLanguage, 'searchQueryRequired') });
      return;
    }

    setIsLoading(true);
    setMessage(null);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Mock results based on search query
      const mockResults: PatentResult[] = [
        {
          id: '1',
          title: getTranslation(currentLanguage, 'mockPatent1Title'),
          patentNumber: 'USD123456',
          applicant: 'Apple Inc.',
          filingDate: '2023-01-15',
          publicationDate: '2023-06-20',
          status: 'granted',
          description: getTranslation(currentLanguage, 'mockPatent1Desc'),
          classification: 'D14/138',
        },
        {
          id: '2',
          title: getTranslation(currentLanguage, 'mockPatent2Title'),
          patentNumber: 'USD789012',
          applicant: 'Samsung Electronics',
          filingDate: '2023-03-10',
          publicationDate: '2023-08-15',
          status: 'pending',
          description: getTranslation(currentLanguage, 'mockPatent2Desc'),
          classification: 'D14/341',
        },
        {
          id: '3',
          title: getTranslation(currentLanguage, 'mockPatent3Title'),
          patentNumber: 'USD345678',
          applicant: 'Google LLC',
          filingDate: '2022-11-20',
          publicationDate: '2023-04-25',
          status: 'granted',
          description: getTranslation(currentLanguage, 'mockPatent3Desc'),
          classification: 'D14/456',
        }
      ];

      setResults(mockResults);
      setMessage({ 
        type: 'success', 
        text: getTranslation(currentLanguage, 'searchCompleted').replace('{count}', mockResults.length.toString())
      });
    } catch (error) {
      setMessage({ type: 'error', text: getTranslation(currentLanguage, 'searchError') });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'granted': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'expired': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'granted': return getTranslation(currentLanguage, 'statusGranted');
      case 'pending': return getTranslation(currentLanguage, 'statusPending');
      case 'expired': return getTranslation(currentLanguage, 'statusExpired');
      default: return status;
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center">
        <div className="flex items-center justify-center mb-4">
          <div className="p-3 bg-gray-100 rounded-full">
            <Search className="h-8 w-8 text-gray-600" />
          </div>
        </div>
        <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
          {getTranslation(currentLanguage, 'designPatentSearch')}
        </h1>
        <p className="text-lg text-gray-600 max-w-3xl mx-auto">
          {getTranslation(currentLanguage, 'designPatentSearchDesc')}
        </p>
      </div>

      {/* Development Notice */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0" />
          <div>
            <h3 className="text-sm font-medium text-yellow-800 mb-1">
              {getTranslation(currentLanguage, 'developmentNotice')}
            </h3>
            <p className="text-sm text-yellow-700">
              {getTranslation(currentLanguage, 'developmentNoticeDesc')}
            </p>
          </div>
        </div>
      </div>

      {/* Search Section */}
      <div className="card p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
          <Search className="h-5 w-5 mr-2 text-gray-600" />
          {getTranslation(currentLanguage, 'searchPatents')}
        </h2>
        
        <div className="space-y-4">
          {/* Search Type Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {getTranslation(currentLanguage, 'searchType')}
            </label>
            <div className="flex flex-wrap gap-2">
              {[
                { value: 'keyword', label: getTranslation(currentLanguage, 'searchByKeyword') },
                { value: 'number', label: getTranslation(currentLanguage, 'searchByNumber') },
                { value: 'applicant', label: getTranslation(currentLanguage, 'searchByApplicant') }
              ].map((type) => (
                <button
                  key={type.value}
                  onClick={() => setSearchType(type.value as any)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    searchType === type.value
                      ? 'bg-gray-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {type.label}
                </button>
              ))}
            </div>
          </div>

          {/* Search Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {getTranslation(currentLanguage, 'searchQuery')}
            </label>
            <div className="flex gap-3">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder={getTranslation(currentLanguage, 'searchPlaceholder')}
                className="input flex-1"
                onKeyPress={(e) => e.key === 'Enter' && searchPatents()}
              />
              <button
                onClick={searchPatents}
                disabled={isLoading}
                className="btn-primary px-6 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    {getTranslation(currentLanguage, 'searching')}
                  </div>
                ) : (
                  <>
                    <Search className="h-4 w-4 mr-2" />
                    {getTranslation(currentLanguage, 'search')}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Message Display */}
      {message && (
        <div className={`p-4 rounded-lg ${
          message.type === 'success' ? 'bg-green-50 text-green-800 border border-green-200' :
          message.type === 'error' ? 'bg-red-50 text-red-800 border border-red-200' :
          'bg-blue-50 text-blue-800 border border-blue-200'
        }`}>
          <div className="flex items-start">
            {message.type === 'success' && <div className="h-5 w-5 text-green-600 mt-0.5 mr-3">✓</div>}
            {message.type === 'error' && <AlertCircle className="h-5 w-5 text-red-600 mt-0.5 mr-3" />}
            {message.type === 'info' && <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-3" />}
            <p className="text-sm">{message.text}</p>
          </div>
        </div>
      )}

      {/* Search Results */}
      {results.length > 0 && (
        <div className="card p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <FileText className="h-5 w-5 mr-2 text-gray-600" />
            {getTranslation(currentLanguage, 'searchResults')} ({results.length})
          </h2>
          
          <div className="space-y-4">
            {results.map((patent) => (
              <div key={patent.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="font-semibold text-gray-900">{patent.title}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(patent.status)}`}>
                        {getStatusText(patent.status)}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 mb-3">
                      <div>
                        <span className="font-medium">{getTranslation(currentLanguage, 'patentNumber')}:</span> {patent.patentNumber}
                      </div>
                      <div>
                        <span className="font-medium">{getTranslation(currentLanguage, 'applicant')}:</span> {patent.applicant}
                      </div>
                      <div>
                        <span className="font-medium">{getTranslation(currentLanguage, 'filingDate')}:</span> {patent.filingDate}
                      </div>
                      <div>
                        <span className="font-medium">{getTranslation(currentLanguage, 'publicationDate')}:</span> {patent.publicationDate}
                      </div>
                    </div>
                    
                    <p className="text-gray-700 text-sm mb-3">{patent.description}</p>
                    
                    <div className="flex items-center gap-4">
                      <button
                        onClick={() => setSelectedPatent(patent)}
                        className="btn-outline text-sm px-4 py-2"
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        {getTranslation(currentLanguage, 'viewDetails')}
                      </button>
                      <button className="text-gray-500 hover:text-gray-700 text-sm flex items-center">
                        <ExternalLink className="h-4 w-4 mr-1" />
                        {getTranslation(currentLanguage, 'viewOriginal')}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Patent Detail Modal */}
      {selectedPatent && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900">
                  {getTranslation(currentLanguage, 'patentDetails')}
                </h2>
                <button
                  onClick={() => setSelectedPatent(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">{selectedPatent.title}</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-gray-700">{getTranslation(currentLanguage, 'patentNumber')}:</span>
                      <p className="text-gray-600">{selectedPatent.patentNumber}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">{getTranslation(currentLanguage, 'applicant')}:</span>
                      <p className="text-gray-600">{selectedPatent.applicant}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">{getTranslation(currentLanguage, 'filingDate')}:</span>
                      <p className="text-gray-600">{selectedPatent.filingDate}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">{getTranslation(currentLanguage, 'publicationDate')}:</span>
                      <p className="text-gray-600">{selectedPatent.publicationDate}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">{getTranslation(currentLanguage, 'status')}:</span>
                      <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedPatent.status)}`}>
                        {getStatusText(selectedPatent.status)}
                      </span>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">{getTranslation(currentLanguage, 'classification')}:</span>
                      <p className="text-gray-600">{selectedPatent.classification}</p>
                    </div>
                  </div>
                </div>
                
                <div>
                  <span className="font-medium text-gray-700">{getTranslation(currentLanguage, 'description')}:</span>
                  <p className="text-gray-600 mt-1">{selectedPatent.description}</p>
                </div>
              </div>
              
              <div className="flex justify-end gap-3 mt-6">
                <button
                  onClick={() => setSelectedPatent(null)}
                  className="btn-outline px-4 py-2"
                >
                  {getTranslation(currentLanguage, 'close')}
                </button>
                <button className="btn-primary px-4 py-2">
                  <Download className="h-4 w-4 mr-2" />
                  {getTranslation(currentLanguage, 'downloadReport')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DesignPatentSearch;
