import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Calculator, ArrowRightLeft, Package, Ruler, Weight, Droplets, Thermometer, Zap, Copy, Check, RotateCcw } from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
import { getTranslation } from '../../utils/translations';
import { recordToolCalculation } from '../../utils/analytics';

interface ConversionCategory {
  id: string;
  name: string;
  icon: React.ReactNode;
  color: string;
  units: ConversionUnit[];
}

interface ConversionUnit {
  id: string;
  name: string;
  symbol: string;
  toBase: number;
  precision?: number;
}

const UnitConverter = () => {
  const { currentLanguage } = useLanguage();
  const [selectedCategory, setSelectedCategory] = useState('length');
  const [fromUnit, setFromUnit] = useState('');
  const [toUnit, setToUnit] = useState('');
  const [inputValue, setInputValue] = useState('');
  const [result, setResult] = useState('');
  const [copiedResult, setCopiedResult] = useState(false);
  const [conversionHistory, setConversionHistory] = useState<Array<{
    from: string;
    to: string;
    value: string;
    result: string;
    category: string;
    timestamp: Date;
  }>>([]);

  const categories: ConversionCategory[] = useMemo(() => [
    {
      id: 'length',
      name: getTranslation(currentLanguage, 'ucCategoryLength'),
      icon: <Ruler className="h-5 w-5" />,
      color: 'bg-blue-500',
      units: [
        { id: 'mm', name: getTranslation(currentLanguage, 'ucUnitMm'), symbol: 'mm', toBase: 0.001 },
        { id: 'cm', name: getTranslation(currentLanguage, 'ucUnitCm'), symbol: 'cm', toBase: 0.01 },
        { id: 'm', name: getTranslation(currentLanguage, 'ucUnitM'), symbol: 'm', toBase: 1 },
        { id: 'km', name: getTranslation(currentLanguage, 'ucUnitKm'), symbol: 'km', toBase: 1000 },
        { id: 'in', name: getTranslation(currentLanguage, 'ucUnitIn'), symbol: 'in', toBase: 0.0254 },
        { id: 'ft', name: getTranslation(currentLanguage, 'ucUnitFt'), symbol: 'ft', toBase: 0.3048 },
        { id: 'yd', name: getTranslation(currentLanguage, 'ucUnitYd'), symbol: 'yd', toBase: 0.9144 },
        { id: 'mil', name: getTranslation(currentLanguage, 'ucUnitMil'), symbol: 'mil', toBase: 1609.34 }
      ]
    },
    {
      id: 'weight',
      name: getTranslation(currentLanguage, 'ucCategoryWeight'),
      icon: <Weight className="h-5 w-5" />,
      color: 'bg-green-500',
      units: [
        { id: 'mg', name: getTranslation(currentLanguage, 'ucUnitMg'), symbol: 'mg', toBase: 0.000001 },
        { id: 'g', name: getTranslation(currentLanguage, 'ucUnitG'), symbol: 'g', toBase: 0.001 },
        { id: 'kg', name: getTranslation(currentLanguage, 'ucUnitKg'), symbol: 'kg', toBase: 1 },
        { id: 't', name: getTranslation(currentLanguage, 'ucUnitT'), symbol: 't', toBase: 1000 },
        { id: 'oz', name: getTranslation(currentLanguage, 'ucUnitOz'), symbol: 'oz', toBase: 0.0283495 },
        { id: 'lb', name: getTranslation(currentLanguage, 'ucUnitLb'), symbol: 'lb', toBase: 0.453592 },
        { id: 'st', name: getTranslation(currentLanguage, 'ucUnitSt'), symbol: 'st', toBase: 6.35029 },
        { id: 'ton', name: getTranslation(currentLanguage, 'ucUnitTon'), symbol: 'ton', toBase: 907.185 }
      ]
    },
    {
      id: 'volume',
      name: getTranslation(currentLanguage, 'ucCategoryVolume'),
      icon: <Droplets className="h-5 w-5" />,
      color: 'bg-cyan-500',
      units: [
        { id: 'ml', name: getTranslation(currentLanguage, 'ucUnitMl'), symbol: 'ml', toBase: 0.001 },
        { id: 'l', name: getTranslation(currentLanguage, 'ucUnitL'), symbol: 'L', toBase: 1 },
        { id: 'm3', name: getTranslation(currentLanguage, 'ucUnitM3'), symbol: 'm³', toBase: 1000 },
        { id: 'floz', name: getTranslation(currentLanguage, 'ucUnitFloz'), symbol: 'fl oz', toBase: 0.0295735 },
        { id: 'cup', name: getTranslation(currentLanguage, 'ucUnitCup'), symbol: 'cup', toBase: 0.236588 },
        { id: 'pt', name: getTranslation(currentLanguage, 'ucUnitPt'), symbol: 'pt', toBase: 0.473176 },
        { id: 'qt', name: getTranslation(currentLanguage, 'ucUnitQt'), symbol: 'qt', toBase: 0.946353 },
        { id: 'gal', name: getTranslation(currentLanguage, 'ucUnitGal'), symbol: 'gal', toBase: 3.78541 }
      ]
    },
    {
      id: 'area',
      name: getTranslation(currentLanguage, 'ucCategoryArea'),
      icon: <Package className="h-5 w-5" />,
      color: 'bg-purple-500',
      units: [
        { id: 'mm2', name: getTranslation(currentLanguage, 'ucUnitMm2'), symbol: 'mm²', toBase: 0.000001 },
        { id: 'cm2', name: getTranslation(currentLanguage, 'ucUnitCm2'), symbol: 'cm²', toBase: 0.0001 },
        { id: 'm2', name: getTranslation(currentLanguage, 'ucUnitM2'), symbol: 'm²', toBase: 1 },
        { id: 'km2', name: getTranslation(currentLanguage, 'ucUnitKm2'), symbol: 'km²', toBase: 1000000 },
        { id: 'in2', name: getTranslation(currentLanguage, 'ucUnitIn2'), symbol: 'in²', toBase: 0.00064516 },
        { id: 'ft2', name: getTranslation(currentLanguage, 'ucUnitFt2'), symbol: 'ft²', toBase: 0.092903 },
        { id: 'yd2', name: getTranslation(currentLanguage, 'ucUnitYd2'), symbol: 'yd²', toBase: 0.836127 },
        { id: 'acre', name: getTranslation(currentLanguage, 'ucUnitAcre'), symbol: 'acre', toBase: 4046.86 }
      ]
    },
    {
      id: 'temperature',
      name: getTranslation(currentLanguage, 'ucCategoryTemperature'),
      icon: <Thermometer className="h-5 w-5" />,
      color: 'bg-orange-500',
      units: [
        { id: 'c', name: getTranslation(currentLanguage, 'ucUnitC'), symbol: '°C', toBase: 1 },
        { id: 'f', name: getTranslation(currentLanguage, 'ucUnitF'), symbol: '°F', toBase: 1 },
        { id: 'k', name: getTranslation(currentLanguage, 'ucUnitK'), symbol: 'K', toBase: 1 },
        { id: 'r', name: getTranslation(currentLanguage, 'ucUnitR'), symbol: '°R', toBase: 1 }
      ]
    },
    {
      id: 'power',
      name: getTranslation(currentLanguage, 'ucCategoryPower'),
      icon: <Zap className="h-5 w-5" />,
      color: 'bg-amber-500',
      units: [
        { id: 'w', name: getTranslation(currentLanguage, 'ucUnitW'), symbol: 'W', toBase: 1 },
        { id: 'kw', name: getTranslation(currentLanguage, 'ucUnitKw'), symbol: 'kW', toBase: 1000 },
        { id: 'hp', name: getTranslation(currentLanguage, 'ucUnitHp'), symbol: 'hp', toBase: 745.7 },
        { id: 'btu', name: getTranslation(currentLanguage, 'ucUnitBtu'), symbol: 'BTU/h', toBase: 0.293071 },
        { id: 'j', name: getTranslation(currentLanguage, 'ucUnitJ'), symbol: 'J', toBase: 1 },
        { id: 'kj', name: getTranslation(currentLanguage, 'ucUnitKj'), symbol: 'kJ', toBase: 1000 },
        { id: 'cal', name: getTranslation(currentLanguage, 'ucUnitCal'), symbol: 'cal', toBase: 4.184 },
        { id: 'kcal', name: getTranslation(currentLanguage, 'ucUnitKcal'), symbol: 'kcal', toBase: 4184 }
      ]
    }
  ], [currentLanguage]);

  const currentCategory = categories.find(cat => cat.id === selectedCategory) || categories[0];

  useEffect(() => {
    if (currentCategory.units.length >= 2) {
      setFromUnit(currentCategory.units[0].id);
      setToUnit(currentCategory.units[1].id);
    }
  }, [selectedCategory, currentCategory.units]);

  const performConversion = useCallback((value: string, from: string, to: string, category: string) => {
    if (!value || isNaN(parseFloat(value))) return '';

    const numValue = parseFloat(value);
    const cat = categories.find(c => c.id === category);
    if (!cat) return '';

    const fromUnitData = cat.units.find(u => u.id === from);
    const toUnitData = cat.units.find(u => u.id === to);
    if (!fromUnitData || !toUnitData) return '';

    let result: number;

    if (category === 'temperature') {
      result = convertTemperature(numValue, from, to);
    } else {
      const baseValue = numValue * fromUnitData.toBase;
      result = baseValue / toUnitData.toBase;
    }

    const precision = toUnitData.precision || 6;
    return parseFloat(result.toPrecision(precision)).toString();
  }, [categories]);

  const convertTemperature = (value: number, from: string, to: string): number => {
    let celsius: number;
    switch (from) {
      case 'c': celsius = value; break;
      case 'f': celsius = (value - 32) * 5/9; break;
      case 'k': celsius = value - 273.15; break;
      case 'r': celsius = (value - 491.67) * 5/9; break;
      default: celsius = value;
    }

    switch (to) {
      case 'c': return celsius;
      case 'f': return celsius * 9/5 + 32;
      case 'k': return celsius + 273.15;
      case 'r': return celsius * 9/5 + 491.67;
      default: return celsius;
    }
  };

  useEffect(() => {
    if (inputValue && fromUnit && toUnit) {
      const convertedResult = performConversion(inputValue, fromUnit, toUnit, selectedCategory);
      setResult(convertedResult);
    } else {
      setResult('');
    }
  }, [inputValue, fromUnit, toUnit, selectedCategory, performConversion]);

  const swapUnits = () => {
    const tempUnit = fromUnit;
    setFromUnit(toUnit);
    setToUnit(tempUnit);
    
    if (result) {
      setInputValue(result);
    }
  };

  const copyResult = async () => {
    if (result) {
      try {
        await navigator.clipboard.writeText(result);
        setCopiedResult(true);
        setTimeout(() => setCopiedResult(false), 2000);
      } catch (error) {
        console.error('Failed to copy:', error);
      }
    }
  };

  const addToHistory = () => {
    if (inputValue && result && fromUnit && toUnit) {
      const fromUnitData = currentCategory.units.find(u => u.id === fromUnit);
      const toUnitData = currentCategory.units.find(u => u.id === toUnit);
      
      if (fromUnitData && toUnitData) {
        const historyItem = {
          from: `${inputValue} ${fromUnitData.symbol}`,
          to: `${result} ${toUnitData.symbol}`,
          value: inputValue,
          result: result,
          category: selectedCategory,
          timestamp: new Date()
        };
        
        setConversionHistory(prev => [historyItem, ...prev.slice(0, 9)]);
        
        recordToolCalculation('unit-converter', {
          action: 'conversion',
          category: selectedCategory,
          from_unit: fromUnit,
          to_unit: toUnit,
          input_value: inputValue,
          result: result
        });
      }
    }
  };

  const clearInput = () => {
    setInputValue('');
    setResult('');
  };

  const handleHistoryItemClick = (item: {
    from: string;
    to: string;
    value: string;
    result: string;
    category: string;
    timestamp: Date;
  }) => {
    setInputValue(item.value);
    setSelectedCategory(item.category);
    
    setTimeout(() => {
      const cat = categories.find(c => c.id === item.category);
      if (cat) {
        const fromUnitMatch = cat.units.find(u => item.from.includes(u.symbol));
        const toUnitMatch = cat.units.find(u => item.to.includes(u.symbol));
        if (fromUnitMatch) setFromUnit(fromUnitMatch.id);
        if (toUnitMatch) setToUnit(toUnitMatch.id);
      }
    }, 100);
  };

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="flex items-center justify-center mb-4">
          <div className="bg-emerald-500 p-3 rounded-full mr-4">
            <Calculator className="h-8 w-8 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {getTranslation(currentLanguage, 'unitConverter')}
            </h1>
            <p className="text-gray-600 mt-2">
              {getTranslation(currentLanguage, 'unitConverterHeader')}
            </p>
          </div>
        </div>
      </div>

      {/* Category Selection */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{getTranslation(currentLanguage, 'ucSelectCategory')}</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`p-4 rounded-xl border transition-all duration-300 ${
                selectedCategory === category.id
                  ? 'border-orange-500 bg-orange-50 shadow-md'
                  : 'border-gray-200 hover:border-orange-300 bg-white hover:bg-gray-50'
              }`}
            >
              <div className={`${category.color} text-white p-2 rounded-lg w-fit mx-auto mb-2`}>
                {category.icon}
              </div>
              <div className="text-sm font-medium text-gray-900 text-center">
                {category.name}
              </div>
            </button>
          ))}
        </div>
      </div>

      <div className="grid lg:grid-cols-3 gap-6">
        {/* Conversion Interface */}
        <div className="lg:col-span-2 space-y-6">
          {/* Input Section */}
          <div className="card p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Calculator className="h-5 w-5 mr-2 text-emerald-600" />
              {getTranslation(currentLanguage, 'ucUnitConversion')}
            </h4>

            <div className="grid md:grid-cols-5 gap-4 items-end">
              {/* From Value */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {getTranslation(currentLanguage, 'ucInputValue')}
                </label>
                <input
                  type="number"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  className="input text-lg font-medium"
                  placeholder={getTranslation(currentLanguage, 'ucInputPlaceholder')}
                  step="any"
                />
              </div>

              {/* From Unit */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {getTranslation(currentLanguage, 'ucFrom')}
                </label>
                <select
                  value={fromUnit}
                  onChange={(e) => setFromUnit(e.target.value)}
                  className="select"
                >
                  {currentCategory.units.map((unit) => (
                    <option key={unit.id} value={unit.id}>
                      {unit.name} ({unit.symbol})
                    </option>
                  ))}
                </select>
              </div>

              {/* Swap Button */}
              <div className="flex justify-center">
                <button
                  onClick={swapUnits}
                  className="btn-secondary p-3"
                  title={getTranslation(currentLanguage, 'ucSwapUnits')}
                >
                  <ArrowRightLeft className="h-5 w-5" />
                </button>
              </div>

              {/* To Unit */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {getTranslation(currentLanguage, 'ucTo')}
                </label>
                <select
                  value={toUnit}
                  onChange={(e) => setToUnit(e.target.value)}
                  className="select"
                >
                  {currentCategory.units.map((unit) => (
                    <option key={unit.id} value={unit.id}>
                      {unit.name} ({unit.symbol})
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Result Display */}
            {result && (
              <div className="mt-6 p-4 bg-orange-50 rounded-lg border border-orange-200">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-gray-600 mb-1">{getTranslation(currentLanguage, 'ucConversionResult')}</div>
                    <div className="text-2xl font-bold text-orange-600">
                      {result} {currentCategory.units.find(u => u.id === toUnit)?.symbol}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={copyResult}
                      className="btn-outline flex items-center"
                    >
                      {copiedResult ? (
                        <Check className="h-4 w-4 mr-2" />
                      ) : (
                        <Copy className="h-4 w-4 mr-2" />
                      )}
                      {copiedResult ? getTranslation(currentLanguage, 'ucCopied') : getTranslation(currentLanguage, 'ucCopy')}
                    </button>
                    <button
                      onClick={addToHistory}
                      className="btn-primary"
                    >
                      {getTranslation(currentLanguage, 'ucSave')}
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Quick Actions */}
            <div className="mt-4 flex space-x-3">
              <button
                onClick={clearInput}
                className="btn-outline flex items-center"
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                {getTranslation(currentLanguage, 'ucClear')}
              </button>

              {/* Common Values */}
              <div className="flex space-x-2">
                {['1', '10', '100', '1000'].map((value) => (
                  <button
                    key={value}
                    onClick={() => setInputValue(value)}
                    className="btn-outline text-sm"
                  >
                    {value}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Quick Reference Table */}
          <div className="card p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">
              {currentCategory.name} - {getTranslation(currentLanguage, 'ucCommonReference')}
            </h4>
            <div className="grid md:grid-cols-2 gap-4">
              {currentCategory.units.slice(0, 8).map((unit) => (
                <div key={unit.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-100">
                  <span className="font-medium text-gray-900">
                    1 {unit.symbol}
                  </span>
                  <span className="text-gray-600 text-sm">
                    = {performConversion('1', unit.id, currentCategory.units[0].id, selectedCategory)} {currentCategory.units[0].symbol}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* History and Tips */}
        <div className="space-y-6">
          {/* Conversion History */}
          {conversionHistory.length > 0 && (
            <div className="card p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">
                {getTranslation(currentLanguage, 'ucConversionHistory')}
              </h4>
              <div className="space-y-3 max-h-64 overflow-y-auto">
                {conversionHistory.map((item, index) => (
                  <div
                    key={index}
                    className="p-3 bg-gray-50 rounded-lg border border-gray-100 cursor-pointer hover:bg-gray-100 transition-colors duration-200"
                    onClick={() => handleHistoryItemClick(item)}
                  >
                    <div className="text-sm font-medium text-gray-900">
                      {item.from} → {item.to}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {item.timestamp.toLocaleTimeString()}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Amazon Seller Tips */}
          <div className="card bg-emerald-50 border-emerald-200 p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Package className="h-5 w-5 mr-2 text-emerald-600" />
              {getTranslation(currentLanguage, 'ucAmazonSellerTips')}
            </h4>
            <div className="space-y-3 text-sm text-gray-700">
              <div className="flex items-start">
                <div className="w-2 h-2 bg-emerald-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <span>{getTranslation(currentLanguage, 'ucTip1')}</span>
              </div>
              <div className="flex items-start">
                <div className="w-2 h-2 bg-emerald-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <span>{getTranslation(currentLanguage, 'ucTip2')}</span>
              </div>
              <div className="flex items-start">
                <div className="w-2 h-2 bg-emerald-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <span>{getTranslation(currentLanguage, 'ucTip3')}</span>
              </div>
              <div className="flex items-start">
                <div className="w-2 h-2 bg-emerald-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <span>{getTranslation(currentLanguage, 'ucTip4')}</span>
              </div>
            </div>
          </div>

          {/* Quick Conversions */}
          <div className="card p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">
              {getTranslation(currentLanguage, 'ucQuickConversions')}
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between p-2 bg-gray-50 rounded">
                <span>{getTranslation(currentLanguage, 'ucQuick1Label')}</span>
                <span>{getTranslation(currentLanguage, 'ucQuick1Value')}</span>
              </div>
              <div className="flex justify-between p-2 bg-gray-50 rounded">
                <span>{getTranslation(currentLanguage, 'ucQuick2Label')}</span>
                <span>{getTranslation(currentLanguage, 'ucQuick2Value')}</span>
              </div>
              <div className="flex justify-between p-2 bg-gray-50 rounded">
                <span>{getTranslation(currentLanguage, 'ucQuick3Label')}</span>
                <span>{getTranslation(currentLanguage, 'ucQuick3Value')}</span>
              </div>
              <div className="flex justify-between p-2 bg-gray-50 rounded">
                <span>{getTranslation(currentLanguage, 'ucQuick4Label')}</span>
                <span>{getTranslation(currentLanguage, 'ucQuick4Value')}</span>
              </div>
              <div className="flex justify-between p-2 bg-gray-50 rounded">
                <span>{getTranslation(currentLanguage, 'ucQuick5Label')}</span>
                <span>{getTranslation(currentLanguage, 'ucQuick5Value')}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UnitConverter;
