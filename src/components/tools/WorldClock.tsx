import React, { useState, useEffect } from 'react';
import { Clock, Globe, Sun, Moon, MapPin, RefreshCw } from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
import { getTranslation } from '../../utils/translations';
import { recordToolCalculation } from '../../utils/analytics';

interface TimeZoneInfo {
  id: string;
  name: string;
  timezone: string;
  flag: string;
  city: string;
  country: string;
}

interface TimeData {
  time: string;
  date: string;
  isDaytime: boolean;
  utcOffset: string;
  isDST: boolean;
}

const WorldClock: React.FC = () => {
  const { currentLanguage } = useLanguage();
  const [currentTime, setCurrentTime] = useState<Date>(new Date());
  const [timeZones] = useState<TimeZoneInfo[]>([
    // 当前时区（自动检测）
    {
      id: 'local',
      name: 'currentLocation',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      flag: '📍',
      city: 'currentCity',
      country: 'currentCountry'
    },
    // 美国时区
    {
      id: 'us-eastern',
      name: 'usEastern',
      timezone: 'America/New_York',
      flag: '🇺🇸',
      city: 'New York',
      country: 'United States'
    },
    {
      id: 'us-central',
      name: 'usCentral',
      timezone: 'America/Chicago',
      flag: '🇺🇸',
      city: 'Chicago',
      country: 'United States'
    },
    {
      id: 'us-mountain',
      name: 'usMountain',
      timezone: 'America/Denver',
      flag: '🇺🇸',
      city: 'Denver',
      country: 'United States'
    },
    {
      id: 'us-pacific',
      name: 'usPacific',
      timezone: 'America/Los_Angeles',
      flag: '🇺🇸',
      city: 'Los Angeles',
      country: 'United States'
    },
    // 欧洲时区
    {
      id: 'europe-london',
      name: 'europeLondon',
      timezone: 'Europe/London',
      flag: '🇬🇧',
      city: 'London',
      country: 'United Kingdom'
    },
    {
      id: 'europe-paris',
      name: 'europeParis',
      timezone: 'Europe/Paris',
      flag: '🇫🇷',
      city: 'Paris',
      country: 'France'
    },
    {
      id: 'europe-berlin',
      name: 'europeBerlin',
      timezone: 'Europe/Berlin',
      flag: '🇩🇪',
      city: 'Berlin',
      country: 'Germany'
    },
    // 亚洲时区
    {
      id: 'asia-tokyo',
      name: 'asiaTokyo',
      timezone: 'Asia/Tokyo',
      flag: '🇯🇵',
      city: 'Tokyo',
      country: 'Japan'
    },
    {
      id: 'asia-shanghai',
      name: 'asiaShanghai',
      timezone: 'Asia/Shanghai',
      flag: '🇨🇳',
      city: 'Shanghai',
      country: 'China'
    },
    {
      id: 'asia-singapore',
      name: 'asiaSingapore',
      timezone: 'Asia/Singapore',
      flag: '🇸🇬',
      city: 'Singapore',
      country: 'Singapore'
    },
    {
      id: 'asia-seoul',
      name: 'asiaSeoul',
      timezone: 'Asia/Seoul',
      flag: '🇰🇷',
      city: 'Seoul',
      country: 'South Korea'
    },
    // 其他重要时区
    {
      id: 'australia-sydney',
      name: 'australiaSydney',
      timezone: 'Australia/Sydney',
      flag: '🇦🇺',
      city: 'Sydney',
      country: 'Australia'
    }
  ]);

  // 更新时间
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // 获取时区时间数据
  const getTimeZoneData = (timezone: string): TimeData => {
    const now = new Date();
    
    // 格式化时间
    const timeFormatter = new Intl.DateTimeFormat(currentLanguage === 'zh' ? 'zh-CN' : 'en-US', {
      timeZone: timezone,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
    
    // 格式化日期
    const dateFormatter = new Intl.DateTimeFormat(currentLanguage === 'zh' ? 'zh-CN' : 'en-US', {
      timeZone: timezone,
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      weekday: 'short'
    });

    // 获取UTC偏移
    const offsetFormatter = new Intl.DateTimeFormat('en', {
      timeZone: timezone,
      timeZoneName: 'longOffset'
    });
    
    const parts = offsetFormatter.formatToParts(now);
    const offsetPart = parts.find(part => part.type === 'timeZoneName');
    const utcOffset = offsetPart ? offsetPart.value : '';

    // 判断是否为白天（6:00-18:00为白天）
    const hour = parseInt(timeFormatter.format(now).split(':')[0]);
    const isDaytime = hour >= 6 && hour < 18;

    // 检查是否为夏令时
    const january = new Date(now.getFullYear(), 0, 1);
    const july = new Date(now.getFullYear(), 6, 1);
    const janOffset = january.getTimezoneOffset();
    const julOffset = july.getTimezoneOffset();
    const isDST = Math.max(janOffset, julOffset) !== now.getTimezoneOffset();

    return {
      time: timeFormatter.format(now),
      date: dateFormatter.format(now),
      isDaytime,
      utcOffset,
      isDST
    };
  };

  // 记录工具使用
  useEffect(() => {
    recordToolCalculation('world-clock', {
      action: 'view_time_zones',
      timezone_count: timeZones.length,
      current_timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    });
  }, [timeZones.length]);

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="flex items-center justify-center mb-4">
          <div className="bg-blue-500 p-3 rounded-full mr-4">
            <Globe className="h-8 w-8 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {getTranslation(currentLanguage, 'worldClock')}
            </h1>
            <p className="text-gray-600 mt-2">
              {getTranslation(currentLanguage, 'worldClockDesc')}
            </p>
          </div>
        </div>
      </div>

      {/* Current Time Display */}
      <div className="card p-6 mb-6 bg-orange-50 border-orange-200">
        <div className="text-center">
          <div className="flex items-center justify-center mb-2">
            <Clock className="h-6 w-6 mr-2 text-orange-600" />
            <span className="text-lg font-medium text-gray-900">
              {getTranslation(currentLanguage, 'currentTime')}
            </span>
          </div>
          <div className="text-4xl font-bold mb-2 text-orange-600">
            {getTimeZoneData(timeZones[0].timezone).time}
          </div>
          <div className="text-lg text-gray-700">
            {getTimeZoneData(timeZones[0].timezone).date}
          </div>
          <div className="text-sm text-gray-600 mt-2">
            {getTimeZoneData(timeZones[0].timezone).utcOffset}
            {getTimeZoneData(timeZones[0].timezone).isDST && (
              <span className="badge-primary ml-2">
                {getTranslation(currentLanguage, 'dstActive')}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Time Zones Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {timeZones.slice(1).map((tz) => {
          const timeData = getTimeZoneData(tz.timezone);
          return (
            <div key={tz.id} className="card p-4 hover:shadow-md transition-all duration-300 hover:border-orange-300">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <span className="text-2xl mr-2">{tz.flag}</span>
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      {getTranslation(currentLanguage, tz.name)}
                    </h3>
                    <p className="text-sm text-gray-600">{tz.city}</p>
                  </div>
                </div>
                <div className="text-right">
                  {timeData.isDaytime ? (
                    <Sun className="h-5 w-5 text-yellow-500" />
                  ) : (
                    <Moon className="h-5 w-5 text-blue-500" />
                  )}
                </div>
              </div>

              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 mb-1">
                  {timeData.time}
                </div>
                <div className="text-sm text-gray-600 mb-2">
                  {timeData.date}
                </div>
                <div className="text-xs text-gray-500">
                  {timeData.utcOffset}
                  {timeData.isDST && (
                    <span className="badge-secondary ml-1">
                      DST
                    </span>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Tips */}
      <div className="card bg-gray-50 border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <MapPin className="h-5 w-5 mr-2 text-orange-600" />
          {getTranslation(currentLanguage, 'timeZoneTips')}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-700">
          <div>
            <strong className="text-gray-900">{getTranslation(currentLanguage, 'dstExplanation')}:</strong>
            <p className="mt-1">{getTranslation(currentLanguage, 'dstExplanationText')}</p>
          </div>
          <div>
            <strong className="text-gray-900">{getTranslation(currentLanguage, 'businessHours')}:</strong>
            <p className="mt-1">{getTranslation(currentLanguage, 'businessHoursText')}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorldClock;
