import React, { useState, useCallback, useMemo } from 'react';
import { Type, Copy, Check, RotateCcw, ArrowRight, Zap, FileText, Download, Upload } from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
import { getTranslation } from '../../utils/translations';
import { recordToolCalculation } from '../../utils/analytics';

interface ConversionOption {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  convert: (text: string) => string;
}

const CaseConverter = () => {
  const { currentLanguage } = useLanguage();
  const [inputText, setInputText] = useState('');
  const [selectedConversion, setSelectedConversion] = useState('uppercase');
  const [copiedResults, setCopiedResults] = useState<Record<string, boolean>>({});
  const [conversionHistory, setConversionHistory] = useState<Array<{
    original: string;
    converted: string;
    type: string;
    timestamp: Date;
  }>>([]);

  // 转换函数
  const convertToUpperCase = (text: string) => text.toUpperCase();
  const convertToLowerCase = (text: string) => text.toLowerCase();
  const convertToTitleCase = (text: string) => {
    return text.replace(/\w\S*/g, (txt) => 
      txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
    );
  };
  const convertToSentenceCase = (text: string) => {
    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
  };
  const convertToCamelCase = (text: string) => {
    return text
      .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => 
        index === 0 ? word.toLowerCase() : word.toUpperCase()
      )
      .replace(/\s+/g, '');
  };
  const convertToPascalCase = (text: string) => {
    return text
      .replace(/(?:^\w|[A-Z]|\b\w)/g, (word) => word.toUpperCase())
      .replace(/\s+/g, '');
  };
  const convertToSnakeCase = (text: string) => {
    return text
      .replace(/\W+/g, ' ')
      .split(/ |\B(?=[A-Z])/)
      .map(word => word.toLowerCase())
      .join('_');
  };
  const convertToKebabCase = (text: string) => {
    return text
      .replace(/\W+/g, ' ')
      .split(/ |\B(?=[A-Z])/)
      .map(word => word.toLowerCase())
      .join('-');
  };
  const convertToConstantCase = (text: string) => {
    return text
      .replace(/\W+/g, ' ')
      .split(/ |\B(?=[A-Z])/)
      .map(word => word.toUpperCase())
      .join('_');
  };
  const convertToAlternatingCase = (text: string) => {
    return text
      .split('')
      .map((char, index) => 
        index % 2 === 0 ? char.toLowerCase() : char.toUpperCase()
      )
      .join('');
  };
  const convertToInverseCase = (text: string) => {
    return text
      .split('')
      .map(char => 
        char === char.toUpperCase() ? char.toLowerCase() : char.toUpperCase()
      )
      .join('');
  };

  // 转换选项配置 - 专注于Amazon卖家需求
  const conversionOptions: ConversionOption[] = useMemo(() => [
    {
      id: 'titlecase',
      name: getTranslation(currentLanguage, 'ccTitleCase'),
      description: getTranslation(currentLanguage, 'ccTitleCaseDesc'),
      icon: <FileText className="h-4 w-4" />,
      color: 'bg-blue-500',
      convert: convertToTitleCase
    },
    {
      id: 'uppercase',
      name: getTranslation(currentLanguage, 'ccUpperCase'),
      description: getTranslation(currentLanguage, 'ccUpperCaseDesc'),
      icon: <Type className="h-4 w-4" />,
      color: 'bg-red-500',
      convert: convertToUpperCase
    },
    {
      id: 'lowercase',
      name: getTranslation(currentLanguage, 'ccLowerCase'),
      description: getTranslation(currentLanguage, 'ccLowerCaseDesc'),
      icon: <Type className="h-4 w-4" />,
      color: 'bg-green-500',
      convert: convertToLowerCase
    },
    {
      id: 'sentencecase',
      name: getTranslation(currentLanguage, 'ccSentenceCase'),
      description: getTranslation(currentLanguage, 'ccSentenceCaseDesc'),
      icon: <FileText className="h-4 w-4" />,
      color: 'bg-purple-500',
      convert: convertToSentenceCase
    },
    {
      id: 'inversecase',
      name: getTranslation(currentLanguage, 'ccInverseCase'),
      description: getTranslation(currentLanguage, 'ccInverseCaseDesc'),
      icon: <Type className="h-4 w-4" />,
      color: 'bg-indigo-500',
      convert: convertToInverseCase
    },
    {
      id: 'alternatingcase',
      name: getTranslation(currentLanguage, 'ccAlternatingCase'),
      description: getTranslation(currentLanguage, 'ccAlternatingCaseDesc'),
      icon: <Type className="h-4 w-4" />,
      color: 'bg-orange-500',
      convert: convertToAlternatingCase
    }
  ], [currentLanguage]);

  // 获取转换结果
  const getConvertedText = useCallback((conversionId: string) => {
    if (!inputText.trim()) return '';
    const option = conversionOptions.find(opt => opt.id === conversionId);
    return option ? option.convert(inputText) : '';
  }, [inputText, conversionOptions]);

  // 复制到剪贴板
  const copyToClipboard = useCallback(async (text: string, conversionId: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedResults(prev => ({ ...prev, [conversionId]: true }));
      setTimeout(() => {
        setCopiedResults(prev => ({ ...prev, [conversionId]: false }));
      }, 2000);
      
      // 记录分析数据
      recordToolCalculation('case-converter', {
        conversionType: conversionId,
        textLength: text.length
      });
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  }, []);

  // 清空输入
  const clearInput = useCallback(() => {
    setInputText('');
    setCopiedResults({});
  }, []);

  // 添加到历史记录
  const addToHistory = useCallback((original: string, converted: string, type: string) => {
    const newEntry = {
      original,
      converted,
      type,
      timestamp: new Date()
    };
    setConversionHistory(prev => [newEntry, ...prev.slice(0, 9)]); // 保留最近10条记录
  }, []);

  // 处理转换
  const handleConversion = useCallback((conversionId: string) => {
    if (!inputText.trim()) return;
    
    const converted = getConvertedText(conversionId);
    const option = conversionOptions.find(opt => opt.id === conversionId);
    
    if (converted && option) {
      addToHistory(inputText, converted, option.name);
    }
  }, [inputText, getConvertedText, conversionOptions, addToHistory]);

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* 页面标题 */}
      <div className="text-center">
        <div className="flex items-center justify-center mb-4">
          <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl">
            <Type className="h-8 w-8 text-white" />
          </div>
        </div>
        <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
          {getTranslation(currentLanguage, 'caseConverter')}
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          {getTranslation(currentLanguage, 'caseConverterHeader')}
        </p>
      </div>

      {/* 输入区域 */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-gray-700">
              {getTranslation(currentLanguage, 'ccInputText')}
            </label>
            <div className="flex items-center space-x-2">
              <span className="text-xs text-gray-500">
                {inputText.length} {getTranslation(currentLanguage, 'ccCharacters')}
              </span>
              {inputText && (
                <button
                  onClick={clearInput}
                  className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                  title={getTranslation(currentLanguage, 'ccClear')}
                >
                  <RotateCcw className="h-4 w-4" />
                </button>
              )}
            </div>
          </div>
          <textarea
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            placeholder={getTranslation(currentLanguage, 'ccInputPlaceholder')}
            className="w-full h-32 p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          />
        </div>
      </div>

      {/* 转换选项网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {conversionOptions.map((option) => {
          const convertedText = getConvertedText(option.id);
          const isCopied = copiedResults[option.id];

          return (
            <div
              key={option.id}
              className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 ${option.color} rounded-lg text-white`}>
                    {option.icon}
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{option.name}</h3>
                    <p className="text-xs text-gray-500">{option.description}</p>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <div className="bg-gray-50 rounded-lg p-3 min-h-[80px]">
                  <div className="text-sm text-gray-900 break-words">
                    {convertedText || (
                      <span className="text-gray-400 italic">
                        {getTranslation(currentLanguage, 'ccNoText')}
                      </span>
                    )}
                  </div>
                </div>

                {convertedText && (
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => copyToClipboard(convertedText, option.id)}
                      className="flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors"
                    >
                      {isCopied ? (
                        <>
                          <Check className="h-4 w-4" />
                          <span className="text-sm">{getTranslation(currentLanguage, 'ccCopied')}</span>
                        </>
                      ) : (
                        <>
                          <Copy className="h-4 w-4" />
                          <span className="text-sm">{getTranslation(currentLanguage, 'ccCopy')}</span>
                        </>
                      )}
                    </button>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* 转换历史记录 */}
      {conversionHistory.length > 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {getTranslation(currentLanguage, 'ccHistory')}
          </h3>
          <div className="space-y-3">
            {conversionHistory.slice(0, 5).map((entry, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="text-xs font-medium text-blue-600">{entry.type}</span>
                    <span className="text-xs text-gray-500">
                      {entry.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600 truncate">
                    {entry.original} → {entry.converted}
                  </div>
                </div>
                <button
                  onClick={() => copyToClipboard(entry.converted, `history-${index}`)}
                  className="ml-3 p-1 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <Copy className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Amazon卖家使用技巧 */}
      <div className="bg-gradient-to-br from-orange-50 to-amber-50 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {getTranslation(currentLanguage, 'ccAmazonTips')}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <div className="p-1 bg-blue-500 rounded text-white">
                <FileText className="h-4 w-4" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">
                  {getTranslation(currentLanguage, 'ccAmazonTip1Title')}
                </h4>
                <p className="text-sm text-gray-600">
                  {getTranslation(currentLanguage, 'ccAmazonTip1Desc')}
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="p-1 bg-red-500 rounded text-white">
                <Type className="h-4 w-4" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">
                  {getTranslation(currentLanguage, 'ccAmazonTip2Title')}
                </h4>
                <p className="text-sm text-gray-600">
                  {getTranslation(currentLanguage, 'ccAmazonTip2Desc')}
                </p>
              </div>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <div className="p-1 bg-green-500 rounded text-white">
                <Copy className="h-4 w-4" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">
                  {getTranslation(currentLanguage, 'ccAmazonTip3Title')}
                </h4>
                <p className="text-sm text-gray-600">
                  {getTranslation(currentLanguage, 'ccAmazonTip3Desc')}
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="p-1 bg-purple-500 rounded text-white">
                <Zap className="h-4 w-4" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">
                  {getTranslation(currentLanguage, 'ccAmazonTip4Title')}
                </h4>
                <p className="text-sm text-gray-600">
                  {getTranslation(currentLanguage, 'ccAmazonTip4Desc')}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CaseConverter;
