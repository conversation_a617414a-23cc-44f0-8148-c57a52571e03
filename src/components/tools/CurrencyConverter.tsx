import React, { useState, useEffect, useCallback } from 'react';
import { ArrowUpDown, Clock, DollarSign, Star, TrendingUp, TrendingDown, Minus } from 'lucide-react';
import CurrencyService from '../../services/currencyService';
import { getCurrencyInfo, formatCurrencyAmount, POPULAR_CURRENCIES, SUPPORTED_CURRENCIES } from '../../data/currencyData';
import { recordToolCalculation } from '../../utils/analytics';
import { useLanguage } from '../../hooks/useLanguage';
import { getTranslation } from '../../utils/translations';
import CurrencyTrendChart from './CurrencyConverter/CurrencyTrendChart';
import ToolInfoSections from './CurrencyConverter/ToolInfoSections';

const CurrencyConverter: React.FC = () => {
  const { currentLanguage } = useLanguage();
  const [fromCurrency, setFromCurrency] = useState('USD');
  const [toCurrency, setToCurrency] = useState('CNY');
  const [amount, setAmount] = useState('1');
  const [result, setResult] = useState<number | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [isInitializing, setIsInitializing] = useState(true);

  const [supportedCurrencies] = useState<string[]>(SUPPORTED_CURRENCIES);
  const [serviceError, setServiceError] = useState<{
    hasError: boolean;
    errorType: 'network' | 'api' | 'service' | null;
    errorMessage: string;
    canRetry: boolean;
  }>({
    hasError: false,
    errorType: null,
    errorMessage: '',
    canRetry: true
  });
  const [trendData, setTrendData] = useState<{
    change: number;
    changePercent: number;
    trend: 'up' | 'down' | 'stable';
    period: string;
  } | null>(null);

  const currencyService = CurrencyService.getInstance();

  // 获取趋势数据
  const fetchTrendData = useCallback(async () => {
    if (fromCurrency === toCurrency) {
      setTrendData(null);
      return;
    }

    try {
      const historicalData = await currencyService.getHistoricalRates(fromCurrency, toCurrency, '1d');

      // 处理不同的数据格式
      let dataPoints = null;
      if (historicalData && historicalData.rates && historicalData.rates.length >= 2) {
        dataPoints = historicalData.rates;
      } else if (historicalData && historicalData.dataPoints && historicalData.dataPoints.length >= 2) {
        dataPoints = historicalData.dataPoints;
      }

      if (dataPoints && dataPoints.length >= 2) {
        const currentRate = dataPoints[dataPoints.length - 1].rate;
        const previousRate = dataPoints[0].rate;

        const change = currentRate - previousRate;
        const changePercent = (change / previousRate) * 100;

        let trend: 'up' | 'down' | 'stable' = 'stable';
        if (Math.abs(changePercent) > 0.01) { // 0.01% 以上才算有趋势
          trend = changePercent > 0 ? 'up' : 'down';
        }

        setTrendData({
          change,
          changePercent,
          trend,
          period: '24h' // 使用标识符而不是翻译文本
        });
      } else {
        setTrendData(null);
      }
    } catch (error) {
      console.warn('获取趋势数据失败:', error);
      setTrendData(null);
    }
  }, [fromCurrency, toCurrency, currencyService]);

  // 计算换算结果
  const calculateResult = useCallback(() => {
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount) || numAmount <= 0) {
      setResult(null);
      return;
    }

    const convertedAmount = currencyService.convert(numAmount, fromCurrency, toCurrency);
    setResult(convertedAmount);
    setLastUpdated(currencyService.getLastUpdated());

    // 记录工具使用
    if (convertedAmount !== null) {
      recordToolCalculation('currency-converter', {
        from: fromCurrency,
        to: toCurrency,
        amount: numAmount,
        result: convertedAmount
      });
    }
  }, [amount, fromCurrency, toCurrency, currencyService]);

  // 初始化数据
  useEffect(() => {
    console.log('🔄 CurrencyConverter 开始初始化...');

    const initializeComponent = async () => {
      try {
        // 等待CurrencyService初始化完成
        console.log('⏳ 等待CurrencyService初始化完成...');
        await currencyService.waitForInitialization();
        console.log('✅ CurrencyService初始化完成');

        // 获取数据并设置状态
        setLastUpdated(currencyService.getLastUpdated());

        // 进行初始计算
        calculateResult();

        // 结束初始化状态
        setIsInitializing(false);
        console.log('✅ CurrencyConverter 初始化完成');
      } catch (error) {
        console.error('❌ CurrencyConverter 初始化失败:', error);
        // 即使失败也要结束初始化状态
        setIsInitializing(false);
      }
    };

    // 监听汇率数据更新
    const handleRatesUpdate = () => {
      console.log('📡 CurrencyConverter 收到数据更新通知');
      setLastUpdated(currencyService.getLastUpdated());
      setServiceError(currencyService.getError());
      calculateResult();
    };

    // 设置监听器
    currencyService.addListener(handleRatesUpdate);

    // 开始初始化
    initializeComponent();

    // 清理监听器
    return () => {
      currencyService.removeListener(handleRatesUpdate);
    };
  }, [calculateResult, currencyService]);

  // 当输入变化时重新计算
  useEffect(() => {
    calculateResult();
  }, [calculateResult]);

  // 当货币对变化时获取趋势数据
  useEffect(() => {
    if (!isInitializing) {
      fetchTrendData();
    }
  }, [fromCurrency, toCurrency, isInitializing, fetchTrendData]);

  // 交换货币
  const swapCurrencies = () => {
    setFromCurrency(toCurrency);
    setToCurrency(fromCurrency);
  };

  // 刷新数据
  const refreshData = async () => {
    try {
      await currencyService.refreshData();
    } catch (error) {
      console.error('刷新数据失败:', error);
    }
  };

  // 获取时区信息
  const getTimezoneInfo = (): string => {
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const now = new Date();
    const offset = now.getTimezoneOffset();
    const offsetHours = Math.floor(Math.abs(offset) / 60);
    const offsetMinutes = Math.abs(offset) % 60;
    const offsetSign = offset <= 0 ? '+' : '-';

    const offsetString = `UTC${offsetSign}${offsetHours.toString().padStart(2, '0')}:${offsetMinutes.toString().padStart(2, '0')}`;

    // 根据语言返回不同格式
    if (currentLanguage === 'en') {
      return `${timezone} (${offsetString})`;
    } else {
      return `${timezone} (${offsetString})`;
    }
  };





  // 获取汇率
  const getExchangeRate = (): number | null => {
    if (fromCurrency === toCurrency) return 1;
    return currencyService.convert(1, fromCurrency, toCurrency);
  };

  const exchangeRate = getExchangeRate();

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6">
      {/* 工具头部 */}
      <div className="tool-header mb-6">
        <div className="flex items-center">
          <div className="tool-header-icon">
            <DollarSign className="h-6 w-6" />
          </div>
          <div>
            <h1 className="tool-header-title">{getTranslation(currentLanguage, 'ccTitle')}</h1>
            <p className="tool-header-description">
              {getTranslation(currentLanguage, 'ccDescription')}
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 pb-4">
        {/* 左侧统一卡片区域 */}
        <div className="xl:col-span-2">
          <div className="card p-4 sm:p-6 shadow-xl border-0 h-full" style={{ background: 'linear-gradient(135deg, #FFFFFF 0%, #FAFAFA 100%)' }}>
            {/* 数据更新信息 */}
            <div className="mb-4 sm:mb-6">
              <div className="flex items-center mb-3">
                <div className="w-6 h-6 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-2">
                  <Clock className="h-3 w-3 text-white" />
                </div>
                <h4 className="text-base font-bold" style={{ color: '#232F3E' }}>{getTranslation(currentLanguage, 'ccDataUpdate')}</h4>
              </div>
              {serviceError.hasError ? (
                <div className="p-3 rounded-lg bg-gradient-to-r from-red-50 to-red-100 border border-red-200">
                  <div className="text-sm">
                    <div className="font-bold text-red-800 mb-2">
                      {getTranslation(currentLanguage, 'ccApiError')}
                    </div>
                    <div className="text-red-700 mb-3">
                      {serviceError.errorType === 'api' && serviceError.errorMessage.includes('缓存')
                        ? getTranslation(currentLanguage, 'ccUsingCachedData')
                        : getTranslation(currentLanguage, 'ccApiErrorDesc')
                      }
                    </div>
                    {serviceError.canRetry && (
                      <button
                        onClick={refreshData}
                        className="px-3 py-1 bg-red-600 text-white text-xs rounded-lg hover:bg-red-700 transition-colors"
                      >
                        {getTranslation(currentLanguage, 'ccRefreshData')}
                      </button>
                    )}
                  </div>
                </div>
              ) : (
                <div className="p-3 rounded-lg bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200">
                  <div className="text-sm text-gray-600">
                    {lastUpdated ? (
                      <div>
                        <div className="font-bold text-gray-800">
                          {getTranslation(currentLanguage, 'ccLastUpdated')}：{lastUpdated.toLocaleDateString()} {lastUpdated.toLocaleTimeString()}
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          {getTranslation(currentLanguage, 'ccTimezone')}：{getTimezoneInfo()}
                        </div>
                      </div>
                    ) : (
                      <div className="font-medium text-gray-700">{getTranslation(currentLanguage, 'ccLoading')}</div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* 货币换算区域 */}
            <div className="mb-4 sm:mb-6">
              <div className="mb-3 sm:mb-4">
                <h3 className="text-lg font-bold" style={{ color: '#232F3E' }}>
                  {getTranslation(currentLanguage, 'ccCurrencyConversion')}
                </h3>
              </div>

              {/* 货币换算卡片 */}
              <div className="bg-white rounded-2xl border-2 border-gray-100 p-4 sm:p-6 shadow-lg">
                {/* 源货币输入 */}
                <div className="mb-4 sm:mb-6">
                  <label className="label flex items-center mb-4">
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                      <DollarSign className="h-4 w-4 text-white" />
                    </div>
                    <span className="text-lg font-bold" style={{ color: '#232F3E' }}>{getTranslation(currentLanguage, 'ccConvertAmount')}</span>
                  </label>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="relative">
                      <input
                        type="number"
                        value={amount}
                        onChange={(e) => setAmount(e.target.value)}
                        className="input pl-16 pr-4 text-xl font-bold h-16 border-2 border-gray-200 focus:border-orange-400 w-full rounded-xl"
                        placeholder={getTranslation(currentLanguage, 'ccAmountPlaceholder')}
                        min="0"
                        step="0.01"
                      />
                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-lg font-bold pointer-events-none" style={{ color: '#FF9900' }}>
                        {getCurrencyInfo(fromCurrency, currentLanguage).symbol}
                      </div>
                    </div>
                    <div className="relative">
                      <select
                        value={fromCurrency}
                        onChange={(e) => setFromCurrency(e.target.value)}
                        className="select pl-20 pr-12 appearance-none h-16 border-2 border-gray-200 focus:border-orange-400 text-base font-medium w-full bg-white rounded-xl"
                      >
                        {supportedCurrencies.map((currency) => {
                          const info = getCurrencyInfo(currency, currentLanguage);
                          return (
                            <option key={currency} value={currency}>
                              {currency} - {info.name}
                            </option>
                          );
                        })}
                      </select>
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 flex items-center pointer-events-none">
                        <span className="text-xl mr-1">{getCurrencyInfo(fromCurrency, currentLanguage).flag}</span>
                        <span className="text-sm font-bold text-gray-700">{fromCurrency}</span>
                      </div>
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                        <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 交换按钮 */}
                <div className="flex justify-center my-4">
                  <button
                    onClick={swapCurrencies}
                    className="group w-12 h-12 rounded-full border-3 border-orange-300 bg-gradient-to-br from-orange-100 to-orange-200 hover:from-orange-200 hover:to-orange-300 flex items-center justify-center transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-110"
                    title={getTranslation(currentLanguage, 'ccSwapCurrencies')}
                  >
                    <ArrowUpDown className="h-5 w-5 group-hover:rotate-180 transition-transform duration-500" style={{ color: '#FF9900' }} />
                  </button>
                </div>

                {/* 目标货币 */}
                <div className="mb-6">
                  <label className="label flex items-center mb-4">
                    <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
                      <ArrowUpDown className="h-4 w-4 text-white" />
                    </div>
                    <span className="text-lg font-bold" style={{ color: '#232F3E' }}>{getTranslation(currentLanguage, 'ccConvertResult')}</span>
                  </label>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="relative">
                      <div className="input bg-gradient-to-r from-orange-50 via-orange-100 to-orange-50 border-2 border-orange-300 flex items-center pl-16 pr-4 text-2xl font-bold h-16 shadow-inner w-full rounded-xl" style={{ color: '#232F3E' }}>
                        {isInitializing ? (
                          <div className="flex items-center">
                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500 mr-2"></div>
                            <span className="text-lg text-gray-500">加载中...</span>
                          </div>
                        ) : serviceError.hasError && !result ? (
                          <div className="flex items-center text-red-600">
                            <span className="text-lg">{getTranslation(currentLanguage, 'ccNoRealTimeData')}</span>
                          </div>
                        ) : (
                          result !== null ? formatCurrencyAmount(result, toCurrency) : '--'
                        )}
                      </div>
                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-xl pointer-events-none">
                        {getCurrencyInfo(toCurrency, currentLanguage).flag}
                      </div>
                      {result !== null && (
                        <div className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                          {getTranslation(currentLanguage, 'ccRealTime')}
                        </div>
                      )}
                    </div>
                    <div className="relative">
                      <select
                        value={toCurrency}
                        onChange={(e) => setToCurrency(e.target.value)}
                        className="select pl-20 pr-12 appearance-none h-16 border-2 border-gray-200 focus:border-orange-400 text-base font-medium w-full bg-white rounded-xl"
                      >
                        {supportedCurrencies.map((currency) => {
                          const info = getCurrencyInfo(currency, currentLanguage);
                          return (
                            <option key={currency} value={currency}>
                              {currency} - {info.name}
                            </option>
                          );
                        })}
                      </select>
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 flex items-center pointer-events-none">
                        <span className="text-xl mr-1">{getCurrencyInfo(toCurrency, currentLanguage).flag}</span>
                        <span className="text-sm font-bold text-gray-700">{toCurrency}</span>
                      </div>
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                        <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 汇率信息卡片 */}
              {exchangeRate && (
                <div className="bg-gradient-to-br from-orange-50 via-orange-100 to-yellow-50 border-2 border-orange-300 rounded-2xl p-4 shadow-xl relative overflow-hidden mt-6">
                  <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-orange-200 to-orange-300 rounded-full -mr-10 -mt-10 opacity-50"></div>
                  <div className="relative">
                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                      <div className="flex items-center">
                        <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mr-4 shadow-lg flex-shrink-0">
                          <DollarSign className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <span className="text-base font-bold text-gray-700">{getTranslation(currentLanguage, 'ccExchangeRate')}</span>
                          <div className="text-sm text-gray-500 mt-1 flex items-center">
                            <span>{getCurrencyInfo(fromCurrency, currentLanguage).country}</span>
                            <ArrowUpDown className="h-3 w-3 mx-2 rotate-90" />
                            <span>{getCurrencyInfo(toCurrency, currentLanguage).country}</span>
                          </div>
                        </div>
                      </div>
                      <div className="text-left lg:text-right">
                        <div className="flex items-center justify-start lg:justify-end text-lg lg:text-xl font-bold mb-1 flex-wrap" style={{ color: '#FF9900' }}>
                          <span className="text-xl lg:text-2xl mr-2">{getCurrencyInfo(fromCurrency, currentLanguage).flag}</span>
                          <span className="text-base lg:text-lg">1 {getCurrencyInfo(fromCurrency, currentLanguage).symbol}</span>
                          <span className="mx-2 text-gray-400">=</span>
                          <span className="text-base lg:text-lg">{formatCurrencyAmount(exchangeRate, toCurrency)}</span>
                          <span className="text-xl lg:text-2xl ml-2">{getCurrencyInfo(toCurrency, currentLanguage).flag}</span>
                        </div>
                        <div className="text-sm text-gray-600 text-left lg:text-right">
                          {getCurrencyInfo(fromCurrency, currentLanguage).name} → {getCurrencyInfo(toCurrency, currentLanguage).name}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 简单趋势分析 */}
              {result !== null && trendData && (
                <div className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`w-8 h-8 rounded-lg flex items-center justify-center mr-3 ${
                        trendData.trend === 'up' ? 'bg-green-100' :
                        trendData.trend === 'down' ? 'bg-red-100' : 'bg-gray-100'
                      }`}>
                        {trendData.trend === 'up' && <TrendingUp className="h-4 w-4 text-green-600" />}
                        {trendData.trend === 'down' && <TrendingDown className="h-4 w-4 text-red-600" />}
                        {trendData.trend === 'stable' && <Minus className="h-4 w-4 text-gray-600" />}
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-700">
                          {getTranslation(currentLanguage, 'cc24HourTrend')}
                        </div>
                        <div className={`text-xs ${
                          trendData.trend === 'up' ? 'text-green-600' :
                          trendData.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                        }`}>
                          {trendData.trend === 'up' && getTranslation(currentLanguage, 'ccRateRising')}
                          {trendData.trend === 'down' && getTranslation(currentLanguage, 'ccRateFalling')}
                          {trendData.trend === 'stable' && getTranslation(currentLanguage, 'ccRateStable')}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`text-sm font-bold ${
                        trendData.trend === 'up' ? 'text-green-600' :
                        trendData.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                      }`}>
                        {trendData.changePercent > 0 ? '+' : ''}{trendData.changePercent.toFixed(2)}%
                      </div>
                      <div className="text-xs text-gray-500">
                        {trendData.change > 0 ? '+' : ''}{trendData.change.toFixed(4)}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 汇率走势图 - 移到左侧区域底部 */}
              <div className="mt-6">
                <CurrencyTrendChart
                  fromCurrency={fromCurrency}
                  toCurrency={toCurrency}
                />
              </div>

            </div>
          </div>
        </div>

        {/* 右侧热门货币 */}
        <div className="xl:sticky xl:top-6 xl:self-start h-full">
          {/* 热门货币 */}
          <div className="card p-6 shadow-lg border-0 h-full flex flex-col" style={{ background: 'linear-gradient(135deg, #FFFFFF 0%, #FAFAFA 100%)' }}>
            <div className="flex items-center mb-6">
              <div className="w-8 h-8 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg flex items-center justify-center mr-3">
                <Star className="h-4 w-4 text-white" />
              </div>
              <h4 className="text-lg font-bold" style={{ color: '#232F3E' }}>{getTranslation(currentLanguage, 'ccPopularCurrencies')}</h4>
            </div>
            <div className="space-y-3 flex-1">
              {POPULAR_CURRENCIES.map((currency) => {
                // 获取当前汇率数据 (1 USD = ? currency)
                const currentRate = currencyService.convert(1, 'USD', currency);
                const info = getCurrencyInfo(currency, currentLanguage);
                return (
                  <div key={currency} className="flex items-center justify-between p-4 rounded-xl border-2 border-gray-100 hover:border-orange-300 hover:bg-gradient-to-r hover:from-orange-50 hover:to-orange-100 transition-all duration-300 cursor-pointer shadow-sm hover:shadow-md transform hover:scale-105"
                       onClick={() => {
                         if (fromCurrency !== currency) {
                           setToCurrency(currency);
                         } else {
                           setFromCurrency(currency);
                         }
                       }}>
                    <div className="flex items-center">
                      <span className="text-2xl mr-3">{info.flag}</span>
                      <div>
                        <div className="text-base font-bold" style={{ color: '#232F3E' }}>
                          {currency}
                        </div>
                        <div className="text-sm text-gray-500">
                          {info.name}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold" style={{ color: '#FF9900' }}>
                        {currentRate ? formatCurrencyAmount(currentRate, currency) : '--'}
                      </div>
                      <div className="text-xs text-gray-500 font-medium">
                        vs USD
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* 工具详细信息 */}
      <ToolInfoSections />
    </div>
  );
};

export default CurrencyConverter;
