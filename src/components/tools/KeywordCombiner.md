# 关键词组合器 (Keyword Combiner)

## 📋 工具概述

关键词组合器是一个专为Amazon卖家设计的智能关键词生成工具，能够自动生成所有可能的关键词组合，用于优化广告投放和Listing关键词策略。

## 🎯 主要功能

### 核心功能
- **智能组合生成**: 输入2-3组不同的关键词，自动生成所有可能的组合
- **多组支持**: 支持2-3个关键词组，每组可包含多个关键词
- **实时预览**: 实时显示生成的组合数量和内容
- **灵活排序**: 支持按原始顺序、长度、词数排序
- **批量操作**: 支持选择多个组合进行批量复制

### 交互功能
- **一键复制**: 单击任意组合即可复制到剪贴板
- **批量选择**: 支持多选组合进行批量操作
- **导出功能**: 支持将所有组合导出为TXT文件
- **清空重置**: 一键清空所有输入内容

## 🌍 多语言支持

工具完整支持6种语言：
- 🇨🇳 简体中文 (zh)
- 🇭🇰 繁体中文 (zh-TW)
- 🇺🇸 英文 (en)
- 🇯🇵 日文 (ja)
- 🇮🇩 印尼文 (id)
- 🇻🇳 越南文 (vi)

## 💡 使用场景

### Amazon PPC广告
- 生成长尾关键词组合用于精准投放
- 测试不同关键词组合的转化效果
- 优化广告投放ROI

### Listing优化
- 在产品标题中合理分布关键词
- 优化产品要点和描述的关键词密度
- 提升搜索排名和曝光度

### 关键词研究
- 发现竞争度较低的长尾关键词
- 扩展关键词库
- 分析关键词组合趋势

## 🚀 使用方法

### 基本操作
1. **输入关键词**: 在关键词组中输入相关词汇，用逗号或换行分隔
2. **添加组别**: 点击"添加组"按钮增加第三个关键词组（可选）
3. **查看结果**: 系统自动生成所有可能的组合
4. **选择排序**: 根据需要选择排序方式
5. **复制使用**: 点击组合进行复制或批量导出

### 实际示例
```
关键词组1: 无线, 蓝牙, 高品质
关键词组2: 耳机, 音箱
关键词组3: 黑色, 白色

生成结果:
- 无线耳机黑色
- 无线耳机白色
- 无线音箱黑色
- 无线音箱白色
- 蓝牙耳机黑色
- 蓝牙耳机白色
- 蓝牙音箱黑色
- 蓝牙音箱白色
- 高品质耳机黑色
- 高品质耳机白色
- 高品质音箱黑色
- 高品质音箱白色
```

## 📊 技术特性

### 性能优化
- **高效算法**: 使用递归算法快速生成组合
- **内存优化**: 智能管理大量组合数据
- **实时计算**: 输入变化时实时更新结果

### 用户体验
- **响应式设计**: 完美适配桌面端和移动端
- **直观界面**: 清晰的视觉层次和操作流程
- **快捷操作**: 支持键盘快捷键和批量操作

## 🎨 设计系统

### 视觉风格
- **主色调**: 紫色渐变 (Purple to Pink)
- **图标**: Shuffle (洗牌图标)
- **布局**: 卡片式设计，清晰分区

### 交互设计
- **悬停效果**: 鼠标悬停时的视觉反馈
- **选中状态**: 清晰的选中状态指示
- **加载动画**: 平滑的状态转换动画

## 🔧 开发信息

### 文件结构
```
src/components/tools/
├── KeywordCombiner.tsx        # 主组件文件
├── KeywordCombiner.md          # 技术文档（本文件）
src/locales/modules/tools/KeywordCombiner/
├── zh.ts                       # 简体中文翻译
├── zh-TW.ts                   # 繁体中文翻译
├── en.ts                      # 英语翻译
├── ja.ts                      # 日语翻译
├── id.ts                      # 印尼语翻译
└── vi.ts                      # 越南语翻译
src/test/
└── keywordCombiner.test.ts    # 功能测试文件
```

### 核心算法
```typescript
const generateRecursive = (currentCombination: string[], groupIndex: number) => {
  if (groupIndex >= validGroups.length) {
    combinations.push(currentCombination.join(' '));
    return;
  }
  
  for (const keyword of validGroups[groupIndex].keywords) {
    generateRecursive([...currentCombination, keyword], groupIndex + 1);
  }
};
```

## 📈 使用建议

### 最佳实践
1. **关键词数量**: 每组建议不超过10个关键词，确保生成速度
2. **组合筛选**: 生成后筛选最相关的组合使用
3. **定期更新**: 根据市场变化定期更新关键词组合

### 注意事项
- 避免关键词堆砌，保持内容自然
- 注意关键词的相关性和搜索意图
- 定期分析组合的转化效果

## 🚀 未来规划

### 功能扩展
- [ ] 关键词热度分析
- [ ] 竞争度评估
- [ ] 搜索量预测
- [ ] 自动优化建议

### 性能优化
- [ ] 大数据量处理优化
- [ ] 缓存机制改进
- [ ] 导出格式扩展

---

**开发者**: AI Assistant  
**最后更新**: 2025年1月11日  
**版本**: v1.0.0
