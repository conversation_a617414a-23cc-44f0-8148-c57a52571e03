import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>Triangle, CheckCircle, Copy, Check, RefreshCw, Lightbulb, Target, Shield, Zap, Eye, XCircle, Info, Database } from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
import { getTranslation } from '../../utils/translations';
import { fixTitleCompliance } from '../../lib/ai';
import { checkTitleCompliance, type ComplianceResult } from '../../utils/complianceCheck';
import { saveTitleFixHistory } from '../../utils/titleFixHistory';
import { getCachedTitleFix, cacheTitleFix } from '../../utils/titleFixCache';
import { checkAIUsageLimit, recordAIUsage, type UsageStats } from '../../utils/aiUsageLimit';
import { isAIAvailable } from '../../lib/ai';
import ComparisonView from './TitleFixer/ComparisonView';
import UsageLimitDisplay from './TitleFixer/UsageLimitDisplay';
import ViolationHighlightComponent from './TitleFixer/ViolationHighlight';
import ToolInfoSections from './TitleFixer/ToolInfoSections';
import AIServiceStatus from '../shared/AIServiceStatus';

interface TitleFixResult {
  fixedTitles: string[];
  complianceScore: number;
}

// 兼容旧格式的API响应类型
interface LegacyTitleFixResult {
  fixedTitle?: string;
  fixedTitles?: string[];
  changes?: string[];
  explanation?: string | object;
  complianceScore?: number;
}

const TitleFixer = () => {
  const { currentLanguage } = useLanguage();
  const [originalTitle, setOriginalTitle] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [category, setCategory] = useState('general');
  const [fixResult, setFixResult] = useState<TitleFixResult | null>(null);
  const [isFixing, setIsFixing] = useState(false);
  const [aiError, setAiError] = useState<string | null>(null);
  const [copiedTitle, setCopiedTitle] = useState(false);
  const [selectedTitleIndex, setSelectedTitleIndex] = useState(0);
  const [complianceResult, setComplianceResult] = useState<ComplianceResult | null>(null);
  const [usageStats, setUsageStats] = useState<UsageStats | null>(null);
  const [showUsageInfo, setShowUsageInfo] = useState(false);
  const [isUsingCache, setIsUsingCache] = useState(false);

  // 检查AI服务是否可用
  const isAvailable = isAIAvailable();

  // 类别选项 - 简化为两个主要类别
  const categories = [
    { id: 'general', name: getTranslation(currentLanguage, 'categoryGeneral'), maxLength: 200 },
    { id: 'apparel', name: getTranslation(currentLanguage, 'categoryApparel'), maxLength: 125 }
  ];
  
  // 获取当前的最大字符限制
  const getMaxLength = () => {
    if (category === 'apparel') {
      return 125;
    }
    return 200; // 美国市场默认200字符
  };

  useEffect(() => {
    // 初始化使用统计
    updateUsageStats();
  }, []);

  // 更新使用统计
  const updateUsageStats = () => {
    const stats = checkAIUsageLimit('title-fixer'); // 自动使用每日10次，每小时3次的配置
    setUsageStats(stats);
  };

  // 实时合规检查
  useEffect(() => {
    if (originalTitle.trim()) {
      const result = checkTitleCompliance(originalTitle, category, currentLanguage);
      setComplianceResult(result);
    } else {
      setComplianceResult(null);
    }
  }, [originalTitle, category, currentLanguage]);

  const handleFixTitle = async () => {
    if (!originalTitle.trim()) {
      setAiError(getTranslation(currentLanguage, 'tfInputRequired'));
      return;
    }

    // 检查使用限制
    const currentUsageStats = checkAIUsageLimit('title-fixer');
    setUsageStats(currentUsageStats);

    // 传递实时合规检查的违规项给AI
    const violations = complianceResult?.violations || [];

    // 首先检查缓存
    const cachedResult = getCachedTitleFix(originalTitle, 'US', category, violations);

    if (cachedResult) {
      console.log('Using cached result for title fix');
      setIsUsingCache(true);
      setFixResult(cachedResult.result);
      setShowUsageInfo(true);
      return;
    }

    // 缓存未命中，需要调用AI
    if (currentUsageStats.isLimited) {
      setAiError(getTranslation(currentLanguage, 'tfUsageLimitExceeded'));
      setShowUsageInfo(true);
      return;
    }

    setIsFixing(true);
    setAiError(null);
    setFixResult(null);
    setIsUsingCache(false);

    try {
      // 检查 AI 服务可用性
      if (!isAIAvailable()) {
        setAiError(getTranslation(currentLanguage, 'aiServiceUnavailable'));
        return;
      }

      // 使用专业的标题修复函数
      const result = await fixTitleCompliance(
        originalTitle,
        errorMessage,
        'US', // 固定使用美国市场
        category,
        violations,
        'STANDARD', // 使用标准模型确保质量
        currentLanguage // 传递当前语言
      );

      // fixTitleCompliance 已经返回解析好的结果
      const parsedResult = result;

      // 记录AI使用
      recordAIUsage('title-fixer', !!parsedResult);
      updateUsageStats();

      if (parsedResult) {
        // 处理和清理API响应
        let fixedTitles: string[] = [];
        let complianceScore: number = 0;

        // 确保 fixedTitles 是数组
        if (parsedResult.fixedTitles && Array.isArray(parsedResult.fixedTitles)) {
          fixedTitles = parsedResult.fixedTitles;
        } else if (parsedResult.fixedTitle) {
          // 转换旧格式为新格式
          fixedTitles = [parsedResult.fixedTitle];
        } else {
          fixedTitles = [getTranslation(currentLanguage, 'tfFixFailed')];
        }

        // 确保 complianceScore 是数字
        complianceScore = typeof parsedResult.complianceScore === 'number' ? parsedResult.complianceScore : 85;

        const cleanResult = {
          fixedTitles,
          complianceScore
        };

        // 缓存清理后的结果
        cacheTitleFix(originalTitle, 'US', category, violations, cleanResult);

        setFixResult(cleanResult);

        // 保存到历史记录
        saveTitleFixHistory({
          originalTitle,
          fixedTitles,
          selectedVersion: 0, // 默认选择第一个版本
          marketplace: 'US', // 固定使用美国市场
          category,
          complianceScore,
          violations: complianceResult?.violations || []
        });
      }
    } catch (error) {
      console.error('Title fix failed:', error);
      setAiError(error instanceof Error ? error.message : getTranslation(currentLanguage, 'tfAiServiceUnavailable'));
    } finally {
      setIsFixing(false);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedTitle(true);
      setTimeout(() => setCopiedTitle(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const getSelectedTitle = () => {
    if (!fixResult || !fixResult.fixedTitles || fixResult.fixedTitles.length === 0) {
      return '';
    }
    return fixResult.fixedTitles[selectedTitleIndex] || fixResult.fixedTitles[0];
  };

  // 重置选中的标题索引当结果改变时
  React.useEffect(() => {
    if (fixResult && fixResult.fixedTitles) {
      setSelectedTitleIndex(0);
    }
  }, [fixResult]);

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-emerald-600';
    if (score >= 80) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    if (score >= 60) return 'text-orange-600';
    return 'text-red-600';
  };

  const getScoreBackground = (score: number) => {
    if (score >= 90) return 'bg-emerald-50 border-emerald-200';
    if (score >= 80) return 'bg-green-50 border-green-200';
    if (score >= 70) return 'bg-yellow-50 border-yellow-200';
    if (score >= 60) return 'bg-orange-50 border-orange-200';
    return 'bg-red-50 border-red-200';
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto w-full px-4 sm:px-6 lg:px-8 py-4">
        <div className="card shadow-amazon-xl mb-8">
          {/* Header */}
          <div className="tool-header flex-shrink-0">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="flex items-center">
                <div className="tool-header-icon">
                  <Wrench className="h-6 w-6 sm:h-8 sm:w-8" />
                </div>
                <div>
                  <h1 className="tool-header-title text-lg sm:text-xl lg:text-2xl">
                    {getTranslation(currentLanguage, 'titleFixer')}
                  </h1>
                  <p className="tool-header-description text-sm sm:text-base">
                    {getTranslation(currentLanguage, 'titleFixerHeader')}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                {isAvailable ? (
                  <div className="flex items-center bg-white/20 backdrop-blur-sm px-3 py-2 sm:px-4 rounded-amazon-lg border border-white/30">
                    <Zap className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                    <span className="text-xs sm:text-sm font-medium">{getTranslation(currentLanguage, 'tfAiSmartFix')}</span>
                  </div>
                ) : (
                  <div className="flex items-center bg-white/10 backdrop-blur-sm px-3 py-2 sm:px-4 rounded-amazon-lg border border-white/20">
                    <AlertTriangle className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                    <span className="text-xs sm:text-sm font-medium">{getTranslation(currentLanguage, 'tfBasicVersion')}</span>
                  </div>
                )}

                {/* 使用统计按钮 */}
                {usageStats && (
                  <button
                    onClick={() => setShowUsageInfo(!showUsageInfo)}
                    className={`flex items-center px-2 py-1 rounded-lg text-xs font-medium transition-colors ${
                      usageStats.isLimited
                        ? 'bg-red-100 text-red-700 border border-red-200'
                        : 'bg-green-100 text-green-700 border border-green-200'
                    }`}
                  >
                    <Info className="h-3 w-3 mr-1" />
                    {usageStats.totalUsage}/10
                  </button>
                )}
              </div>
            </div>
          </div>

          <div className="flex-1 p-3 sm:p-4 lg:p-6 overflow-y-auto min-h-0">
            {/* Top Section: Category Selection and AI Status */}
            <div className="mb-3 sm:mb-4 lg:mb-6 space-y-3 sm:space-y-4">
              <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-3 sm:p-4">
                <label className="label text-sm">
                  {getTranslation(currentLanguage, 'tfCategory')}
                </label>
                <select
                  value={category}
                  onChange={(e) => setCategory(e.target.value)}
                  className="select text-sm"
                >
                  {categories.map((cat) => (
                    <option key={cat.id} value={cat.id}>
                      {cat.name}
                    </option>
                  ))}
                </select>
                <p className="text-xs text-amazon-gray-500 mt-1">
                  {category === 'apparel'
                    ? getTranslation(currentLanguage, 'tfApparelLimit')
                    : getTranslation(currentLanguage, 'tfUsMarketLimit')
                  }
                </p>
              </div>

              {/* AI 服务状态显示 */}
              {isAvailable && (
                <AIServiceStatus className="shadow-sm" />
              )}
            </div>

            {/* Main Content Grid - 响应式布局优化 */}
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-3 sm:gap-4 lg:gap-6">
              {/* Left Column: Input Section - 占5列 */}
              <div className="lg:col-span-5 space-y-3 sm:space-y-4">

              {/* Original Title Input */}
              <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-3 sm:p-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {getTranslation(currentLanguage, 'tfOriginalTitle')}
                  <span className="text-red-500 ml-1">*</span>
                </label>
                <div className="relative">
                  <textarea
                    value={originalTitle}
                    onChange={(e) => setOriginalTitle(e.target.value)}
                    className={`w-full px-3 py-2 sm:px-4 sm:py-3 border rounded-lg focus:ring-2 focus:border-transparent transition-all duration-200 resize-none text-sm sm:text-base min-h-[100px] ${
                      originalTitle.length > getMaxLength()
                        ? 'border-red-300 focus:ring-red-500'
                        : 'border-gray-300 focus:ring-emerald-500'
                    }`}
                    rows={4}
                    placeholder={getTranslation(currentLanguage, 'tfOriginalTitlePlaceholder')}
                    maxLength={500}
                  />
                  {originalTitle.length > getMaxLength() * 0.8 && (
                    <div className="absolute top-2 right-2">
                      <div className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full ${
                        originalTitle.length > getMaxLength() ? 'bg-red-500' : 'bg-amber-500'
                      }`}></div>
                    </div>
                  )}
                </div>
                <div className="flex flex-col sm:flex-row sm:justify-between text-xs sm:text-sm mt-2 gap-1 sm:gap-0">
                  <span className={`${originalTitle.length > getMaxLength() ? 'text-red-600 font-medium' : 'text-gray-500'}`}>
                    {originalTitle.length} / {getMaxLength()} {getTranslation(currentLanguage, 'tfCharacters')}
                  </span>
                  <span className="text-gray-500">
                    {originalTitle.trim().split(/\s+/).filter(w => w.length > 0).length} {getTranslation(currentLanguage, 'tfWords')}
                  </span>
                </div>
                {originalTitle.length > getMaxLength() && (
                  <div className="mt-2 p-2 sm:p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-xs sm:text-sm text-red-700 flex items-start">
                      <AlertTriangle className="h-3 w-3 sm:h-4 sm:w-4 mr-2 mt-0.5 flex-shrink-0" />
                      {getTranslation(currentLanguage, 'tfTitleTooLong').replace('{maxLength}', getMaxLength().toString())}
                    </p>
                  </div>
                )}
              </div>

              {/* Violation Highlight for Original Title */}
              {complianceResult && originalTitle.trim() && complianceResult.violations.length > 0 && (
                <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-3 sm:p-4">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">{getTranslation(currentLanguage, 'tfViolationHighlight')}</h5>
                  <ViolationHighlightComponent
                    text={originalTitle}
                    violations={complianceResult.violations}
                    className=""
                  />
                </div>
              )}

              {/* Amazon Error Message Input */}
              <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-3 sm:p-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {getTranslation(currentLanguage, 'tfAmazonError')}
                  <span className="text-gray-400 ml-1 text-xs">({getTranslation(currentLanguage, 'tfOptional')})</span>
                </label>
                <div className="relative">
                  <textarea
                    value={errorMessage}
                    onChange={(e) => setErrorMessage(e.target.value)}
                    className="w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200 resize-none text-sm sm:text-base"
                    rows={3}
                    placeholder={getTranslation(currentLanguage, 'tfAmazonErrorPlaceholder')}
                  />
                </div>
                <div className="mt-2 p-2 bg-amber-50/50 border border-amber-100 rounded-lg">
                  <p className="text-xs sm:text-sm text-amber-800 flex items-start">
                    <Info className="h-3 w-3 sm:h-4 sm:w-4 mr-2 mt-0.5 flex-shrink-0" />
                    <span>
                      {getTranslation(currentLanguage, 'tfAmazonErrorHelp')}
                      <br />
                      <span className="text-xs text-amber-600 mt-1 block">
                        {getTranslation(currentLanguage, 'tfAmazonErrorExample')}
                      </span>
                    </span>
                  </p>
                </div>
              </div>

              {/* AI Service Status */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="flex items-center text-blue-700 text-sm">
                  <Zap className="h-4 w-4 mr-2" />
                  <span>{getTranslation(currentLanguage, 'tfUsingGeminiService')}</span>
                  {isAIAvailable() ? (
                    <CheckCircle className="h-4 w-4 ml-2 text-green-600" />
                  ) : (
                    <AlertTriangle className="h-4 w-4 ml-2 text-red-600" />
                  )}
                </div>
              </div>

              {/* Fix Title Button */}
              {originalTitle.trim() && (
                <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl border border-purple-200 shadow-sm p-4">
                  {/* 缓存状态指示 */}
                  {isUsingCache && (
                    <div className="mb-3 flex items-center text-green-700 text-xs">
                      <Database className="h-3 w-3 mr-1" />
                      <span>{getTranslation(currentLanguage, 'tfCacheHit')}</span>
                    </div>
                  )}

                  <button
                    onClick={handleFixTitle}
                    disabled={isFixing || (usageStats?.isLimited && !getCachedTitleFix(originalTitle, 'US', category, complianceResult?.violations || []))}
                    className={`w-full flex items-center justify-center px-4 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-lg font-medium transition-all duration-200 transform hover:scale-[1.02] disabled:opacity-50 disabled:transform-none shadow-lg text-sm sm:text-base ${
                      isFixing ? 'cursor-not-allowed' : ''
                    }`}
                  >
                    {isFixing ? (
                      <RefreshCw className="h-4 w-4 sm:h-5 sm:w-5 mr-2 animate-spin" />
                    ) : getCachedTitleFix(originalTitle, 'US', category, complianceResult?.violations || []) ? (
                      <Database className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                    ) : (
                      <Zap className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                    )}
                    {isFixing
                      ? getTranslation(currentLanguage, 'tfFixingInProgress')
                      : getCachedTitleFix(originalTitle, 'US', category, complianceResult?.violations || [])
                        ? getTranslation(currentLanguage, 'tfCacheHit')
                        : getTranslation(currentLanguage, 'tfAiRequest')
                    }
                  </button>
                </div>
              )}

              {/* 使用限制显示 */}
              {showUsageInfo && usageStats && (
                <UsageLimitDisplay
                  usageStats={usageStats}
                  isVisible={showUsageInfo}
                  onRefresh={() => window.location.reload()}
                />
              )}

              {/* AI Error Display */}
              {aiError && (
                <div className="bg-white rounded-xl border border-red-200 shadow-sm p-4">
                  <div className="flex items-start">
                    <AlertTriangle className="h-4 w-4 text-red-600 mr-2 mt-0.5 flex-shrink-0" />
                    <div className="flex-1">
                      <h4 className="text-red-900 font-medium text-sm mb-1">AI 服务提示</h4>
                      <p className="text-red-800 text-xs">{aiError}</p>
                    </div>
                  </div>
                </div>
              )}

              {!isAvailable && (
                <div className="bg-white rounded-xl border border-yellow-200 shadow-sm p-4">
                  <div className="flex items-center">
                    <AlertTriangle className="h-4 w-4 text-yellow-600 mr-2" />
                    <p className="text-yellow-800 text-xs">
                      {getTranslation(currentLanguage, 'tfAiUnavailable')}
                    </p>
                  </div>
                </div>
              )}
              </div>

              {/* Right Column: Analysis Results - 占7列 */}
              <div className="lg:col-span-7 space-y-3 sm:space-y-4">
              
              {/* Overall Score */}
              {fixResult && (
                <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-3 sm:p-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                    <Target className="h-4 w-4 mr-2" />
                    {getTranslation(currentLanguage, 'tfFixResultScore')}
                  </h3>
                  <div className="flex items-center justify-center">
                    <div className={`rounded-xl px-6 py-4 border-2 ${getScoreBackground(fixResult.complianceScore)} text-center`}>
                      <div className={`text-3xl sm:text-4xl font-bold ${getScoreColor(fixResult.complianceScore)}`}>
                        {Math.round(fixResult.complianceScore)}
                      </div>
                      <div className="text-xs text-gray-600 mt-1">
                        {getTranslation(currentLanguage, 'tfComplianceScoreOutOf')}
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              {/* Real-time Compliance Check - 响应式优化 */}
              {complianceResult && originalTitle.trim() && (
                <div className={`bg-white rounded-xl border ${
                  complianceResult.isCompliant
                    ? 'border-emerald-200'
                    : 'border-red-200'
                } shadow-sm`}>

                  {/* 小屏幕：紧凑显示 */}
                  <div className="block sm:hidden p-3">
                    <div className={`flex items-center justify-between text-xs px-2 py-1.5 rounded-full mb-2 ${
                      complianceResult.isCompliant
                        ? 'bg-green-100 text-green-700'
                        : 'bg-red-100 text-red-700'
                    }`}>
                      <div className="flex items-center">
                        <Eye className="h-3 w-3 mr-1.5" />
                        <span className="font-medium">{getTranslation(currentLanguage, 'tfRealTimeCheck')}</span>
                      </div>
                      {complianceResult.isCompliant ? (
                        <CheckCircle className="h-3 w-3" />
                      ) : (
                        <span className="text-xs font-medium">
                          {complianceResult.violations.filter(v => v.severity === 'error').length} 问题
                        </span>
                      )}
                    </div>

                    {/* 违规项：只显示前2个，其余折叠 */}
                    {complianceResult.violations.length > 0 && (
                      <div className="space-y-1">
                        {complianceResult.violations.slice(0, 2).map((violation, index) => (
                          <div key={index} className={`p-1.5 rounded text-xs ${
                            violation.severity === 'error'
                              ? 'bg-red-50 text-red-700 border-l-2 border-red-300'
                              : 'bg-yellow-50 text-yellow-700 border-l-2 border-yellow-300'
                          }`}>
                            <div className="flex items-start">
                              <AlertTriangle className="h-3 w-3 mr-1.5 mt-0.5 flex-shrink-0" />
                              <span className="line-clamp-2">{violation.message}</span>
                            </div>
                          </div>
                        ))}

                        {complianceResult.violations.length > 2 && (
                          <details className="group">
                            <summary className="text-xs text-blue-600 cursor-pointer hover:text-blue-800 py-1">
                              查看更多 ({complianceResult.violations.length - 2} 项)
                            </summary>
                            <div className="space-y-1 mt-1">
                              {complianceResult.violations.slice(2).map((violation, index) => (
                                <div key={index + 2} className={`p-1.5 rounded text-xs ${
                                  violation.severity === 'error'
                                    ? 'bg-red-50 text-red-700 border-l-2 border-red-300'
                                    : 'bg-yellow-50 text-yellow-700 border-l-2 border-yellow-300'
                                }`}>
                                  <div className="flex items-start">
                                    <AlertTriangle className="h-3 w-3 mr-1.5 mt-0.5 flex-shrink-0" />
                                    <span>{violation.message}</span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </details>
                        )}
                      </div>
                    )}
                  </div>

                  {/* 大屏幕：完整显示 */}
                  <div className="hidden sm:block p-4">
                    <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                      <Eye className="h-5 w-5 mr-2" />
                      {getTranslation(currentLanguage, 'tfRealTimeCheck')}
                    </h3>

                    <div className={`flex items-center text-sm px-3 py-2 rounded-full mb-4 ${
                      complianceResult.isCompliant
                        ? 'bg-green-100 text-green-700'
                        : 'bg-red-100 text-red-700'
                    }`}>
                      {complianceResult.isCompliant ? (
                        <>
                          <CheckCircle className="h-4 w-4 mr-2" />
                          {getTranslation(currentLanguage, 'tfNoViolations')}
                        </>
                      ) : (
                        <>
                          <XCircle className="h-4 w-4 mr-2" />
                          {complianceResult.violations.filter(v => v.severity === 'error').length} {getTranslation(currentLanguage, 'tfViolations')}
                        </>
                      )}
                    </div>

                    {/* Violations */}
                    {complianceResult.violations.length > 0 && (
                      <div className="space-y-2 mb-4">
                        <h5 className="text-sm font-medium text-gray-700">{getTranslation(currentLanguage, 'tfViolationItems')}:</h5>
                        <div className="space-y-2">
                          {complianceResult.violations.map((violation, index) => (
                            <div key={index} className={`p-3 rounded-lg text-sm ${
                              violation.severity === 'error'
                                ? 'bg-red-100 border border-red-200 text-red-800'
                                : 'bg-yellow-100 border border-yellow-200 text-yellow-800'
                            }`}>
                              <div className="flex items-start">
                                <AlertTriangle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                                <span>{violation.message}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Suggestions */}
                    {complianceResult.suggestions.length > 0 && (
                      <div>
                        <h5 className="text-sm font-medium text-gray-700 mb-2">{getTranslation(currentLanguage, 'tfSuggestionsList')}:</h5>
                        <ul className="space-y-2">
                          {complianceResult.suggestions.slice(0, 3).map((suggestion, index) => (
                            <li key={index} className="text-sm text-gray-600 flex items-start">
                              <span className="w-2 h-2 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                              {suggestion}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Fixed Titles Results */}
              {fixResult && (
                <>
                  {/* Fixed Titles Display */}
                  <div className="bg-white rounded-xl border border-green-200 p-3 sm:p-4 shadow-sm">
                    <h4 className="text-sm font-medium text-gray-700 mb-3 flex flex-col sm:flex-row sm:items-center gap-2">
                      <div className="flex items-center">
                        <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                        {getTranslation(currentLanguage, 'tfFixedTitles')}
                      </div>
                      <span className="text-xs sm:text-sm text-green-600 bg-green-100 px-2 py-1 sm:px-3 rounded-full font-medium self-start sm:ml-auto">
                        {fixResult.fixedTitles?.length || 1} {getTranslation(currentLanguage, 'tfVersionCount')}
                      </span>
                    </h4>

                    {/* Title Tabs */}
                    {fixResult.fixedTitles && fixResult.fixedTitles.length > 1 && (
                      <div className="flex space-x-1 mb-4 bg-white/70 p-1 rounded-xl shadow-inner">
                        {fixResult.fixedTitles.map((_, index) => (
                          <button
                            key={index}
                            onClick={() => setSelectedTitleIndex(index)}
                            className={`flex-1 px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                              selectedTitleIndex === index
                                ? 'bg-white text-green-800 shadow-md transform scale-105'
                                : 'text-green-700 hover:text-green-800 hover:bg-white/50'
                            }`}
                          >
                            <div className="flex flex-col items-center">
                              <span className="text-xs opacity-75">
                                {index === 0 ? getTranslation(currentLanguage, 'tfMinimalChanges') :
                                 index === 1 ? getTranslation(currentLanguage, 'tfSeoOptimized') :
                                 getTranslation(currentLanguage, 'tfConversionOptimized')}
                              </span>
                              <span>{getTranslation(currentLanguage, 'tfVersion')} {index + 1}</span>
                            </div>
                          </button>
                        ))}
                      </div>
                    )}

                    {/* Selected Title Display */}
                    <div className="bg-white border border-green-200 rounded-xl p-3 sm:p-4 lg:p-5 mb-3 sm:mb-4 shadow-sm">
                      <div className="flex items-start justify-between mb-2">
                        <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full font-medium">
                          {getTranslation(currentLanguage, 'tfOptimizedTitle')}
                        </span>
                      </div>

                      {/* Fixed Title with Violation Highlight */}
                      {(() => {
                        const selectedTitle = getSelectedTitle();
                        const fixedTitleCompliance = checkTitleCompliance(selectedTitle, category, currentLanguage);

                        return fixedTitleCompliance.violations.length > 0 ? (
                          <div className="mb-3">
                            <ViolationHighlightComponent
                              text={selectedTitle}
                              violations={fixedTitleCompliance.violations}
                              className=""
                            />
                          </div>
                        ) : (
                          <p className="text-green-800 leading-relaxed text-base font-medium mb-3">
                            {selectedTitle}
                          </p>
                        );
                      })()}

                      <div className="flex justify-between text-sm text-green-600">
                        <span>{getSelectedTitle().length} {getTranslation(currentLanguage, 'tfCharacters')}</span>
                        <span>{getSelectedTitle().trim().split(/\s+/).filter(w => w.length > 0).length} {getTranslation(currentLanguage, 'tfWords')}</span>
                      </div>
                    </div>

                    {/* Copy Button */}
                    <button
                      onClick={() => copyToClipboard(getSelectedTitle())}
                      className={`w-full py-3 px-4 rounded-xl font-medium transition-all duration-200 flex items-center justify-center ${
                        copiedTitle
                          ? 'bg-green-200 text-green-800 border-2 border-green-300'
                          : 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-md hover:shadow-lg'
                      }`}
                    >
                      {copiedTitle ? (
                        <>
                          <Check className="h-5 w-5 mr-2" />
                          {getTranslation(currentLanguage, 'tfCopiedToClipboard')}
                        </>
                      ) : (
                        <>
                          <Copy className="h-5 w-5 mr-2" />
                          {getTranslation(currentLanguage, 'tfCopyOptimizedTitle')}
                        </>
                      )}
                    </button>
                  </div>

                  {/* Comparison View */}
                  <ComparisonView
                    originalTitle={originalTitle}
                    fixedTitle={getSelectedTitle()}
                    className="mt-3 sm:mt-4"
                  />

                  {/* Changes and Explanation sections removed per user request */}
                </>
              )}

              {!fixResult && !complianceResult && (
                <div className="bg-white rounded-xl border border-gray-200 p-6 sm:p-8 text-center shadow-sm">
                  <Wrench className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">{getTranslation(currentLanguage, 'tfStartTitleFix')}</h4>
                  <p className="text-gray-600">{getTranslation(currentLanguage, 'tfInputTitleAndError')}</p>
                </div>
              )}
              </div>
            </div>
          </div>

          {/* Policy Tips - 所有屏幕尺寸都默认收起 */}
          <div className="flex-shrink-0 bg-gradient-to-r from-orange-50 to-red-50 border-t border-orange-200">
            <details className="group">
              <summary className="flex items-center justify-between p-3 sm:p-4 cursor-pointer hover:bg-orange-100/50 transition-colors">
                <div className="flex items-center">
                  <AlertTriangle className="h-4 w-4 sm:h-5 sm:w-5 text-orange-500 mr-2 sm:mr-3 flex-shrink-0" />
                  <span className="font-medium text-gray-900 text-sm sm:text-base">{getTranslation(currentLanguage, 'tfPolicyTips')}</span>
                </div>
                <svg className="w-4 h-4 sm:w-5 sm:h-5 text-orange-500 transition-transform group-open:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </summary>

              <div className="px-3 pb-3 sm:px-4 sm:pb-4 lg:px-6 lg:pb-6">
                {/* 小屏幕：垂直布局 */}
                <div className="block sm:hidden space-y-2 text-xs text-gray-700">
                  <div className="bg-white/70 rounded-lg p-2">
                    <p className="font-medium text-gray-800 mb-1">{getTranslation(currentLanguage, 'tfCharacterLimit')}</p>
                    <p>• {getTranslation(currentLanguage, 'tfCharacterLimitDesc')}</p>
                  </div>
                  <div className="bg-white/70 rounded-lg p-2">
                    <p className="font-medium text-gray-800 mb-1">{getTranslation(currentLanguage, 'tfSpecialCharacters')}</p>
                    <p>• {getTranslation(currentLanguage, 'tfSpecialCharactersDesc')}</p>
                  </div>
                  <div className="bg-white/70 rounded-lg p-2">
                    <p className="font-medium text-gray-800 mb-1">{getTranslation(currentLanguage, 'tfRepeatedWords')}</p>
                    <p>• {getTranslation(currentLanguage, 'tfRepeatedWordsDesc')}</p>
                  </div>
                  <div className="bg-white/70 rounded-lg p-2">
                    <p className="font-medium text-gray-800 mb-1">{getTranslation(currentLanguage, 'tfBrandException')}</p>
                    <p>• {getTranslation(currentLanguage, 'tfBrandExceptionDesc')}</p>
                  </div>
                </div>

                {/* 大屏幕：网格布局 */}
                <div className="hidden sm:block">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 text-sm text-gray-700">
                    <div>
                      <p className="font-medium mb-1">{getTranslation(currentLanguage, 'tfCharacterLimit')}</p>
                      <p>• {getTranslation(currentLanguage, 'tfCharacterLimitDesc')}</p>
                    </div>
                    <div>
                      <p className="font-medium mb-1">{getTranslation(currentLanguage, 'tfSpecialCharacters')}</p>
                      <p>• {getTranslation(currentLanguage, 'tfSpecialCharactersDesc')}</p>
                    </div>
                    <div>
                      <p className="font-medium mb-1">{getTranslation(currentLanguage, 'tfRepeatedWords')}</p>
                      <p>• {getTranslation(currentLanguage, 'tfRepeatedWordsDesc')}</p>
                    </div>
                    <div>
                      <p className="font-medium mb-1">{getTranslation(currentLanguage, 'tfBrandException')}</p>
                      <p>• {getTranslation(currentLanguage, 'tfBrandExceptionDesc')}</p>
                    </div>
                  </div>
                </div>
              </div>
            </details>
          </div>
        </div>

        {/* 工具详细信息部分 */}
        <ToolInfoSections />
      </div>
    </div>
  );
};

export default TitleFixer;
