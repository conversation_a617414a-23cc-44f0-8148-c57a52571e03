import { useState, useMemo, useEffect } from 'react';
import { Calendar, TrendingUp, Gift, ShoppingCart, Star, AlertCircle, Clock } from 'lucide-react';
import { getTranslation } from '../../locales';
import { useLanguage } from '../../hooks/useLanguage';
import { format, differenceInDays, differenceInHours, differenceInMinutes, isAfter, parseISO } from 'date-fns';

interface Holiday {
  name: string;
  date: string;
  dateRange?: string;
  value: 'high' | 'medium' | 'low';
  category: 'major' | 'shopping' | 'seasonal' | 'special';
  actionTips: string[];
  description: string;
}

const holidays2025: Holiday[] = [
  {
    name: 'backToSchool',
    date: '2025-08-01',
    dateRange: '2025-07-15 to 2025-08-31',
    value: 'high',
    category: 'seasonal',
    actionTips: ['prepareInventory', 'optimizeListings', 'runPromotions'],
    description: 'backToSchoolDesc'
  },
  {
    name: 'laborDay',
    date: '2025-09-01',
    value: 'medium',
    category: 'shopping',
    actionTips: ['weekendSales', 'outdoorProducts', 'lastSummerDeals'],
    description: 'laborDayDesc'
  },
  {
    name: 'primeDay2',
    date: '2025-10-15',
    dateRange: '2025-10-14 to 2025-10-15',
    value: 'high',
    category: 'major',
    actionTips: ['submitDeals', 'increaseInventory', 'optimizePPC'],
    description: 'primeDay2Desc'
  },
  {
    name: 'halloween',
    date: '2025-10-31',
    value: 'medium',
    category: 'seasonal',
    actionTips: ['costumeInventory', 'candyBulkSales', 'decorations'],
    description: 'halloweenDesc'
  },
  {
    name: 'thanksgiving',
    date: '2025-11-27',
    value: 'medium',
    category: 'seasonal',
    actionTips: ['kitchenware', 'hostingSupplies', 'earlyBF'],
    description: 'thanksgivingDesc'
  },
  {
    name: 'blackFriday',
    date: '2025-11-28',
    value: 'high',
    category: 'major',
    actionTips: ['deepDiscounts', 'lightningDeals', 'bundleOffers'],
    description: 'blackFridayDesc'
  },
  {
    name: 'cyberMonday',
    date: '2025-12-01',
    value: 'high',
    category: 'major',
    actionTips: ['techDeals', 'onlineExclusive', 'emailMarketing'],
    description: 'cyberMondayDesc'
  },
  {
    name: 'christmas',
    date: '2025-12-25',
    dateRange: '2025-12-01 to 2025-12-24',
    value: 'high',
    category: 'major',
    actionTips: ['giftWrapping', 'expeditedShipping', 'giftGuides'],
    description: 'christmasDesc'
  }
];

const holidays2026: Holiday[] = [
  {
    name: 'newYear',
    date: '2026-01-01',
    value: 'medium',
    category: 'seasonal',
    actionTips: ['resolutionProducts', 'fitnessGear', 'organization'],
    description: 'newYearDesc'
  },
  {
    name: 'valentinesDay',
    date: '2026-02-14',
    value: 'medium',
    category: 'seasonal',
    actionTips: ['giftSets', 'romanticItems', 'lastMinuteShipping'],
    description: 'valentinesDayDesc'
  },
  {
    name: 'easter',
    date: '2026-04-05',
    value: 'medium',
    category: 'seasonal',
    actionTips: ['springProducts', 'kidsToys', 'candyBaskets'],
    description: 'easterDesc'
  },
  {
    name: 'mothersDay',
    date: '2026-05-10',
    value: 'medium',
    category: 'seasonal',
    actionTips: ['giftIdeas', 'personalizedItems', 'flowerDelivery'],
    description: 'mothersDayDesc'
  },
  {
    name: 'memorialDay',
    date: '2026-05-25',
    value: 'medium',
    category: 'shopping',
    actionTips: ['outdoorGear', 'summerStart', 'patrioticItems'],
    description: 'memorialDayDesc'
  },
  {
    name: 'fathersDay',
    date: '2026-06-21',
    value: 'medium',
    category: 'seasonal',
    actionTips: ['toolsElectronics', 'sportsGear', 'grillingSeason'],
    description: 'fathersDayDesc'
  },
  {
    name: 'independenceDay',
    date: '2026-07-04',
    value: 'medium',
    category: 'seasonal',
    actionTips: ['patrioticDecor', 'outdoorParty', 'fireworksSafety'],
    description: 'independenceDayDesc'
  },
  {
    name: 'primeDay1',
    date: '2026-07-15',
    dateRange: '2026-07-14 to 2026-07-15',
    value: 'high',
    category: 'major',
    actionTips: ['earlyAccess', 'primeExclusive', 'stockUp'],
    description: 'primeDay1Desc'
  }
];

export default function AmzSellerCalendar() {
  const { currentLanguage } = useLanguage();
  const [selectedYear, setSelectedYear] = useState<'2025' | '2026'>('2025');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [currentTime, setCurrentTime] = useState(new Date());

  // 更新当前时间，用于倒计时
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // 每分钟更新一次

    return () => clearInterval(timer);
  }, []);

  const holidays = selectedYear === '2025' ? holidays2025 : holidays2026;

  // 过滤未来的节日并按日期排序
  const filteredHolidays = useMemo(() => {
    const now = new Date();
    let filtered = holidays.filter(holiday => {
      const holidayDate = parseISO(holiday.date);
      return isAfter(holidayDate, now) || format(holidayDate, 'yyyy-MM-dd') === format(now, 'yyyy-MM-dd');
    });

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(h => h.category === selectedCategory);
    }

    // 按日期排序
    return filtered.sort((a, b) => {
      const dateA = parseISO(a.date);
      const dateB = parseISO(b.date);
      return dateA.getTime() - dateB.getTime();
    });
  }, [holidays, selectedCategory]);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'major': return <Star className="h-4 w-4" />;
      case 'shopping': return <ShoppingCart className="h-4 w-4" />;
      case 'seasonal': return <Gift className="h-4 w-4" />;
      case 'special': return <TrendingUp className="h-4 w-4" />;
      default: return <Calendar className="h-4 w-4" />;
    }
  };

  const getValueColor = (value: string) => {
    switch (value) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  // 计算倒计时
  const getCountdown = (holidayDate: string) => {
    const targetDate = parseISO(holidayDate);
    const now = currentTime;

    const days = differenceInDays(targetDate, now);
    const hours = differenceInHours(targetDate, now) % 24;
    const minutes = differenceInMinutes(targetDate, now) % 60;

    const daysText = getTranslation(currentLanguage, 'amzSellerCalendar.countdown.days');
    const hoursText = getTranslation(currentLanguage, 'amzSellerCalendar.countdown.hours');
    const minutesText = getTranslation(currentLanguage, 'amzSellerCalendar.countdown.minutes');
    const startingText = getTranslation(currentLanguage, 'amzSellerCalendar.countdown.starting');

    if (days > 0) {
      return `${days} ${daysText} ${hours} ${hoursText}`;
    } else if (hours > 0) {
      return `${hours} ${hoursText} ${minutes} ${minutesText}`;
    } else if (minutes > 0) {
      return `${minutes} ${minutesText}`;
    } else {
      return startingText;
    }
  };

  // 获取倒计时颜色
  const getCountdownColor = (holidayDate: string) => {
    const targetDate = parseISO(holidayDate);
    const days = differenceInDays(targetDate, currentTime);

    if (days <= 7) return 'text-red-600 bg-red-50';
    if (days <= 30) return 'text-orange-600 bg-orange-50';
    return 'text-blue-600 bg-blue-50';
  };

  // 根据语言格式化日期
  const formatDateByLanguage = (date: Date) => {
    switch (currentLanguage) {
      case 'en':
        return format(date, 'MMM dd, yyyy');
      case 'ja':
        return format(date, 'yyyy年MM月dd日');
      case 'zh-TW':
        return format(date, 'yyyy年MM月dd日');
      case 'id':
        return format(date, 'dd MMM yyyy');
      case 'vi':
        return format(date, 'dd/MM/yyyy');
      default: // zh
        return format(date, 'yyyy年MM月dd日');
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
            <Calendar className="h-6 w-6 text-[#FF9900]" />
            {getTranslation(currentLanguage, 'amzSellerCalendar.title')}
          </h2>
          <div className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-lg">
            {getTranslation(currentLanguage, 'amzSellerCalendar.today')}: {formatDateByLanguage(currentTime)}
          </div>
        </div>
        <p className="text-gray-600">
          {getTranslation(currentLanguage, 'amzSellerCalendar.description')}
        </p>
        {filteredHolidays.length === 0 && (
          <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-yellow-800 text-sm">
              {getTranslation(currentLanguage, 'amzSellerCalendar.noUpcomingHolidays')}
            </p>
          </div>
        )}
      </div>

      <div className="mb-6 flex flex-wrap gap-4">
        <div className="flex gap-2">
          <button
            onClick={() => setSelectedYear('2025')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              selectedYear === '2025' 
                ? 'bg-[#FF9900] text-white' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            2025
          </button>
          <button
            onClick={() => setSelectedYear('2026')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              selectedYear === '2026' 
                ? 'bg-[#FF9900] text-white' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            2026
          </button>
        </div>

        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="px-4 py-2 rounded-lg border border-gray-300 focus:border-[#FF9900] focus:outline-none"
        >
          <option value="all">{getTranslation(currentLanguage, 'amzSellerCalendar.allCategories')}</option>
          <option value="major">{getTranslation(currentLanguage, 'amzSellerCalendar.major')}</option>
          <option value="shopping">{getTranslation(currentLanguage, 'amzSellerCalendar.shopping')}</option>
          <option value="seasonal">{getTranslation(currentLanguage, 'amzSellerCalendar.seasonal')}</option>
          <option value="special">{getTranslation(currentLanguage, 'amzSellerCalendar.special')}</option>
        </select>
      </div>

      <div className="space-y-4">
        {filteredHolidays.length > 0 && (
          <div className="text-sm text-gray-500 mb-4">
            {getTranslation(currentLanguage, 'amzSellerCalendar.showingCount').replace('{count}', filteredHolidays.length.toString())}
          </div>
        )}
        {filteredHolidays.map((holiday, index) => {
          const days = differenceInDays(parseISO(holiday.date), currentTime);
          const isUrgent = days <= 7;
          const isComingSoon = days <= 30;

          return (
            <div
              key={index}
              className={`border rounded-lg p-4 hover:shadow-md transition-all duration-200 ${
                isUrgent ? 'border-red-200 bg-red-50' :
                isComingSoon ? 'border-orange-200 bg-orange-50' :
                'border-gray-200 bg-white hover:bg-gray-50'
              }`}
            >
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  {getCategoryIcon(holiday.category)}
                  <h3 className="text-lg font-semibold text-gray-800">
                    {getTranslation(currentLanguage, `amzSellerCalendar.holidays.${holiday.name}`)}
                  </h3>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getValueColor(holiday.value)}`}>
                    {getTranslation(currentLanguage, `amzSellerCalendar.value.${holiday.value}`)}
                  </span>
                </div>
                <div className="flex items-center gap-3 text-sm text-gray-600">
                  <span>
                    {holiday.dateRange || format(new Date(holiday.date), 'yyyy-MM-dd')}
                  </span>
                  <div className={`flex items-center gap-1 px-2 py-1 rounded-md ${getCountdownColor(holiday.date)}`}>
                    <Clock className="h-3 w-3" />
                    <span className="text-xs font-medium">
                      {getCountdown(holiday.date)}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <p className="text-gray-700 mb-3">
              {getTranslation(currentLanguage, `amzSellerCalendar.holidays.${holiday.description}`)}
            </p>

            <div className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-center gap-2 mb-2">
                <AlertCircle className="h-4 w-4 text-[#FF9900]" />
                <h4 className="font-semibold text-gray-800">
                  {getTranslation(currentLanguage, 'amzSellerCalendar.actionTips')}
                </h4>
              </div>
              <ul className="space-y-1">
                {holiday.actionTips.map((tip, tipIndex) => (
                  <li key={tipIndex} className="text-sm text-gray-700 flex items-start gap-2">
                    <span className="text-[#FF9900] mt-1">•</span>
                    <span>{getTranslation(currentLanguage, `amzSellerCalendar.tips.${tip}`)}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
          );
        })}
      </div>

      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start gap-2">
          <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
          <div>
            <h4 className="font-semibold text-blue-900 mb-1">
              {getTranslation(currentLanguage, 'amzSellerCalendar.proTip')}
            </h4>
            <p className="text-sm text-blue-800">
              {getTranslation(currentLanguage, 'amzSellerCalendar.proTipDesc')}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}