import React, { useState, useEffect } from 'react';
import { Package, DollarSign, Truck, Calculator } from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
import { getTranslation } from '../../utils/translations';
import ToolInfoSections from './FBACalculator/ToolInfoSections';

interface FBAData {
  length: number;
  width: number;
  height: number;
  weight: number;
  category: string;
  isOversized: boolean;
  isDangerous: boolean;
  isApparel: boolean;
}

const FBACalculator = () => {
  const { currentLanguage } = useLanguage();
  const [marketplace, setMarketplace] = useState('US');
  const [data, setData] = useState<FBAData>({
    length: 0,
    width: 0,
    height: 0,
    weight: 0,
    category: 'standard',
    isOversized: false,
    isDangerous: false,
    isApparel: false
  });

  const [fees, setFees] = useState({
    fulfillmentFee: 0,
    storageFee: 0,
    longTermStorageFee: 0,
    removalFee: 0,
    returnProcessingFee: 0,
    totalFees: 0
  });

  const marketplaces = [
    { code: 'US', name: 'Amazon.com (US)', currency: '$' },
    { code: 'UK', name: 'Amazon.co.uk (UK)', currency: '£' },
    { code: 'DE', name: 'Amazon.de (Germany)', currency: '€' },
    { code: 'CA', name: 'Amazon.ca (Canada)', currency: 'C$' }
  ];

  const currentMarketplace = marketplaces.find(m => m.code === marketplace) || marketplaces[0];

  const getSizeTierTranslationKey = (tier: string) => {
    switch (tier) {
      case 'small-standard':
        return 'smallStandard';
      case 'large-standard':
        return 'largeStandard';
      case 'large-bulky':
        return 'largeBulky';
      case 'extra-large':
        return 'extraLarge';
      default:
        return 'smallStandard';
    }
  };

  useEffect(() => {
    calculateFees();
  }, [data, marketplace]);

  const calculateDimensions = () => {
    const { length, width, height, weight, category } = data;
    const dimensionalWeight = (length * width * height) / 139;
    const billableWeight = Math.max(weight, dimensionalWeight);

    // Determine size tier based on 2025 Amazon rules
    const longestSide = Math.max(length, width, height);
    const medianSide = [length, width, height].sort((a, b) => b - a)[1];
    const shortestSide = Math.min(length, width, height);

    let sizeTier = 'small-standard';

    // Small Standard: ≤16 oz, ≤15×12×0.75 inches
    if (weight <= 1 && longestSide <= 15 && medianSide <= 12 && shortestSide <= 0.75) {
      sizeTier = 'small-standard';
    }
    // Large Standard: ≤20 lb, ≤18×14×8 inches
    else if (weight <= 20 && longestSide <= 18 && medianSide <= 14 && shortestSide <= 8) {
      sizeTier = 'large-standard';
    }
    // Large Bulky: ≤50 lb, ≤59×33×33 inches
    else if (weight <= 50 && longestSide <= 59 && medianSide <= 33 && shortestSide <= 33) {
      sizeTier = 'large-bulky';
    }
    // Extra Large: >50 lb or larger dimensions
    else {
      sizeTier = 'extra-large';
    }

    // Override based on category selection
    if (category === 'oversize') {
      if (sizeTier === 'small-standard' || sizeTier === 'large-standard') {
        sizeTier = 'large-bulky';
      }
    }

    return { billableWeight, sizeTier };
  };

  const calculateFees = () => {
    const { billableWeight, sizeTier } = calculateDimensions();
    const volume = (data.length * data.width * data.height) / 1728; // cubic feet

    // 2025 FBA Fulfillment Fees (US marketplace)
    let fulfillmentFee = 0;

    if (sizeTier === 'small-standard') {
      // Small Standard (≤16 oz, ≤15×12×0.75 inches)
      if (billableWeight <= 0.125) fulfillmentFee = 3.22; // 2 oz or less
      else if (billableWeight <= 0.25) fulfillmentFee = 3.40; // 2-4 oz
      else if (billableWeight <= 0.375) fulfillmentFee = 3.58; // 4-6 oz
      else if (billableWeight <= 0.5) fulfillmentFee = 3.76; // 6-8 oz
      else if (billableWeight <= 0.625) fulfillmentFee = 3.94; // 8-10 oz
      else if (billableWeight <= 0.75) fulfillmentFee = 4.12; // 10-12 oz
      else if (billableWeight <= 0.875) fulfillmentFee = 4.29; // 12-14 oz
      else fulfillmentFee = 4.47; // 14-16 oz
    } else if (sizeTier === 'large-standard') {
      // Large Standard (≤20 lb, ≤18×14×8 inches)
      if (billableWeight <= 0.25) fulfillmentFee = 4.95; // 4 oz or less
      else if (billableWeight <= 0.5) fulfillmentFee = 5.23; // 4-8 oz
      else if (billableWeight <= 0.75) fulfillmentFee = 5.51; // 8-12 oz
      else if (billableWeight <= 1) fulfillmentFee = 5.79; // 12-16 oz
      else if (billableWeight <= 2) fulfillmentFee = 6.44; // 1-2 lb
      else if (billableWeight <= 3) fulfillmentFee = 6.92; // 2-3 lb
      else fulfillmentFee = 6.92 + (billableWeight - 3) * 0.32; // 3+ lb
    } else if (sizeTier === 'large-bulky') {
      // Large Bulky (≤50 lb, ≤59×33×33 inches)
      fulfillmentFee = 9.61 + Math.max(0, billableWeight - 1) * 0.38;
    } else {
      // Extra Large (>50 lb)
      if (billableWeight <= 70) {
        fulfillmentFee = 26.33 + Math.max(0, billableWeight - 1) * 0.38;
      } else if (billableWeight <= 150) {
        fulfillmentFee = 26.33 + 69 * 0.38 + Math.max(0, billableWeight - 70) * 0.75;
      } else {
        fulfillmentFee = 26.33 + 69 * 0.38 + 80 * 0.75 + Math.max(0, billableWeight - 150) * 0.75;
      }
    }

    // Apparel surcharge
    if (data.isApparel) {
      fulfillmentFee += 0.40;
    }

    // Dangerous goods surcharge
    if (data.isDangerous) {
      fulfillmentFee += 0.12;
    }

    // 2025 Storage fees (per cubic foot per month)
    // Standard: $0.87 (Jan-Sep), $2.40 (Oct-Dec)
    // Oversize: $0.56 (Jan-Sep), $1.40 (Oct-Dec)
    const currentMonth = new Date().getMonth() + 1;
    const isPeakSeason = currentMonth >= 10 || currentMonth <= 12;

    let monthlyStorageRate;
    if (sizeTier === 'small-standard' || sizeTier === 'large-standard') {
      monthlyStorageRate = isPeakSeason ? 2.40 : 0.87;
    } else {
      monthlyStorageRate = isPeakSeason ? 1.40 : 0.56;
    }

    const storageFee = volume * monthlyStorageRate;

    // Long-term storage fee (after 365 days) - 2025 rates
    const longTermStorageFee = Math.max(volume * 6.90, 0.15); // $6.90/ft³ or $0.15/unit, whichever is greater

    // 2025 Removal fees
    let removalFee = 0;
    if (sizeTier === 'small-standard' || sizeTier === 'large-standard') {
      if (billableWeight <= 0.5) removalFee = 0.25;
      else if (billableWeight <= 1) removalFee = 0.30;
      else if (billableWeight <= 2) removalFee = 0.35;
      else removalFee = 0.40 + Math.max(0, billableWeight - 2) * 0.20;
    } else {
      // Oversize removal fees
      if (billableWeight <= 1) removalFee = 3.12;
      else if (billableWeight <= 2) removalFee = 4.30;
      else if (billableWeight <= 4) removalFee = 6.36;
      else if (billableWeight <= 10) removalFee = 10.04;
      else removalFee = 10.04 + Math.max(0, billableWeight - 10) * 0.20;
    }

    // Return processing fee (estimated)
    const returnProcessingFee = fulfillmentFee * 0.15;

    const totalFees = fulfillmentFee + storageFee + removalFee + returnProcessingFee;

    setFees({
      fulfillmentFee,
      storageFee,
      longTermStorageFee,
      removalFee,
      returnProcessingFee,
      totalFees
    });
  };

  const handleInputChange = (field: keyof FBAData, value: string | boolean) => {
    if (typeof value === 'boolean') {
      setData(prev => ({ ...prev, [field]: value }));
    } else if (field === 'category') {
      // Keep category as string
      setData(prev => ({ ...prev, [field]: value }));
    } else {
      // Convert numeric fields to numbers
      const numValue = parseFloat(value) || 0;
      setData(prev => ({ ...prev, [field]: numValue }));
    }
  };

  const { billableWeight, sizeTier } = calculateDimensions();

  return (
    <div className="max-w-6xl mx-auto">
      <div className="bg-white rounded-2xl shadow-lg p-8">
        <div className="flex items-center mb-8">
          <div className="bg-red-500 text-white p-3 rounded-xl mr-4">
            <Package className="h-8 w-8" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {getTranslation(currentLanguage, 'fbaCalculator')}
            </h1>
            <p className="text-gray-600 mt-2">
              {getTranslation(currentLanguage, 'fbaCalculatorDesc')}
            </p>
          </div>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Input Section */}
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {getTranslation(currentLanguage, 'marketplace')}
              </label>
              <select
                value={marketplace}
                onChange={(e) => setMarketplace(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
              >
                {marketplaces.map((mp) => (
                  <option key={mp.code} value={mp.code}>
                    {mp.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="bg-gray-50 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Calculator className="h-5 w-5 mr-2" />
                {getTranslation(currentLanguage, 'productDimensions')}
              </h3>
              
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {getTranslation(currentLanguage, 'length')} (inches)
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    value={data.length || ''}
                    onChange={(e) => handleInputChange('length', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="12.0"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {getTranslation(currentLanguage, 'width')} (inches)
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    value={data.width || ''}
                    onChange={(e) => handleInputChange('width', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="8.0"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {getTranslation(currentLanguage, 'height')} (inches)
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    value={data.height || ''}
                    onChange={(e) => handleInputChange('height', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="2.0"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {getTranslation(currentLanguage, 'weight')} (lbs)
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    value={data.weight || ''}
                    onChange={(e) => handleInputChange('weight', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="1.5"
                  />
                </div>
              </div>

              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {getTranslation(currentLanguage, 'productCategory')}
                  </label>
                  <select
                    value={data.category}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  >
                    <option value="standard">{getTranslation(currentLanguage, 'standardSize')}</option>
                    <option value="oversize">{getTranslation(currentLanguage, 'oversizeCategory')}</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={data.isDangerous}
                      onChange={(e) => handleInputChange('isDangerous', e.target.checked)}
                      className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      {getTranslation(currentLanguage, 'dangerousGoods')}
                    </span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={data.isApparel}
                      onChange={(e) => handleInputChange('isApparel', e.target.checked)}
                      className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      {getTranslation(currentLanguage, 'apparelCategory')}
                    </span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Results Section */}
          <div className="space-y-6">
            {/* Product Info */}
            <div className="bg-gradient-to-r from-red-50 to-pink-50 rounded-xl p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                <Truck className="h-6 w-6 mr-2 text-red-600" />
                {getTranslation(currentLanguage, 'productInfo')}
              </h3>

              <div className="grid grid-cols-2 gap-4">
                <div className="bg-white rounded-lg p-4">
                  <div className="text-sm text-gray-600 mb-1">
                    {getTranslation(currentLanguage, 'sizeTier')}
                  </div>
                  <div className="text-lg font-bold text-gray-900">
                    {getTranslation(currentLanguage, getSizeTierTranslationKey(sizeTier))}
                  </div>
                </div>

                <div className="bg-white rounded-lg p-4">
                  <div className="text-sm text-gray-600 mb-1">
                    {getTranslation(currentLanguage, 'billableWeight')}
                  </div>
                  <div className="text-lg font-bold text-gray-900">
                    {billableWeight.toFixed(2)} lbs
                  </div>
                </div>

                <div className="bg-white rounded-lg p-4">
                  <div className="text-sm text-gray-600 mb-1">
                    {getTranslation(currentLanguage, 'volume')}
                  </div>
                  <div className="text-lg font-bold text-gray-900">
                    {((data.length * data.width * data.height) / 1728).toFixed(3)} ft³
                  </div>
                </div>

                <div className="bg-white rounded-lg p-4">
                  <div className="text-sm text-gray-600 mb-1">
                    {getTranslation(currentLanguage, 'dimensionalWeight')}
                  </div>
                  <div className="text-lg font-bold text-gray-900">
                    {((data.length * data.width * data.height) / 139).toFixed(2)} lbs
                  </div>
                </div>
              </div>
            </div>

            {/* Fee Breakdown */}
            <div className="bg-gray-50 rounded-xl p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <DollarSign className="h-5 w-5 mr-2" />
                {getTranslation(currentLanguage, 'fbaFeeBreakdown')}
              </h4>
              
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">{getTranslation(currentLanguage, 'fulfillmentFee')}</span>
                  <span className="font-medium">{currentMarketplace.currency}{fees.fulfillmentFee.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">{getTranslation(currentLanguage, 'monthlyStorageFee')}</span>
                  <span className="font-medium">{currentMarketplace.currency}{fees.storageFee.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">{getTranslation(currentLanguage, 'longTermStorageFee')}</span>
                  <span className="font-medium">{currentMarketplace.currency}{fees.longTermStorageFee.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">{getTranslation(currentLanguage, 'removalFee')}</span>
                  <span className="font-medium">{currentMarketplace.currency}{fees.removalFee.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">{getTranslation(currentLanguage, 'returnProcessingFee')}</span>
                  <span className="font-medium">{currentMarketplace.currency}{fees.returnProcessingFee.toFixed(2)}</span>
                </div>
                <div className="border-t pt-3 flex justify-between font-bold text-lg">
                  <span>{getTranslation(currentLanguage, 'totalMonthlyFees')}</span>
                  <span className="text-red-600">
                    {currentMarketplace.currency}{fees.totalFees.toFixed(2)}
                  </span>
                </div>
              </div>
            </div>

            {/* Tips */}
            <div className="bg-blue-50 rounded-xl p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">
                {getTranslation(currentLanguage, 'fbaOptimizationTips')}
              </h4>
              <div className="space-y-2 text-sm text-gray-700">
                <p>• {getTranslation(currentLanguage, 'fbaTip1')}</p>
                <p>• {getTranslation(currentLanguage, 'fbaTip2')}</p>
                <p>• {getTranslation(currentLanguage, 'fbaTip3')}</p>
                <p>• {getTranslation(currentLanguage, 'fbaTip4')}</p>
              </div>
            </div>
          </div>
        </div>

        {/* 工具详细信息部分 */}
        <div className="mt-8">
          <ToolInfoSections />
        </div>
      </div>
    </div>
  );
};

export default FBACalculator;