import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Info, BookOpen, HelpCircle, Lightbulb } from 'lucide-react';
import { useLanguage } from '../../../hooks/useLanguage';
import { getTranslation } from '../../../utils/translations';

const ToolInfoSections: React.FC = () => {
  const { currentLanguage } = useLanguage();
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({});

  const toggleSection = (sectionId: string) => {
    setOpenSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  const sections = [
    {
      id: 'intro',
      icon: <Info className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'fbaToolIntroTitle'),
      content: (
        <div className="space-y-4">
          <p className="text-gray-700 leading-relaxed">
            {getTranslation(currentLanguage, 'fbaToolIntroContent')}
          </p>
        </div>
      )
    },
    {
      id: 'features',
      icon: <Info className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'fbaToolFeaturesTitle'),
      content: (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-900 mb-2 flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              {getTranslation(currentLanguage, 'fbaFeatureAccurateCalculation')}
            </h4>
            <p className="text-blue-700 text-sm">
              {getTranslation(currentLanguage, 'fbaFeatureAccurateCalculationDesc')}
            </p>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <h4 className="font-semibold text-green-900 mb-2 flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              {getTranslation(currentLanguage, 'fbaFeatureMultiMarketplace')}
            </h4>
            <p className="text-green-700 text-sm">
              {getTranslation(currentLanguage, 'fbaFeatureMultiMarketplaceDesc')}
            </p>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <h4 className="font-semibold text-purple-900 mb-2 flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              {getTranslation(currentLanguage, 'fbaFeatureSizeTierAnalysis')}
            </h4>
            <p className="text-purple-700 text-sm">
              {getTranslation(currentLanguage, 'fbaFeatureSizeTierAnalysisDesc')}
            </p>
          </div>
          <div className="bg-orange-50 p-4 rounded-lg">
            <h4 className="font-semibold text-orange-900 mb-2 flex items-center gap-2">
              <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
              {getTranslation(currentLanguage, 'fbaFeatureOptimizationTips')}
            </h4>
            <p className="text-orange-700 text-sm">
              {getTranslation(currentLanguage, 'fbaFeatureOptimizationTipsDesc')}
            </p>
          </div>
        </div>
      )
    },
    {
      id: 'guide',
      icon: <BookOpen className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'fbaUsageGuideTitle'),
      content: (
        <div className="space-y-6">
          {[1, 2, 3, 4, 5].map(step => (
            <div key={step} className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center font-semibold text-sm">
                {step}
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-gray-900 mb-2">
                  {getTranslation(currentLanguage, `fbaStep${step}Title`)}
                </h4>
                <p className="text-gray-700 leading-relaxed">
                  {getTranslation(currentLanguage, `fbaStep${step}Content`)}
                </p>
              </div>
            </div>
          ))}
        </div>
      )
    },
    {
      id: 'faq',
      icon: <HelpCircle className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'fbaFaqTitle'),
      content: (
        <div className="space-y-6">
          {[1, 2, 3].map(faq => (
            <div key={faq} className="border-l-4 border-blue-500 pl-4">
              <h4 className="font-semibold text-gray-900 mb-2">
                {getTranslation(currentLanguage, `fbaFaq${faq}Question`)}
              </h4>
              <p className="text-gray-700 leading-relaxed">
                {getTranslation(currentLanguage, `fbaFaq${faq}Answer`)}
              </p>
            </div>
          ))}
        </div>
      )
    },
    {
      id: 'best-practices',
      icon: <Lightbulb className="h-5 w-5" />,
      title: getTranslation(currentLanguage, 'fbaBestPracticesTitle'),
      content: (
        <div className="space-y-3">
          {[1, 2, 3, 4, 5].map(practice => {
            const practiceKey = `fbaBestPractice${practice}`;
            const practiceText = getTranslation(currentLanguage, practiceKey);
            
            // 只显示有翻译的最佳实践
            if (practiceText === practiceKey) {
              return null;
            }
            
            return (
              <div key={practice} className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-gray-700 leading-relaxed">
                  {practiceText}
                </p>
              </div>
            );
          })}
        </div>
      )
    }
  ];

  return (
    <div className="space-y-4">
      {sections.map((section) => (
        <div key={section.id} className="bg-white rounded-xl border border-gray-200 overflow-hidden">
          <button
            onClick={() => toggleSection(section.id)}
            className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
          >
            <div className="flex items-center gap-3">
              <div className="text-blue-600">
                {section.icon}
              </div>
              <h3 className="text-lg font-semibold text-gray-900">
                {section.title}
              </h3>
            </div>
            <div className="text-gray-400">
              {openSections[section.id] ? (
                <ChevronUp className="h-5 w-5" />
              ) : (
                <ChevronDown className="h-5 w-5" />
              )}
            </div>
          </button>
          
          {openSections[section.id] && (
            <div className="px-6 pb-6">
              <div className="border-t border-gray-100 pt-4">
                {section.content}
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default ToolInfoSections;
