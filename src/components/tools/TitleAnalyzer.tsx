import React, { useState, useEffect } from 'react';
import { FileText, CheckCircle, AlertTriangle, Info, Sparkles, Zap, Target, Lightbulb, RefreshCw, AlertCircle, TrendingUp, BarChart3, <PERSON><PERSON>, Check, Shield } from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
import { getTranslation } from '../../utils/translations';
import { titleAnalyzerService, TitleAnalysisResult } from '../../lib/ai/services/titleAnalyzer';
import { checkAIUsageLimit, recordAIUsage, type UsageStats } from '../../utils/aiUsageLimit';
import UsageLimitInfo from './TitleAnalyzer/UsageLimitInfo';
import ToolInfoSections from './TitleAnalyzer/ToolInfoSections';
import AIServiceStatus from '../shared/AIServiceStatus';

import { useAIProvider } from '../../hooks/useAIProvider';
import { isAIAvailable } from '../../lib/ai';

interface TitleAnalysis {
  score: number;
  length: number;
  wordCount: number;
  keywordDensity: number;
  readabilityScore: number;
  issues: string[];
  suggestions: string[];
  complianceScore: number;
  seoScore: number;
  marketplaceCompliance: number;
  categoryOptimization: number;
}

// 使用新的AI服务接口
// TitleAnalysisResult 已从 titleAnalyzerService 导入

const TitleAnalyzer = () => {
  const { currentLanguage } = useLanguage();
  const { currentProvider, isAvailable } = useAIProvider();
  const [title, setTitle] = useState('');
  const [targetKeyword, setTargetKeyword] = useState('');
  const [productFeatures, setProductFeatures] = useState('');
  const [marketplace, setMarketplace] = useState('US');
  const [category, setCategory] = useState('general');
  const [analysis, setAnalysis] = useState<TitleAnalysis | null>(null);
  const [complianceAnalysis, setComplianceAnalysis] = useState<any | null>(null);
  const [aiOptimization, setAiOptimization] = useState<TitleAnalysisResult | null>(null);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [geminiAvailable, setGeminiAvailable] = useState(false);
  const [aiError, setAiError] = useState<string | null>(null);
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);
  const [usageStats, setUsageStats] = useState<UsageStats | null>(null);
  const [showUsageInfo, setShowUsageInfo] = useState(false);

  const marketplaces = [
    { code: 'US', name: 'Amazon.com (US)', maxLength: 200, language: 'English' },
    { code: 'UK', name: 'Amazon.co.uk (UK)', maxLength: 200, language: 'English' },
    { code: 'DE', name: 'Amazon.de (Germany)', maxLength: 200, language: 'German' },
    { code: 'FR', name: 'Amazon.fr (France)', maxLength: 200, language: 'French' },
    { code: 'IT', name: 'Amazon.it (Italy)', maxLength: 200, language: 'Italian' },
    { code: 'ES', name: 'Amazon.es (Spain)', maxLength: 200, language: 'Spanish' },
    { code: 'CA', name: 'Amazon.ca (Canada)', maxLength: 200, language: 'English/French' },
    { code: 'JP', name: 'Amazon.co.jp (Japan)', maxLength: 100, language: 'Japanese' },
    { code: 'AU', name: 'Amazon.com.au (Australia)', maxLength: 200, language: 'English' },
    { code: 'IN', name: 'Amazon.in (India)', maxLength: 200, language: 'English' }
  ];

  const categories = [
    { id: 'general', name: getTranslation(currentLanguage, 'generalCategory'), restrictions: [] },
    { id: 'electronics', name: getTranslation(currentLanguage, 'electronicsCategory'), restrictions: ['warranty', 'compatibility'] },
    { id: 'clothing', name: getTranslation(currentLanguage, 'clothingCategory'), restrictions: ['size', 'material', 'care'] },
    { id: 'home', name: getTranslation(currentLanguage, 'homeCategory'), restrictions: ['dimensions', 'material'] },
    { id: 'books', name: getTranslation(currentLanguage, 'booksCategory'), restrictions: ['edition', 'language'] },
    { id: 'health', name: getTranslation(currentLanguage, 'healthCategory'), restrictions: ['fda', 'claims'] },
    { id: 'beauty', name: 'Beauty & Personal Care', restrictions: ['ingredients', 'claims'] },
    { id: 'sports', name: 'Sports & Outdoors', restrictions: ['safety', 'age'] },
    { id: 'automotive', name: 'Automotive', restrictions: ['compatibility', 'safety'] },
    { id: 'toys', name: 'Toys & Games', restrictions: ['age', 'safety', 'choking'] }
  ];

  const currentMarketplace = marketplaces.find(m => m.code === marketplace) || marketplaces[0];
  const currentCategory = categories.find(c => c.id === category) || categories[0];

  useEffect(() => {
    // 使用新的统一AI服务状态
    const aiAvailable = isAIAvailable();
    setGeminiAvailable(aiAvailable);
    // 初始化使用统计
    updateUsageStats();
  }, []);

  // 更新使用统计
  const updateUsageStats = () => {
    const stats = checkAIUsageLimit('title-analyzer'); // 自动使用每日10次，每小时3次的配置
    setUsageStats(stats);
  };

  useEffect(() => {
    if (title.trim()) {
      analyzeTitle();
    } else {
      setAnalysis(null);
      setComplianceAnalysis(null);
    }
  }, [title, targetKeyword, marketplace, category]);

  const analyzeTitle = () => {
    const issues: string[] = [];
    const suggestions: string[] = [];
    let score = 100;
    let complianceScore = 100;
    let seoScore = 100;
    let marketplaceCompliance = 100;
    let categoryOptimization = 100;

    // 基础长度分析
    const length = title.length;
    const wordCount = title.trim().split(/\s+/).length;

    if (length === 0) {
      issues.push(getTranslation(currentLanguage, 'titleEmpty'));
      score -= 50;
    } else if (length > currentMarketplace.maxLength) {
      issues.push(getTranslation(currentLanguage, 'titleTooLong').replace('{max}', currentMarketplace.maxLength.toString()));
      score -= 30;
      complianceScore -= 25;
      marketplaceCompliance -= 30;
    } else if (length < 50) {
      issues.push(getTranslation(currentLanguage, 'titleTooShort'));
      score -= 20;
      seoScore -= 15;
    }

    // 关键词分析（增强版）
    let keywordDensity = 0;
    if (targetKeyword.trim()) {
      const keywordLower = targetKeyword.toLowerCase();
      const titleLower = title.toLowerCase();
      
      // 精确匹配
      const exactMatches = (titleLower.match(new RegExp(`\\b${keywordLower}\\b`, 'g')) || []).length;
      // 部分匹配
      const partialMatches = (titleLower.match(new RegExp(keywordLower, 'g')) || []).length;
      
      keywordDensity = ((exactMatches * 2 + partialMatches) / wordCount) * 100;
      
      if (exactMatches === 0) {
        issues.push(getTranslation(currentLanguage, 'keywordMissing'));
        score -= 25;
        seoScore -= 30;
      } else {
        const keywordPosition = titleLower.indexOf(keywordLower);
        if (keywordPosition > 50) {
          issues.push(getTranslation(currentLanguage, 'keywordTooLate'));
          score -= 15;
          seoScore -= 20;
        }
        
        // 关键词密度检查
        if (keywordDensity > 15) {
          issues.push(getTranslation(currentLanguage, 'taKeywordDensityHigh'));
          score -= 10;
          complianceScore -= 15;
        }
      }
    }

    // 2025年亚马逊政策合规检查（增强版）
    const forbiddenWords = [
      'best', 'amazing', 'perfect', 'ultimate', 'premium', 'top', 'great', 'excellent',
      'awesome', 'fantastic', 'incredible', 'outstanding', 'superior', 'unbeatable',
      'world-class', 'revolutionary', 'breakthrough', 'cutting-edge', 'state-of-the-art',
      '#1', 'number one', 'bestseller', 'award-winning', 'guaranteed', 'miracle',
      'magic', 'instant', 'secret', 'exclusive', 'limited time', 'special offer'
    ];
    
    const foundForbidden = forbiddenWords.filter(word => 
      title.toLowerCase().includes(word.toLowerCase())
    );
    
    if (foundForbidden.length > 0) {
      issues.push(getTranslation(currentLanguage, 'forbiddenWords').replace('{words}', foundForbidden.join(', ')));
      score -= foundForbidden.length * 8;
      complianceScore -= foundForbidden.length * 12;
    }

    // 特殊字符检查（更严格）
    const specialChars = (title.match(/[™®©]/g) || []).length;
    const otherSymbols = (title.match(/[!@#$%^&*()+=\[\]{}|\\:";'<>?,./]/g) || []).length;
    
    if (specialChars > 2) {
      issues.push(getTranslation(currentLanguage, 'tooManySpecialChars'));
      score -= 10;
      complianceScore -= 15;
    }
    
    if (otherSymbols > 3) {
      issues.push(getTranslation(currentLanguage, 'taTooManyPunctuation'));
      score -= 8;
    }

    // 大小写检查（更详细）
    const allCapsWords = title.match(/\b[A-Z]{3,}\b/g) || [];
    const properNouns = ['USB', 'LED', 'LCD', 'GPS', 'WiFi', 'Bluetooth', 'HD', 'DVD', 'CD', 'AI', 'VR', 'AR', 'CPU', 'GPU', 'RAM', 'SSD'];
    const invalidCapsWords = allCapsWords.filter(word => !properNouns.includes(word));
    
    if (invalidCapsWords.length > 2) {
      issues.push(getTranslation(currentLanguage, 'tooManyCapitalWords'));
      score -= 12;
      complianceScore -= 10;
    }

    // 品牌名称位置检查
    const brandKeywords = ['brand', 'by', 'from'];
    const hasBrandInfo = brandKeywords.some(keyword => 
      title.toLowerCase().includes(keyword)
    );
    
    if (!hasBrandInfo && wordCount > 8) {
      suggestions.push(getTranslation(currentLanguage, 'taBrandInfoSuggestion'));
      seoScore -= 5;
    }

    // 类别特定检查
    if (currentCategory.restrictions.length > 0) {
      const categoryKeywords = {
        warranty: ['warranty', 'guarantee'],
        compatibility: ['compatible', 'fits', 'works with'],
        size: ['size', 'fit', 'dimension'],
        material: ['material', 'made of', 'fabric'],
        fda: ['FDA', 'approved', 'certified'],
        safety: ['safe', 'safety', 'tested'],
        age: ['age', 'years', 'months']
      };

      currentCategory.restrictions.forEach(restriction => {
        const keywords = categoryKeywords[restriction as keyof typeof categoryKeywords] || [];
        const hasRestrictionInfo = keywords.some(keyword => 
          title.toLowerCase().includes(keyword.toLowerCase())
        );
        
        if (!hasRestrictionInfo) {
          suggestions.push(getTranslation(currentLanguage, 'taConsiderAddingInfo').replace('{restriction}', restriction));
          categoryOptimization -= 10;
        }
      });
    }

    // 移动端显示优化
    if (length > 80) {
      suggestions.push(getTranslation(currentLanguage, 'taMobileDisplaySuggestion'));
      seoScore -= 8;
    }

    // 市场特定检查
    if (marketplace === 'JP' && length > 50) {
      suggestions.push(getTranslation(currentLanguage, 'taJapanMarketSuggestion'));
      marketplaceCompliance -= 15;
    }

    // SEO优化建议
    if (length < 100) {
      suggestions.push(getTranslation(currentLanguage, 'addMoreDetails'));
      seoScore -= 10;
    }
    
    if (targetKeyword.trim() && !title.toLowerCase().includes(targetKeyword.toLowerCase())) {
      suggestions.push(getTranslation(currentLanguage, 'includeTargetKeyword'));
      seoScore -= 20;
    }
    
    if (wordCount < 8) {
      suggestions.push(getTranslation(currentLanguage, 'addMoreDescriptiveWords'));
      seoScore -= 15;
    }

    // 可读性评分（改进算法）
    const avgWordLength = title.replace(/\s+/g, '').length / wordCount;
    const complexWords = title.split(/\s+/).filter(word => word.length > 8).length;
    const readabilityScore = Math.max(0, 100 - (avgWordLength * 2) - (complexWords * 5));

    setAnalysis({
      score: Math.max(0, score),
      length,
      wordCount,
      keywordDensity,
      readabilityScore,
      complianceScore: Math.max(0, complianceScore),
      seoScore: Math.max(0, seoScore),
      marketplaceCompliance: Math.max(0, marketplaceCompliance),
      categoryOptimization: Math.max(0, categoryOptimization),
      issues,
      suggestions
    });
  };

  const handleAIOptimization = async () => {
    if (!title.trim()) return;

    // 检查使用限制
    const currentUsageStats = checkAIUsageLimit('title-analyzer');
    setUsageStats(currentUsageStats);

    if (currentUsageStats.isLimited) {
      setAiError(getTranslation(currentLanguage, 'taUsageLimitExceeded'));
      setShowUsageInfo(true);
      return;
    }

    // 检查 AI 服务可用性
    if (!isAvailable) {
      setAiError(getTranslation(currentLanguage, 'aiServiceUnavailable'));
      return;
    }

    setIsOptimizing(true);
    setAiError(null);

    try {
      const features = productFeatures.split(',').map(f => f.trim()).filter(f => f.length > 0);

      // 使用新的 titleAnalyzerService
      const result = await titleAnalyzerService.analyzeAndOptimize(
        title,
        targetKeyword,
        marketplace,
        category,
        features,
        'STANDARD',
        currentLanguage // 传递当前语言
      );

      // 直接使用返回的结果
      setAiOptimization(result);

      // 记录AI使用
      recordAIUsage('title-analyzer', true);
      updateUsageStats();
    } catch (error) {
      console.error('AI optimization failed:', error);
      setAiError(error instanceof Error ? error.message : getTranslation(currentLanguage, 'taAiOptimizationError'));
      // 记录失败的AI使用
      recordAIUsage('title-analyzer', false);
      updateUsageStats();
    } finally {
      setIsOptimizing(false);
    }
  };



  const useOptimizedTitle = (optimizedTitle: string) => {
    setTitle(optimizedTitle);
    // 清除之前的AI结果，重新分析
    setAiOptimization(null);
  };

  const copyToClipboard = async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-emerald-600';
    if (score >= 60) return 'text-amber-600';
    return 'text-red-600';
  };

  const getScoreBackground = (score: number) => {
    if (score >= 80) return 'bg-emerald-50 border-emerald-200';
    if (score >= 60) return 'bg-amber-50 border-amber-200';
    return 'bg-red-50 border-red-200';
  };

  const getScoreBadgeColor = (score: number) => {
    if (score >= 80) return 'bg-emerald-500';
    if (score >= 60) return 'bg-amber-500';
    return 'bg-red-500';
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="card overflow-hidden shadow-amazon-xl mb-8">
          {/* Header */}
        <div className="tool-header">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="tool-header-icon">
                <FileText className="h-8 w-8" />
              </div>
              <div>
                <h1 className="tool-header-title">
                  {getTranslation(currentLanguage, 'titleAnalyzer')}
                </h1>
                <p className="tool-header-description">
                  {getTranslation(currentLanguage, 'taToolDescription')}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              {geminiAvailable ? (
                <div className="flex items-center bg-white/20 backdrop-blur-sm px-4 py-2 rounded-amazon-lg border border-white/30">
                  <Sparkles className="h-4 w-4 mr-2" />
                  <span className="text-sm font-medium">{getTranslation(currentLanguage, 'taAiEnhanced')}</span>
                </div>
              ) : (
                <div className="flex items-center bg-white/10 backdrop-blur-sm px-4 py-2 rounded-amazon-lg border border-white/20">
                  <AlertCircle className="h-4 w-4 mr-2" />
                  <span className="text-sm font-medium">{getTranslation(currentLanguage, 'taBasicVersion')}</span>
                </div>
              )}

              {/* 使用统计按钮 */}
              {usageStats && geminiAvailable && (
                <button
                  onClick={() => setShowUsageInfo(!showUsageInfo)}
                  className={`flex items-center px-2 py-1 rounded-lg text-xs font-medium transition-colors ${
                    usageStats.isLimited
                      ? 'bg-red-100 text-red-700 border border-red-200'
                      : 'bg-green-100 text-green-700 border border-green-200'
                  }`}
                >
                  <Info className="h-3 w-3 mr-1" />
                  {usageStats.totalUsage}/10
                </button>
              )}
            </div>
          </div>
        </div>

        <div className="p-8">
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Input Section */}
            <div className="lg:col-span-2 space-y-6">
              {/* Marketplace & Category */}
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="label">
                    {getTranslation(currentLanguage, 'marketplace')}
                  </label>
                  <select
                    value={marketplace}
                    onChange={(e) => setMarketplace(e.target.value)}
                    className="select"
                  >
                    {marketplaces.map((mp) => (
                      <option key={mp.code} value={mp.code}>
                        {mp.name} ({mp.language})
                      </option>
                    ))}
                  </select>
                  <p className="text-xs text-amazon-gray-500 mt-1">
                    {getTranslation(currentLanguage, 'taMaxLength')}: {currentMarketplace.maxLength} {getTranslation(currentLanguage, 'taCharacters')}
                  </p>
                </div>

                <div>
                  <label className="label">
                    {getTranslation(currentLanguage, 'category')}
                  </label>
                  <select
                    value={category}
                    onChange={(e) => setCategory(e.target.value)}
                    className="select"
                  >
                    {categories.map((cat) => (
                      <option key={cat.id} value={cat.id}>
                        {cat.name}
                      </option>
                    ))}
                  </select>
                  {currentCategory.restrictions.length > 0 && (
                    <p className="text-xs text-amazon-gray-500 mt-1">
                      {getTranslation(currentLanguage, 'taCategoryNote')}: {currentCategory.restrictions.join(', ')} {getTranslation(currentLanguage, 'taInfo')}
                    </p>
                  )}
                </div>
              </div>

              {/* Target Keyword */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {getTranslation(currentLanguage, 'targetKeyword')} ({getTranslation(currentLanguage, 'optional')})
                </label>
                <input
                  type="text"
                  value={targetKeyword}
                  onChange={(e) => setTargetKeyword(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                  placeholder={getTranslation(currentLanguage, 'taTargetKeywordPlaceholder')}
                />
                <p className="text-xs text-gray-500 mt-1">
                  {getTranslation(currentLanguage, 'taKeywordInputNote')}
                </p>
              </div>

              {/* Product Features */}
              {geminiAvailable && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {getTranslation(currentLanguage, 'taProductFeatures')} {getTranslation(currentLanguage, 'taOptional')}
                  </label>
                  <input
                    type="text"
                    value={productFeatures}
                    onChange={(e) => setProductFeatures(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                    placeholder={getTranslation(currentLanguage, 'taProductFeaturesPlaceholder')}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {getTranslation(currentLanguage, 'taFeaturesNote')}
                  </p>
                </div>
              )}

              {/* Title Input */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {getTranslation(currentLanguage, 'productTitle')}
                </label>
                <div className="relative">
                  <textarea
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200 resize-none"
                    rows={4}
                    placeholder={getTranslation(currentLanguage, 'titlePlaceholder')}
                  />
                  {title.length > currentMarketplace.maxLength * 0.8 && (
                    <div className="absolute top-2 right-2">
                      <div className={`w-3 h-3 rounded-full ${
                        title.length > currentMarketplace.maxLength ? 'bg-red-500' : 'bg-amber-500'
                      }`}></div>
                    </div>
                  )}
                </div>
                <div className="flex justify-between text-sm mt-2">
                  <span className={`${title.length > currentMarketplace.maxLength ? 'text-red-600 font-medium' : 'text-gray-500'}`}>
                    {title.length} / {currentMarketplace.maxLength} {getTranslation(currentLanguage, 'characters')}
                  </span>
                  <span className="text-gray-500">
                    {title.trim().split(/\s+/).filter(w => w.length > 0).length} {getTranslation(currentLanguage, 'words')}
                  </span>
                </div>
              </div>

              {/* AI 服务状态显示 */}
              {geminiAvailable && (
                <AIServiceStatus className="shadow-sm" />
              )}

              {/* AI Action Button */}
              {title.trim() && geminiAvailable && (
                <div className="flex justify-center">
                  <button
                    onClick={handleAIOptimization}
                    disabled={isOptimizing || (usageStats?.isLimited)}
                    className={`flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-lg font-medium transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:transform-none shadow-lg ${
                      usageStats?.isLimited ? 'cursor-not-allowed' : ''
                    }`}
                  >
                    {isOptimizing ? (
                      <RefreshCw className="h-5 w-5 mr-3 animate-spin" />
                    ) : (
                      <Sparkles className="h-5 w-5 mr-3" />
                    )}
                    {getTranslation(currentLanguage, 'taAiSmartOptimization')}
                  </button>
                </div>
              )}

              {/* 使用限制显示 */}
              {showUsageInfo && usageStats && (
                <UsageLimitInfo
                  usageStats={usageStats}
                  isVisible={showUsageInfo}
                />
              )}

              {/* AI Error Display */}
              {aiError && (
                <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                  <div className="flex items-start">
                    <AlertTriangle className="h-5 w-5 text-red-600 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="text-red-900 font-medium mb-1">{getTranslation(currentLanguage, 'taAiServiceTip')}</h4>
                      <p className="text-red-800 text-sm">{aiError}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Analysis Results */}
            <div className="space-y-6">
              {analysis && (
                <>
                  {/* Overall Score */}
                  <div className={`rounded-xl p-6 border-2 ${getScoreBackground(analysis.score)} shadow-lg`}>
                    <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                      <Target className="h-5 w-5 mr-2" />
                      {getTranslation(currentLanguage, 'overallScore')}
                    </h3>
                    <div className="text-center">
                      <div className="relative inline-flex items-center justify-center">
                        <div className={`text-5xl font-bold ${getScoreColor(analysis.score)}`}>
                          {Math.round(analysis.score)}
                        </div>
                        <div className={`absolute -top-2 -right-2 w-6 h-6 ${getScoreBadgeColor(analysis.score)} rounded-full flex items-center justify-center`}>
                          <CheckCircle className="h-4 w-4 text-white" />
                        </div>
                      </div>
                      <div className="text-sm text-gray-600 mt-2">
                        {getTranslation(currentLanguage, 'outOf100')}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {getTranslation(currentLanguage, 'taBasedOn2025Policy')}
                      </div>
                    </div>
                  </div>

                  {/* Detailed Scores Grid */}
                  <div className="grid grid-cols-2 gap-3">
                    <div className="bg-gradient-to-br from-emerald-50 to-teal-50 border border-emerald-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-700">{getTranslation(currentLanguage, 'taSeoScore')}</span>
                        <TrendingUp className="h-4 w-4 text-emerald-600" />
                      </div>
                      <div className={`text-2xl font-bold ${getScoreColor(analysis.seoScore)}`}>
                        {Math.round(analysis.seoScore)}
                      </div>
                    </div>

                    <div className="bg-gradient-to-br from-blue-50 to-cyan-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-700">{getTranslation(currentLanguage, 'taComplianceScore')}</span>
                        <Shield className="h-4 w-4 text-blue-600" />
                      </div>
                      <div className={`text-2xl font-bold ${getScoreColor(analysis.complianceScore)}`}>
                        {Math.round(analysis.complianceScore)}
                      </div>
                    </div>

                    <div className="bg-gradient-to-br from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-4">
                      <div className="text-sm text-gray-600 mb-1">{getTranslation(currentLanguage, 'taMarketAdaptability')}</div>
                      <div className={`text-lg font-bold ${getScoreColor(analysis.marketplaceCompliance)}`}>
                        {Math.round(analysis.marketplaceCompliance)}
                      </div>
                    </div>

                    <div className="bg-gradient-to-br from-amber-50 to-orange-50 border border-amber-200 rounded-lg p-4">
                      <div className="text-sm text-gray-600 mb-1">{getTranslation(currentLanguage, 'taCategoryOptimization')}</div>
                      <div className={`text-lg font-bold ${getScoreColor(analysis.categoryOptimization)}`}>
                        {Math.round(analysis.categoryOptimization)}
                      </div>
                    </div>
                  </div>

                  {/* Quick Stats */}
                  <div className="bg-gray-50 border border-gray-200 rounded-xl p-4">
                    <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                      <BarChart3 className="h-4 w-4 mr-2" />
                      {getTranslation(currentLanguage, 'taTitleStatistics')}
                    </h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">{getTranslation(currentLanguage, 'taKeywordDensityLabel')}</span>
                        <span className="font-medium">{analysis.keywordDensity.toFixed(1)}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">{getTranslation(currentLanguage, 'taReadabilityLabel')}</span>
                        <span className="font-medium">{Math.round(analysis.readabilityScore)}</span>
                      </div>
                    </div>
                  </div>

                  {/* Issues */}
                  {analysis.issues.length > 0 && (
                    <div className="bg-red-50 border border-red-200 rounded-xl p-6">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <AlertTriangle className="h-5 w-5 mr-2 text-red-600" />
                        {getTranslation(currentLanguage, 'issuesFound')} ({analysis.issues.length})
                      </h4>
                      <div className="space-y-3">
                        {analysis.issues.map((issue, index) => (
                          <div key={index} className="flex items-start bg-white rounded-lg p-3 border border-red-100">
                            <div className="w-2 h-2 bg-red-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                            <span className="text-red-800 text-sm leading-relaxed">{issue}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Suggestions */}
                  {analysis.suggestions.length > 0 && (
                    <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <Lightbulb className="h-5 w-5 mr-2 text-blue-600" />
                        {getTranslation(currentLanguage, 'taOptimizationSuggestions')}
                      </h4>
                      <div className="space-y-3">
                        {analysis.suggestions.map((suggestion, index) => (
                          <div key={index} className="flex items-start bg-white rounded-lg p-3 border border-blue-100">
                            <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                            <span className="text-blue-800 text-sm leading-relaxed">{suggestion}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Success Message */}
                  {analysis.score >= 80 && analysis.issues.length === 0 && (
                    <div className="bg-emerald-50 border border-emerald-200 rounded-xl p-6">
                      <div className="flex items-center">
                        <CheckCircle className="h-6 w-6 text-emerald-600 mr-3" />
                        <div>
                          <h4 className="text-lg font-semibold text-emerald-900">{getTranslation(currentLanguage, 'taTitleOptimizedWell')}</h4>
                          <p className="text-emerald-800 text-sm">{getTranslation(currentLanguage, 'taTitleOptimizedDesc')}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </>
              )}

              {!analysis && (
                <div className="bg-gradient-to-br from-gray-50 to-gray-100 border border-gray-200 rounded-xl p-8 text-center">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">{getTranslation(currentLanguage, 'taStartAnalyzing')}</h4>
                  <p className="text-gray-600">{getTranslation(currentLanguage, 'taStartAnalyzingDesc')}</p>
                </div>
              )}
            </div>
          </div>

          {/* AI Optimization Results */}
          {aiOptimization && (
            <div className="mt-8 bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <Sparkles className="h-6 w-6 mr-3 text-purple-600" />
                {getTranslation(currentLanguage, 'taAiOptimizationSuggestions')}
              </h3>

              {/* Optimized Titles */}
              <div className="mb-8">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">{getTranslation(currentLanguage, 'taOptimizedTitleSuggestions')}</h4>
                <div className="space-y-4">
                  {aiOptimization.optimizedTitles.map((optimizedTitle, index) => (
                    <div key={index} className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center mb-3">
                            <div className="bg-purple-100 text-purple-700 px-3 py-1 rounded-full text-sm font-medium mr-3">
                              {getTranslation(currentLanguage, 'taOption')} {index + 1}
                            </div>
                            <div className="flex items-center space-x-2 text-sm text-gray-500">
                              <span>{optimizedTitle.length} {getTranslation(currentLanguage, 'taCharacters')}</span>
                              <span>•</span>
                              <span>{optimizedTitle.split(/\s+/).length} {getTranslation(currentLanguage, 'taWords')}</span>
                            </div>
                          </div>
                          <div className="text-gray-900 leading-relaxed mb-3 p-3 bg-gray-50 rounded-lg">
                            {optimizedTitle}
                          </div>
                        </div>
                        <div className="ml-4 flex flex-col gap-2">
                          <button
                            onClick={() => copyToClipboard(optimizedTitle, index)}
                            className="px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center"
                          >
                            {copiedIndex === index ? (
                              <Check className="h-4 w-4" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </button>
                          <button
                            onClick={() => useOptimizedTitle(optimizedTitle)}
                            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm font-medium transition-colors duration-200 shadow-sm"
                          >
                            {getTranslation(currentLanguage, 'taUse')}
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Improvements, Keywords, SEO Tips and Marketing Advice */}
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">{getTranslation(currentLanguage, 'taImprovementPoints')}</h4>
                  <div className="space-y-3">
                    {aiOptimization.improvements.map((improvement, index) => (
                      <div key={index} className="flex items-start bg-white rounded-lg p-3 border border-purple-100">
                        <Zap className="h-4 w-4 text-purple-600 mt-1 mr-2 flex-shrink-0" />
                        <span className="text-gray-700 text-sm leading-relaxed">{improvement}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">{getTranslation(currentLanguage, 'taKeywordSuggestions')}</h4>
                  <div className="flex flex-wrap gap-2">
                    {aiOptimization.keywordSuggestions.map((keyword, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm font-medium border border-purple-200"
                      >
                        {keyword}
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              {/* SEO Tips */}
              {aiOptimization.seoTips && aiOptimization.seoTips.length > 0 && (
                <div className="mt-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <TrendingUp className="h-5 w-5 mr-2 text-green-600" />
                    {getTranslation(currentLanguage, 'taOptimizationSuggestions')}
                  </h4>
                  <div className="space-y-3">
                    {aiOptimization.seoTips.map((tip, index) => (
                      <div key={index} className="flex items-start bg-white rounded-lg p-3 border border-green-100">
                        <TrendingUp className="h-4 w-4 text-green-600 mt-1 mr-2 flex-shrink-0" />
                        <span className="text-gray-700 text-sm leading-relaxed">{tip}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Marketing Advice */}
              {aiOptimization.marketingAdvice && aiOptimization.marketingAdvice.length > 0 && (
                <div className="mt-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <Target className="h-5 w-5 mr-2 text-orange-600" />
                    {getTranslation(currentLanguage, 'taMarketingAdvice')}
                  </h4>
                  <div className="space-y-3">
                    {aiOptimization.marketingAdvice.map((advice, index) => (
                      <div key={index} className="flex items-start bg-white rounded-lg p-3 border border-orange-100">
                        <Target className="h-4 w-4 text-orange-600 mt-1 mr-2 flex-shrink-0" />
                        <span className="text-gray-700 text-sm leading-relaxed">{advice}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}


        </div>
        </div>

        {/* 工具详细信息部分 */}
        <ToolInfoSections />
      </div>
    </div>
  );
};

export default TitleAnalyzer;