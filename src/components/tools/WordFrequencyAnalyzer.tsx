import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { BarChart3, Co<PERSON>, Check, RotateCcw, Download, Filter, Search, TrendingUp, Target, Zap, AlertCircle, Info } from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
import { getTranslation } from '../../utils/translations';
import { recordToolCalculation } from '../../utils/analytics';
import ToolInfoSections from './WordFrequencyAnalyzer/ToolInfoSections';

interface WordFrequency {
  word: string;
  count: number;
  percentage: number;
  category: 'high' | 'medium' | 'low';
}

interface AnalysisResult {
  totalWords: number;
  uniqueWords: number;
  wordFrequencies: WordFrequency[];
  topKeywords: WordFrequency[];
  averageWordLength: number;
  readabilityScore: number;
}

const WordFrequencyAnalyzer: React.FC = () => {
  const { currentLanguage } = useLanguage();
  const [inputText, setInputText] = useState('');
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);
  const [filterType, setFilterType] = useState<'all' | 'high' | 'medium' | 'low'>('all');
  const [minWordLength, setMinWordLength] = useState(2);
  const [excludeCommonWords, setExcludeCommonWords] = useState(true);
  const [sortBy, setSortBy] = useState<'frequency' | 'alphabetical'>('frequency');

  // 常见停用词列表 - 专注于英文等西方语言
  const commonWords = useMemo(() => {
    return ['the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have', 'i', 'it', 'for', 'not', 'on', 'with', 'he', 'as', 'you', 'do', 'at', 'this', 'but', 'his', 'by', 'from', 'they', 'we', 'say', 'her', 'she', 'or', 'an', 'will', 'my', 'one', 'all', 'would', 'there', 'their', 'what', 'so', 'up', 'out', 'if', 'about', 'who', 'get', 'which', 'go', 'me', 'when', 'make', 'can', 'like', 'time', 'no', 'just', 'him', 'know', 'take', 'people', 'into', 'year', 'your', 'good', 'some', 'could', 'them', 'see', 'other', 'than', 'then', 'now', 'look', 'only', 'come', 'its', 'over', 'think', 'also', 'back', 'after', 'use', 'two', 'how', 'our', 'work', 'first', 'well', 'way', 'even', 'want', 'because', 'any', 'these', 'give', 'day', 'most', 'us', 'is', 'was', 'are', 'been', 'has', 'had', 'were', 'said', 'each', 'them', 'may', 'during', 'before', 'here', 'through', 'where', 'why', 'both', 'few', 'more', 'most', 'such', 'own', 'same', 'too', 'very', 'should'];
  }, []);

  // 文本预处理函数 - 专注于英文等西方语言
  const preprocessText = useCallback((text: string): string[] => {
    // 移除标点符号和特殊字符，保留字母、数字
    const cleanText = text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
    
    // 按空格分割单词
    let words = cleanText.split(/\s+/).filter(word => word.length >= minWordLength);

    // 过滤停用词和电商常见无意义词汇
    if (excludeCommonWords) {
      const ecommerceStopWords = ['new', 'hot', 'sale', 'free', 'shipping', 'deal', 'offer', 'best',
                                  'top', 'premium', 'quality', 'high', 'professional', 'perfect', 'great',
                                  'amazing', 'awesome', 'super', 'ultra', 'pro', 'plus', 'max', 'mini',
                                  'pack', 'set', 'kit', 'bundle', 'piece', 'pcs', 'count', 'size'];
      const allStopWords = [...commonWords, ...ecommerceStopWords];
      words = words.filter(word => !allStopWords.includes(word));
    }

    return words;
  }, [minWordLength, excludeCommonWords, commonWords]);

  // 计算词频
  const calculateWordFrequency = useCallback((words: string[]): AnalysisResult => {
    const wordCount: { [key: string]: number } = {};
    
    // 统计词频
    words.forEach(word => {
      wordCount[word] = (wordCount[word] || 0) + 1;
    });

    const totalWords = words.length;
    const uniqueWords = Object.keys(wordCount).length;
    
    // 计算平均词长
    const averageWordLength = words.reduce((sum, word) => sum + word.length, 0) / totalWords;
    
    // 计算可读性评分（简化版）
    const readabilityScore = Math.min(100, Math.max(0, 100 - (averageWordLength - 4) * 10));

    // 创建词频数组
    const wordFrequencies: WordFrequency[] = Object.entries(wordCount)
      .map(([word, count]) => {
        const percentage = (count / totalWords) * 100;
        let category: 'high' | 'medium' | 'low' = 'low';
        
        if (percentage >= 2) category = 'high';
        else if (percentage >= 0.5) category = 'medium';
        
        return { word, count, percentage, category };
      })
      .sort((a, b) => {
        if (sortBy === 'frequency') {
          return b.count - a.count;
        } else {
          return a.word.localeCompare(b.word);
        }
      });

    // 获取前10个高频词作为关键词
    const topKeywords = wordFrequencies.slice(0, 10);

    return {
      totalWords,
      uniqueWords,
      wordFrequencies,
      topKeywords,
      averageWordLength,
      readabilityScore
    };
  }, [sortBy]);

  // 执行分析
  const handleAnalyze = useCallback(async () => {
    if (!inputText.trim()) return;

    setIsAnalyzing(true);
    
    try {
      // 模拟分析延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const words = preprocessText(inputText);
      const result = calculateWordFrequency(words);
      
      setAnalysisResult(result);
      
      // 记录工具使用
      recordToolCalculation('word-frequency-analyzer', {
        textLength: inputText.length,
        totalWords: result.totalWords,
        uniqueWords: result.uniqueWords
      });
    } catch (error) {
      console.error('Analysis error:', error);
    } finally {
      setIsAnalyzing(false);
    }
  }, [inputText, preprocessText, calculateWordFrequency]);

  // 复制功能
  const handleCopy = useCallback(async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (error) {
      console.error('Copy failed:', error);
    }
  }, []);

  // 重置功能
  const handleReset = useCallback(() => {
    setInputText('');
    setAnalysisResult(null);
    setFilterType('all');
    setMinWordLength(2);
    setExcludeCommonWords(true);
    setSortBy('frequency');
  }, []);

  // 导出功能
  const handleExport = useCallback(() => {
    if (!analysisResult) return;

    const exportData = {
      analysis_date: new Date().toISOString(),
      total_words: analysisResult.totalWords,
      unique_words: analysisResult.uniqueWords,
      average_word_length: analysisResult.averageWordLength.toFixed(2),
      readability_score: analysisResult.readabilityScore.toFixed(1),
      word_frequencies: analysisResult.wordFrequencies.map(wf => ({
        word: wf.word,
        count: wf.count,
        percentage: wf.percentage.toFixed(2) + '%'
      }))
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `word-frequency-analysis-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [analysisResult]);

  // 示例文本功能
  const handleLoadExample = useCallback(() => {
    const exampleText = 'Wireless Bluetooth Headphones Noise Cancelling Sports Earbuds Long Battery Life Waterproof Compatible with iPhone Android High Quality Stereo Sound with Charging Case Bluetooth 5.0 Quick Connect Comfortable Fit Sports Sweatproof Music Calls Portable Storage';
    setInputText(exampleText);
  }, []);

  // 过滤词频结果
  const filteredResults = useMemo(() => {
    if (!analysisResult) return [];
    
    let filtered = analysisResult.wordFrequencies;
    
    if (filterType !== 'all') {
      filtered = filtered.filter(wf => wf.category === filterType);
    }
    
    return filtered;
  }, [analysisResult, filterType]);

  // 自动分析
  useEffect(() => {
    if (inputText.trim() && inputText.length > 10) {
      const timeoutId = setTimeout(() => {
        handleAnalyze();
      }, 1000);
      
      return () => clearTimeout(timeoutId);
    }
  }, [inputText, handleAnalyze]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <div className="max-w-7xl mx-auto p-4 lg:p-8">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-amazon-orange to-orange-600 rounded-2xl text-white shadow-lg mb-4">
            <BarChart3 className="h-8 w-8" />
          </div>
          <h1 className="text-3xl lg:text-4xl font-bold text-amazon-squid-ink mb-2">
            {getTranslation(currentLanguage, 'wordFrequencyAnalyzer')}
          </h1>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            {getTranslation(currentLanguage, 'wordFrequencyAnalyzerDesc')}
          </p>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-12 gap-8">
          <div className="xl:col-span-4 space-y-6">
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-amazon-squid-ink flex items-center">
                  <div className="w-8 h-8 bg-amazon-orange-light rounded-lg flex items-center justify-center mr-3">
                    <Search className="h-4 w-4 text-amazon-orange" />
                  </div>
                  {getTranslation(currentLanguage, 'textToAnalyze')}
                </h3>
                <button
                  onClick={handleLoadExample}
                  className="px-3 py-1 text-sm text-amazon-orange bg-amazon-orange-light rounded-lg hover:bg-amazon-orange hover:text-white transition-all duration-200"
                >
                  {getTranslation(currentLanguage, 'loadExample')}
                </button>
              </div>
              
              <textarea
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                placeholder={getTranslation(currentLanguage, 'textPlaceholder')}
                className="w-full h-40 p-4 border-2 border-gray-200 rounded-xl resize-none focus:ring-2 focus:ring-amazon-orange focus:border-amazon-orange transition-all duration-200 text-sm"
              />
              
              <div className="flex items-center justify-between mt-4 text-sm text-gray-500">
                <div className="flex space-x-4">
                  <span className="flex items-center">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                    {inputText.length} {getTranslation(currentLanguage, 'characters')}
                  </span>
                  <span className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    {inputText.trim().split(/\s+/).filter(w => w).length} {getTranslation(currentLanguage, 'words')}
                  </span>
                </div>
              </div>
            </div>

            {/* 分析设置和控制 */}
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6">
              <h3 className="text-lg font-semibold text-amazon-squid-ink mb-4 flex items-center">
                <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                  <Filter className="h-4 w-4 text-purple-600" />
                </div>
                {getTranslation(currentLanguage, 'analysisSettings')}
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {getTranslation(currentLanguage, 'minWordLength')}
                  </label>
                  <select
                    value={minWordLength}
                    onChange={(e) => setMinWordLength(Number(e.target.value))}
                    className="w-full p-3 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                  >
                    <option value={1}>1 {getTranslation(currentLanguage, 'characters')}</option>
                    <option value={2}>2 {getTranslation(currentLanguage, 'characters')}</option>
                    <option value={3}>3 {getTranslation(currentLanguage, 'characters')}</option>
                    <option value={4}>4 {getTranslation(currentLanguage, 'characters')}</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {getTranslation(currentLanguage, 'sortBy')}
                  </label>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as 'frequency' | 'alphabetical')}
                    className="w-full p-3 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                  >
                    <option value="frequency">{getTranslation(currentLanguage, 'byFrequency')}</option>
                    <option value="alphabetical">{getTranslation(currentLanguage, 'alphabetically')}</option>
                  </select>
                </div>
              </div>

              <div className="mb-6">
                <label className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors">
                  <input
                    type="checkbox"
                    checked={excludeCommonWords}
                    onChange={(e) => setExcludeCommonWords(e.target.checked)}
                    className="w-5 h-5 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                  />
                  <span className="text-sm font-medium text-gray-700">
                    {getTranslation(currentLanguage, 'excludeCommonWords')}
                  </span>
                </label>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={handleAnalyze}
                  disabled={!inputText.trim() || isAnalyzing}
                  className="flex-1 bg-gradient-to-r from-amazon-orange to-orange-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center shadow-lg"
                >
                  {isAnalyzing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                      {getTranslation(currentLanguage, 'analyzing')}
                    </>
                  ) : (
                    <>
                      <BarChart3 className="h-4 w-4 mr-2" />
                      {getTranslation(currentLanguage, 'analyzeText')}
                    </>
                  )}
                </button>

                <button
                  onClick={handleReset}
                  className="px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-xl font-semibold hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 flex items-center"
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  {getTranslation(currentLanguage, 'reset')}
                </button>
              </div>
            </div>
          </div>

          {/* 右侧：结果区域 */}
          <div className="xl:col-span-8 space-y-6">
            {!analysisResult ? (
              <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-12 text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <BarChart3 className="h-10 w-10 text-gray-400" />
                </div>
                <h3 className="text-xl font-semibold text-amazon-squid-ink mb-3">
                  {getTranslation(currentLanguage, 'startAnalysis')}
                </h3>
                <p className="text-gray-600 max-w-md mx-auto">
                  {getTranslation(currentLanguage, 'enterTextToAnalyze')}
                </p>
              </div>
            ) : (
              <>
                {/* 分析概览 */}
                <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-semibold text-amazon-squid-ink flex items-center">
                      <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                        <TrendingUp className="h-4 w-4 text-green-600" />
                      </div>
                      {getTranslation(currentLanguage, 'analysisOverview')}
                    </h3>
                    <button
                      onClick={handleExport}
                      className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors flex items-center text-sm font-medium"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      {getTranslation(currentLanguage, 'export')}
                    </button>
                  </div>

                  <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-xl border border-blue-200">
                      <div className="text-2xl font-bold text-blue-600 mb-1">{analysisResult.totalWords}</div>
                      <div className="text-sm text-blue-700">{getTranslation(currentLanguage, 'totalWords')}</div>
                    </div>
                    <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-xl border border-purple-200">
                      <div className="text-2xl font-bold text-purple-600 mb-1">{analysisResult.uniqueWords}</div>
                      <div className="text-sm text-purple-700">{getTranslation(currentLanguage, 'uniqueWords')}</div>
                    </div>
                    <div className="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-xl border border-green-200">
                      <div className="text-2xl font-bold text-green-600 mb-1">{analysisResult.averageWordLength.toFixed(1)}</div>
                      <div className="text-sm text-green-700">{getTranslation(currentLanguage, 'avgWordLength')}</div>
                    </div>
                    <div className="bg-gradient-to-br from-orange-50 to-orange-100 p-4 rounded-xl border border-orange-200">
                      <div className="text-2xl font-bold text-orange-600 mb-1">{analysisResult.readabilityScore.toFixed(0)}</div>
                      <div className="text-sm text-orange-700">{getTranslation(currentLanguage, 'readabilityScore')}</div>
                    </div>
                  </div>
                </div>

                {/* 高频关键词和词频列表 */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* 高频关键词 */}
                  <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6">
                    <h3 className="text-lg font-semibold text-amazon-squid-ink mb-4 flex items-center">
                      <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                        <Target className="h-4 w-4 text-red-600" />
                      </div>
                      {getTranslation(currentLanguage, 'topKeywords')}
                    </h3>

                    <div className="space-y-3">
                      {analysisResult.topKeywords.slice(0, 5).map((keyword, index) => (
                        <div key={index} className="group flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl hover:from-amazon-orange-light hover:to-orange-100 transition-all duration-200">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-amazon-orange text-white rounded-lg flex items-center justify-center text-sm font-bold">
                              {index + 1}
                            </div>
                            <div>
                              <span className="font-semibold text-gray-900 text-base">{keyword.word}</span>
                              <div className="text-xs text-gray-600">Count: {keyword.count}</div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-3">
                            <span className="text-lg font-bold text-amazon-orange">
                              {keyword.percentage.toFixed(1)}%
                            </span>
                            <button
                              onClick={() => handleCopy(keyword.word, index)}
                              className="p-2 text-gray-400 hover:text-amazon-orange hover:bg-white rounded-lg transition-all duration-200"
                            >
                              {copiedIndex === index ? (
                                <Check className="h-4 w-4 text-green-500" />
                              ) : (
                                <Copy className="h-4 w-4" />
                              )}
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 词频详细列表 */}
                  <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-amazon-squid-ink flex items-center">
                        <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                          <BarChart3 className="h-4 w-4 text-indigo-600" />
                        </div>
                        {getTranslation(currentLanguage, 'wordFrequencyList')}
                      </h3>

                      <select
                        value={filterType}
                        onChange={(e) => setFilterType(e.target.value as any)}
                        className="px-3 py-2 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200 text-sm"
                      >
                        <option value="all">{getTranslation(currentLanguage, 'allWords')}</option>
                        <option value="high">{getTranslation(currentLanguage, 'highFrequency')}</option>
                        <option value="medium">{getTranslation(currentLanguage, 'mediumFrequency')}</option>
                        <option value="low">{getTranslation(currentLanguage, 'lowFrequency')}</option>
                      </select>
                    </div>

                    <div className="max-h-80 overflow-y-auto space-y-2">
                      {filteredResults.slice(0, 20).map((wordFreq, index) => (
                        <div key={index} className="group flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-all duration-200 border border-transparent hover:border-gray-200">
                          <div className="flex items-center space-x-3">
                            <div className={`w-3 h-3 rounded-full ${
                              wordFreq.category === 'high' ? 'bg-red-500' :
                              wordFreq.category === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                            }`}></div>
                            <span className="font-medium text-gray-900">{wordFreq.word}</span>
                          </div>
                          <div className="flex items-center space-x-3">
                            <span className="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded">{wordFreq.count}</span>
                            <span className="text-sm font-semibold text-amazon-orange min-w-[3rem] text-right">
                              {wordFreq.percentage.toFixed(1)}%
                            </span>
                            <button
                              onClick={() => handleCopy(wordFreq.word, index + 1000)}
                              className="p-2 text-gray-400 hover:text-amazon-orange hover:bg-white rounded-lg transition-all duration-200 opacity-0 group-hover:opacity-100"
                            >
                              {copiedIndex === index + 1000 ? (
                                <Check className="h-4 w-4 text-green-500" />
                              ) : (
                                <Copy className="h-4 w-4" />
                              )}
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>

                    {filteredResults.length === 0 && (
                      <div className="text-center py-8 text-gray-500">
                        <AlertCircle className="h-8 w-8 mx-auto mb-3 text-gray-400" />
                        <span className="text-sm">{getTranslation(currentLanguage, 'noWordsFound')}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* 分析建议 */}
                <div className="bg-gradient-to-br from-yellow-50 to-orange-50 rounded-2xl p-6 border border-yellow-200">
                  <h3 className="text-lg font-semibold text-amazon-squid-ink mb-4 flex items-center">
                    <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                      <Zap className="h-4 w-4 text-yellow-600" />
                    </div>
                    {getTranslation(currentLanguage, 'analysisInsights')}
                  </h3>

                  <div className="space-y-4">
                    <div className="flex items-start space-x-3 p-3 bg-white rounded-lg border border-yellow-200">
                      <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <Info className="h-3 w-3 text-blue-600" />
                      </div>
                      <span className="text-sm text-gray-700 leading-relaxed">
                        {getTranslation(currentLanguage, 'keywordDiversityTip')}
                      </span>
                    </div>
                    <div className="flex items-start space-x-3 p-3 bg-white rounded-lg border border-yellow-200">
                      <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <Info className="h-3 w-3 text-green-600" />
                      </div>
                      <span className="text-sm text-gray-700 leading-relaxed">
                        {getTranslation(currentLanguage, 'competitorAnalysisTip')}
                      </span>
                    </div>
                    <div className="flex items-start space-x-3 p-3 bg-white rounded-lg border border-yellow-200">
                      <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <Info className="h-3 w-3 text-purple-600" />
                      </div>
                      <span className="text-sm text-gray-700 leading-relaxed">
                        {getTranslation(currentLanguage, 'listingOptimizationTip')}
                      </span>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>

        {/* 工具详细信息部分 */}
        <div className="mt-8">
          <ToolInfoSections />
        </div>
      </div>
    </div>
  );
};

export default WordFrequencyAnalyzer;
