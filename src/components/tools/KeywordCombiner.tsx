import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { Shuffle, Copy, Check, RotateCcw, Download, Plus, Minus, Zap, Target, TrendingUp } from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
import { getTranslation } from '../../utils/translations';
import { recordToolCalculation } from '../../utils/analytics';
import ToolInfoSections from './KeywordCombiner/ToolInfoSections';

interface KeywordGroup {
  id: string;
  keywords: string[];
  label: string;
  inputValue: string; // 添加输入值字段
}

interface CombinationResult {
  combination: string;
  wordCount: number;
  charCount: number;
}

const KeywordCombiner = () => {
  const { currentLanguage } = useLanguage();
  const [keywordGroups, setKeywordGroups] = useState<KeywordGroup[]>([
    { id: '1', keywords: [], label: 'Group 1', inputValue: '' },
    { id: '2', keywords: [], label: 'Group 2', inputValue: '' }
  ]);
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);
  const [selectedCombinations, setSelectedCombinations] = useState<Set<number>>(new Set());
  const [sortBy, setSortBy] = useState<'length' | 'words' | 'original'>('original');

  // 更新标签当语言改变时
  useEffect(() => {
    setKeywordGroups(prev => prev.map((group, index) => ({
      ...group,
      label: getTranslation(currentLanguage, `kcGroup${index + 1}`)
    })));
  }, [currentLanguage]);

  // 添加关键词组
  const addKeywordGroup = useCallback(() => {
    if (keywordGroups.length < 3) {
      const newGroup: KeywordGroup = {
        id: Date.now().toString(),
        keywords: [],
        label: getTranslation(currentLanguage, `kcGroup${keywordGroups.length + 1}`),
        inputValue: ''
      };
      setKeywordGroups(prev => [...prev, newGroup]);
    }
  }, [keywordGroups.length, currentLanguage]);

  // 删除关键词组
  const removeKeywordGroup = useCallback((groupId: string) => {
    if (keywordGroups.length > 2) {
      setKeywordGroups(prev => prev.filter(group => group.id !== groupId));
    }
  }, [keywordGroups.length]);

  // 处理关键词输入
  const handleKeywordInput = useCallback((groupId: string, value: string) => {
    // 更新输入值和解析关键词
    const keywords = value.split(/[,，\n]/).map(k => k.trim()).filter(k => k !== '');
    setKeywordGroups(prev =>
      prev.map(group =>
        group.id === groupId
          ? { ...group, inputValue: value, keywords: keywords }
          : group
      )
    );
  }, []);

  // 生成所有可能的组合
  const generateCombinations = useMemo((): CombinationResult[] => {
    const validGroups = keywordGroups.filter(group => group.keywords.length > 0);
    
    if (validGroups.length < 2) {
      return [];
    }

    const combinations: string[] = [];
    
    // 递归生成组合
    const generateRecursive = (currentCombination: string[], groupIndex: number) => {
      if (groupIndex >= validGroups.length) {
        combinations.push(currentCombination.join(' '));
        return;
      }
      
      for (const keyword of validGroups[groupIndex].keywords) {
        generateRecursive([...currentCombination, keyword], groupIndex + 1);
      }
    };

    generateRecursive([], 0);

    // 转换为结果对象并排序
    let results = combinations.map(combination => ({
      combination,
      wordCount: combination.split(' ').length,
      charCount: combination.length
    }));

    // 根据排序方式排序
    switch (sortBy) {
      case 'length':
        results.sort((a, b) => a.charCount - b.charCount);
        break;
      case 'words':
        results.sort((a, b) => a.wordCount - b.wordCount);
        break;
      default:
        // 保持原始顺序
        break;
    }

    return results;
  }, [keywordGroups, sortBy]);

  // 复制到剪贴板
  const copyToClipboard = useCallback(async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
      
      // 记录分析数据
      recordToolCalculation('keyword-combiner', {
        combinationLength: text.length,
        wordCount: text.split(' ').length,
        totalCombinations: generateCombinations.length
      });
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  }, [generateCombinations.length]);

  // 复制所有选中的组合
  const copySelectedCombinations = useCallback(async () => {
    const selectedTexts = Array.from(selectedCombinations)
      .map(index => generateCombinations[index]?.combination)
      .filter(Boolean);
    
    if (selectedTexts.length > 0) {
      try {
        await navigator.clipboard.writeText(selectedTexts.join('\n'));
        // 可以添加成功提示
      } catch (err) {
        console.error('Failed to copy selected combinations: ', err);
      }
    }
  }, [selectedCombinations, generateCombinations]);

  // 下载组合结果
  const downloadCombinations = useCallback(() => {
    const content = generateCombinations.map(result => result.combination).join('\n');
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'keyword-combinations.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [generateCombinations]);

  // 清空所有输入
  const clearAll = useCallback(() => {
    setKeywordGroups(prev => prev.map(group => ({ ...group, keywords: [], inputValue: '' })));
    setSelectedCombinations(new Set());
  }, []);

  // 切换组合选择
  const toggleCombinationSelection = useCallback((index: number) => {
    setSelectedCombinations(prev => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  }, []);

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* 页面标题 */}
      <div className="text-center">
        <div className="flex items-center justify-center mb-4">
          <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl">
            <Shuffle className="h-8 w-8 text-white" />
          </div>
        </div>
        <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
          {getTranslation(currentLanguage, 'keywordCombiner')}
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          {getTranslation(currentLanguage, 'keywordCombinerHeader')}
        </p>
      </div>

      {/* 关键词输入区域 */}
      <div className="bg-white rounded-2xl shadow-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">
            {getTranslation(currentLanguage, 'kcInputSection')}
          </h2>
          <div className="flex gap-2">
            {keywordGroups.length < 3 && (
              <button
                onClick={addKeywordGroup}
                className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                <Plus className="h-4 w-4" />
                {getTranslation(currentLanguage, 'kcAddGroup')}
              </button>
            )}
            <button
              onClick={clearAll}
              className="flex items-center gap-2 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
            >
              <RotateCcw className="h-4 w-4" />
              {getTranslation(currentLanguage, 'kcClearAll')}
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {keywordGroups.map((group, index) => (
            <div key={group.id} className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="block text-sm font-medium text-gray-700">
                  {group.label}
                </label>
                {keywordGroups.length > 2 && (
                  <button
                    onClick={() => removeKeywordGroup(group.id)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <Minus className="h-4 w-4" />
                  </button>
                )}
              </div>
              <textarea
                value={group.inputValue}
                onChange={(e) => handleKeywordInput(group.id, e.target.value)}
                placeholder={getTranslation(currentLanguage, 'kcKeywordPlaceholder')}
                className="w-full h-24 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
              />
              <div className="text-xs text-gray-500">
                {getTranslation(currentLanguage, 'kcKeywordCount')}: {group.keywords.length}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 组合结果区域 */}
      {generateCombinations.length > 0 && (
        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">
              {getTranslation(currentLanguage, 'kcResultsSection')} ({generateCombinations.length})
            </h2>
            <div className="flex items-center gap-4">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as 'length' | 'words' | 'original')}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
              >
                <option value="original">{getTranslation(currentLanguage, 'kcSortOriginal')}</option>
                <option value="length">{getTranslation(currentLanguage, 'kcSortLength')}</option>
                <option value="words">{getTranslation(currentLanguage, 'kcSortWords')}</option>
              </select>
              {selectedCombinations.size > 0 && (
                <button
                  onClick={copySelectedCombinations}
                  className="flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                >
                  <Copy className="h-4 w-4" />
                  {getTranslation(currentLanguage, 'kcCopySelected')} ({selectedCombinations.size})
                </button>
              )}
              <button
                onClick={downloadCombinations}
                className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                <Download className="h-4 w-4" />
                {getTranslation(currentLanguage, 'kcDownload')}
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
            {generateCombinations.map((result, index) => (
              <div
                key={index}
                className={`p-4 border rounded-lg cursor-pointer transition-all ${
                  selectedCombinations.has(index)
                    ? 'border-purple-500 bg-purple-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => toggleCombinationSelection(index)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 break-words">
                      {result.combination}
                    </p>
                    <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                      <span>{result.wordCount} {getTranslation(currentLanguage, 'kcWords')}</span>
                      <span>{result.charCount} {getTranslation(currentLanguage, 'kcChars')}</span>
                    </div>
                  </div>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      copyToClipboard(result.combination, index);
                    }}
                    className="ml-2 p-1 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    {copiedIndex === index ? (
                      <Check className="h-4 w-4 text-green-500" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Amazon卖家使用技巧 */}
      <div className="bg-gradient-to-br from-orange-50 to-yellow-50 rounded-2xl p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <Zap className="h-5 w-5 text-orange-500" />
          {getTranslation(currentLanguage, 'kcAmazonTips')}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex gap-3">
            <Target className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="font-medium text-gray-900">{getTranslation(currentLanguage, 'kcTip1Title')}</h4>
              <p className="text-sm text-gray-600 mt-1">{getTranslation(currentLanguage, 'kcTip1Desc')}</p>
            </div>
          </div>
          <div className="flex gap-3">
            <TrendingUp className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="font-medium text-gray-900">{getTranslation(currentLanguage, 'kcTip2Title')}</h4>
              <p className="text-sm text-gray-600 mt-1">{getTranslation(currentLanguage, 'kcTip2Desc')}</p>
            </div>
          </div>
        </div>
      </div>

      {/* 工具详细信息部分 */}
      <div className="mt-8">
        <ToolInfoSections />
      </div>
    </div>
  );
};

export default KeywordCombiner;
