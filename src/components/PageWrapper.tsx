import React from 'react';
import { useParams, useLocation } from 'react-router-dom';
import { SEOHead } from './SEOHead';

interface PageWrapperProps {
  children: React.ReactNode;
  page?: 'home' | 'tools' | 'tool-specific' | 'feedback';
  customTitle?: string;
  customDescription?: string;
  customKeywords?: string;
}

export const PageWrapper: React.FC<PageWrapperProps> = ({
  children,
  page = 'home',
  customTitle,
  customDescription,
  customKeywords
}) => {
  const { toolId } = useParams();
  const location = useLocation();
  
  // 根据路径自动判断页面类型
  const getPageType = () => {
    if (page !== 'home') return page;
    
    if (location.pathname === '/') return 'home';
    if (location.pathname === '/tools') return 'tools';
    if (location.pathname.startsWith('/tools/')) return 'tool-specific';
    if (location.pathname === '/feedback') return 'feedback';
    
    return 'home';
  };

  const pageType = getPageType();

  return (
    <>
      <SEOHead
        page={pageType}
        toolId={toolId}
        customTitle={customTitle}
        customDescription={customDescription}
        customKeywords={customKeywords}
      />
      {children}
    </>
  );
};
