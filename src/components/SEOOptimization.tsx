import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useLanguage } from '../hooks/useLanguage';
import { SEO_CONFIG, TOOL_SEO_CONFIG } from '../config/seo';

interface SEOOptimizationProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'product';
  toolId?: string;
  noIndex?: boolean;
}

export const SEOOptimization: React.FC<SEOOptimizationProps> = ({
  title,
  description,
  keywords,
  image,
  url,
  type = 'website',
  toolId,
  noIndex = false
}) => {
  const { language } = useLanguage();
  
  // 获取工具特定的 SEO 配置
  const toolConfig = toolId ? TOOL_SEO_CONFIG[toolId as keyof typeof TOOL_SEO_CONFIG] : null;
  
  // 构建最终的 SEO 数据
  const seoTitle = title || 
    (toolConfig?.title[language as keyof typeof toolConfig.title]) || 
    (language === 'en' ? SEO_CONFIG.defaultTitleEn : SEO_CONFIG.defaultTitle);
    
  const seoDescription = description || 
    (toolConfig?.description[language as keyof typeof toolConfig.description]) || 
    (language === 'en' ? SEO_CONFIG.defaultDescriptionEn : SEO_CONFIG.defaultDescription);
    
  const seoKeywords = keywords || 
    (toolConfig?.keywords[language as keyof typeof toolConfig.keywords]) || 
    SEO_CONFIG.keywords[language as keyof typeof SEO_CONFIG.keywords] || 
    SEO_CONFIG.keywords.zh;
    
  const seoImage = image || SEO_CONFIG.openGraph.images[0].url;
  const seoUrl = url || SEO_CONFIG.siteUrl;
  
  // 获取当前语言配置
  const currentLang = SEO_CONFIG.languages.find(lang => lang.code === language) || SEO_CONFIG.languages[0];
  
  return (
    <Helmet>
      {/* 基础 Meta 标签 */}
      <title>{seoTitle}</title>
      <meta name="description" content={seoDescription} />
      <meta name="keywords" content={seoKeywords.join(', ')} />
      
      {/* 语言和地区 */}
      <html lang={currentLang.hreflang} />
      <meta name="language" content={currentLang.hreflang} />
      <meta name="geo.region" content={currentLang.region} />

      {/* Canonical URL */}
      <link rel="canonical" href={seoUrl} />
      
      {/* 搜索引擎指令 */}
      {noIndex ? (
        <meta name="robots" content="noindex, nofollow" />
      ) : (
        <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
      )}
      <meta name="googlebot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
      <meta name="bingbot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
      
      {/* 网站验证 */}
      {SEO_CONFIG.verification.google && (
        <meta name="google-site-verification" content={SEO_CONFIG.verification.google} />
      )}
      {SEO_CONFIG.verification.bing && SEO_CONFIG.verification.bing !== 'your-bing-verification-code' && (
        <meta name="msvalidate.01" content={SEO_CONFIG.verification.bing} />
      )}

      {SEO_CONFIG.verification.baidu && (
        <meta name="baidu-site-verification" content={SEO_CONFIG.verification.baidu} />
      )}
      
      {/* Open Graph */}
      <meta property="og:type" content={type} />
      <meta property="og:title" content={seoTitle} />
      <meta property="og:description" content={seoDescription} />
      <meta property="og:image" content={seoImage} />
      <meta property="og:url" content={seoUrl} />
      <meta property="og:site_name" content={SEO_CONFIG.openGraph.siteName} />
      <meta property="og:locale" content={currentLang.hreflang.replace('-', '_')} />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content={SEO_CONFIG.twitter.card} />
      <meta name="twitter:site" content={SEO_CONFIG.twitter.site} />
      <meta name="twitter:creator" content={SEO_CONFIG.twitter.creator} />
      <meta name="twitter:title" content={seoTitle} />
      <meta name="twitter:description" content={seoDescription} />
      <meta name="twitter:image" content={seoImage} />
      
      {/* 移动端优化 */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes" />
      <meta name="format-detection" content="telephone=no" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      
      {/* 性能优化 */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="dns-prefetch" href="https://api.amzova.com" />
      <link rel="preconnect" href="https://api.amzova.com" />
      
      {/* 多语言支持 */}
      {SEO_CONFIG.languages.map(lang => (
        <link
          key={lang.code}
          rel="alternate"
          hrefLang={lang.hreflang}
          href={`${SEO_CONFIG.siteUrl}${lang.code === 'zh' ? '' : `?lang=${lang.code}`}`}
        />
      ))}
      <link rel="alternate" hrefLang="x-default" href={SEO_CONFIG.siteUrl} />
      
      {/* 结构化数据 - 组织信息 */}
      <script type="application/ld+json">
        {JSON.stringify(SEO_CONFIG.structuredData.organization)}
      </script>
      
      {/* 结构化数据 - 网站信息 */}
      <script type="application/ld+json">
        {JSON.stringify(SEO_CONFIG.structuredData.website)}
      </script>
      
      {/* 工具特定的结构化数据 */}
      {toolId && (
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "SoftwareApplication",
            "name": seoTitle,
            "description": seoDescription,
            "url": seoUrl,
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Web Browser",
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "USD"
            },
            "author": SEO_CONFIG.structuredData.organization
          })}
        </script>
      )}
      
      {/* 面包屑导航结构化数据 */}
      {toolId && (
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": [
              {
                "@type": "ListItem",
                "position": 1,
                "name": "首页",
                "item": SEO_CONFIG.siteUrl
              },
              {
                "@type": "ListItem",
                "position": 2,
                "name": "工具",
                "item": `${SEO_CONFIG.siteUrl}/tools`
              },
              {
                "@type": "ListItem",
                "position": 3,
                "name": seoTitle,
                "item": seoUrl
              }
            ]
          })}
        </script>
      )}
      
      {/* 中文搜索引擎优化 */}
      <meta name="applicable-device" content="pc,mobile" />
      <meta name="MobileOptimized" content="width" />
      <meta name="HandheldFriendly" content="true" />
      
      {/* 百度搜索引擎优化 */}
      <meta name="baidu-gxt-verify-token" content={SEO_CONFIG.verification.baidu} />
      <meta name="sogou_site_verification" content={SEO_CONFIG.verification.sogou} />
      <meta name="360-site-verification" content={SEO_CONFIG.verification.qihoo360} />
      
      {/* 缓存控制 */}
      <meta httpEquiv="Cache-Control" content="public, max-age=31536000" />
      <meta httpEquiv="Expires" content="31536000" />
    </Helmet>
  );
};

export default SEOOptimization;
