import React from 'react';
import { ExternalLink, Mail, Phone } from 'lucide-react';
import { useLanguage } from '../hooks/useLanguage';
import { getTranslation } from '../utils/translations';

interface AdSpaceProps {
  position: 'header' | 'sidebar' | 'footer' | 'between-sections';
  size?: 'small' | 'medium' | 'large';
}

const AdSpace: React.FC<AdSpaceProps> = ({ position, size = 'medium' }) => {
  const { currentLanguage } = useLanguage();

  // 根据位置和大小确定样式
  const getContainerClasses = () => {
    const baseClasses = "bg-gradient-to-r from-orange-50 to-amber-50 border border-orange-200 rounded-lg overflow-hidden";
    
    switch (size) {
      case 'small':
        return `${baseClasses} p-4`;
      case 'large':
        return `${baseClasses} p-8`;
      default:
        return `${baseClasses} p-6`;
    }
  };

  const getTextClasses = () => {
    switch (size) {
      case 'small':
        return "text-sm";
      case 'large':
        return "text-lg";
      default:
        return "text-base";
    }
  };

  // 广告招商内容
  const adContent = {
    'zh': {
      title: '🎯 广告招商',
      subtitle: '与10,000+亚马逊卖家建立连接',
      description: '在AmzOva平台投放广告，精准触达目标用户群体',
      features: [
        '月访问量50,000+',
        '精准亚马逊卖家用户',
        '多种广告位选择',
        '数据透明可追踪'
      ],
      cta: '了解广告方案',
      contact: '商务合作'
    },
    'en': {
      title: '🎯 Advertising Partnership',
      subtitle: 'Connect with 10,000+ Amazon Sellers',
      description: 'Advertise on AmzOva platform to reach your target audience precisely',
      features: [
        '50,000+ Monthly Visits',
        'Targeted Amazon Sellers',
        'Multiple Ad Placements',
        'Transparent Analytics'
      ],
      cta: 'Learn More',
      contact: 'Business Partnership'
    },
    'zh-TW': {
      title: '🎯 廣告招商',
      subtitle: '與10,000+亞馬遜賣家建立連接',
      description: '在AmzOva平台投放廣告，精準觸達目標用戶群體',
      features: [
        '月訪問量50,000+',
        '精準亞馬遜賣家用戶',
        '多種廣告位選擇',
        '數據透明可追蹤'
      ],
      cta: '了解廣告方案',
      contact: '商務合作'
    }
  };

  const content = adContent[currentLanguage as keyof typeof adContent] || adContent['en'];

  // 根据位置决定是否显示（避免影响用户体验）
  const shouldShow = () => {
    // 在工具页面不显示广告
    if (window.location.pathname.startsWith('/tools/')) {
      return false;
    }
    // 在管理页面不显示广告
    if (window.location.pathname.startsWith('/admin')) {
      return false;
    }
    // 在反馈页面不显示广告
    if (window.location.pathname.startsWith('/feedback')) {
      return false;
    }
    // 只在首页显示，避免干扰其他页面
    if (position === 'between-sections' && window.location.pathname !== '/') {
      return false;
    }
    return true;
  };

  if (!shouldShow()) {
    return null;
  }

  return (
    <div className={getContainerClasses()}>
      <div className="flex flex-col lg:flex-row items-center justify-between space-y-4 lg:space-y-0 lg:space-x-6">
        {/* 左侧内容 */}
        <div className="flex-1 text-center lg:text-left">
          <h3 className={`font-bold text-orange-800 mb-2 ${size === 'large' ? 'text-xl' : 'text-lg'}`}>
            {content.title}
          </h3>
          <p className={`text-orange-700 mb-3 ${getTextClasses()}`}>
            {content.subtitle}
          </p>
          <p className={`text-orange-600 mb-4 ${size === 'small' ? 'text-xs' : 'text-sm'}`}>
            {content.description}
          </p>
          
          {size !== 'small' && (
            <div className="grid grid-cols-2 gap-2 mb-4">
              {content.features.map((feature, index) => (
                <div key={index} className="flex items-center text-orange-600 text-xs">
                  <div className="w-1.5 h-1.5 bg-orange-400 rounded-full mr-2"></div>
                  {feature}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 右侧CTA */}
        <div className="flex flex-col space-y-2">
          <a
            href="mailto:<EMAIL>?subject=AmzOva广告合作咨询"
            className="inline-flex items-center px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors duration-200 text-sm font-medium"
          >
            <Mail className="h-4 w-4 mr-2" />
            {content.cta}
          </a>
          
          {size !== 'small' && (
            <div className="text-center">
              <p className="text-xs text-orange-600 mb-1">{content.contact}</p>
              <p className="text-xs text-orange-500"><EMAIL></p>
            </div>
          )}
        </div>
      </div>

      {/* 装饰性元素 */}
      <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-orange-200 to-transparent rounded-full opacity-20 -mr-10 -mt-10"></div>
      <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-amber-200 to-transparent rounded-full opacity-20 -ml-8 -mb-8"></div>
    </div>
  );
};

export default AdSpace;
