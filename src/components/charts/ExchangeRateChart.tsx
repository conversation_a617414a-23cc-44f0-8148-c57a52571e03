import React, { useState, useEffect, useCallback } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  TimeScale,
  Filler,
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import 'chartjs-adapter-date-fns';
import { TrendingUp, Clock, BarChart3, Loader2 } from 'lucide-react';
import CurrencyService from '../../services/currencyService';
import { useLanguage } from '../../hooks/useLanguage';
import { getTranslation } from '../../utils/translations';

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  TimeScale,
  Filler
);

interface ExchangeRateChartProps {
  fromCurrency: string;
  toCurrency: string;
  className?: string;
}

interface ChartDataPoint {
  time: string;
  rate: number;
  min?: number;
  max?: number;
  count?: number;
}

const ExchangeRateChart: React.FC<ExchangeRateChartProps> = ({
  fromCurrency,
  toCurrency,
  className = ''
}) => {
  const { currentLanguage } = useLanguage();
  const [timeRange, setTimeRange] = useState<'1h' | '1d' | '1m' | '3m'>('1d');
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<{
    current: number;
    change: number;
    changePercent: number;
    high: number;
    low: number;
    volatility: number;
    trend: 'up' | 'down' | 'stable';
  } | null>(null);

  const currencyService = CurrencyService.getInstance();

  // 优化的时间范围选项 - 聚焦实际使用场景
  const timeRangeOptions = [
    {
      value: '1h',
      label: getTranslation(currentLanguage, 'ccRealTimeFluctuation'),
      description: getTranslation(currentLanguage, 'ccPast1Hour'),
      icon: Clock,
      useCase: getTranslation(currentLanguage, 'ccShortTermTrading')
    },
    {
      value: '1d',
      label: getTranslation(currentLanguage, 'ccTodayTrend'),
      description: getTranslation(currentLanguage, 'ccPast24Hours'),
      icon: BarChart3,
      useCase: getTranslation(currentLanguage, 'ccDayTrading')
    },
    {
      value: '1m',
      label: getTranslation(currentLanguage, 'ccMonthlyTrend'),
      description: getTranslation(currentLanguage, 'ccPast30Days'),
      icon: TrendingUp,
      useCase: getTranslation(currentLanguage, 'ccMediumTermPlanning')
    },
    {
      value: '3m',
      label: getTranslation(currentLanguage, 'ccQuarterlyAnalysis'),
      description: getTranslation(currentLanguage, 'ccPast90Days'),
      icon: TrendingUp,
      useCase: getTranslation(currentLanguage, 'ccLongTermTrend')
    },
  ];

  // 获取历史汇率数据
  const fetchHistoricalData = useCallback(async () => {
    if (!fromCurrency || !toCurrency || fromCurrency === toCurrency) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const data = await currencyService.getHistoricalRates(fromCurrency, toCurrency, timeRange);
      
      if (data && data.dataPoints && data.dataPoints.length > 0) {
        setChartData(data.dataPoints);
        
        // 计算增强的统计数据
        const rates = data.dataPoints.map((point: ChartDataPoint) => point.rate);
        const current = rates[rates.length - 1];
        const previous = rates[0];
        const change = current - previous;
        const changePercent = previous !== 0 ? (change / previous) * 100 : 0;
        const high = Math.max(...rates);
        const low = Math.min(...rates);

        // 计算波动率（标准差）
        const mean = rates.reduce((sum, rate) => sum + rate, 0) / rates.length;
        const variance = rates.reduce((sum, rate) => sum + Math.pow(rate - mean, 2), 0) / rates.length;
        const volatility = Math.sqrt(variance);

        // 判断趋势
        const recentRates = rates.slice(-Math.min(5, rates.length)); // 最近5个数据点
        const recentTrend = recentRates[recentRates.length - 1] - recentRates[0];
        const trend = Math.abs(recentTrend) < (volatility * 0.5) ? 'stable' :
                     recentTrend > 0 ? 'up' : 'down';

        setStats({
          current,
          change,
          changePercent,
          high,
          low,
          volatility,
          trend
        });
      } else {
        setChartData([]);
        setStats(null);
      }
    } catch (err) {
      console.error('获取历史汇率数据失败:', err);
      setError(err instanceof Error ? err.message : '获取数据失败');
      setChartData([]);
      setStats(null);
    } finally {
      setLoading(false);
    }
  }, [fromCurrency, toCurrency, timeRange, currencyService]);

  // 当货币对或时间范围变化时重新获取数据
  useEffect(() => {
    fetchHistoricalData();
  }, [fetchHistoricalData]);

  // 图表配置
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: false,
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
        backgroundColor: 'rgba(17, 24, 39, 0.95)',
        titleColor: '#f9fafb',
        bodyColor: '#f9fafb',
        borderColor: stats && stats.changePercent >= 0 ? '#059669' : '#dc2626',
        borderWidth: 2,
        cornerRadius: 8,
        padding: 12,
        titleFont: {
          size: 14,
          weight: 'bold'
        },
        bodyFont: {
          size: 13
        },
        callbacks: {
          title: (context: any) => {
            const date = new Date(context[0].parsed.x);
            return date.toLocaleString(currentLanguage === 'zh' ? 'zh-CN' : 'en-US', {
              month: 'short',
              day: 'numeric',
              hour: timeRange === '1h' ? '2-digit' : undefined,
              minute: timeRange === '1h' ? '2-digit' : undefined,
              hour12: false
            });
          },
          label: (context: any) => {
            const rate = context.parsed.y.toFixed(4);
            return `汇率: ${rate}`;
          },
          afterLabel: (context: any) => {
            if (stats) {
              const currentRate = context.parsed.y;
              const change = currentRate - stats.current;
              const changePercent = (change / stats.current) * 100;
              return `变化: ${change >= 0 ? '+' : ''}${change.toFixed(4)} (${change >= 0 ? '+' : ''}${changePercent.toFixed(2)}%)`;
            }
            return '';
          }
        }
      }
    },
    scales: {
      x: {
        type: 'time' as const,
        time: {
          displayFormats: {
            hour: 'HH:mm',
            day: 'MM-dd',
            week: 'MM-dd',
            month: 'MM-dd'
          }
        },
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
        ticks: {
          color: '#6b7280',
          maxTicksLimit: 8
        }
      },
      y: {
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
        ticks: {
          color: '#6b7280',
          callback: function(value: any) {
            return parseFloat(value).toFixed(6);
          }
        }
      }
    },
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    elements: {
      point: {
        radius: 0,
        hoverRadius: 4,
      },
      line: {
        tension: 0.1,
      }
    }
  };

  // 优化的图表数据
  const data = {
    labels: chartData.map(point => new Date(point.time)),
    datasets: [
      {
        label: `${fromCurrency}/${toCurrency}`,
        data: chartData.map(point => point.rate),
        borderColor: stats && stats.changePercent >= 0 ? '#059669' : '#dc2626',
        backgroundColor: stats && stats.changePercent >= 0 ?
          'linear-gradient(180deg, rgba(5, 150, 105, 0.2) 0%, rgba(5, 150, 105, 0.05) 100%)' :
          'linear-gradient(180deg, rgba(220, 38, 38, 0.2) 0%, rgba(220, 38, 38, 0.05) 100%)',
        borderWidth: 3,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: stats && stats.changePercent >= 0 ? '#059669' : '#dc2626',
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
        pointRadius: 0,
        pointHoverRadius: 6,
        pointHoverBackgroundColor: stats && stats.changePercent >= 0 ? '#059669' : '#dc2626',
        pointHoverBorderColor: '#ffffff',
        pointHoverBorderWidth: 3,
      }
    ]
  };

  // 格式化数字
  const formatNumber = (num: number, decimals: number = 6) => {
    return num.toFixed(decimals);
  };

  // 格式化百分比
  const formatPercent = (num: number) => {
    const sign = num >= 0 ? '+' : '';
    return `${sign}${num.toFixed(2)}%`;
  };

  // 生成市场洞察
  const getMarketInsight = (stats: any, timeRange: string) => {
    const { changePercent, volatility, trend, current } = stats;
    const absChange = Math.abs(changePercent);

    if (timeRange === '1h') {
      if (absChange > 0.5) {
        return `${getTranslation(currentLanguage, 'ccMarketInsightHighVolatility')}（${formatPercent(changePercent)}）${getTranslation(currentLanguage, 'ccRecommendCloseMonitoring')}`;
      } else if (trend === 'stable') {
        return getTranslation(currentLanguage, 'ccMarketInsightStableShortTerm');
      } else {
        return trend === 'up' ?
          getTranslation(currentLanguage, 'ccMarketInsightRisingTrend') :
          getTranslation(currentLanguage, 'ccMarketInsightFallingTrend');
      }
    } else if (timeRange === '1d') {
      if (absChange > 2) {
        return `${getTranslation(currentLanguage, 'ccMarketInsight24hSignificant')}（${formatPercent(changePercent)}）${getTranslation(currentLanguage, 'ccMarketInsight24hActive')}`;
      } else if (absChange < 0.5) {
        return getTranslation(currentLanguage, 'ccMarketInsightDailyMild');
      } else {
        return changePercent > 0 ?
          getTranslation(currentLanguage, 'ccMarketInsightRisingDaily') :
          getTranslation(currentLanguage, 'ccMarketInsightFallingDaily');
      }
    } else if (timeRange === '1m') {
      if (absChange > 5) {
        return `${getTranslation(currentLanguage, 'ccMarketInsightMonthlyLarge')}（${formatPercent(changePercent)}）${getTranslation(currentLanguage, 'ccMarketInsightBatchTrading')}`;
      } else {
        return trend === 'up' ?
          getTranslation(currentLanguage, 'ccMarketInsightMonthlyRising') :
          trend === 'down' ?
          getTranslation(currentLanguage, 'ccMarketInsightMonthlyFalling') :
          getTranslation(currentLanguage, 'ccMarketInsightMonthlyConsolidation');
      }
    } else {
      if (absChange > 10) {
        return `${getTranslation(currentLanguage, 'ccMarketInsightQuarterlySignificant')}（${formatPercent(changePercent)}）${getTranslation(currentLanguage, 'ccMarketInsightFundamentalChange')}`;
      } else {
        return trend === 'stable' ?
          getTranslation(currentLanguage, 'ccMarketInsightQuarterlyStable') :
          getTranslation(currentLanguage, 'ccMarketInsightQuarterlyDirectional');
      }
    }
  };

  if (fromCurrency === toCurrency) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="text-center text-gray-500">
          <BarChart3 className="w-12 h-12 mx-auto mb-2 opacity-50" />
          <p>{getTranslation(currentLanguage, 'ccSelectDifferentCurrencies')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* 头部 */}
      <div className="p-3 sm:p-4 border-b border-gray-200">
        {/* 标题和时间范围选择 */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-3">
          <div className="flex items-center space-x-2">
            <TrendingUp className="w-5 h-5 text-blue-600" />
            <h3 className="text-base sm:text-lg font-semibold text-gray-900">
              汇率走势
            </h3>
            <span className="text-sm text-gray-500">
              {fromCurrency}/{toCurrency}
            </span>
          </div>

          {/* 紧凑的时间范围选择 */}
          <div className="flex gap-1 overflow-x-auto">
            {timeRangeOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => setTimeRange(option.value as any)}
                className={`flex-shrink-0 px-2 sm:px-3 py-1.5 text-xs sm:text-sm rounded-md transition-all duration-200 ${
                  timeRange === option.value
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
                title={option.useCase}
              >
                <div className="flex items-center space-x-1">
                  <option.icon className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className="font-medium">{option.label}</span>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* 紧凑的统计信息 */}
        {stats && (
          <div className="space-y-3">
            {/* 核心指标 - 响应式网格 */}
            <div className="grid grid-cols-2 lg:grid-cols-3 gap-2 sm:gap-3">
              {/* 当前汇率 */}
              <div className="bg-gray-50 rounded-lg p-2 sm:p-3 col-span-2 lg:col-span-1">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-xs sm:text-sm text-gray-500">{getTranslation(currentLanguage, 'ccCurrentRate')}</div>
                    <div className="text-lg sm:text-xl font-bold text-gray-900">{formatNumber(stats.current, 4)}</div>
                  </div>
                  <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${
                    stats.trend === 'up' ? 'bg-green-100 text-green-700' :
                    stats.trend === 'down' ? 'bg-red-100 text-red-700' :
                    'bg-gray-100 text-gray-700'
                  }`}>
                    <TrendingUp className={`w-3 h-3 ${
                      stats.trend === 'down' ? 'rotate-180' :
                      stats.trend === 'stable' ? 'rotate-90' : ''
                    }`} />
                    <span className="hidden sm:inline">
                      {stats.trend === 'up' ? getTranslation(currentLanguage, 'ccRising') :
                       stats.trend === 'down' ? getTranslation(currentLanguage, 'ccFalling') :
                       getTranslation(currentLanguage, 'ccStable')}
                    </span>
                  </div>
                </div>
              </div>

              {/* 涨跌幅 */}
              <div className="bg-gray-50 rounded-lg p-2 sm:p-3">
                <div className="text-xs sm:text-sm text-gray-500">
                  {timeRange === '1h' ? getTranslation(currentLanguage, 'cc1Hour') :
                   timeRange === '1d' ? getTranslation(currentLanguage, 'cc24Hours') :
                   timeRange === '1m' ? getTranslation(currentLanguage, 'cc30Days') :
                   getTranslation(currentLanguage, 'cc90Days')}{getTranslation(currentLanguage, 'ccChangeRange')}
                </div>
                <div className={`text-lg sm:text-xl font-bold ${stats.changePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatPercent(stats.changePercent)}
                </div>
                <div className={`text-xs sm:text-sm ${stats.changePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {stats.change >= 0 ? '+' : ''}{formatNumber(stats.change, 4)}
                </div>
              </div>

              {/* 波动区间 */}
              <div className="bg-gray-50 rounded-lg p-2 sm:p-3">
                <div className="text-xs sm:text-sm text-gray-500">波动区间</div>
                <div className="text-sm sm:text-base font-semibold text-gray-900">
                  {formatNumber(stats.low, 4)} - {formatNumber(stats.high, 4)}
                </div>
                <div className="text-xs text-gray-600">
                  波幅: {formatPercent((stats.high - stats.low) / stats.current * 100)}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 图表区域 */}
      <div className="p-3 sm:p-4">
        {loading ? (
          <div className="flex items-center justify-center h-32 sm:h-40">
            <Loader2 className="w-6 h-6 sm:w-8 sm:h-8 animate-spin text-blue-600" />
            <span className="ml-2 text-sm sm:text-base text-gray-600">加载中...</span>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-32 sm:h-40 text-red-600">
            <div className="text-center">
              <BarChart3 className="w-8 h-8 sm:w-12 sm:h-12 mx-auto mb-2 opacity-50" />
              <p className="text-sm">{error}</p>
              <button
                onClick={fetchHistoricalData}
                className="mt-2 px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                重试
              </button>
            </div>
          </div>
        ) : chartData.length === 0 ? (
          <div className="flex items-center justify-center h-32 sm:h-40 text-gray-500">
            <div className="text-center">
              <BarChart3 className="w-8 h-8 sm:w-12 sm:h-12 mx-auto mb-2 opacity-50" />
              <p className="text-sm">暂无数据</p>
            </div>
          </div>
        ) : (
          <div className="h-40 sm:h-48 lg:h-56">
            <Line data={data} options={chartOptions} />
          </div>
        )}

        {/* 市场洞察 - 移到图表下方，更紧凑 */}
        {stats && (
          <div className="mt-3 bg-blue-50 rounded-lg p-3">
            <div className="flex items-start space-x-2">
              <div className="flex-shrink-0 mt-0.5">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                  <TrendingUp className="w-3 h-3 text-blue-600" />
                </div>
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-blue-900 mb-1">{getTranslation(currentLanguage, 'ccMarketInsight')}</h4>
                <p className="text-xs sm:text-sm text-blue-700 leading-relaxed">
                  {getMarketInsight(stats, timeRange)}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ExchangeRateChart;
