import React from 'react';
import { Star, Quote } from 'lucide-react';
import { useLanguage } from '../hooks/useLanguage';
import { getTranslation } from '../utils/translations';

const CustomerReviews: React.FC = () => {
  const { currentLanguage } = useLanguage();

  const reviews = [
    {
      name: getTranslation(currentLanguage, 'review1Name'),
      title: getTranslation(currentLanguage, 'review1Title'),
      content: getTranslation(currentLanguage, 'review1Content'),
      rating: parseFloat(getTranslation(currentLanguage, 'review1Rating')),
      avatar: '👨‍💼'
    },
    {
      name: getTranslation(currentLanguage, 'review2Name'),
      title: getTranslation(currentLanguage, 'review2Title'),
      content: getTranslation(currentLanguage, 'review2Content'),
      rating: parseFloat(getTranslation(currentLanguage, 'review2Rating')),
      avatar: '👩‍💼'
    },
    {
      name: getTranslation(currentLanguage, 'review3Name'),
      title: getTranslation(currentLanguage, 'review3Title'),
      content: getTranslation(currentLanguage, 'review3Content'),
      rating: parseFloat(getTranslation(currentLanguage, 'review3Rating')),
      avatar: '👨‍💻'
    },
    {
      name: getTranslation(currentLanguage, 'review4Name'),
      title: getTranslation(currentLanguage, 'review4Title'),
      content: getTranslation(currentLanguage, 'review4Content'),
      rating: parseFloat(getTranslation(currentLanguage, 'review4Rating')),
      avatar: '👩‍🚀'
    }
  ];

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
      );
    }

    if (hasHalfStar) {
      stars.push(
        <Star key="half" className="h-5 w-5 text-yellow-400 fill-current opacity-50" />
      );
    }

    const remainingStars = 5 - Math.ceil(rating);
    for (let i = 0; i < remainingStars; i++) {
      stars.push(
        <Star key={`empty-${i}`} className="h-5 w-5 text-gray-300" />
      );
    }

    return stars;
  };

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 标题 */}
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold mb-6" style={{ color: '#232F3E' }}>
            {getTranslation(currentLanguage, 'customerReviewsTitle')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {getTranslation(currentLanguage, 'customerReviewsSubtitle')}
          </p>
        </div>

        {/* 评价网格 */}
        <div className="grid md:grid-cols-2 gap-8">
          {reviews.map((review, index) => (
            <div
              key={index}
              className="p-6 rounded-xl border transition-all duration-300 hover:shadow-md"
              style={{
                backgroundColor: '#FFF5E6',
                borderColor: '#FFD699'
              }}
            >
              {/* 引用图标 */}
              <div className="mb-6">
                <Quote className="h-8 w-8 opacity-50" style={{ color: '#FF9900' }} />
              </div>

              {/* 评价内容 */}
              <p className="text-gray-700 text-lg leading-relaxed mb-6">
                "{review.content}"
              </p>

              {/* 评分 */}
              <div className="flex items-center mb-6">
                {renderStars(review.rating)}
                <span className="ml-2 text-gray-600 font-medium">
                  {review.rating.toFixed(1)}
                </span>
              </div>

              {/* 用户信息 */}
              <div className="flex items-center">
                <div className="text-3xl mr-4">
                  {review.avatar}
                </div>
                <div>
                  <div className="font-bold text-lg" style={{ color: '#232F3E' }}>
                    {review.name}
                  </div>
                  <div className="font-medium" style={{ color: '#FF9900' }}>
                    {review.title}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 总体评分展示 */}
        <div className="mt-16 text-center">
          <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 max-w-md mx-auto">
            <div className="text-5xl font-bold text-emerald-600 mb-2">4.9</div>
            <div className="flex justify-center mb-2">
              {renderStars(4.9)}
            </div>
            <div className="text-gray-600">
              {getTranslation(currentLanguage, 'averageRating')}
            </div>
            <div className="text-sm text-gray-500 mt-2">
              基于 2,500+ 用户评价
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CustomerReviews;
