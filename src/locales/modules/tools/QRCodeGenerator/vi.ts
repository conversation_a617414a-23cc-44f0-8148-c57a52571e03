export const qrCodeGeneratorVi = {
  // Thông tin cơ bản của công cụ
  qrCodeGenerator: 'Trình Tạo Mã QR',
  qrCodeGeneratorDesc: 'Chuyển đổi liên kết thành mã QR với kiểu dáng tùy chỉnh và tính năng tải xuống cho quảng bá offline và chia sẻ',
  qrCodeGeneratorShortDesc: 'Chuyển liên kết thành mã QR với kiểu tùy chỉnh',

  // Khu vực nhập liệu
  enterUrl: 'Nhập URL',
  urlPlaceholder: 'Nhập liên kết sản phẩm, liên kết cửa hàng hoặc bất kỳ URL nào...',
  generateQR: 'Tạo Mã QR',
  regenerateQR: 'Tạo Lại',
  
  // Tùy chọn tùy chỉnh
  customizeOptions: 'Tùy Chọn Tùy Chỉnh',
  qrSize: '<PERSON><PERSON>ch Thước Mã QR',
  qrColor: '<PERSON><PERSON><PERSON>ề<PERSON> Trước',
  backgroundColor: '<PERSON><PERSON><PERSON>ề<PERSON>',
  errorCorrectionLevel: 'Mức Độ Sửa Lỗi',
  
  // Tùy chọn kích thước
  sizeSmall: 'Nhỏ (128x128)',
  sizeMedium: 'Trung Bình (256x256)',
  sizeLarge: 'Lớn (512x512)',
  sizeXLarge: 'Rất Lớn (1024x1024)',
  
  // Mức độ sửa lỗi
  errorLevelL: 'Thấp (7%)',
  errorLevelM: 'Trung Bình (15%)',
  errorLevelQ: 'Cao (25%)',
  errorLevelH: 'Cao Nhất (30%)',
  
  // Nút hành động
  downloadPNG: 'Tải Xuống PNG',
  downloadSVG: 'Tải Xuống SVG',
  copyImage: 'Sao Chép Hình Ảnh',
  printQR: 'In Mã QR',
  
  // Khu vực xem trước
  qrPreview: 'Xem Trước Mã QR',
  noQRGenerated: 'Chưa tạo mã QR nào',
  generateFirst: 'Vui lòng nhập URL và nhấp nút tạo',
  
  // Thông báo thành công
  qrGenerated: 'Tạo mã QR thành công!',
  imageCopied: 'Hình ảnh đã được sao chép vào clipboard',
  downloadStarted: 'Bắt đầu tải xuống',
  
  // Thông báo lỗi
  invalidUrl: 'Vui lòng nhập URL hợp lệ',
  urlRequired: 'Vui lòng nhập URL để chuyển đổi',
  generateError: 'Lỗi khi tạo mã QR, vui lòng thử lại',
  copyError: 'Sao chép thất bại, vui lòng lưu hình ảnh thủ công',
  downloadError: 'Tải xuống thất bại, vui lòng thử lại',
  
  // Mẹo sử dụng
  usageTips: 'Mẹo Sử Dụng',
  tip1: 'Hỗ trợ bất kỳ URL hợp lệ nào bao gồm liên kết sản phẩm, liên kết cửa hàng, mạng xã hội, v.v.',
  tip2: 'Mức độ sửa lỗi cao hơn tạo mã QR phức tạp hơn nhưng có khả năng chịu lỗi tốt hơn',
  tip3: 'Sử dụng màu nền trước tối và màu nền sau sáng để có kết quả quét tối ưu',
  tip4: 'Kích thước lớn phù hợp cho in ấn, kích thước nhỏ cho chia sẻ trực tuyến',
  tip5: 'Mã QR được tạo có thể sử dụng trên bao bì, tài liệu quảng cáo, danh thiếp, v.v.',
  
  // Trường hợp sử dụng
  useCases: 'Trường Hợp Sử Dụng',
  useCase1: 'Quảng Bá Sản Phẩm: Chuyển đổi liên kết sản phẩm thành mã QR cho bao bì hoặc tờ rơi',
  useCase2: 'Lưu Lượng Cửa Hàng: Tạo mã QR liên kết cửa hàng để khách hàng truy cập dễ dàng',
  useCase3: 'Chia Sẻ Xã Hội: Chuyển đổi liên kết mạng xã hội thành mã QR cho quảng bá offline',
  useCase4: 'Tiếp Thị Sự Kiện: Tạo mã QR trang sự kiện để tăng sự tham gia',
  useCase5: 'Hỗ Trợ Khách Hàng: Tạo mã QR liên kết dịch vụ khách hàng để đơn giản hóa quy trình liên hệ',
  
  // Thông tin chất lượng
  qualityInfo: 'Thông Tin Chất Lượng',
  qualityDesc: 'Mã QR được tạo sử dụng thuật toán chất lượng cao đảm bảo nhận dạng trên các thiết bị quét khác nhau',
  
  // Thông tin định dạng
  formatInfo: 'Thông Tin Định Dạng',
  pngFormat: 'Định dạng PNG: Phù hợp cho sử dụng web và in ấn, kích thước file nhỏ hơn',
  svgFormat: 'Định dạng SVG: Đồ họa vector, có thể phóng to vô hạn mà không mất chất lượng, phù hợp cho in ấn chuyên nghiệp',
  
  // Mẹo bảo mật
  securityTip: 'Mẹo Bảo Mật',
  securityDesc: 'Vui lòng đảm bảo URL nhập vào an toàn và đáng tin cậy, tránh tạo mã QR cho các trang web độc hại'
};
