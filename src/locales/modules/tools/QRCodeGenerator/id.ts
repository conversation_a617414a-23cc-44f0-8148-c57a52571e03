export const qrCodeGeneratorId = {
  // Informasi dasar alat
  qrCodeGenerator: 'Generator Kode QR',
  qrCodeGeneratorDesc: 'Kon<PERSON>i tautan menjadi kode QR dengan gaya yang dapat disesuaikan dan fitur unduh untuk promosi offline dan berbagi',
  qrCodeGeneratorShortDesc: 'Konversi tautan ke kode QR dengan gaya kustom',

  // Area input
  enterUrl: 'Masukkan URL',
  urlPlaceholder: '<PERSON><PERSON>kkan tautan produk, tautan toko, atau URL apa pun...',
  generateQR: 'Buat Kode QR',
  regenerateQR: 'Buat Ulang',
  
  // Opsi kustomisasi
  customizeOptions: 'Opsi Kustomisasi',
  qrSize: 'Ukuran Kode QR',
  qrColor: 'Warna Latar Depan',
  backgroundColor: 'Warna Latar Belakang',
  errorCorrectionLevel: 'Tingkat Koreksi Kesalahan',
  
  // Opsi ukuran
  sizeSmall: 'Kecil (128x128)',
  sizeMedium: 'Sedang (256x256)',
  sizeLarge: 'Besar (512x512)',
  sizeXLarge: 'Sangat Besar (1024x1024)',
  
  // Tingkat koreksi kesalahan
  errorLevelL: 'Rendah (7%)',
  errorLevelM: 'Sedang (15%)',
  errorLevelQ: 'Tinggi (25%)',
  errorLevelH: 'Tertinggi (30%)',
  
  // Tombol aksi
  downloadPNG: 'Unduh PNG',
  downloadSVG: 'Unduh SVG',
  copyImage: 'Salin Gambar',
  printQR: 'Cetak Kode QR',
  
  // Area pratinjau
  qrPreview: 'Pratinjau Kode QR',
  noQRGenerated: 'Belum ada kode QR yang dibuat',
  generateFirst: 'Silakan masukkan URL dan klik tombol buat',
  
  // Pesan sukses
  qrGenerated: 'Kode QR berhasil dibuat!',
  imageCopied: 'Gambar disalin ke clipboard',
  downloadStarted: 'Unduhan dimulai',
  
  // Pesan kesalahan
  invalidUrl: 'Silakan masukkan URL yang valid',
  urlRequired: 'Silakan masukkan URL untuk dikonversi',
  generateError: 'Kesalahan saat membuat kode QR, silakan coba lagi',
  copyError: 'Gagal menyalin, silakan simpan gambar secara manual',
  downloadError: 'Unduhan gagal, silakan coba lagi',
  
  // Tips penggunaan
  usageTips: 'Tips Penggunaan',
  tip1: 'Mendukung URL valid apa pun termasuk tautan produk, tautan toko, media sosial, dll.',
  tip2: 'Tingkat koreksi kesalahan yang lebih tinggi membuat kode QR lebih kompleks tetapi dengan toleransi kesalahan yang lebih baik',
  tip3: 'Gunakan warna latar depan gelap dan warna latar belakang terang untuk hasil pemindaian optimal',
  tip4: 'Ukuran besar cocok untuk pencetakan, ukuran kecil untuk berbagi online',
  tip5: 'Kode QR yang dihasilkan dapat digunakan pada kemasan, materi promosi, kartu nama, dll.',
  
  // Kasus penggunaan
  useCases: 'Kasus Penggunaan',
  useCase1: 'Promosi Produk: Konversi tautan produk menjadi kode QR untuk kemasan atau brosur',
  useCase2: 'Lalu Lintas Toko: Buat kode QR tautan toko untuk akses pelanggan yang mudah',
  useCase3: 'Berbagi Sosial: Konversi tautan media sosial menjadi kode QR untuk promosi offline',
  useCase4: 'Pemasaran Acara: Buat kode QR halaman acara untuk meningkatkan partisipasi',
  useCase5: 'Dukungan Pelanggan: Buat kode QR tautan layanan pelanggan untuk menyederhanakan proses kontak',
  
  // Informasi kualitas
  qualityInfo: 'Informasi Kualitas',
  qualityDesc: 'Kode QR yang dihasilkan menggunakan algoritma berkualitas tinggi yang memastikan pengenalan pada berbagai perangkat pemindai',
  
  // Informasi format
  formatInfo: 'Informasi Format',
  pngFormat: 'Format PNG: Cocok untuk penggunaan web dan pencetakan, ukuran file lebih kecil',
  svgFormat: 'Format SVG: Grafik vektor, dapat diperbesar tanpa batas tanpa kehilangan kualitas, cocok untuk pencetakan profesional',
  
  // Tips keamanan
  securityTip: 'Tips Keamanan',
  securityDesc: 'Pastikan URL yang dimasukkan aman dan terpercaya, hindari membuat kode QR untuk situs web berbahaya'
};
