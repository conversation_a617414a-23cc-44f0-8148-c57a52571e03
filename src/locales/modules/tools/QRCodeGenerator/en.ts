export const qrCodeGeneratorEn = {
  // Tool basic information
  qrCodeGenerator: 'QR Code Generator',
  qrCodeGeneratorDesc: 'Convert links to QR codes with customizable styles and download features for offline promotion and sharing',
  qrCodeGeneratorShortDesc: 'Convert links to QR codes with custom styles',

  // Input area
  enterUrl: 'Enter URL',
  urlPlaceholder: 'Enter product link, store link, or any URL...',
  generateQR: 'Generate QR Code',
  regenerateQR: 'Regenerate',
  
  // Customization options
  customizeOptions: 'Customization Options',
  qrSize: 'QR Code Size',
  qrColor: 'Foreground Color',
  backgroundColor: 'Background Color',
  errorCorrectionLevel: 'Error Correction Level',
  
  // Size options
  sizeSmall: 'Small (128x128)',
  sizeMedium: 'Medium (256x256)',
  sizeLarge: 'Large (512x512)',
  sizeXLarge: 'Extra Large (1024x1024)',
  
  // Error correction levels
  errorLevelL: 'Low (7%)',
  errorLevelM: 'Medium (15%)',
  errorLevelQ: 'High (25%)',
  errorLevelH: 'Highest (30%)',
  
  // Action buttons
  downloadPNG: 'Download PNG',
  downloadSVG: 'Download SVG',
  copyImage: 'Copy Image',
  printQR: 'Print QR Code',
  
  // Preview area
  qrPreview: 'QR Code Preview',
  noQRGenerated: 'No QR code generated yet',
  generateFirst: 'Please enter a URL and click generate button',
  
  // Success messages
  qrGenerated: 'QR code generated successfully!',
  imageCopied: 'Image copied to clipboard',
  downloadStarted: 'Download started',
  
  // Error messages
  invalidUrl: 'Please enter a valid URL',
  urlRequired: 'Please enter a URL to convert',
  generateError: 'Error generating QR code, please try again',
  copyError: 'Copy failed, please save image manually',
  downloadError: 'Download failed, please try again',
  
  // Usage tips
  usageTips: 'Usage Tips',
  tip1: 'Supports any valid URL including product links, store links, social media, etc.',
  tip2: 'Higher error correction levels create more complex QR codes but with better fault tolerance',
  tip3: 'Use dark foreground and light background colors for optimal scanning results',
  tip4: 'Large sizes are suitable for printing, small sizes for online sharing',
  tip5: 'Generated QR codes can be used on packaging, promotional materials, business cards, etc.',
  
  // Use cases
  useCases: 'Use Cases',
  useCase1: 'Product Promotion: Convert product links to QR codes for packaging or flyers',
  useCase2: 'Store Traffic: Generate store link QR codes for easy customer access',
  useCase3: 'Social Sharing: Convert social media links to QR codes for offline promotion',
  useCase4: 'Event Marketing: Create event page QR codes to increase participation',
  useCase5: 'Customer Support: Generate customer service link QR codes to simplify contact process',
  
  // Quality information
  qualityInfo: 'Quality Information',
  qualityDesc: 'Generated QR codes use high-quality algorithms ensuring recognition on various scanning devices',
  
  // Format information
  formatInfo: 'Format Information',
  pngFormat: 'PNG format: Suitable for web use and printing, smaller file size',
  svgFormat: 'SVG format: Vector graphics, infinitely scalable without quality loss, suitable for professional printing',
  
  // Security tip
  securityTip: 'Security Tip',
  securityDesc: 'Please ensure the entered URL is safe and trustworthy, avoid generating QR codes for malicious websites'
};
