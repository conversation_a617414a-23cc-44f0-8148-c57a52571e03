export const qrCodeGeneratorZh = {
  // 工具基本信息
  qrCodeGenerator: '二维码生成器',
  qrCodeGeneratorDesc: '将链接转换为二维码，支持自定义样式和下载功能，方便线下推广和分享',
  qrCodeGeneratorShortDesc: '链接转二维码，支持自定义样式',

  // 输入区域
  enterUrl: '输入链接',
  urlPlaceholder: '请输入产品链接、店铺链接或任何网址...',
  generateQR: '生成二维码',
  regenerateQR: '重新生成',
  
  // 自定义选项
  customizeOptions: '自定义选项',
  qrSize: '二维码尺寸',
  qrColor: '前景色',
  backgroundColor: '背景色',
  errorCorrectionLevel: '错误纠正级别',
  
  // 尺寸选项
  sizeSmall: '小 (128x128)',
  sizeMedium: '中 (256x256)',
  sizeLarge: '大 (512x512)',
  sizeXLarge: '超大 (1024x1024)',
  
  // 错误纠正级别
  errorLevelL: '低 (7%)',
  errorLevelM: '中 (15%)',
  errorLevelQ: '高 (25%)',
  errorLevelH: '最高 (30%)',
  
  // 操作按钮
  downloadPNG: '下载 PNG',
  downloadSVG: '下载 SVG',
  copyImage: '复制图片',
  printQR: '打印二维码',
  
  // 预览区域
  qrPreview: '二维码预览',
  noQRGenerated: '暂未生成二维码',
  generateFirst: '请先输入链接并点击生成按钮',
  
  // 成功消息
  qrGenerated: '二维码生成成功！',
  imageCopied: '图片已复制到剪贴板',
  downloadStarted: '下载已开始',
  
  // 错误消息
  invalidUrl: '请输入有效的网址',
  urlRequired: '请输入要转换的链接',
  generateError: '生成二维码时出错，请重试',
  copyError: '复制失败，请手动保存图片',
  downloadError: '下载失败，请重试',
  
  // 使用提示
  usageTips: '使用技巧',
  tip1: '支持任何有效的网址，包括产品链接、店铺链接、社交媒体等',
  tip2: '错误纠正级别越高，二维码越复杂但容错性越强',
  tip3: '建议使用深色前景色和浅色背景色以确保最佳扫描效果',
  tip4: '大尺寸二维码适合打印使用，小尺寸适合在线分享',
  tip5: '生成的二维码可用于产品包装、宣传材料、名片等',
  
  // 应用场景
  useCases: '应用场景',
  useCase1: '产品推广：将产品链接转为二维码，印在包装或宣传单上',
  useCase2: '店铺引流：生成店铺链接二维码，方便客户快速访问',
  useCase3: '社交分享：将社交媒体链接转为二维码，便于线下推广',
  useCase4: '活动营销：创建活动页面二维码，提高参与度',
  useCase5: '客服支持：生成客服链接二维码，简化客户联系流程',
  
  // 质量说明
  qualityInfo: '质量说明',
  qualityDesc: '生成的二维码采用高质量算法，确保在各种扫描设备上都能正常识别',
  
  // 格式说明
  formatInfo: '格式说明',
  pngFormat: 'PNG格式：适合网页使用和打印，文件较小',
  svgFormat: 'SVG格式：矢量图形，可无限缩放不失真，适合专业印刷',
  
  // 安全提示
  securityTip: '安全提示',
  securityDesc: '请确保输入的链接安全可信，避免生成恶意网址的二维码'
};
