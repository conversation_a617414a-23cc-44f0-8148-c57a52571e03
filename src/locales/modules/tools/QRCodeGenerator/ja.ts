export const qrCodeGeneratorJa = {
  // ツール基本情報
  qrCodeGenerator: 'QRコード生成器',
  qrCodeGeneratorDesc: 'リンクをQRコードに変換し、カスタムスタイルとダウンロード機能でオフライン宣伝と共有を支援',
  qrCodeGeneratorShortDesc: 'リンクをQRコードに変換、カスタムスタイル対応',

  // 入力エリア
  enterUrl: 'URLを入力',
  urlPlaceholder: '商品リンク、ストアリンク、または任意のURLを入力...',
  generateQR: 'QRコード生成',
  regenerateQR: '再生成',
  
  // カスタマイズオプション
  customizeOptions: 'カスタマイズオプション',
  qrSize: 'QRコードサイズ',
  qrColor: '前景色',
  backgroundColor: '背景色',
  errorCorrectionLevel: 'エラー訂正レベル',
  
  // サイズオプション
  sizeSmall: '小 (128x128)',
  sizeMedium: '中 (256x256)',
  sizeLarge: '大 (512x512)',
  sizeXLarge: '特大 (1024x1024)',
  
  // エラー訂正レベル
  errorLevelL: '低 (7%)',
  errorLevelM: '中 (15%)',
  errorLevelQ: '高 (25%)',
  errorLevelH: '最高 (30%)',
  
  // アクションボタン
  downloadPNG: 'PNG ダウンロード',
  downloadSVG: 'SVG ダウンロード',
  copyImage: '画像をコピー',
  printQR: 'QRコードを印刷',
  
  // プレビューエリア
  qrPreview: 'QRコードプレビュー',
  noQRGenerated: 'QRコードが生成されていません',
  generateFirst: 'URLを入力して生成ボタンをクリックしてください',
  
  // 成功メッセージ
  qrGenerated: 'QRコードが正常に生成されました！',
  imageCopied: '画像がクリップボードにコピーされました',
  downloadStarted: 'ダウンロードが開始されました',
  
  // エラーメッセージ
  invalidUrl: '有効なURLを入力してください',
  urlRequired: '変換するURLを入力してください',
  generateError: 'QRコード生成中にエラーが発生しました。再試行してください',
  copyError: 'コピーに失敗しました。手動で画像を保存してください',
  downloadError: 'ダウンロードに失敗しました。再試行してください',
  
  // 使用のヒント
  usageTips: '使用のヒント',
  tip1: '商品リンク、ストアリンク、ソーシャルメディアなど、任意の有効なURLをサポート',
  tip2: 'エラー訂正レベルが高いほど、QRコードは複雑になりますが耐障害性が向上します',
  tip3: '最適なスキャン結果を得るために、濃い前景色と薄い背景色の使用を推奨',
  tip4: '大きなサイズは印刷に適し、小さなサイズはオンライン共有に適しています',
  tip5: '生成されたQRコードは、パッケージ、宣伝材料、名刺などに使用できます',
  
  // 使用例
  useCases: '使用例',
  useCase1: '商品宣伝：商品リンクをQRコードに変換し、パッケージやチラシに印刷',
  useCase2: 'ストア誘導：ストアリンクのQRコードを生成し、顧客の簡単アクセスを実現',
  useCase3: 'ソーシャル共有：ソーシャルメディアリンクをQRコードに変換し、オフライン宣伝に活用',
  useCase4: 'イベントマーケティング：イベントページのQRコードを作成し、参加率を向上',
  useCase5: 'カスタマーサポート：カスタマーサービスリンクのQRコードを生成し、連絡プロセスを簡素化',
  
  // 品質情報
  qualityInfo: '品質情報',
  qualityDesc: '生成されたQRコードは高品質アルゴリズムを使用し、様々なスキャンデバイスでの認識を保証',
  
  // フォーマット情報
  formatInfo: 'フォーマット情報',
  pngFormat: 'PNG形式：ウェブ使用と印刷に適し、ファイルサイズが小さい',
  svgFormat: 'SVG形式：ベクターグラフィック、無限拡大可能で品質劣化なし、プロ印刷に適している',
  
  // セキュリティのヒント
  securityTip: 'セキュリティのヒント',
  securityDesc: '入力するURLが安全で信頼できることを確認し、悪意のあるウェブサイトのQRコード生成を避けてください'
};
