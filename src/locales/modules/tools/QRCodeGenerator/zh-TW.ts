export const qrCodeGeneratorZhTW = {
  // 工具基本資訊
  qrCodeGenerator: '二維碼生成器',
  qrCodeGeneratorDesc: '將連結轉換為二維碼，支援自訂樣式和下載功能，方便線下推廣和分享',
  qrCodeGeneratorShortDesc: '連結轉二維碼，支援自訂樣式',

  // 輸入區域
  enterUrl: '輸入連結',
  urlPlaceholder: '請輸入產品連結、店鋪連結或任何網址...',
  generateQR: '生成二維碼',
  regenerateQR: '重新生成',
  
  // 自訂選項
  customizeOptions: '自訂選項',
  qrSize: '二維碼尺寸',
  qrColor: '前景色',
  backgroundColor: '背景色',
  errorCorrectionLevel: '錯誤糾正級別',
  
  // 尺寸選項
  sizeSmall: '小 (128x128)',
  sizeMedium: '中 (256x256)',
  sizeLarge: '大 (512x512)',
  sizeXLarge: '超大 (1024x1024)',
  
  // 錯誤糾正級別
  errorLevelL: '低 (7%)',
  errorLevelM: '中 (15%)',
  errorLevelQ: '高 (25%)',
  errorLevelH: '最高 (30%)',
  
  // 操作按鈕
  downloadPNG: '下載 PNG',
  downloadSVG: '下載 SVG',
  copyImage: '複製圖片',
  printQR: '列印二維碼',
  
  // 預覽區域
  qrPreview: '二維碼預覽',
  noQRGenerated: '暫未生成二維碼',
  generateFirst: '請先輸入連結並點擊生成按鈕',
  
  // 成功訊息
  qrGenerated: '二維碼生成成功！',
  imageCopied: '圖片已複製到剪貼簿',
  downloadStarted: '下載已開始',
  
  // 錯誤訊息
  invalidUrl: '請輸入有效的網址',
  urlRequired: '請輸入要轉換的連結',
  generateError: '生成二維碼時出錯，請重試',
  copyError: '複製失敗，請手動儲存圖片',
  downloadError: '下載失敗，請重試',
  
  // 使用技巧
  usageTips: '使用技巧',
  tip1: '支援任何有效的網址，包括產品連結、店鋪連結、社交媒體等',
  tip2: '錯誤糾正級別越高，二維碼越複雜但容錯性越強',
  tip3: '建議使用深色前景色和淺色背景色以確保最佳掃描效果',
  tip4: '大尺寸二維碼適合列印使用，小尺寸適合線上分享',
  tip5: '生成的二維碼可用於產品包裝、宣傳材料、名片等',
  
  // 應用場景
  useCases: '應用場景',
  useCase1: '產品推廣：將產品連結轉為二維碼，印在包裝或宣傳單上',
  useCase2: '店鋪引流：生成店鋪連結二維碼，方便客戶快速訪問',
  useCase3: '社交分享：將社交媒體連結轉為二維碼，便於線下推廣',
  useCase4: '活動營銷：創建活動頁面二維碼，提高參與度',
  useCase5: '客服支援：生成客服連結二維碼，簡化客戶聯繫流程',
  
  // 品質說明
  qualityInfo: '品質說明',
  qualityDesc: '生成的二維碼採用高品質演算法，確保在各種掃描設備上都能正常識別',
  
  // 格式說明
  formatInfo: '格式說明',
  pngFormat: 'PNG格式：適合網頁使用和列印，檔案較小',
  svgFormat: 'SVG格式：向量圖形，可無限縮放不失真，適合專業印刷',
  
  // 安全提示
  securityTip: '安全提示',
  securityDesc: '請確保輸入的連結安全可信，避免生成惡意網址的二維碼'
};
