// Listing优化器翻译 - 简体中文
export const listingOptimizerZh = {
  // Listing Optimizer
  listingOptimizer: 'Listing优化器',
  listingOptimizerDesc: '全面优化您的亚马逊产品listing，提高搜索排名和转化率。',
  bulletPoints: '产品要点',
  bulletPoint: '要点',
  productDescription: '产品描述',
  descriptionPlaceholder: '输入您的详细产品描述...',
  searchTerms: '搜索词',
  searchTermsPlaceholder: '输入用逗号分隔的后台搜索词...',
  optimizeListing: '优化Listing',
  listingOptimizationScore: 'Listing优化评分',
  overallListingQuality: '整体listing质量和优化水平',
  title: '标题',
  description: '描述',
  keywords: '关键词',
  issues: '问题',
  bestPractices: '最佳实践',
  titleBestPractices: '标题最佳实践',
  bulletPointBestPractices: '要点最佳实践',
  startOptimizingListing: '开始优化您的Listing',

  // Listing Optimizer Messages
  titleRequired: '产品标题是必需的',
  titleNeedsCapitalization: '标题应使用正确的大小写',
  needMoreBulletPoints: '添加至少3个要点以提高转化率',
  bulletPointTooShort: '要点{index}太短（最少50个字符）',
  bulletPointTooLong: '要点{index}超过255字符限制',
  descriptionRequired: '产品描述是必需的',
  descriptionTooShort: '描述应至少200个字符',
  descriptionTooLong: '描述超过2000字符限制',
  useHTMLFormatting: '考虑使用HTML格式以提高可读性',
  searchTermsRequired: '后台搜索词是必需的',
  needMoreKeywords: '添加更多搜索词（目标15-20个相关关键词）',
  duplicateKeywords: '从搜索词中删除重复的关键词',
  titleOptimal: '标题长度和结构是最佳的',
  improveTitleSuggestion: '改进标题长度、关键词位置和格式',
  bulletPointsOptimal: '要点优化良好',
  improveBulletPoints: '用更多细节和好处增强要点',
  titleTip1: '在前50个字符内包含主要关键词',
  titleTip2: '使用正确的大小写（不是全大写）',
  titleTip3: '避免像"最好"或"惊人"这样的主观声明',
  bulletTip1: '每个要点以关键好处或特性开始',
  bulletTip2: '每个要点使用150-200个字符',

  // Tool Names
  listingOptimizerTool: 'Listing优化器',

  // 工具详细信息部分标题
  loToolDetailInfoTitle: '工具详细信息',
  loToolDetailInfoSubtitle: '了解更多关于Listing优化器的使用方法和最佳实践',

  // 工具介绍部分
  loToolIntroTitle: '工具介绍',
  loToolIntroContent: 'Listing优化器是一款专业的Amazon产品页面优化工具，基于Amazon A9算法和最新的SEO最佳实践，帮助卖家全面优化产品标题、要点、描述和后台搜索词。通过智能分析和专业建议，显著提升产品的搜索排名、点击率和转化率，让您的产品在竞争激烈的Amazon市场中脱颖而出。',

  loToolFeaturesTitle: '核心功能',
  loFeatureComprehensiveAnalysis: '全面分析',
  loFeatureComprehensiveAnalysisDesc: '深度分析标题、要点、描述和搜索词的优化程度',
  loFeatureScoreSystem: '评分系统',
  loFeatureScoreSystemDesc: '基于Amazon算法提供详细的优化评分和改进建议',
  loFeatureRealTimeCheck: '实时检查',
  loFeatureRealTimeCheckDesc: '输入内容时实时检测问题并提供优化建议',
  loFeatureBestPractices: '最佳实践指导',
  loFeatureBestPracticesDesc: '提供基于Amazon政策和成功案例的专业优化建议',

  // 使用指南部分
  loUsageGuideTitle: '使用指南',
  loStep1Title: '第一步：输入产品标题',
  loStep1Content: '在标题输入框中填写您的产品标题。工具会自动检测标题长度、关键词分布、大小写格式等关键因素，并提供实时的优化建议。',
  loStep2Title: '第二步：添加产品要点',
  loStep2Content: '填写3-5个产品要点，每个要点应突出产品的核心特性和优势。工具会检查要点的长度、结构和关键词密度，确保符合Amazon最佳实践。',
  loStep3Title: '第三步：完善产品描述',
  loStep3Content: '输入详细的产品描述，建议使用HTML格式提高可读性。工具会分析描述的长度、关键词覆盖和结构合理性。',
  loStep4Title: '第四步：设置后台搜索词',
  loStep4Content: '添加相关的后台搜索词，用逗号分隔。这些词不会在前台显示，但对搜索排名至关重要。工具会检查关键词的相关性和重复性。',
  loStep5Title: '第五步：查看优化建议',
  loStep5Content: '根据工具提供的评分和具体建议，逐项优化您的listing内容，直到达到最佳的优化效果。',

  // FAQ部分
  loFaqTitle: '常见问题',
  loFaq1Question: '为什么我的listing评分较低？',
  loFaq1Answer: 'Listing评分基于多个因素：标题关键词密度、要点完整性、描述详细程度、搜索词相关性等。低分通常意味着某些关键要素需要优化，请根据具体建议逐项改进。',

  loFaq2Question: '产品要点应该写多少个？',
  loFaq2Answer: 'Amazon允许最多5个要点，建议至少填写3个。每个要点应该突出不同的产品特性或优势，长度控制在150-200字符之间，以获得最佳的显示效果。',

  loFaq3Question: '后台搜索词有什么作用？',
  loFaq3Answer: '后台搜索词不会在产品页面显示，但对搜索排名至关重要。它们帮助您的产品在相关搜索中被发现，建议包含15-20个相关关键词，避免重复前台已有的词汇。',

  loFaq4Question: '如何提高listing的转化率？',
  loFaq4Answer: '转化率提升需要综合优化：标题要包含核心关键词、要点要突出产品优势、描述要详细专业、图片要高质量。同时关注客户评价和定价策略。',

  loFaq5Question: '优化后多久能看到效果？',
  loFaq5Answer: 'Amazon通常需要24-48小时更新listing信息，搜索排名的改善可能需要1-2周时间。建议持续监控关键词排名和转化数据，根据表现调整优化策略。',

  loFaq6Question: '工具的评分标准是什么？',
  loFaq6Answer: '评分基于Amazon A9算法的关键因素：关键词相关性(30%)、内容完整性(25%)、格式规范性(20%)、用户体验(15%)、SEO优化(10%)。90分以上为优秀，80-89分为良好。',

  // 最佳实践部分
  loBestPracticesTitle: '最佳实践建议',
  loBestPractice1: '标题前50个字符包含最重要的关键词，确保在移动端完整显示',
  loBestPractice2: '每个要点以强有力的特性或优势开头，使用数字和具体描述',
  loBestPractice3: '产品描述使用HTML格式，包含详细规格和使用场景',
  loBestPractice4: '后台搜索词包含同义词、拼写变体和长尾关键词',
  loBestPractice5: '定期根据搜索词报告和竞争对手分析更新listing内容',
  loBestPractice6: '结合A/B测试验证不同版本的listing表现效果',
};
