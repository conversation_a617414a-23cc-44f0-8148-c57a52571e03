// Listing Optimizer translations - English
export const listingOptimizerEn = {
  // Listing Optimizer
  listingOptimizer: 'Listing Optimizer',
  listingOptimizerDesc: 'Comprehensively optimize your Amazon product listing to improve search ranking and conversion rate.',
  bulletPoints: 'Bullet Points',
  bulletPoint: 'Bullet Point',
  productDescription: 'Product Description',
  descriptionPlaceholder: 'Enter your detailed product description...',
  searchTerms: 'Search Terms',
  searchTermsPlaceholder: 'Enter comma-separated backend search terms...',
  optimizeListing: 'Optimize Listing',
  listingOptimizationScore: 'Listing Optimization Score',
  overallListingQuality: 'Overall listing quality and optimization level',
  title: 'Title',
  description: 'Description',
  keywords: 'Keywords',
  issues: 'Issues',
  bestPractices: 'Best Practices',
  titleBestPractices: 'Title Best Practices',
  bulletPointBestPractices: 'Bullet Point Best Practices',
  startOptimizingListing: 'Start Optimizing Your Listing',

  // Listing Optimizer Messages
  titleRequired: 'Product title is required',
  titleNeedsCapitalization: 'Title should use proper capitalization',
  needMoreBulletPoints: 'Add at least 3 bullet points to improve conversion rate',
  bulletPointTooShort: 'Bullet point {index} is too short (minimum 50 characters)',
  bulletPointTooLong: 'Bullet point {index} exceeds 255 character limit',
  descriptionRequired: 'Product description is required',
  descriptionTooShort: 'Description should be at least 200 characters',
  descriptionTooLong: 'Description exceeds 2000 character limit',
  useHTMLFormatting: 'Consider using HTML formatting for better readability',
  searchTermsRequired: 'Backend search terms are required',
  needMoreKeywords: 'Add more search terms (target 15-20 relevant keywords)',
  duplicateKeywords: 'Remove duplicate keywords from search terms',
  titleOptimal: 'Title length and structure are optimal',
  improveTitleSuggestion: 'Improve title length, keyword placement and formatting',
  bulletPointsOptimal: 'Bullet points are well optimized',
  improveBulletPoints: 'Enhance bullet points with more details and benefits',
  titleTip1: 'Include main keywords within the first 50 characters',
  titleTip2: 'Use proper capitalization (not all caps)',
  titleTip3: 'Avoid subjective claims like "best" or "amazing"',
  bulletTip1: 'Start each bullet point with a key benefit or feature',
  bulletTip2: 'Use 150-200 characters per bullet point',

  // Tool Names
  listingOptimizerTool: 'Listing Optimizer',

  // Tool Detail Info Section Titles
  loToolDetailInfoTitle: 'Tool Detailed Information',
  loToolDetailInfoSubtitle: 'Learn more about how to use the Listing Optimizer and best practices',

  // Tool Introduction Section
  loToolIntroTitle: 'Tool Introduction',
  loToolIntroContent: 'The Listing Optimizer is a professional Amazon product page optimization tool based on Amazon\'s A9 algorithm and the latest SEO best practices. It helps sellers comprehensively optimize product titles, bullet points, descriptions, and backend search terms. Through intelligent analysis and professional recommendations, it significantly improves product search rankings, click-through rates, and conversion rates, making your products stand out in the competitive Amazon marketplace.',

  loToolFeaturesTitle: 'Core Features',
  loFeatureComprehensiveAnalysis: 'Comprehensive Analysis',
  loFeatureComprehensiveAnalysisDesc: 'In-depth analysis of title, bullet points, description, and search terms optimization',
  loFeatureScoreSystem: 'Scoring System',
  loFeatureScoreSystemDesc: 'Detailed optimization scores and improvement suggestions based on Amazon algorithm',
  loFeatureRealTimeCheck: 'Real-time Check',
  loFeatureRealTimeCheckDesc: 'Real-time problem detection and optimization suggestions as you type',
  loFeatureBestPractices: 'Best Practices Guidance',
  loFeatureBestPracticesDesc: 'Professional optimization advice based on Amazon policies and success cases',

  // Usage Guide Section
  loUsageGuideTitle: 'Usage Guide',
  loStep1Title: 'Step 1: Enter Product Title',
  loStep1Content: 'Fill in your product title in the title input box. The tool will automatically detect key factors such as title length, keyword distribution, and case formatting, providing real-time optimization suggestions.',
  loStep2Title: 'Step 2: Add Product Bullet Points',
  loStep2Content: 'Fill in 3-5 product bullet points, each highlighting core product features and benefits. The tool will check bullet point length, structure, and keyword density to ensure compliance with Amazon best practices.',
  loStep3Title: 'Step 3: Complete Product Description',
  loStep3Content: 'Enter a detailed product description, preferably using HTML formatting for better readability. The tool will analyze description length, keyword coverage, and structural reasonableness.',
  loStep4Title: 'Step 4: Set Backend Search Terms',
  loStep4Content: 'Add relevant backend search terms separated by commas. These terms won\'t display on the front end but are crucial for search rankings. The tool will check keyword relevance and duplication.',
  loStep5Title: 'Step 5: Review Optimization Suggestions',
  loStep5Content: 'Based on the scores and specific suggestions provided by the tool, optimize your listing content item by item until you achieve the best optimization results.',

  // FAQ Section
  loFaqTitle: 'Frequently Asked Questions',
  loFaq1Question: 'Why is my listing score low?',
  loFaq1Answer: 'Listing scores are based on multiple factors: title keyword density, bullet point completeness, description detail level, search term relevance, etc. Low scores usually indicate that certain key elements need optimization. Please improve item by item according to specific suggestions.',

  loFaq2Question: 'How many bullet points should I write?',
  loFaq2Answer: 'Amazon allows up to 5 bullet points, with at least 3 recommended. Each bullet point should highlight different product features or benefits, with length controlled between 150-200 characters for optimal display.',

  loFaq3Question: 'What is the purpose of backend search terms?',
  loFaq3Answer: 'Backend search terms don\'t display on the product page but are crucial for search rankings. They help your product be discovered in relevant searches. Include 15-20 relevant keywords, avoiding duplication of front-end terms.',

  loFaq4Question: 'How to improve listing conversion rates?',
  loFaq4Answer: 'Conversion rate improvement requires comprehensive optimization: titles should include core keywords, bullet points should highlight product advantages, descriptions should be detailed and professional, and images should be high quality. Also focus on customer reviews and pricing strategy.',

  loFaq5Question: 'How long after optimization can I see results?',
  loFaq5Answer: 'Amazon typically takes 24-48 hours to update listing information, and search ranking improvements may take 1-2 weeks. It\'s recommended to continuously monitor keyword rankings and conversion data, adjusting optimization strategies based on performance.',

  loFaq6Question: 'What are the tool\'s scoring criteria?',
  loFaq6Answer: 'Scoring is based on key factors of Amazon\'s A9 algorithm: keyword relevance (30%), content completeness (25%), format compliance (20%), user experience (15%), SEO optimization (10%). 90+ is excellent, 80-89 is good.',

  // Best Practices Section
  loBestPracticesTitle: 'Best Practice Recommendations',
  loBestPractice1: 'Include the most important keywords in the first 50 characters of the title to ensure complete display on mobile',
  loBestPractice2: 'Start each bullet point with a strong feature or benefit, using numbers and specific descriptions',
  loBestPractice3: 'Use HTML formatting in product descriptions, including detailed specifications and usage scenarios',
  loBestPractice4: 'Include synonyms, spelling variants, and long-tail keywords in backend search terms',
  loBestPractice5: 'Regularly update listing content based on search term reports and competitor analysis',
  loBestPractice6: 'Use A/B testing to verify the performance of different listing versions',
};
