// Pengoptimal Listing terjemahan - Bahasa Indonesia
export const listingOptimizerId = {
  // Listing Optimizer
  listingOptimizer: 'Pengoptimal Listing',
  listingOptimizerDesc: 'Optimalkan listing produk Amazon Anda secara komprehensif untuk meningkatkan peringkat pencarian dan tingkat konversi.',
  bulletPoints: 'Poin-poin <PERSON>',
  bulletPoint: 'Poin Utama',
  productDescription: 'Deskripsi Produk',
  descriptionPlaceholder: 'Masukkan deskripsi produk detail Anda...',
  searchTerms: 'Istilah Pencarian',
  searchTermsPlaceholder: 'Masukkan istilah pencarian backend yang dipisahkan koma...',
  optimizeListing: 'Optimalkan Listing',
  listingOptimizationScore: 'Skor Optimasi Listing',
  overallListingQuality: 'Kualitas listing keseluruhan dan tingkat optimasi',
  title: '<PERSON>du<PERSON>',
  description: '<PERSON>krip<PERSON>',
  keywords: 'Kata Kunci',
  issues: 'Masalah',
  bestPractices: 'Praktik Terbaik',
  titleBestPractices: 'Praktik Terbaik Judul',
  bulletPointBestPractices: 'Praktik Terbaik Poin Utama',
  startOptimizingListing: 'Mulai Mengoptimalkan Listing Anda',

  // Listing Optimizer Messages
  titleRequired: 'Judul produk diperlukan',
  titleNeedsCapitalization: 'Judul harus menggunakan kapitalisasi yang tepat',
  needMoreBulletPoints: 'Tambahkan setidaknya 3 poin utama untuk meningkatkan tingkat konversi',
  bulletPointTooShort: 'Poin utama {index} terlalu pendek (minimum 50 karakter)',
  bulletPointTooLong: 'Poin utama {index} melebihi batas 255 karakter',
  descriptionRequired: 'Deskripsi produk diperlukan',
  descriptionTooShort: 'Deskripsi harus setidaknya 200 karakter',
  descriptionTooLong: 'Deskripsi melebihi batas 2000 karakter',
  useHTMLFormatting: 'Pertimbangkan menggunakan format HTML untuk keterbacaan yang lebih baik',
  searchTermsRequired: 'Istilah pencarian backend diperlukan',
  needMoreKeywords: 'Tambahkan lebih banyak istilah pencarian (target 15-20 kata kunci yang relevan)',
  duplicateKeywords: 'Hapus kata kunci duplikat dari istilah pencarian',
  titleOptimal: 'Panjang dan struktur judul optimal',
  improveTitleSuggestion: 'Tingkatkan panjang judul, penempatan kata kunci, dan format',
  bulletPointsOptimal: 'Poin utama dioptimalkan dengan baik',
  improveBulletPoints: 'Tingkatkan poin utama dengan lebih banyak detail dan manfaat',
  titleTip1: 'Sertakan kata kunci utama dalam 50 karakter pertama',
  titleTip2: 'Gunakan kapitalisasi yang tepat (bukan semua huruf kapital)',
  titleTip3: 'Hindari klaim subjektif seperti "terbaik" atau "menakjubkan"',
  bulletTip1: 'Mulai setiap poin utama dengan manfaat atau fitur utama',
  bulletTip2: 'Gunakan 150-200 karakter per poin utama',

  // Tool Names
  listingOptimizerTool: 'Pengoptimal Listing',

  // Judul Bagian Informasi Detail Alat
  loToolDetailInfoTitle: 'Informasi Detail Alat',
  loToolDetailInfoSubtitle: 'Pelajari lebih lanjut tentang cara menggunakan Pengoptimal Listing dan praktik terbaik',

  // Bagian Pengenalan Alat
  loToolIntroTitle: 'Pengenalan Alat',
  loToolIntroContent: 'Pengoptimal Listing adalah alat optimasi halaman produk Amazon profesional berdasarkan algoritma A9 Amazon dan praktik SEO terbaru.',

  loToolFeaturesTitle: 'Fitur Inti',
  loFeatureComprehensiveAnalysis: 'Analisis Komprehensif',
  loFeatureComprehensiveAnalysisDesc: 'Analisis mendalam tingkat optimasi judul, poin utama, deskripsi, dan kata pencarian',

  // Bagian Panduan Penggunaan
  loUsageGuideTitle: 'Panduan Penggunaan',
  loStep1Title: 'Langkah 1: Masukkan Judul Produk',
  loStep1Content: 'Isi judul produk Anda di kotak input judul. Alat akan secara otomatis mendeteksi faktor-faktor kunci seperti panjang judul dan distribusi kata kunci.',

  // Bagian FAQ
  loFaqTitle: 'Pertanyaan Umum',
  loFaq1Question: 'Mengapa skor listing saya rendah?',
  loFaq1Answer: 'Skor listing berdasarkan beberapa faktor. Skor rendah biasanya berarti elemen kunci tertentu perlu optimasi.',
};
