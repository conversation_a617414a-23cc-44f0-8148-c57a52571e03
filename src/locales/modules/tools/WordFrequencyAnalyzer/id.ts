// Analisis <PERSON>si Kata Terjemahan - Bahasa Indonesia
export const wordFrequencyAnalyzerId = {
  // Informasi Dasar Alat
  wordFrequencyAnalyzer: 'Analisis Frekuensi Kata',
  wordFrequencyAnalyzerDesc: 'Analisis frekuensi kata dalam teks untuk mengoptimalkan tata letak kata kunci dan strategi analisis listing kompetitor.',
  
  // Elemen Antarmuka
  textToAnalyze: 'Teks untuk Dianalisis',
  textPlaceholder: 'Tempel judul produk, poin-poin, deskripsi, atau konten listing kompetitor di sini...\n\nContoh:\nHeadphone Bluetooth Nirkabel Peredam Bising Earbuds Olahraga Baterai Tahan Lama Tahan Air Kompatibel iPhone Android Suara Stereo Berkualitas Tinggi dengan Case Pengisian',
  characters: 'karakter',
  words: 'kata',
  
  // Pengaturan Analisis
  analysisSettings: 'Pengaturan Analisis',
  minWordLength: 'Panjang Kata Minimum',
  excludeCommonWords: 'Kecualikan Kata Umum',
  sortBy: 'Urutkan Berdasarkan',
  byFrequency: 'Berdasarkan Frekuensi',
  alphabetically: 'Berdasarkan Abjad',
  
  // Tombol Aksi
  analyzeText: 'Analisis Teks',
  analyzing: 'Menganalisis...',
  reset: 'Reset',
  export: 'Ekspor',
  loadExample: 'Muat Contoh',
  
  // Hasil Analisis
  startAnalysis: 'Mulai Analisis Frekuensi Kata',
  enterTextToAnalyze: 'Masukkan konten teks dan kami akan menganalisis frekuensi kata dan distribusi kata kunci untuk Anda',
  analysisOverview: 'Ringkasan Analisis',
  totalWords: 'Total Kata',
  uniqueWords: 'Kata Unik',
  avgWordLength: 'Rata-rata Panjang Kata',
  readabilityScore: 'Skor Keterbacaan',
  
  // Analisis Kata Kunci
  topKeywords: 'Kata Kunci Teratas',
  wordFrequencyList: 'Daftar Frekuensi Kata',
  allWords: 'Semua Kata',
  highFrequency: 'Frekuensi Tinggi (≥2%)',
  mediumFrequency: 'Frekuensi Sedang (0.5-2%)',
  lowFrequency: 'Frekuensi Rendah (<0.5%)',
  noWordsFound: 'Tidak ada kata yang ditemukan sesuai kriteria',
  
  // Wawasan Analisis
  analysisInsights: 'Wawasan Analisis',
  keywordDiversityTip: 'Keragaman Kata Kunci: Konsentrasi berlebihan kata frekuensi tinggi dapat membatasi cakupan pencarian. Pertimbangkan untuk meningkatkan penggunaan kata kunci frekuensi sedang.',
  competitorAnalysisTip: 'Analisis Kompetitor: Membandingkan kata frekuensi tinggi kompetitor dapat mengungkap kata kunci trending dan peluang kata kunci potensial.',
  listingOptimizationTip: 'Optimasi Listing: Distribusikan kata kunci frekuensi tinggi secara wajar di judul, poin-poin, dan deskripsi untuk menghindari keyword stuffing.',
  
  // Deskripsi Alat
  wordFrequencyAnalyzerShortDesc: 'Analisis frekuensi kata teks untuk optimasi kata kunci',

  // Tool Info Sections
  wfaToolIntroTitle: 'Pengenalan Alat',
  wfaToolIntroContent: 'Analisis Frekuensi Kata adalah alat analisis teks profesional yang dirancang untuk menganalisis frekuensi dan distribusi kata dalam konten teks. Alat ini sangat cocok untuk penjual Amazon untuk menganalisis tata letak kata kunci listing kompetitor, mengoptimalkan deskripsi produk mereka sendiri, dan mengembangkan strategi kata kunci yang lebih efektif. Melalui analisis data frekuensi kata yang mendalam, Anda dapat menemukan kata kunci bernilai tinggi, mengoptimalkan kepadatan kata kunci, dan meningkatkan peringkat pencarian produk.',

  wfaToolFeaturesTitle: 'Fitur Inti',
  wfaFeatureTextAnalysis: 'Analisis Teks Cerdas',
  wfaFeatureTextAnalysisDesc: 'Secara otomatis menganalisis distribusi frekuensi kata dalam teks, mengidentifikasi kata kunci frekuensi tinggi, sedang, dan rendah',
  wfaFeatureCompetitorAnalysis: 'Analisis Kompetitor',
  wfaFeatureCompetitorAnalysisDesc: 'Menganalisis tata letak kata kunci dan strategi listing kompetitor',
  wfaFeatureKeywordOptimization: 'Optimasi Kata Kunci',
  wfaFeatureKeywordOptimizationDesc: 'Memberikan rekomendasi optimasi kepadatan dan distribusi kata kunci profesional',
  wfaFeatureDataExport: 'Ekspor Data',
  wfaFeatureDataExportDesc: 'Mendukung ekspor hasil analisis dalam format JSON untuk analisis lebih lanjut',

  wfaUsageGuideTitle: 'Panduan Penggunaan',
  wfaStep1Title: 'Langkah 1: Masukkan Teks untuk Dianalisis',
  wfaStep1Content: 'Tempel judul produk, poin-poin, deskripsi, atau konten listing kompetitor yang ingin Anda analisis ke dalam kotak teks. Mendukung analisis teks campuran Cina dan Inggris.',
  wfaStep2Title: 'Langkah 2: Konfigurasi Pengaturan Analisis',
  wfaStep2Content: 'Sesuaikan parameter analisis seperti panjang kata minimum, apakah mengecualikan stop words, metode pengurutan, dll., untuk mendapatkan hasil analisis yang lebih akurat.',
  wfaStep3Title: 'Langkah 3: Jalankan Analisis Frekuensi Kata',
  wfaStep3Content: 'Klik tombol "Analisis Teks" untuk memulai analisis, atau sistem akan secara otomatis menganalisis setelah memasukkan teks. Proses analisis biasanya selesai dalam 1 detik.',
  wfaStep4Title: 'Langkah 4: Lihat Hasil Analisis',
  wfaStep4Content: 'Lihat ringkasan analisis, daftar kata kunci frekuensi tinggi, dan distribusi frekuensi kata yang detail. Setiap kata akan menampilkan jumlah kemunculan dan persentase.',
  wfaStep5Title: 'Langkah 5: Terapkan Wawasan Analisis',
  wfaStep5Content: 'Berdasarkan hasil analisis dan rekomendasi profesional, optimalkan tata letak kata kunci listing produk Anda atau kembangkan strategi iklan yang ditargetkan.',

  wfaFaqTitle: 'Pertanyaan yang Sering Diajukan',
  wfaFaq1Question: 'Bagaimana mengatur panjang kata minimum yang tepat?',
  wfaFaq1Answer: 'Kami merekomendasikan mengaturnya ke 2-3 karakter. Mengatur ke 2 menangkap lebih banyak kata pendek, sedangkan mengatur ke 3 menyaring sebagian besar kata pendek yang tidak bermakna. Untuk teks bahasa Inggris, kami merekomendasikan mengatur ke 3.',

  wfaFaq2Question: 'Haruskah saya mengecualikan stop words?',
  wfaFaq2Answer: 'Kami merekomendasikan mengaktifkan penyaringan stop words. Sistem secara otomatis menyaring stop words umum seperti "the", "and", "of", serta kata-kata e-commerce yang tidak bermakna seperti "new", "hot", "sale", memungkinkan Anda fokus pada kata kunci yang benar-benar berharga.',

  wfaFaq3Question: 'Bagaimana memahami kategori frekuensi kata?',
  wfaFaq3Answer: 'Kata frekuensi tinggi (≥2%): Kata kunci inti dengan tingkat kemunculan sangat tinggi; Kata frekuensi sedang (0.5-2%): Kata kunci pendukung penting; Kata frekuensi rendah (<0.5%): Kata kunci ekor panjang atau istilah khusus. Distribusi kata kunci yang wajar harus mencakup kata-kata dari semua tingkat frekuensi.',

  wfaBestPracticesTitle: 'Praktik Terbaik',
  wfaBestPractice1: 'Analisis kompetitor: Secara teratur menganalisis konten listing kompetitor teratas untuk menemukan kata kunci trending pasar dan tren yang muncul',
  wfaBestPractice2: 'Kontrol kepadatan kata kunci: Pastikan kata kunci inti menyumbang 2-5% untuk menghindari keyword stuffing',
  wfaBestPractice3: 'Keseimbangan keragaman: Pertahankan distribusi yang wajar dari kata frekuensi tinggi, sedang, dan rendah untuk meningkatkan cakupan pencarian',
  wfaBestPractice4: 'Optimasi reguler: Secara teratur sesuaikan tata letak kata kunci judul produk, poin-poin, dan deskripsi berdasarkan hasil analisis frekuensi kata',
  wfaBestPractice5: 'Pendekatan berbasis data: Gabungkan data analisis frekuensi kata dengan data konversi aktual untuk mengembangkan strategi kata kunci yang lebih tepat'
};
