// Word Frequency Analyzer Translation - English
export const wordFrequencyAnalyzerEn = {
  // Tool Basic Information
  wordFrequencyAnalyzer: 'Word Frequency Analyzer',
  wordFrequencyAnalyzerDesc: 'Analyze word frequency in text to optimize keyword layout and strategy for competitor listing analysis.',
  
  // Interface Elements
  textToAnalyze: 'Text to Analyze',
  textPlaceholder: 'Paste your product title, bullet points, description, or competitor listing content here...\n\nExample:\nWireless Bluetooth Headphones Noise Cancelling Sports Earbuds Long Battery Life Waterproof Compatible with iPhone Android High Quality Stereo Sound with Charging Case',
  characters: 'characters',
  words: 'words',
  
  // Analysis Settings
  analysisSettings: 'Analysis Settings',
  minWordLength: 'Minimum Word Length',
  excludeCommonWords: 'Exclude Common Stop Words',
  sortBy: 'Sort By',
  byFrequency: 'By Frequency',
  alphabetically: 'Alphabetically',
  
  // Action Buttons
  analyzeText: 'Analyze Text',
  analyzing: 'Analyzing...',
  reset: 'Reset',
  export: 'Export',
  loadExample: 'Load Example',
  
  // Analysis Results
  startAnalysis: 'Start Word Frequency Analysis',
  enterTextToAnalyze: 'Enter text content and we will analyze word frequency and keyword distribution for you',
  analysisOverview: 'Analysis Overview',
  totalWords: 'Total Words',
  uniqueWords: 'Unique Words',
  avgWordLength: 'Avg Word Length',
  readabilityScore: 'Readability Score',
  
  // Keyword Analysis
  topKeywords: 'Top Keywords',
  wordFrequencyList: 'Word Frequency List',
  allWords: 'All Words',
  highFrequency: 'High Frequency (≥2%)',
  mediumFrequency: 'Medium Frequency (0.5-2%)',
  lowFrequency: 'Low Frequency (<0.5%)',
  noWordsFound: 'No words found matching criteria',
  
  // Analysis Insights
  analysisInsights: 'Analysis Insights',
  keywordDiversityTip: 'Keyword Diversity: Over-concentration of high-frequency words may limit search coverage. Consider increasing medium-frequency keyword usage.',
  competitorAnalysisTip: 'Competitor Analysis: Comparing competitor high-frequency words can reveal trending keywords and potential keyword opportunities.',
  listingOptimizationTip: 'Listing Optimization: Distribute high-frequency keywords reasonably across title, bullet points, and description to avoid keyword stuffing.',
  
  // Tool Description
  wordFrequencyAnalyzerShortDesc: 'Analyze text word frequency for keyword optimization',

  // Tool Info Sections
  wfaToolIntroTitle: 'Tool Introduction',
  wfaToolIntroContent: 'The Word Frequency Analyzer is a professional text analysis tool designed to analyze the frequency and distribution of words in text content. This tool is particularly suitable for Amazon sellers to analyze competitor listing keyword layouts, optimize their own product descriptions, and develop more effective keyword strategies. Through in-depth word frequency data analysis, you can discover high-value keywords, optimize keyword density, and improve product search rankings.',

  wfaToolFeaturesTitle: 'Core Features',
  wfaFeatureTextAnalysis: 'Smart Text Analysis',
  wfaFeatureTextAnalysisDesc: 'Automatically analyze word frequency distribution in text, identify high, medium, and low frequency keywords',
  wfaFeatureCompetitorAnalysis: 'Competitor Analysis',
  wfaFeatureCompetitorAnalysisDesc: 'Analyze competitor listing keyword layouts and strategies',
  wfaFeatureKeywordOptimization: 'Keyword Optimization',
  wfaFeatureKeywordOptimizationDesc: 'Provide professional keyword density and distribution optimization recommendations',
  wfaFeatureDataExport: 'Data Export',
  wfaFeatureDataExportDesc: 'Support exporting analysis results in JSON format for further analysis',

  wfaUsageGuideTitle: 'Usage Guide',
  wfaStep1Title: 'Step 1: Input Text to Analyze',
  wfaStep1Content: 'Paste the product title, bullet points, description, or competitor listing content you want to analyze into the text box. Supports mixed Chinese and English text analysis.',
  wfaStep2Title: 'Step 2: Configure Analysis Settings',
  wfaStep2Content: 'Adjust analysis parameters such as minimum word length, whether to exclude stop words, sorting method, etc., to get more accurate analysis results.',
  wfaStep3Title: 'Step 3: Execute Word Frequency Analysis',
  wfaStep3Content: 'Click the "Analyze Text" button to start analysis, or the system will automatically analyze after entering text. The analysis process usually completes within 1 second.',
  wfaStep4Title: 'Step 4: View Analysis Results',
  wfaStep4Content: 'View analysis overview, high-frequency keyword list, and detailed word frequency distribution. Each word will display occurrence count and percentage.',
  wfaStep5Title: 'Step 5: Apply Analysis Insights',
  wfaStep5Content: 'Based on analysis results and professional recommendations, optimize your product listing keyword layout or develop targeted advertising strategies.',

  wfaFaqTitle: 'Frequently Asked Questions',
  wfaFaq1Question: 'How to set appropriate minimum word length?',
  wfaFaq1Answer: 'We recommend setting it to 2-3 characters. Setting to 2 captures more short words, while setting to 3 filters out most meaningless short words. For English text, we recommend setting to 3.',

  wfaFaq2Question: 'Should I exclude stop words?',
  wfaFaq2Answer: 'We recommend enabling stop word filtering. The system automatically filters common stop words like "the", "and", "of", as well as common e-commerce meaningless words like "new", "hot", "sale", allowing you to focus on truly valuable keywords.',

  wfaFaq3Question: 'How to understand word frequency categories?',
  wfaFaq3Answer: 'High frequency words (≥2%): Core keywords with very high occurrence rates; Medium frequency words (0.5-2%): Important auxiliary keywords; Low frequency words (<0.5%): Long-tail keywords or special terms. A reasonable keyword distribution should include words from all frequency levels.',

  wfaBestPracticesTitle: 'Best Practices',
  wfaBestPractice1: 'Competitor analysis: Regularly analyze top competitor listing content to discover market trending keywords and emerging trends',
  wfaBestPractice2: 'Keyword density control: Ensure core keywords account for 2-5% to avoid keyword stuffing',
  wfaBestPractice3: 'Diversity balance: Maintain reasonable distribution of high, medium, and low frequency words to improve search coverage',
  wfaBestPractice4: 'Regular optimization: Regularly adjust product title, bullet points, and description keyword layout based on word frequency analysis results',
  wfaBestPractice5: 'Data-driven approach: Combine word frequency analysis data with actual conversion data to develop more precise keyword strategies'
};
