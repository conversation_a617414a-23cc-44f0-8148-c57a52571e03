// 単語頻度分析器翻訳 - 日本語
export const wordFrequencyAnalyzerJa = {
  // ツール基本情報
  wordFrequencyAnalyzer: '単語頻度分析器',
  wordFrequencyAnalyzerDesc: 'テキスト内の単語出現頻度を分析し、競合商品リスティングのキーワード配置と最適化戦略を分析します。',
  
  // インターフェース要素
  textToAnalyze: '分析対象テキスト',
  textPlaceholder: '商品タイトル、箇条書き、説明文、または競合リスティング内容をここに貼り付けてください...\n\n例：\nワイヤレスBluetoothヘッドホン ノイズキャンセリング スポーツイヤホン 長時間バッテリー 防水 iPhone Android対応 高音質ステレオサウンド 充電ケース付き',
  characters: '文字',
  words: '単語',
  
  // 分析設定
  analysisSettings: '分析設定',
  minWordLength: '最小単語長',
  excludeCommonWords: '一般的なストップワードを除外',
  sortBy: '並び順',
  byFrequency: '頻度順',
  alphabetically: 'アルファベット順',
  
  // アクションボタン
  analyzeText: 'テキスト分析',
  analyzing: '分析中...',
  reset: 'リセット',
  export: 'エクスポート',
  loadExample: 'サンプル読み込み',
  
  // 分析結果
  startAnalysis: '単語頻度分析を開始',
  enterTextToAnalyze: 'テキスト内容を入力すると、単語出現頻度とキーワード分布を分析します',
  analysisOverview: '分析概要',
  totalWords: '総単語数',
  uniqueWords: 'ユニーク単語数',
  avgWordLength: '平均単語長',
  readabilityScore: '読みやすさスコア',
  
  // キーワード分析
  topKeywords: '高頻度キーワード',
  wordFrequencyList: '単語頻度詳細リスト',
  allWords: '全単語',
  highFrequency: '高頻度語(≥2%)',
  mediumFrequency: '中頻度語(0.5-2%)',
  lowFrequency: '低頻度語(<0.5%)',
  noWordsFound: '条件に一致する単語が見つかりません',
  
  // 分析洞察
  analysisInsights: '分析洞察',
  keywordDiversityTip: 'キーワード多様性：高頻度語の過度な集中は検索カバレッジに影響する可能性があります。中頻度キーワードの使用を適度に増やすことをお勧めします。',
  competitorAnalysisTip: '競合分析：競合の高頻度語を比較することで、市場のトレンドキーワードと潜在的なキーワード機会を発見できます。',
  listingOptimizationTip: 'リスティング最適化：高頻度キーワードをタイトル、箇条書き、説明文に合理的に配置し、キーワードの詰め込みを避けてください。',
  
  // ツール説明
  wordFrequencyAnalyzerShortDesc: 'テキスト単語頻度分析でキーワード最適化',

  // Tool Info Sections
  wfaToolIntroTitle: 'ツール紹介',
  wfaToolIntroContent: '単語頻度分析器は、テキスト内の各単語の出現頻度と分布を分析するための専門的なテキスト分析ツールです。このツールは、Amazon販売者が競合商品リスティングのキーワードレイアウトを分析し、自社の商品説明を最適化し、より効果的なキーワード戦略を策定するのに特に適しています。単語頻度データの詳細分析により、高価値キーワードを発見し、キーワード密度を最適化し、商品検索ランキングを向上させることができます。',

  wfaToolFeaturesTitle: 'コア機能',
  wfaFeatureTextAnalysis: 'スマートテキスト分析',
  wfaFeatureTextAnalysisDesc: 'テキスト内の単語頻度分布を自動分析し、高頻度、中頻度、低頻度キーワードを識別',
  wfaFeatureCompetitorAnalysis: '競合分析',
  wfaFeatureCompetitorAnalysisDesc: '競合リスティングのキーワードレイアウトと戦略を分析',
  wfaFeatureKeywordOptimization: 'キーワード最適化',
  wfaFeatureKeywordOptimizationDesc: 'プロフェッショナルなキーワード密度と分布最適化の推奨事項を提供',
  wfaFeatureDataExport: 'データエクスポート',
  wfaFeatureDataExportDesc: '分析結果をJSON形式でエクスポートし、さらなる分析をサポート',

  wfaUsageGuideTitle: '使用ガイド',
  wfaStep1Title: 'ステップ1：分析対象テキストの入力',
  wfaStep1Content: '分析したい商品タイトル、箇条書き、説明文、または競合リスティング内容をテキストボックスに貼り付けます。中英混合テキスト分析をサポートしています。',
  wfaStep2Title: 'ステップ2：分析設定の構成',
  wfaStep2Content: '最小単語長、ストップワードの除外、ソート方法などの分析パラメータを必要に応じて調整し、より正確な分析結果を得ます。',
  wfaStep3Title: 'ステップ3：単語頻度分析の実行',
  wfaStep3Content: '「テキスト分析」ボタンをクリックして分析を開始するか、テキストを入力後にシステムが自動的に分析します。分析プロセスは通常1秒以内に完了します。',
  wfaStep4Title: 'ステップ4：分析結果の確認',
  wfaStep4Content: '分析概要、高頻度キーワードリスト、詳細な単語頻度分布を確認します。各単語は出現回数と割合が表示されます。',
  wfaStep5Title: 'ステップ5：分析洞察の適用',
  wfaStep5Content: '分析結果とプロフェッショナルな推奨事項に基づいて、商品リスティングのキーワードレイアウトを最適化するか、ターゲット広告戦略を策定します。',

  wfaFaqTitle: 'よくある質問',
  wfaFaq1Question: '適切な最小単語長の設定方法は？',
  wfaFaq1Answer: '2-3文字に設定することをお勧めします。2に設定するとより多くの短い単語を捕捉し、3に設定すると意味のない短い単語の大部分をフィルタリングします。英語テキストの場合は3に設定することをお勧めします。',

  wfaFaq2Question: 'ストップワードを除外すべきですか？',
  wfaFaq2Answer: 'ストップワードフィルタリングを有効にすることをお勧めします。システムは「the」、「and」、「of」などの一般的なストップワードや、「new」、「hot」、「sale」などのEコマースでよく見られる意味のない単語を自動的にフィルタリングし、本当に価値のあるキーワードに集中できるようにします。',

  wfaFaq3Question: '単語頻度分類の理解方法は？',
  wfaFaq3Answer: '高頻度語(≥2%)：出現頻度が非常に高いコアキーワード；中頻度語(0.5-2%)：重要な補助キーワード；低頻度語(<0.5%)：ロングテールキーワードまたは特殊用語。合理的なキーワード分布には、すべての頻度レベルの単語が含まれるべきです。',

  wfaBestPracticesTitle: 'ベストプラクティス',
  wfaBestPractice1: '競合分析：トップ競合のリスティング内容を定期的に分析し、市場のトレンドキーワードと新興トレンドを発見',
  wfaBestPractice2: 'キーワード密度制御：コアキーワードが2-5%の範囲になるようにし、キーワードの詰め込みを避ける',
  wfaBestPractice3: '多様性のバランス：高頻度語、中頻度語、低頻度語の合理的な分布を維持し、検索カバレッジを向上',
  wfaBestPractice4: '定期的な最適化：単語頻度分析結果に基づいて商品タイトル、箇条書き、説明文のキーワードレイアウトを定期的に調整',
  wfaBestPractice5: 'データ駆動アプローチ：単語頻度分析データと実際のコンバージョンデータを組み合わせ、より精密なキーワード戦略を策定'
};
