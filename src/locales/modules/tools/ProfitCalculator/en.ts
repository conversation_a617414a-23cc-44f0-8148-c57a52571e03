// Profit Calculator translations - English
export const profitCalculatorEn = {
  // Profit Calculator
  profitCalculator: 'Profit Calculator',
  profitCalculatorDesc: 'Accurately calculate Amazon sales profit, including fee analysis and return on investment.',
  productCost: 'Product Cost',
  sellingPrice: 'Selling Price',
  costOfGoods: 'Cost of Goods',
  shippingCost: 'Shipping Cost',
  amazonFees: 'Amazon Fees',
  autoCalculated: 'Auto Calculated',
  advertisingCost: 'Advertising Cost',
  otherCosts: 'Other Costs',
  profitAnalysis: 'Profit Analysis',
  grossProfit: 'Gross Profit',
  netProfit: 'Net Profit',
  profitMargin: 'Profit Margin',
  roi: 'Return on Investment',
  costBreakdown: 'Cost Breakdown',
  recommendations: 'Recommendations',
  lowMarginWarning: 'Low profit margin, recommend optimizing cost structure or increasing selling price',
  goodMarginMessage: 'Good profit margin, maintain current strategy',
  lowRoiWarning: 'Low return on investment, consider optimizing operational efficiency',
  profitTip1: 'Regularly monitor competitor prices and adjust pricing strategy',
  profitTip2: 'Optimize inventory management to reduce storage costs',
  profitTip3: 'Improve product quality and customer satisfaction to reduce return rates',

  // Category names for profit calculator
  generalCategory: 'General Merchandise',
  electronicsCategory: 'Electronics',
  clothingCategory: 'Clothing & Accessories',
  homeCategory: 'Home & Garden',
  booksCategory: 'Books & Media',
  healthCategory: 'Health & Beauty',
  automotiveCategory: 'Automotive',
  beautyCategory: 'Beauty & Personal Care',

  // New 2025 features
  displayCurrency: 'Display Currency',
  marketplace: 'Marketplace',
  category: 'Category',
  lastUpdated: 'Last Updated',

  // 2025 Amazon fee updates
  referralFee: 'Referral Fee',
  digitalServicesFee: 'Digital Services Fee',
  closingFee: 'Closing Fee',
  fbaFulfillmentFee: 'FBA Fulfillment Fee',
  storageFee: 'Storage Fee',
  inboundPlacementFee: 'Inbound Placement Fee',
  returnsProcessingFee: 'Returns Processing Fee',

  // Enhanced tips for 2025
  profitTip4: 'Stay updated with 2025 Amazon fee structure changes',
  profitTip5: 'Leverage low-price FBA rates for competitive pricing',
  profitTip6: 'Consider Digital Services Tax impact for European and Canadian markets',
};
