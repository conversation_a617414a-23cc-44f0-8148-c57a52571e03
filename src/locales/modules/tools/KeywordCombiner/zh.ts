// 关键词组合器翻译 - 简体中文
export const keywordCombinerZh = {
  // Keyword Combiner
  keywordCombiner: '关键词组合器',
  keywordCombinerDesc: '智能生成关键词组合，优化Amazon广告投放和Listing关键词策略。',
  keywordCombinerHeader: '输入2-3组不同的关键词，自动生成所有可能的组合，用于广告投放和Listing优化',

  // Input Section
  kcInputSection: '关键词输入',
  kcGroup1: '关键词组 1',
  kcGroup2: '关键词组 2',
  kcGroup3: '关键词组 3',
  kcAddGroup: '添加组',
  kcClearAll: '清空全部',
  kcKeywordPlaceholder: '请输入关键词，用逗号、中文逗号或换行分隔\n例如：无线,蓝牙,高品质\n或者：\n无线\n蓝牙\n高品质',
  kcKeywordCount: '关键词数量',

  // Results Section
  kcResultsSection: '组合结果',
  kcSortOriginal: '原始顺序',
  kcSortLength: '按长度排序',
  kcSortWords: '按词数排序',
  kcCopySelected: '复制选中',
  kcDownload: '下载结果',
  kcWords: '词',
  kcChars: '字符',

  // Actions
  kcCopy: '复制',
  kcCopied: '已复制',
  kcSelectAll: '全选',
  kcDeselectAll: '取消全选',

  // Amazon Seller Tips
  kcAmazonTips: 'Amazon卖家使用技巧',
  kcTip1Title: '广告投放策略',
  kcTip1Desc: '将生成的关键词组合用于PPC广告，测试不同组合的转化效果，优化广告投放ROI',
  kcTip2Title: 'Listing关键词优化',
  kcTip2Desc: '在产品标题、要点和描述中合理分布关键词组合，提升搜索排名和曝光度',
  kcTip3Title: '长尾关键词挖掘',
  kcTip3Desc: '组合生成的长尾关键词竞争度较低，更容易获得排名，适合新品推广',
  kcTip4Title: '关键词密度控制',
  kcTip4Desc: '避免关键词堆砌，合理控制关键词密度，保持内容自然流畅',

  // Examples and Suggestions
  kcExampleTitle: '使用示例',
  kcExample1: '示例：无线 + 蓝牙 + 耳机 = 无线蓝牙耳机',
  kcExample2: '示例：防水 + 手机 + 壳 = 防水手机壳',
  kcExample3: '示例：有机 + 棉质 + 婴儿服装 = 有机棉质婴儿服装',

  // Validation Messages
  kcMinGroupsError: '至少需要2组关键词才能生成组合',
  kcEmptyGroupError: '请在每组中至少输入一个关键词',
  kcMaxCombinationsWarning: '组合数量较多，建议精简关键词以获得更精准的结果',

  // Statistics
  kcTotalCombinations: '总组合数',
  kcAverageLength: '平均长度',
  kcShortestCombination: '最短组合',
  kcLongestCombination: '最长组合',

  // Export Options
  kcExportOptions: '导出选项',
  kcExportTxt: '导出为TXT',
  kcExportCsv: '导出为CSV',
  kcExportJson: '导出为JSON',

  // Advanced Features
  kcAdvancedOptions: '高级选项',
  kcIncludeVariations: '包含变体',
  kcExcludeDuplicates: '排除重复',
  kcMaxCombinationLength: '最大组合长度',
  kcMinCombinationLength: '最小组合长度',

  // Performance Tips
  kcPerformanceTips: '性能优化建议',
  kcPerformanceTip1: '每组关键词建议不超过10个，以确保生成速度',
  kcPerformanceTip2: '总组合数超过1000时，建议分批处理',
  kcPerformanceTip3: '使用筛选功能快速找到目标关键词组合',

  // Tool Info Sections
  kcToolIntroTitle: '工具介绍',
  kcToolIntroContent: '关键词组合器是一款专为Amazon卖家设计的智能关键词生成工具，能够自动生成所有可能的关键词组合，用于优化广告投放和Listing关键词策略。该工具支持2-3个关键词组的智能组合，帮助您发现高价值的长尾关键词，提升产品搜索排名和广告投放效果。',

  kcToolFeaturesTitle: '核心功能',
  kcFeatureSmartGeneration: '智能组合生成',
  kcFeatureSmartGenerationDesc: '输入2-3组关键词，自动生成所有可能的组合',
  kcFeatureMultiGroup: '多组支持',
  kcFeatureMultiGroupDesc: '支持最多3个关键词组，每组可包含多个关键词',
  kcFeatureRealTimePreview: '实时预览',
  kcFeatureRealTimePreviewDesc: '实时显示生成的组合数量和内容',
  kcFeatureFlexibleSort: '灵活排序',
  kcFeatureFlexibleSortDesc: '支持按原始顺序、长度、词数排序',

  kcUsageGuideTitle: '使用指南',
  kcStep1Title: '第一步：输入关键词组',
  kcStep1Content: '在关键词组输入框中输入相关关键词，每个关键词用逗号、中文逗号或换行分隔。建议每组包含3-8个相关关键词。',
  kcStep2Title: '第二步：添加更多关键词组',
  kcStep2Content: '点击"添加组"按钮添加第二个和第三个关键词组。不同组的关键词应该代表产品的不同属性或特征。',
  kcStep3Title: '第三步：查看生成结果',
  kcStep3Content: '工具会自动生成所有可能的关键词组合，显示每个组合的词数和字符数统计信息。',
  kcStep4Title: '第四步：选择和复制',
  kcStep4Content: '点击任意组合即可复制到剪贴板，或使用多选功能批量选择多个组合进行复制。',
  kcStep5Title: '第五步：导出和应用',
  kcStep5Content: '使用导出功能将所有组合保存为TXT文件，然后应用到Amazon广告投放或Listing优化中。',

  kcFaqTitle: '常见问题',
  kcFaq1Question: '每个关键词组应该包含多少个关键词？',
  kcFaq1Answer: '建议每组包含3-8个关键词。关键词太少会导致组合数量不足，太多会产生过多组合难以筛选。',

  kcFaq2Question: '如何选择不同组的关键词？',
  kcFaq2Answer: '不同组应该代表产品的不同属性。例如：第一组是产品类型（耳机、音箱），第二组是特征（无线、蓝牙），第三组是品质（高品质、专业）。',

  kcFaq3Question: '生成的组合数量太多怎么办？',
  kcFaq3Answer: '可以使用排序功能按长度或词数排序，优先选择适中长度的组合。也可以减少每组的关键词数量来控制总组合数。',

  kcBestPracticesTitle: '最佳实践',
  kcBestPractice1: '使用同义词和变体：在同一组中包含同义词和变体形式，增加覆盖面',
  kcBestPractice2: '控制组合长度：优先选择2-4个词的组合，既有搜索量又不会太长',
  kcBestPractice3: '测试不同组合：将生成的组合用于A/B测试，找出转化率最高的关键词',
  kcBestPractice4: '定期更新关键词：根据市场趋势和竞争情况定期更新关键词组合',
  kcBestPractice5: '结合搜索数据：将生成的组合与关键词搜索量数据结合，优先选择高搜索量的组合'
};
