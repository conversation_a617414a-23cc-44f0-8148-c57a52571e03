// B<PERSON><PERSON> dịch Keyword Combiner - Tiếng Việt
export const keywordCombinerVi = {
  // Keyword Combiner
  keywordCombiner: 'Công Cụ Kết Hợp Từ Khóa',
  keywordCombinerDesc: 'Tạ<PERSON> thông minh các kết hợp từ khóa để tối ưu hóa chiến lược quảng cáo Amazon và từ khóa listing.',
  keywordCombinerHeader: 'Nhập 2-3 nhóm từ khóa khác nhau để tự động tạo tất cả các kết hợp có thể cho quảng cáo và tối ưu hóa listing',

  // Input Section
  kcInputSection: 'Nhập Từ Khóa',
  kcGroup1: 'Nhóm Từ Khóa 1',
  kcGroup2: 'Nhóm Từ Khóa 2',
  kcGroup3: 'Nhó<PERSON> Từ Khóa 3',
  kcAddGroup: 'Thê<PERSON>ó<PERSON>',
  kcClearAll: '<PERSON><PERSON><PERSON> Tất C<PERSON>',
  kcKeywordPlaceholder: '<PERSON><PERSON><PERSON><PERSON> từ khó<PERSON>, phân cách bằng dấu phẩy hoặc xuống dòng...',
  kcKeywordCount: 'Số Lượng Từ Khóa',

  // Results Section
  kcResultsSection: 'Kết Quả Kết Hợp',
  kcSortOriginal: 'Thứ Tự Gốc',
  kcSortLength: 'Sắp Xếp Theo Độ Dài',
  kcSortWords: 'Sắp Xếp Theo Số Từ',
  kcCopySelected: 'Sao Chép Đã Chọn',
  kcDownload: 'Tải Xuống Kết Quả',
  kcWords: 'từ',
  kcChars: 'ký tự',

  // Actions
  kcCopy: 'Sao Chép',
  kcCopied: 'Đã Sao Chép',
  kcSelectAll: 'Chọn Tất Cả',
  kcDeselectAll: 'Bỏ Chọn Tất Cả',

  // Amazon Seller Tips
  kcAmazonTips: 'Mẹo Cho Người Bán Amazon',
  kcTip1Title: 'Chiến Lược Quảng Cáo PPC',
  kcTip1Desc: 'Sử dụng các kết hợp từ khóa được tạo cho quảng cáo PPC, kiểm tra tỷ lệ chuyển đổi của các kết hợp khác nhau để tối ưu hóa ROI quảng cáo',
  kcTip2Title: 'Tối Ưu Hóa Từ Khóa Listing',
  kcTip2Desc: 'Phân bố chiến lược các kết hợp từ khóa trong tiêu đề sản phẩm, điểm đặc sắc và mô tả để cải thiện thứ hạng tìm kiếm và khả năng hiển thị',
  kcTip3Title: 'Khám Phá Từ Khóa Đuôi Dài',
  kcTip3Desc: 'Các kết hợp từ khóa đuôi dài được tạo có cạnh tranh thấp hơn và dễ xếp hạng hơn, hoàn hảo cho việc ra mắt sản phẩm mới',
  kcTip4Title: 'Kiểm Soát Mật Độ Từ Khóa',
  kcTip4Desc: 'Tránh nhồi nhét từ khóa, duy trì mật độ từ khóa hợp lý trong khi giữ nội dung tự nhiên và dễ đọc',

  // Examples and Suggestions
  kcExampleTitle: 'Ví Dụ Sử Dụng',
  kcExample1: 'Ví dụ: Không Dây + Bluetooth + Tai Nghe = Tai Nghe Bluetooth Không Dây',
  kcExample2: 'Ví dụ: Chống Nước + Điện Thoại + Ốp = Ốp Điện Thoại Chống Nước',
  kcExample3: 'Ví dụ: Hữu Cơ + Cotton + Quần Áo Trẻ Em = Quần Áo Trẻ Em Cotton Hữu Cơ',

  // Validation Messages
  kcMinGroupsError: 'Cần ít nhất 2 nhóm từ khóa để tạo kết hợp',
  kcEmptyGroupError: 'Vui lòng nhập ít nhất một từ khóa trong mỗi nhóm',
  kcMaxCombinationsWarning: 'Phát hiện số lượng kết hợp lớn, hãy xem xét tinh chỉnh từ khóa để có kết quả chính xác hơn',

  // Statistics
  kcTotalCombinations: 'Tổng Số Kết Hợp',
  kcAverageLength: 'Độ Dài Trung Bình',
  kcShortestCombination: 'Kết Hợp Ngắn Nhất',
  kcLongestCombination: 'Kết Hợp Dài Nhất',

  // Export Options
  kcExportOptions: 'Tùy Chọn Xuất',
  kcExportTxt: 'Xuất dưới dạng TXT',
  kcExportCsv: 'Xuất dưới dạng CSV',
  kcExportJson: 'Xuất dưới dạng JSON',

  // Advanced Features
  kcAdvancedOptions: 'Tùy Chọn Nâng Cao',
  kcIncludeVariations: 'Bao Gồm Biến Thể',
  kcExcludeDuplicates: 'Loại Trừ Trùng Lặp',
  kcMaxCombinationLength: 'Độ Dài Kết Hợp Tối Đa',
  kcMinCombinationLength: 'Độ Dài Kết Hợp Tối Thiểu',

  // Performance Tips
  kcPerformanceTips: 'Mẹo Tối Ưu Hóa Hiệu Suất',
  kcPerformanceTip1: 'Khuyến nghị không quá 10 từ khóa mỗi nhóm để đảm bảo tốc độ tạo',
  kcPerformanceTip2: 'Khi tổng số kết hợp vượt quá 1000, hãy xem xét xử lý theo lô',
  kcPerformanceTip3: 'Sử dụng tính năng lọc để nhanh chóng tìm các kết hợp từ khóa mục tiêu',

  // Tool Info Sections
  kcToolIntroTitle: 'Giới Thiệu Công Cụ',
  kcToolIntroContent: 'Công Cụ Kết Hợp Từ Khóa là một công cụ tạo từ khóa thông minh được thiết kế đặc biệt cho người bán Amazon. Nó tự động tạo ra tất cả các kết hợp từ khóa có thể để tối ưu hóa chiến dịch quảng cáo và chiến lược từ khóa listing. Công cụ hỗ trợ kết hợp thông minh của 2-3 nhóm từ khóa, giúp bạn khám phá các từ khóa đuôi dài có giá trị cao để cải thiện thứ hạng tìm kiếm sản phẩm và hiệu suất quảng cáo.',

  kcToolFeaturesTitle: 'Tính Năng Cốt Lõi',
  kcFeatureSmartGeneration: 'Tạo Kết Hợp Thông Minh',
  kcFeatureSmartGenerationDesc: 'Nhập 2-3 nhóm từ khóa để tự động tạo tất cả các kết hợp có thể',
  kcFeatureMultiGroup: 'Hỗ Trợ Đa Nhóm',
  kcFeatureMultiGroupDesc: 'Hỗ trợ tối đa 3 nhóm từ khóa, mỗi nhóm chứa nhiều từ khóa',
  kcFeatureRealTimePreview: 'Xem Trước Thời Gian Thực',
  kcFeatureRealTimePreviewDesc: 'Hiển thị thời gian thực số lượng kết hợp được tạo và nội dung',
  kcFeatureFlexibleSort: 'Sắp Xếp Linh Hoạt',
  kcFeatureFlexibleSortDesc: 'Sắp xếp theo thứ tự gốc, độ dài hoặc số từ',

  kcUsageGuideTitle: 'Hướng Dẫn Sử Dụng',
  kcStep1Title: 'Bước 1: Nhập Nhóm Từ Khóa',
  kcStep1Content: 'Nhập các từ khóa liên quan vào hộp nhập nhóm từ khóa, phân cách bằng dấu phẩy hoặc xuống dòng. Khuyến nghị 3-8 từ khóa liên quan mỗi nhóm.',
  kcStep2Title: 'Bước 2: Thêm Nhóm Từ Khóa Khác',
  kcStep2Content: 'Nhấp vào nút "Thêm Nhóm" để thêm nhóm từ khóa thứ hai và thứ ba. Từ khóa trong các nhóm khác nhau nên đại diện cho các thuộc tính hoặc tính năng sản phẩm khác nhau.',
  kcStep3Title: 'Bước 3: Xem Kết Quả Được Tạo',
  kcStep3Content: 'Công cụ tự động tạo tất cả các kết hợp từ khóa có thể, hiển thị thống kê số từ và ký tự cho mỗi kết hợp.',
  kcStep4Title: 'Bước 4: Chọn và Sao Chép',
  kcStep4Content: 'Nhấp vào bất kỳ kết hợp nào để sao chép vào clipboard, hoặc sử dụng tính năng chọn nhiều để chọn hàng loạt nhiều kết hợp để sao chép.',
  kcStep5Title: 'Bước 5: Xuất và Áp Dụng',
  kcStep5Content: 'Sử dụng tính năng xuất để lưu tất cả kết hợp dưới dạng file TXT, sau đó áp dụng vào chiến dịch quảng cáo Amazon hoặc tối ưu hóa listing.',

  kcFaqTitle: 'Câu Hỏi Thường Gặp',
  kcFaq1Question: 'Mỗi nhóm nên chứa bao nhiêu từ khóa?',
  kcFaq1Answer: 'Chúng tôi khuyến nghị 3-8 từ khóa mỗi nhóm. Quá ít từ khóa dẫn đến kết hợp không đủ, trong khi quá nhiều tạo ra kết hợp quá mức khó lọc.',

  kcFaq2Question: 'Làm thế nào để chọn từ khóa cho các nhóm khác nhau?',
  kcFaq2Answer: 'Các nhóm khác nhau nên đại diện cho các thuộc tính sản phẩm khác nhau. Ví dụ: Nhóm 1 cho loại sản phẩm (tai nghe, loa), Nhóm 2 cho tính năng (không dây, bluetooth), Nhóm 3 cho chất lượng (cao cấp, chuyên nghiệp).',

  kcFaq3Question: 'Phải làm gì khi có quá nhiều kết hợp được tạo?',
  kcFaq3Answer: 'Sử dụng tính năng sắp xếp để sắp xếp theo độ dài hoặc số từ, ưu tiên các kết hợp có độ dài vừa phải. Bạn cũng có thể giảm từ khóa mỗi nhóm để kiểm soát tổng số kết hợp.',

  kcBestPracticesTitle: 'Thực Hành Tốt Nhất',
  kcBestPractice1: 'Sử dụng từ đồng nghĩa và biến thể: Bao gồm từ đồng nghĩa và dạng biến thể trong cùng một nhóm để tăng phạm vi bao phủ',
  kcBestPractice2: 'Kiểm soát độ dài kết hợp: Ưu tiên kết hợp 2-4 từ có lượng tìm kiếm nhưng không quá dài',
  kcBestPractice3: 'Thử nghiệm các kết hợp khác nhau: Sử dụng kết hợp được tạo cho thử nghiệm A/B để tìm từ khóa chuyển đổi cao nhất',
  kcBestPractice4: 'Cập nhật từ khóa thường xuyên: Thường xuyên cập nhật kết hợp từ khóa dựa trên xu hướng thị trường và cạnh tranh',
  kcBestPractice5: 'Kết hợp với dữ liệu tìm kiếm: Kết hợp các kết hợp được tạo với dữ liệu lượng tìm kiếm từ khóa, ưu tiên kết hợp có lượng cao'
};
