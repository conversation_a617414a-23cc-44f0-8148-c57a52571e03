// Te<PERSON><PERSON><PERSON>an Keyword Combiner - Bahasa Indonesia
export const keywordCombinerId = {
  // Keyword Combiner
  keywordCombiner: 'Penggabung Kata Kunci',
  keywordCombinerDesc: 'Secara cerdas menghasilkan kombinasi kata kunci untuk mengoptimalkan strategi iklan Amazon dan kata kunci listing.',
  keywordCombinerHeader: 'Masukkan 2-3 grup kata kunci yang berbeda untuk secara otomatis menghasilkan semua kombinasi yang mungkin untuk iklan dan optimasi listing',

  // Input Section
  kcInputSection: 'Input Kata Kunci',
  kcGroup1: 'Grup Kata Kunci 1',
  kcGroup2: 'Grup Kata Kunci 2',
  kcGroup3: 'Grup Kata Kunci 3',
  kcAddGroup: 'Tambah Grup',
  kcClearAll: 'Hapus Semua',
  kcKeywordPlaceholder: 'Masukkan kata kunci, dipisahkan dengan koma atau baris baru...',
  kcKeywordCount: 'Jumlah Kata Kunci',

  // Results Section
  kcResultsSection: 'Hasil Kombinasi',
  kcSortOriginal: 'Urutan <PERSON>',
  kcSortLength: 'Urutkan berdasarkan Panjang',
  kcSortWords: 'Urutkan berdasarkan Jumlah Kata',
  kcCopySelected: 'Salin yang Dipilih',
  kcDownload: 'Unduh Hasil',
  kcWords: 'kata',
  kcChars: 'karakter',

  // Actions
  kcCopy: 'Salin',
  kcCopied: 'Tersalin',
  kcSelectAll: 'Pilih Semua',
  kcDeselectAll: 'Batalkan Pilihan Semua',

  // Amazon Seller Tips
  kcAmazonTips: 'Tips Penjual Amazon',
  kcTip1Title: 'Strategi Iklan PPC',
  kcTip1Desc: 'Gunakan kombinasi kata kunci yang dihasilkan untuk iklan PPC, uji tingkat konversi kombinasi yang berbeda untuk mengoptimalkan ROI iklan',
  kcTip2Title: 'Optimasi Kata Kunci Listing',
  kcTip2Desc: 'Distribusikan kombinasi kata kunci secara strategis dalam judul produk, poin-poin, dan deskripsi untuk meningkatkan peringkat pencarian dan visibilitas',
  kcTip3Title: 'Penemuan Kata Kunci Long-tail',
  kcTip3Desc: 'Kombinasi kata kunci long-tail yang dihasilkan memiliki persaingan yang lebih rendah dan lebih mudah untuk mendapat peringkat, sempurna untuk peluncuran produk baru',
  kcTip4Title: 'Kontrol Kepadatan Kata Kunci',
  kcTip4Desc: 'Hindari penumpukan kata kunci, pertahankan kepadatan kata kunci yang wajar sambil menjaga konten tetap alami dan mudah dibaca',

  // Examples and Suggestions
  kcExampleTitle: 'Contoh Penggunaan',
  kcExample1: 'Contoh: Nirkabel + Bluetooth + Headphone = Headphone Bluetooth Nirkabel',
  kcExample2: 'Contoh: Tahan Air + Ponsel + Case = Case Ponsel Tahan Air',
  kcExample3: 'Contoh: Organik + Katun + Pakaian Bayi = Pakaian Bayi Katun Organik',

  // Validation Messages
  kcMinGroupsError: 'Setidaknya 2 grup kata kunci diperlukan untuk menghasilkan kombinasi',
  kcEmptyGroupError: 'Harap masukkan setidaknya satu kata kunci di setiap grup',
  kcMaxCombinationsWarning: 'Jumlah kombinasi yang besar terdeteksi, pertimbangkan untuk menyaring kata kunci untuk hasil yang lebih tepat',

  // Statistics
  kcTotalCombinations: 'Total Kombinasi',
  kcAverageLength: 'Panjang Rata-rata',
  kcShortestCombination: 'Kombinasi Terpendek',
  kcLongestCombination: 'Kombinasi Terpanjang',

  // Export Options
  kcExportOptions: 'Opsi Ekspor',
  kcExportTxt: 'Ekspor sebagai TXT',
  kcExportCsv: 'Ekspor sebagai CSV',
  kcExportJson: 'Ekspor sebagai JSON',

  // Advanced Features
  kcAdvancedOptions: 'Opsi Lanjutan',
  kcIncludeVariations: 'Sertakan Variasi',
  kcExcludeDuplicates: 'Kecualikan Duplikat',
  kcMaxCombinationLength: 'Panjang Kombinasi Maksimal',
  kcMinCombinationLength: 'Panjang Kombinasi Minimal',

  // Performance Tips
  kcPerformanceTips: 'Tips Optimasi Kinerja',
  kcPerformanceTip1: 'Disarankan tidak lebih dari 10 kata kunci per grup untuk memastikan kecepatan generasi',
  kcPerformanceTip2: 'Ketika total kombinasi melebihi 1000, pertimbangkan pemrosesan batch',
  kcPerformanceTip3: 'Gunakan fitur penyaringan untuk dengan cepat menemukan kombinasi kata kunci target',

  // Tool Info Sections
  kcToolIntroTitle: 'Pengenalan Alat',
  kcToolIntroContent: 'Penggabung Kata Kunci adalah alat generasi kata kunci cerdas yang dirancang khusus untuk penjual Amazon. Alat ini secara otomatis menghasilkan semua kombinasi kata kunci yang mungkin untuk mengoptimalkan kampanye iklan dan strategi kata kunci listing. Alat ini mendukung kombinasi cerdas dari 2-3 grup kata kunci, membantu Anda menemukan kata kunci ekor panjang bernilai tinggi untuk meningkatkan peringkat pencarian produk dan kinerja iklan.',

  kcToolFeaturesTitle: 'Fitur Inti',
  kcFeatureSmartGeneration: 'Generasi Kombinasi Cerdas',
  kcFeatureSmartGenerationDesc: 'Masukkan 2-3 grup kata kunci untuk secara otomatis menghasilkan semua kombinasi yang mungkin',
  kcFeatureMultiGroup: 'Dukungan Multi-Grup',
  kcFeatureMultiGroupDesc: 'Mendukung hingga 3 grup kata kunci, masing-masing berisi beberapa kata kunci',
  kcFeatureRealTimePreview: 'Pratinjau Real-Time',
  kcFeatureRealTimePreviewDesc: 'Tampilan real-time dari jumlah kombinasi yang dihasilkan dan konten',
  kcFeatureFlexibleSort: 'Pengurutan Fleksibel',
  kcFeatureFlexibleSortDesc: 'Urutkan berdasarkan urutan asli, panjang, atau jumlah kata',

  kcUsageGuideTitle: 'Panduan Penggunaan',
  kcStep1Title: 'Langkah 1: Masukkan Grup Kata Kunci',
  kcStep1Content: 'Masukkan kata kunci yang relevan di kotak input grup kata kunci, dipisahkan dengan koma atau baris baru. Disarankan 3-8 kata kunci terkait per grup.',
  kcStep2Title: 'Langkah 2: Tambahkan Grup Kata Kunci Lainnya',
  kcStep2Content: 'Klik tombol "Tambah Grup" untuk menambahkan grup kata kunci kedua dan ketiga. Kata kunci dalam grup yang berbeda harus mewakili atribut atau fitur produk yang berbeda.',
  kcStep3Title: 'Langkah 3: Lihat Hasil yang Dihasilkan',
  kcStep3Content: 'Alat secara otomatis menghasilkan semua kombinasi kata kunci yang mungkin, menampilkan statistik jumlah kata dan karakter untuk setiap kombinasi.',
  kcStep4Title: 'Langkah 4: Pilih dan Salin',
  kcStep4Content: 'Klik kombinasi apa pun untuk menyalinnya ke clipboard, atau gunakan fitur multi-pilih untuk memilih beberapa kombinasi secara batch untuk disalin.',
  kcStep5Title: 'Langkah 5: Ekspor dan Terapkan',
  kcStep5Content: 'Gunakan fitur ekspor untuk menyimpan semua kombinasi sebagai file TXT, kemudian terapkan ke kampanye iklan Amazon atau optimasi listing.',

  kcFaqTitle: 'Pertanyaan yang Sering Diajukan',
  kcFaq1Question: 'Berapa banyak kata kunci yang harus dikandung setiap grup?',
  kcFaq1Answer: 'Kami merekomendasikan 3-8 kata kunci per grup. Terlalu sedikit kata kunci menghasilkan kombinasi yang tidak mencukupi, sementara terlalu banyak menciptakan kombinasi berlebihan yang sulit difilter.',

  kcFaq2Question: 'Bagaimana memilih kata kunci untuk grup yang berbeda?',
  kcFaq2Answer: 'Grup yang berbeda harus mewakili atribut produk yang berbeda. Misalnya: Grup 1 untuk jenis produk (headphone, speaker), Grup 2 untuk fitur (nirkabel, bluetooth), Grup 3 untuk kualitas (premium, profesional).',

  kcFaq3Question: 'Apa yang harus dilakukan ketika terlalu banyak kombinasi dihasilkan?',
  kcFaq3Answer: 'Gunakan fitur pengurutan untuk mengurutkan berdasarkan panjang atau jumlah kata, prioritaskan kombinasi dengan panjang sedang. Anda juga dapat mengurangi kata kunci per grup untuk mengontrol jumlah kombinasi total.',

  kcBestPracticesTitle: 'Praktik Terbaik',
  kcBestPractice1: 'Gunakan sinonim dan variasi: Sertakan sinonim dan bentuk varian dalam grup yang sama untuk meningkatkan cakupan',
  kcBestPractice2: 'Kontrol panjang kombinasi: Prioritaskan kombinasi 2-4 kata yang memiliki volume pencarian tetapi tidak terlalu panjang',
  kcBestPractice3: 'Uji kombinasi yang berbeda: Gunakan kombinasi yang dihasilkan untuk pengujian A/B untuk menemukan kata kunci dengan konversi tertinggi',
  kcBestPractice4: 'Perbarui kata kunci secara teratur: Perbarui kombinasi kata kunci secara teratur berdasarkan tren pasar dan persaingan',
  kcBestPractice5: 'Gabungkan dengan data pencarian: Gabungkan kombinasi yang dihasilkan dengan data volume pencarian kata kunci, prioritaskan kombinasi volume tinggi'
};
