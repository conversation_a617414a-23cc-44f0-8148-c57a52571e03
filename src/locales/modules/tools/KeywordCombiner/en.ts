// Keyword Combiner Translation - English
export const keywordCombinerEn = {
  // Keyword Combiner
  keywordCombiner: 'Keyword Combiner',
  keywordCombinerDesc: 'Intelligently generate keyword combinations to optimize Amazon advertising and listing keyword strategies.',
  keywordCombinerHeader: 'Enter 2-3 different keyword groups to automatically generate all possible combinations for advertising and listing optimization',

  // Input Section
  kcInputSection: 'Keyword Input',
  kcGroup1: 'Keyword Group 1',
  kcGroup2: 'Keyword Group 2',
  kcGroup3: 'Keyword Group 3',
  kcAddGroup: 'Add Group',
  kcClearAll: 'Clear All',
  kcKeywordPlaceholder: 'Enter keywords, separated by commas or line breaks\nExample: wireless,bluetooth,premium\nOr:\nwireless\nbluetooth\npremium',
  kcKeywordCount: 'Keyword Count',

  // Results Section
  kcResultsSection: 'Combination Results',
  kcSortOriginal: 'Original Order',
  kcSortLength: 'Sort by Length',
  kcSortWords: 'Sort by Word Count',
  kcCopySelected: 'Copy Selected',
  kcDownload: 'Download Results',
  kcWords: 'words',
  kcChars: 'chars',

  // Actions
  kcCopy: 'Copy',
  kcCopied: 'Copied',
  kcSelectAll: 'Select All',
  kcDeselectAll: 'Deselect All',

  // Amazon Seller Tips
  kcAmazonTips: 'Amazon Seller Tips',
  kcTip1Title: 'PPC Advertising Strategy',
  kcTip1Desc: 'Use generated keyword combinations for PPC ads, test conversion rates of different combinations to optimize advertising ROI',
  kcTip2Title: 'Listing Keyword Optimization',
  kcTip2Desc: 'Distribute keyword combinations strategically in product titles, bullet points, and descriptions to improve search ranking and visibility',
  kcTip3Title: 'Long-tail Keyword Discovery',
  kcTip3Desc: 'Generated long-tail keyword combinations have lower competition and are easier to rank for, perfect for new product launches',
  kcTip4Title: 'Keyword Density Control',
  kcTip4Desc: 'Avoid keyword stuffing, maintain reasonable keyword density while keeping content natural and readable',

  // Examples and Suggestions
  kcExampleTitle: 'Usage Examples',
  kcExample1: 'Example: Wireless + Bluetooth + Headphones = Wireless Bluetooth Headphones',
  kcExample2: 'Example: Waterproof + Phone + Case = Waterproof Phone Case',
  kcExample3: 'Example: Organic + Cotton + Baby Clothes = Organic Cotton Baby Clothes',

  // Validation Messages
  kcMinGroupsError: 'At least 2 keyword groups are required to generate combinations',
  kcEmptyGroupError: 'Please enter at least one keyword in each group',
  kcMaxCombinationsWarning: 'Large number of combinations detected, consider refining keywords for more precise results',

  // Statistics
  kcTotalCombinations: 'Total Combinations',
  kcAverageLength: 'Average Length',
  kcShortestCombination: 'Shortest Combination',
  kcLongestCombination: 'Longest Combination',

  // Export Options
  kcExportOptions: 'Export Options',
  kcExportTxt: 'Export as TXT',
  kcExportCsv: 'Export as CSV',
  kcExportJson: 'Export as JSON',

  // Advanced Features
  kcAdvancedOptions: 'Advanced Options',
  kcIncludeVariations: 'Include Variations',
  kcExcludeDuplicates: 'Exclude Duplicates',
  kcMaxCombinationLength: 'Max Combination Length',
  kcMinCombinationLength: 'Min Combination Length',

  // Performance Tips
  kcPerformanceTips: 'Performance Optimization Tips',
  kcPerformanceTip1: 'Recommend no more than 10 keywords per group to ensure generation speed',
  kcPerformanceTip2: 'When total combinations exceed 1000, consider batch processing',
  kcPerformanceTip3: 'Use filtering features to quickly find target keyword combinations',

  // Tool Info Sections
  kcToolIntroTitle: 'Tool Introduction',
  kcToolIntroContent: 'The Keyword Combiner is an intelligent keyword generation tool designed specifically for Amazon sellers. It automatically generates all possible keyword combinations to optimize advertising campaigns and listing keyword strategies. The tool supports smart combinations of 2-3 keyword groups, helping you discover high-value long-tail keywords to improve product search rankings and advertising performance.',

  kcToolFeaturesTitle: 'Core Features',
  kcFeatureSmartGeneration: 'Smart Combination Generation',
  kcFeatureSmartGenerationDesc: 'Input 2-3 keyword groups to automatically generate all possible combinations',
  kcFeatureMultiGroup: 'Multi-Group Support',
  kcFeatureMultiGroupDesc: 'Support up to 3 keyword groups, each containing multiple keywords',
  kcFeatureRealTimePreview: 'Real-Time Preview',
  kcFeatureRealTimePreviewDesc: 'Real-time display of generated combination count and content',
  kcFeatureFlexibleSort: 'Flexible Sorting',
  kcFeatureFlexibleSortDesc: 'Sort by original order, length, or word count',

  kcUsageGuideTitle: 'Usage Guide',
  kcStep1Title: 'Step 1: Input Keyword Groups',
  kcStep1Content: 'Enter relevant keywords in the keyword group input boxes, separated by commas or line breaks. Recommend 3-8 related keywords per group.',
  kcStep2Title: 'Step 2: Add More Keyword Groups',
  kcStep2Content: 'Click the "Add Group" button to add second and third keyword groups. Keywords in different groups should represent different product attributes or features.',
  kcStep3Title: 'Step 3: View Generated Results',
  kcStep3Content: 'The tool automatically generates all possible keyword combinations, displaying word count and character count statistics for each combination.',
  kcStep4Title: 'Step 4: Select and Copy',
  kcStep4Content: 'Click any combination to copy it to clipboard, or use multi-select feature to batch select multiple combinations for copying.',
  kcStep5Title: 'Step 5: Export and Apply',
  kcStep5Content: 'Use the export feature to save all combinations as a TXT file, then apply them to Amazon advertising campaigns or listing optimization.',

  kcFaqTitle: 'Frequently Asked Questions',
  kcFaq1Question: 'How many keywords should each group contain?',
  kcFaq1Answer: 'We recommend 3-8 keywords per group. Too few keywords result in insufficient combinations, while too many create excessive combinations that are difficult to filter.',

  kcFaq2Question: 'How to choose keywords for different groups?',
  kcFaq2Answer: 'Different groups should represent different product attributes. For example: Group 1 for product type (headphones, speakers), Group 2 for features (wireless, bluetooth), Group 3 for quality (premium, professional).',

  kcFaq3Question: 'What to do when too many combinations are generated?',
  kcFaq3Answer: 'Use sorting features to sort by length or word count, prioritizing moderate-length combinations. You can also reduce keywords per group to control total combination count.',

  kcBestPracticesTitle: 'Best Practices',
  kcBestPractice1: 'Use synonyms and variations: Include synonyms and variant forms in the same group to increase coverage',
  kcBestPractice2: 'Control combination length: Prioritize 2-4 word combinations that have search volume but aren\'t too long',
  kcBestPractice3: 'Test different combinations: Use generated combinations for A/B testing to find highest converting keywords',
  kcBestPractice4: 'Update keywords regularly: Regularly update keyword combinations based on market trends and competition',
  kcBestPractice5: 'Combine with search data: Combine generated combinations with keyword search volume data, prioritizing high-volume combinations'
};
