// キーワードコンバイナー翻訳 - 日本語
export const keywordCombinerJa = {
  // Keyword Combiner
  keywordCombiner: 'キーワードコンバイナー',
  keywordCombinerDesc: 'キーワードの組み合わせを自動生成し、Amazon広告とリスティングのキーワード戦略を最適化します。',
  keywordCombinerHeader: '2-3つの異なるキーワードグループを入力して、広告投稿とリスティング最適化のためのすべての可能な組み合わせを自動生成',

  // Input Section
  kcInputSection: 'キーワード入力',
  kcGroup1: 'キーワードグループ 1',
  kcGroup2: 'キーワードグループ 2',
  kcGroup3: 'キーワードグループ 3',
  kcAddGroup: 'グループ追加',
  kcClearAll: 'すべてクリア',
  kcKeywordPlaceholder: 'キーワードを入力してください。カンマまたは改行で区切ってください...',
  kcKeywordCount: 'キーワード数',

  // Results Section
  kcResultsSection: '組み合わせ結果',
  kcSortOriginal: '元の順序',
  kcSortLength: '長さで並び替え',
  kcSortWords: '単語数で並び替え',
  kcCopySelected: '選択をコピー',
  kcDownload: '結果をダウンロード',
  kcWords: '語',
  kcChars: '文字',

  // Actions
  kcCopy: 'コピー',
  kcCopied: 'コピー済み',
  kcSelectAll: 'すべて選択',
  kcDeselectAll: 'すべて選択解除',

  // Amazon Seller Tips
  kcAmazonTips: 'Amazon販売者のヒント',
  kcTip1Title: 'PPC広告戦略',
  kcTip1Desc: '生成されたキーワード組み合わせをPPC広告に使用し、異なる組み合わせのコンバージョン率をテストして広告ROIを最適化',
  kcTip2Title: 'リスティングキーワード最適化',
  kcTip2Desc: '商品タイトル、箇条書き、説明文にキーワード組み合わせを戦略的に配置し、検索ランキングと可視性を向上',
  kcTip3Title: 'ロングテールキーワード発見',
  kcTip3Desc: '生成されたロングテールキーワード組み合わせは競争が少なく、ランキングしやすいため、新商品の立ち上げに最適',
  kcTip4Title: 'キーワード密度制御',
  kcTip4Desc: 'キーワードの詰め込みを避け、適切なキーワード密度を維持しながらコンテンツを自然で読みやすく保つ',

  // Examples and Suggestions
  kcExampleTitle: '使用例',
  kcExample1: '例：ワイヤレス + Bluetooth + ヘッドフォン = ワイヤレスBluetoothヘッドフォン',
  kcExample2: '例：防水 + 携帯電話 + ケース = 防水携帯電話ケース',
  kcExample3: '例：オーガニック + コットン + ベビー服 = オーガニックコットンベビー服',

  // Validation Messages
  kcMinGroupsError: '組み合わせを生成するには少なくとも2つのキーワードグループが必要です',
  kcEmptyGroupError: '各グループに少なくとも1つのキーワードを入力してください',
  kcMaxCombinationsWarning: '組み合わせ数が多いため、より精密な結果を得るためにキーワードを絞り込むことをお勧めします',

  // Statistics
  kcTotalCombinations: '総組み合わせ数',
  kcAverageLength: '平均長',
  kcShortestCombination: '最短組み合わせ',
  kcLongestCombination: '最長組み合わせ',

  // Export Options
  kcExportOptions: 'エクスポートオプション',
  kcExportTxt: 'TXTとしてエクスポート',
  kcExportCsv: 'CSVとしてエクスポート',
  kcExportJson: 'JSONとしてエクスポート',

  // Advanced Features
  kcAdvancedOptions: '高度なオプション',
  kcIncludeVariations: 'バリエーションを含む',
  kcExcludeDuplicates: '重複を除外',
  kcMaxCombinationLength: '最大組み合わせ長',
  kcMinCombinationLength: '最小組み合わせ長',

  // Performance Tips
  kcPerformanceTips: 'パフォーマンス最適化のヒント',
  kcPerformanceTip1: '生成速度を確保するため、グループあたり10個以下のキーワードを推奨',
  kcPerformanceTip2: '総組み合わせ数が1000を超える場合は、バッチ処理を検討',
  kcPerformanceTip3: 'フィルタリング機能を使用してターゲットキーワード組み合わせを素早く見つける',

  // Tool Info Sections
  kcToolIntroTitle: 'ツール紹介',
  kcToolIntroContent: 'キーワードコンバイナーは、Amazon販売者向けに特別に設計されたインテリジェントなキーワード生成ツールです。広告キャンペーンとリスティングキーワード戦略を最適化するために、すべての可能なキーワード組み合わせを自動生成します。このツールは2-3つのキーワードグループのスマートな組み合わせをサポートし、高価値なロングテールキーワードを発見して商品検索ランキングと広告パフォーマンスを向上させます。',

  kcToolFeaturesTitle: 'コア機能',
  kcFeatureSmartGeneration: 'スマート組み合わせ生成',
  kcFeatureSmartGenerationDesc: '2-3つのキーワードグループを入力して、すべての可能な組み合わせを自動生成',
  kcFeatureMultiGroup: 'マルチグループサポート',
  kcFeatureMultiGroupDesc: '最大3つのキーワードグループをサポート、各グループに複数のキーワードを含めることが可能',
  kcFeatureRealTimePreview: 'リアルタイムプレビュー',
  kcFeatureRealTimePreviewDesc: '生成された組み合わせ数とコンテンツをリアルタイムで表示',
  kcFeatureFlexibleSort: '柔軟なソート',
  kcFeatureFlexibleSortDesc: '元の順序、長さ、単語数でソート可能',

  kcUsageGuideTitle: '使用ガイド',
  kcStep1Title: 'ステップ1：キーワードグループの入力',
  kcStep1Content: 'キーワードグループ入力ボックスに関連キーワードを入力し、カンマまたは改行で区切ります。グループあたり3-8個の関連キーワードを推奨します。',
  kcStep2Title: 'ステップ2：追加キーワードグループの追加',
  kcStep2Content: '「グループ追加」ボタンをクリックして、2番目と3番目のキーワードグループを追加します。異なるグループのキーワードは、商品の異なる属性や特徴を表すべきです。',
  kcStep3Title: 'ステップ3：生成結果の確認',
  kcStep3Content: 'ツールは自動的にすべての可能なキーワード組み合わせを生成し、各組み合わせの単語数と文字数の統計情報を表示します。',
  kcStep4Title: 'ステップ4：選択とコピー',
  kcStep4Content: '任意の組み合わせをクリックしてクリップボードにコピーするか、マルチ選択機能を使用して複数の組み合わせを一括選択してコピーします。',
  kcStep5Title: 'ステップ5：エクスポートと適用',
  kcStep5Content: 'エクスポート機能を使用してすべての組み合わせをTXTファイルとして保存し、Amazon広告キャンペーンやリスティング最適化に適用します。',

  kcFaqTitle: 'よくある質問',
  kcFaq1Question: '各グループにはいくつのキーワードを含めるべきですか？',
  kcFaq1Answer: 'グループあたり3-8個のキーワードを推奨します。キーワードが少なすぎると組み合わせが不足し、多すぎるとフィルタリングが困難な過剰な組み合わせが生成されます。',

  kcFaq2Question: '異なるグループのキーワードをどのように選択しますか？',
  kcFaq2Answer: '異なるグループは商品の異なる属性を表すべきです。例：グループ1は商品タイプ（ヘッドフォン、スピーカー）、グループ2は特徴（ワイヤレス、Bluetooth）、グループ3は品質（プレミアム、プロフェッショナル）。',

  kcFaq3Question: '組み合わせが多すぎる場合はどうしますか？',
  kcFaq3Answer: 'ソート機能を使用して長さや単語数でソートし、適度な長さの組み合わせを優先します。また、グループあたりのキーワード数を減らして総組み合わせ数を制御することもできます。',

  kcBestPracticesTitle: 'ベストプラクティス',
  kcBestPractice1: '同義語とバリエーションの使用：同じグループに同義語とバリエーション形式を含めてカバレッジを増加',
  kcBestPractice2: '組み合わせ長の制御：検索ボリュームがあり、長すぎない2-4語の組み合わせを優先',
  kcBestPractice3: '異なる組み合わせのテスト：生成された組み合わせをA/Bテストに使用し、最高コンバージョンのキーワードを見つける',
  kcBestPractice4: 'キーワードの定期更新：市場トレンドと競争状況に基づいてキーワード組み合わせを定期的に更新',
  kcBestPractice5: '検索データとの組み合わせ：生成された組み合わせをキーワード検索ボリュームデータと組み合わせ、高ボリュームの組み合わせを優先'
};
