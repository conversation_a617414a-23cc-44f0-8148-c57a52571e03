// 世界时钟工具 - 简体中文翻译
export const worldClockZh = {
  // 工具基本信息
  worldClock: '世界时钟',
  worldClockDesc: '查看全球主要城市的实时时间，支持夏令时自动调整',
  worldClockShortDesc: '全球时区实时查看',

  // 时间显示
  currentTime: '当前时间',
  currentLocation: '当前位置',
  currentCity: '本地时间',
  currentCountry: '当前时区',

  // 美国时区
  usEastern: '美国东部时间',
  usCentral: '美国中部时间',
  usMountain: '美国山地时间',
  usPacific: '美国太平洋时间',

  // 欧洲时区
  europeLondon: '英国伦敦时间',
  europeParis: '法国巴黎时间',
  europeBerlin: '德国柏林时间',

  // 亚洲时区
  asiaTokyo: '日本东京时间',
  asiaShanghai: '中国上海时间',
  asiaSingapore: '新加坡时间',
  asiaSeoul: '韩国首尔时间',

  // 其他时区
  australiaSydney: '澳大利亚悉尼时间',

  // 夏令时相关
  dstActive: '夏令时',
  dstExplanation: '夏令时说明',
  dstExplanationText: '夏令时期间，时钟会向前调整1小时，以充分利用日光。通常在春季开始，秋季结束。',

  // 商务时间提示
  businessHours: '商务时间提示',
  businessHoursText: '进行国际商务沟通时，请注意对方的当地时间，避免在非工作时间打扰。一般工作时间为9:00-17:00。',

  // 时区小贴士
  timeZoneTips: '时区小贴士',
  
  // 状态指示
  daytime: '白天',
  nighttime: '夜晚',
  
  // 操作按钮
  refresh: '刷新时间',
  autoUpdate: '自动更新',
  
  // 时间格式
  timeFormat24: '24小时制',
  timeFormat12: '12小时制',
  
  // 错误信息
  timezoneError: '时区数据加载失败',
  refreshError: '刷新失败，请重试',
  
  // 帮助信息
  helpTitle: '使用说明',
  helpContent: '本工具显示全球主要城市的实时时间，自动处理夏令时调整。时间每秒自动更新，确保准确性。',
  
  // 时区缩写说明
  timezoneAbbr: {
    EST: '美国东部标准时间',
    EDT: '美国东部夏令时间',
    CST: '美国中部标准时间',
    CDT: '美国中部夏令时间',
    MST: '美国山地标准时间',
    MDT: '美国山地夏令时间',
    PST: '美国太平洋标准时间',
    PDT: '美国太平洋夏令时间',
    GMT: '格林威治标准时间',
    BST: '英国夏令时间',
    CET: '中欧时间',
    CEST: '中欧夏令时间',
    JST: '日本标准时间',
    CST_CHINA: '中国标准时间',
    SGT: '新加坡时间',
    KST: '韩国标准时间',
    AEDT: '澳大利亚东部夏令时间',
    AEST: '澳大利亚东部标准时间'
  }
};
