// 世界時鐘工具 - 繁體中文翻譯
export const worldClockZhTW = {
  // 工具基本信息
  worldClock: '世界時鐘',
  worldClockDesc: '查看全球主要城市的即時時間，支援夏令時自動調整',
  worldClockShortDesc: '全球時區即時查看',

  // 時間顯示
  currentTime: '目前時間',
  currentLocation: '目前位置',
  currentCity: '本地時間',
  currentCountry: '目前時區',

  // 美國時區
  usEastern: '美國東部時間',
  usCentral: '美國中部時間',
  usMountain: '美國山地時間',
  usPacific: '美國太平洋時間',

  // 歐洲時區
  europeLondon: '英國倫敦時間',
  europeParis: '法國巴黎時間',
  europeBerlin: '德國柏林時間',

  // 亞洲時區
  asiaTokyo: '日本東京時間',
  asiaShanghai: '中國上海時間',
  asiaSingapore: '新加坡時間',
  asiaSeoul: '韓國首爾時間',

  // 其他時區
  australiaSydney: '澳洲雪梨時間',

  // 夏令時相關
  dstActive: '夏令時',
  dstExplanation: '夏令時說明',
  dstExplanationText: '夏令時期間，時鐘會向前調整1小時，以充分利用日光。通常在春季開始，秋季結束。',

  // 商務時間提示
  businessHours: '商務時間提示',
  businessHoursText: '進行國際商務溝通時，請注意對方的當地時間，避免在非工作時間打擾。一般工作時間為9:00-17:00。',

  // 時區小貼士
  timeZoneTips: '時區小貼士',
  
  // 狀態指示
  daytime: '白天',
  nighttime: '夜晚',
  
  // 操作按鈕
  refresh: '重新整理時間',
  autoUpdate: '自動更新',
  
  // 時間格式
  timeFormat24: '24小時制',
  timeFormat12: '12小時制',
  
  // 錯誤信息
  timezoneError: '時區資料載入失敗',
  refreshError: '重新整理失敗，請重試',
  
  // 幫助信息
  helpTitle: '使用說明',
  helpContent: '本工具顯示全球主要城市的即時時間，自動處理夏令時調整。時間每秒自動更新，確保準確性。',
  
  // 時區縮寫說明
  timezoneAbbr: {
    EST: '美國東部標準時間',
    EDT: '美國東部夏令時間',
    CST: '美國中部標準時間',
    CDT: '美國中部夏令時間',
    MST: '美國山地標準時間',
    MDT: '美國山地夏令時間',
    PST: '美國太平洋標準時間',
    PDT: '美國太平洋夏令時間',
    GMT: '格林威治標準時間',
    BST: '英國夏令時間',
    CET: '中歐時間',
    CEST: '中歐夏令時間',
    JST: '日本標準時間',
    CST_CHINA: '中國標準時間',
    SGT: '新加坡時間',
    KST: '韓國標準時間',
    AEDT: '澳洲東部夏令時間',
    AEST: '澳洲東部標準時間'
  }
};
