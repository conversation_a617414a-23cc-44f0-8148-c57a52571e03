// Alat Jam Dunia - Terjemahan Bahasa Indonesia
export const worldClockId = {
  // Informasi Dasar Alat
  worldClock: 'Jam Dunia',
  worldClockDesc: 'Lihat waktu real-time di kota-kota besar dunia dengan penyesuaian waktu musim panas otomatis',
  worldClockShortDesc: 'Tampilan real-time zona waktu global',

  // Tampilan Waktu
  currentTime: 'Waktu Saat Ini',
  currentLocation: 'Lokasi Saat Ini',
  currentCity: 'Waktu Lokal',
  currentCountry: 'Zona Waktu Saat Ini',

  // Zona Waktu AS
  usEastern: 'Waktu Timur AS',
  usCentral: 'Waktu Tengah AS',
  usMountain: 'Waktu Pegunungan AS',
  usPacific: 'Waktu Pasifik AS',

  // Zona Waktu Eropa
  europeLondon: 'Waktu London, Inggris',
  europeParis: 'Waktu Paris, Prancis',
  europeBerlin: 'Waktu Berlin, Jerman',

  // Zona Waktu Asia
  asiaTokyo: 'Waktu Tokyo, Jepang',
  asiaShanghai: 'Waktu Shanghai, China',
  asiaSingapore: 'Waktu Singapura',
  asiaSeoul: 'Waktu Seoul, Korea Selatan',

  // Zona Waktu Lainnya
  australiaSydney: 'Waktu Sydney, Australia',

  // Terkait Waktu Musim Panas
  dstActive: 'DST',
  dstExplanation: 'Waktu Musim Panas',
  dstExplanationText: 'Selama waktu musim panas, jam dimajukan 1 jam untuk memanfaatkan siang hari dengan lebih baik. Biasanya dimulai di musim semi dan berakhir di musim gugur.',

  // Tips Jam Kerja
  businessHours: 'Tips Jam Kerja',
  businessHoursText: 'Saat melakukan komunikasi bisnis internasional, harap perhatikan waktu lokal untuk menghindari gangguan di luar jam kerja. Jam kerja umum adalah 9:00-17:00.',

  // Tips Zona Waktu
  timeZoneTips: 'Tips Zona Waktu',
  
  // Indikator Status
  daytime: 'Siang Hari',
  nighttime: 'Malam Hari',
  
  // Tombol Aksi
  refresh: 'Segarkan Waktu',
  autoUpdate: 'Pembaruan Otomatis',
  
  // Format Waktu
  timeFormat24: 'Format 24 jam',
  timeFormat12: 'Format 12 jam',
  
  // Pesan Error
  timezoneError: 'Gagal memuat data zona waktu',
  refreshError: 'Penyegaran gagal, silakan coba lagi',
  
  // Informasi Bantuan
  helpTitle: 'Petunjuk Penggunaan',
  helpContent: 'Alat ini menampilkan waktu real-time di kota-kota besar dunia, secara otomatis menangani penyesuaian waktu musim panas. Waktu diperbarui setiap detik untuk akurasi.',
  
  // Penjelasan Singkatan Zona Waktu
  timezoneAbbr: {
    EST: 'Waktu Standar Timur',
    EDT: 'Waktu Musim Panas Timur',
    CST: 'Waktu Standar Tengah',
    CDT: 'Waktu Musim Panas Tengah',
    MST: 'Waktu Standar Pegunungan',
    MDT: 'Waktu Musim Panas Pegunungan',
    PST: 'Waktu Standar Pasifik',
    PDT: 'Waktu Musim Panas Pasifik',
    GMT: 'Waktu Greenwich',
    BST: 'Waktu Musim Panas Inggris',
    CET: 'Waktu Eropa Tengah',
    CEST: 'Waktu Musim Panas Eropa Tengah',
    JST: 'Waktu Standar Jepang',
    CST_CHINA: 'Waktu Standar China',
    SGT: 'Waktu Singapura',
    KST: 'Waktu Standar Korea',
    AEDT: 'Waktu Musim Panas Timur Australia',
    AEST: 'Waktu Standar Timur Australia'
  }
};
