// World Clock Tool - English Translation
export const worldClockEn = {
  // Tool Basic Information
  worldClock: 'World Clock',
  worldClockDesc: 'View real-time in major cities worldwide with automatic daylight saving time adjustment',
  worldClockShortDesc: 'Global timezone real-time view',

  // Time Display
  currentTime: 'Current Time',
  currentLocation: 'Current Location',
  currentCity: 'Local Time',
  currentCountry: 'Current Timezone',

  // US Timezones
  usEastern: 'US Eastern Time',
  usCentral: 'US Central Time',
  usMountain: 'US Mountain Time',
  usPacific: 'US Pacific Time',

  // European Timezones
  europeLondon: 'London, UK Time',
  europeParis: 'Paris, France Time',
  europeBerlin: 'Berlin, Germany Time',

  // Asian Timezones
  asiaTokyo: 'Tokyo, Japan Time',
  asiaShanghai: 'Shanghai, China Time',
  asiaSingapore: 'Singapore Time',
  asiaSeoul: 'Seoul, South Korea Time',

  // Other Timezones
  australiaSydney: 'Sydney, Australia Time',

  // Daylight Saving Time Related
  dstActive: 'DST',
  dstExplanation: 'Daylight Saving Time',
  dstExplanationText: 'During daylight saving time, clocks are moved forward by 1 hour to make better use of daylight. It typically starts in spring and ends in autumn.',

  // Business Hours Tips
  businessHours: 'Business Hours Tips',
  businessHoursText: 'When conducting international business communications, please be mindful of the local time to avoid disturbing during non-working hours. General business hours are 9:00-17:00.',

  // Timezone Tips
  timeZoneTips: 'Timezone Tips',
  
  // Status Indicators
  daytime: 'Daytime',
  nighttime: 'Nighttime',
  
  // Action Buttons
  refresh: 'Refresh Time',
  autoUpdate: 'Auto Update',
  
  // Time Format
  timeFormat24: '24-hour format',
  timeFormat12: '12-hour format',
  
  // Error Messages
  timezoneError: 'Failed to load timezone data',
  refreshError: 'Refresh failed, please try again',
  
  // Help Information
  helpTitle: 'Usage Instructions',
  helpContent: 'This tool displays real-time in major cities worldwide, automatically handling daylight saving time adjustments. Time updates every second for accuracy.',
  
  // Timezone Abbreviation Explanations
  timezoneAbbr: {
    EST: 'Eastern Standard Time',
    EDT: 'Eastern Daylight Time',
    CST: 'Central Standard Time',
    CDT: 'Central Daylight Time',
    MST: 'Mountain Standard Time',
    MDT: 'Mountain Daylight Time',
    PST: 'Pacific Standard Time',
    PDT: 'Pacific Daylight Time',
    GMT: 'Greenwich Mean Time',
    BST: 'British Summer Time',
    CET: 'Central European Time',
    CEST: 'Central European Summer Time',
    JST: 'Japan Standard Time',
    CST_CHINA: 'China Standard Time',
    SGT: 'Singapore Time',
    KST: 'Korea Standard Time',
    AEDT: 'Australian Eastern Daylight Time',
    AEST: 'Australian Eastern Standard Time'
  }
};
