// 世界時計ツール - 日本語翻訳
export const worldClockJa = {
  // ツール基本情報
  worldClock: '世界時計',
  worldClockDesc: '世界主要都市のリアルタイム時刻を表示、夏時間自動調整対応',
  worldClockShortDesc: 'グローバルタイムゾーンリアルタイム表示',

  // 時刻表示
  currentTime: '現在時刻',
  currentLocation: '現在地',
  currentCity: 'ローカル時間',
  currentCountry: '現在のタイムゾーン',

  // アメリカのタイムゾーン
  usEastern: 'アメリカ東部時間',
  usCentral: 'アメリカ中部時間',
  usMountain: 'アメリカ山地時間',
  usPacific: 'アメリカ太平洋時間',

  // ヨーロッパのタイムゾーン
  europeLondon: 'イギリス・ロンドン時間',
  europeParis: 'フランス・パリ時間',
  europeBerlin: 'ドイツ・ベルリン時間',

  // アジアのタイムゾーン
  asiaTokyo: '日本・東京時間',
  asiaShanghai: '中国・上海時間',
  asiaSingapore: 'シンガポール時間',
  asiaSeoul: '韓国・ソウル時間',

  // その他のタイムゾーン
  australiaSydney: 'オーストラリア・シドニー時間',

  // 夏時間関連
  dstActive: '夏時間',
  dstExplanation: '夏時間について',
  dstExplanationText: '夏時間期間中は、日光をより有効活用するため時計を1時間進めます。通常春に開始し、秋に終了します。',

  // ビジネス時間のヒント
  businessHours: 'ビジネス時間のヒント',
  businessHoursText: '国際的なビジネス連絡を行う際は、相手の現地時間を考慮し、営業時間外の連絡は避けましょう。一般的な営業時間は9:00-17:00です。',

  // タイムゾーンのヒント
  timeZoneTips: 'タイムゾーンのヒント',
  
  // ステータス表示
  daytime: '昼間',
  nighttime: '夜間',
  
  // 操作ボタン
  refresh: '時刻更新',
  autoUpdate: '自動更新',
  
  // 時刻フォーマット
  timeFormat24: '24時間表示',
  timeFormat12: '12時間表示',
  
  // エラーメッセージ
  timezoneError: 'タイムゾーンデータの読み込みに失敗しました',
  refreshError: '更新に失敗しました。再試行してください',
  
  // ヘルプ情報
  helpTitle: '使用方法',
  helpContent: 'このツールは世界主要都市のリアルタイム時刻を表示し、夏時間調整を自動で処理します。時刻は毎秒自動更新され、正確性を保ちます。',
  
  // タイムゾーン略語説明
  timezoneAbbr: {
    EST: '東部標準時',
    EDT: '東部夏時間',
    CST: '中部標準時',
    CDT: '中部夏時間',
    MST: '山地標準時',
    MDT: '山地夏時間',
    PST: '太平洋標準時',
    PDT: '太平洋夏時間',
    GMT: 'グリニッジ標準時',
    BST: '英国夏時間',
    CET: '中央ヨーロッパ時間',
    CEST: '中央ヨーロッパ夏時間',
    JST: '日本標準時',
    CST_CHINA: '中国標準時',
    SGT: 'シンガポール時間',
    KST: '韓国標準時',
    AEDT: 'オーストラリア東部夏時間',
    AEST: 'オーストラリア東部標準時'
  }
};
