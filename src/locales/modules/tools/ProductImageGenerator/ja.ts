// 商品画像生成器 - 日本語翻訳
export const productImageGeneratorJa = {
  // ツール基本情報
  toolName: 'AI商品画像生成器',
  toolDescription: '先進的なAI技術を使用して、Amazonの商品リスティング用のプロフェッショナルな商品画像を生成',
  shortDescription: 'スマートなAmazon商品画像生成',

  // 主要機能エリア
  uploadSection: '画像アップロード',
  configSection: 'パラメータ設定',
  previewSection: 'プレビュー結果',
  
  // アップロード関連
  uploadPrompt: 'クリックして商品画像をアップロード',
  uploadHint: 'JPG、PNG形式をサポート、最大10MB',
  changeImage: '画像を変更',
  uploadedImage: 'アップロードされた商品画像',
  dragDropHint: 'ここに画像をドラッグするかクリックしてアップロード',
  
  // 画像説明
  promptLabel: '画像説明',
  promptRequired: '画像説明 *',
  promptPlaceholder: '生成したい商品画像を説明してください。例：このBluetoothヘッドセットをモダンなデスクに置き、背景はシンプルな白色...',
  promptEmpty: '画像説明を入力してください',

  // Amazon画像タイプ
  amazonImageTypeLabel: 'Amazon画像タイプ',
  amazonImageTypeDescription: '作成したいAmazon画像のタイプを選択してください。システムが最適な仕様とスタイルを自動調整します',

  // Amazon画像タイプオプション
  amazonMain: 'メイン画像 - 白背景商品画像 (1:1)',
  amazonLifestyle: 'ライフスタイル画像 - 使用シーン展示 (1:1)',
  amazonDetail: '詳細画像 - 商品特写機能 (1:1)',
  amazonComparison: '比較画像 - サイズ色比較 (1:1)',
  amazonInfographic: 'インフォグラフィック画像 - 図文セールスポイント展示 (1:1)',
  amazonAplus: 'A+コンテンツ画像 - ブランドストーリー (16:9)',
  amazonBrandStore: 'ブランドストア画像 - ストア装飾 (16:9)',

  // 現在の設定
  currentConfig: '現在の設定',
  aspectRatio: '比率',
  studioStyle: 'スタジオスタイル',
  lifestyleStyle: 'ライフスタイルスタイル',
  productStyle: '商品展示スタイル',
  sceneStyle: 'シーンスタイル',
  amazonSpec: 'Amazon仕様',

  // Amazon仕様
  amazonSpecLabel: 'Amazon画像仕様',
  specMain: 'メイン画像 - 白背景、商品85%カバレッジ',
  specAdditional: '追加画像 - 商品の特徴と使用シーンを表示',
  specAplus: 'A+コンテンツ画像 - ブランドストーリーと商品ショーケース',
  specBrand: 'ブランドストア画像 - バナーとプロモーション画像',
  
  // 画像スタイル
  styleLabel: '画像スタイル',
  styleStudio: 'スタジオ',
  styleLifestyle: 'ライフスタイル',
  styleProduct: '商品',
  styleScene: 'シーン',
  
  // アスペクト比
  aspectRatioLabel: 'アスペクト比',
  ratio1to1: '1:1 (正方形)',
  ratio3to4: '3:4 (縦型)',
  ratio4to3: '4:3 (横型)',
  ratio9to16: '9:16 (モバイル縦型)',
  ratio16to9: '16:9 (ワイドスクリーン)',
  
  // アクションボタン
  generateButton: '画像生成',
  generating: '生成中...',
  clearButton: 'クリア',
  downloadButton: '画像ダウンロード',
  retryButton: '再生成',
  
  // プレビューエリア
  previewTitle: 'プレビュー結果',
  previewEmpty: '生成された画像がここに表示されます',
  generatedImage: '生成された商品画像',
  
  // ステータスメッセージ
  generateSuccess: '画像生成成功！',
  downloadSuccess: '画像ダウンロード成功！',
  generateError: '画像生成に失敗しました。再試行してください',
  downloadError: '画像ダウンロードに失敗しました。再試行してください',
  uploadError: 'ファイルアップロードに失敗しました。再試行してください',
  fileSizeError: '画像ファイルサイズは10MBを超えることはできません',
  fileTypeError: '有効な画像ファイルをアップロードしてください',
  
  // 使用制限
  usageLimit: '使用制限',
  dailyLimit: '1日の制限',
  remainingUses: '残り使用回数',
  usageExceeded: '本日の使用制限に達しました',
  usageLimited: '使用が制限されています。後でもう一度お試しください',
  freeUserLimit: '無料ユーザーは1日5回の生成に制限されています',
  upgradePrompt: 'より多くの使用回数を得るためにProにアップグレード',
  
  // 使用説明
  instructionsTitle: '使用説明：',
  instruction1: '• 商品画像をアップロード（オプション）するか、テキスト説明から直接生成',
  instruction2: '• 背景、シーン、スタイルなど、希望する画像効果を詳しく説明',
  instruction3: '• 適切なAmazon画像仕様とアスペクト比を選択',
  instruction4: '• 生成ボタンをクリックすると、AIがプロフェッショナルな商品画像を作成',
  instruction5: '• 無料ユーザーは1日5回の生成に制限されています。賢く使用してください',
  
  // 高度な機能（予約済み）
  advancedOptions: '高度なオプション',
  backgroundRemoval: '背景除去',
  qualityEnhancement: '品質向上',
  batchProcessing: 'バッチ処理',
  customTemplates: 'カスタムテンプレート',
  
  // エラー処理
  networkError: 'ネットワーク接続に失敗しました。接続を確認して再試行してください',
  serverError: 'サーバーエラーです。後で再試行してください',
  timeoutError: 'AIリクエストがタイムアウトしました。後で再試行してください',
  quotaExceeded: '使用クォータが枯渇しました。明日再試行してください',
  
  // ヒントとヘルプ
  tips: 'ヒント',
  tip1: '詳細な説明により、AIはより正確な画像を生成できます',
  tip2: 'メイン画像はAmazonの要件に準拠するため白背景を使用することをお勧めします',
  tip3: 'ライフスタイル画像は購入者の注意を引きやすいです',
  tip4: '複数回生成して、最も満足のいく結果を選択できます',
  
  // 品質設定（予約済み）
  qualitySettings: '品質設定',
  qualityStandard: '標準品質',
  qualityHigh: '高品質',
  qualityUltra: '超高品質',
  
  // エクスポートオプション
  exportOptions: 'エクスポートオプション',
  exportPNG: 'PNGとしてエクスポート',
  exportJPG: 'JPGとしてエクスポート',
  exportWebP: 'WebPとしてエクスポート',
  
  // 履歴（予約済み）
  history: '履歴',
  recentGenerations: '最近の生成',
  favorites: 'お気に入り',
  
  // 共有（予約済み）
  share: '共有',
  copyLink: 'リンクをコピー',
  shareToSocial: 'ソーシャルメディアに共有',

  // AIサービス説明
  aiServiceTitle: 'AI画像生成サービス',
  aiServiceDescription: '先進的なAI技術に基づき、プロフェッショナルな商品画像生成サービスを提供：',
  feature1: 'テキスト説明による商品画像生成をサポート',
  feature2: '既存の画像に基づく編集と最適化',
  feature3: 'Amazon 2025年最新画像仕様要件に準拠',
  feature4: '複数のスタイル選択：スタジオ、ライフスタイル、シーンなど',
  feature5: 'スマート背景置換と商品ハイライト表示'
};
