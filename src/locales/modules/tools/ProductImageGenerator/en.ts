// Product Image Generator - English Translation
export const productImageGeneratorEn = {
  // Tool basic information
  toolName: 'AI Product Image Generator',
  toolDescription: 'Generate professional product images for your Amazon listings using advanced AI technology',
  shortDescription: 'Smart Amazon product image generation',

  // Main functional areas
  uploadSection: 'Image Upload',
  configSection: 'Configuration',
  previewSection: 'Preview Results',
  
  // Upload related
  uploadPrompt: 'Click to upload product image',
  uploadHint: 'Supports JPG, PNG formats, max 10MB',
  changeImage: 'Change Image',
  uploadedImage: 'Uploaded product image',
  dragDropHint: 'Drag image here or click to upload',
  
  // Image description
  promptLabel: 'Image Description',
  promptRequired: 'Image Description *',
  promptPlaceholder: 'Describe the product image you want to generate, e.g.: Place this Bluetooth headset on a modern desk with a clean white background...',
  promptEmpty: 'Please enter image description',
  
  // Amazon image types
  amazonImageTypeLabel: 'Amazon Image Type',
  amazonImageTypeDescription: 'Select the type of Amazon image you want to create, system will auto-match optimal specifications and style',

  // Amazon image type options
  amazonMain: 'Main Image - White background product (1:1)',
  amazonLifestyle: 'Lifestyle Image - Usage scenarios (1:1)',
  amazonDetail: 'Detail Image - Product features (1:1)',
  amazonComparison: 'Comparison Image - Size/color comparison (1:1)',
  amazonInfographic: 'Infographic Image - Feature highlights (1:1)',
  amazonAplus: 'A+ Content Image - Brand story (16:9)',
  amazonBrandStore: 'Brand Store Image - Store decoration (16:9)',

  // Current configuration
  currentConfig: 'Current Config',
  aspectRatio: 'Aspect Ratio',
  studioStyle: 'Studio Style',
  lifestyleStyle: 'Lifestyle Style',
  productStyle: 'Product Style',
  sceneStyle: 'Scene Style',
  amazonSpec: 'Amazon Spec',

  // Image style (simplified)
  styleLabel: 'Image Style',
  styleStudio: 'Studio',
  styleLifestyle: 'Lifestyle',
  styleProduct: 'Product',
  styleScene: 'Scene',
  
  // Action buttons
  generateButton: 'Generate Image',
  generating: 'Generating...',
  clearButton: 'Clear',
  downloadButton: 'Download Image',
  retryButton: 'Regenerate',
  
  // Preview area
  previewTitle: 'Preview Results',
  previewEmpty: 'Generated images will appear here',
  generatedImage: 'Generated product image',
  
  // Status messages
  generateSuccess: 'Image generated successfully!',
  downloadSuccess: 'Image downloaded successfully!',
  generateError: 'Image generation failed, please try again',
  downloadError: 'Image download failed, please try again',
  uploadError: 'File upload failed, please try again',
  fileSizeError: 'Image file size cannot exceed 10MB',
  fileTypeError: 'Please upload a valid image file',
  
  // Usage limits
  usageLimit: 'Usage Limits',
  dailyLimit: 'Daily Limit',
  remainingUses: 'Remaining Uses',
  usageExceeded: 'Daily usage limit reached',
  usageLimited: 'Usage limited, please try again later',
  freeUserLimit: 'Free users limited to 5 generations per day (resets at Beijing midnight)',
  upgradePrompt: 'Upgrade to Pro for more usage',
  suspiciousActivity: 'Suspicious activity detected, please try again later',

  // Turnstile verification
  securityVerification: 'Security Verification',
  verificationDescription: 'To prevent abuse, we need to verify that you are a real user',
  verificationFailed: 'Verification failed, please try again',
  verificationError: 'Verification error, please try again',
  
  // Instructions
  instructionsTitle: 'Instructions:',
  instruction1: '• Upload your product image (optional) or generate directly from text description',
  instruction2: '• Describe in detail the image effect you want, including background, scene, style, etc.',
  instruction3: '• Select appropriate Amazon image specifications and aspect ratio',
  instruction4: '• Click generate button, AI will create professional product images for you',
  instruction5: '• Free users limited to 5 generations per day, please use wisely',
  
  // Advanced features (reserved)
  advancedOptions: 'Advanced Options',
  backgroundRemoval: 'Background Removal',
  qualityEnhancement: 'Quality Enhancement',
  batchProcessing: 'Batch Processing',
  customTemplates: 'Custom Templates',
  
  // Error handling
  networkError: 'Network connection failed, please check your connection and try again',
  serverError: 'Server error, please try again later',
  timeoutError: 'AI request timeout, please try again later',
  quotaExceeded: 'Usage quota exhausted, please try again tomorrow',
  
  // Tips and help
  tips: 'Tips',
  tip1: 'Detailed descriptions help AI generate more accurate images',
  tip2: 'Main images should use white background to comply with Amazon requirements',
  tip3: 'Lifestyle images are more likely to attract buyer attention',
  tip4: 'You can generate multiple times and choose the most satisfactory result',
  
  // Quality settings (reserved)
  qualitySettings: 'Quality Settings',
  qualityStandard: 'Standard Quality',
  qualityHigh: 'High Quality',
  qualityUltra: 'Ultra Quality',
  
  // Export options
  exportOptions: 'Export Options',
  exportPNG: 'Export as PNG',
  exportJPG: 'Export as JPG',
  exportWebP: 'Export as WebP',
  
  // History (reserved)
  history: 'History',
  recentGenerations: 'Recent Generations',
  favorites: 'Favorites',
  
  // Sharing (reserved)
  share: 'Share',
  copyLink: 'Copy Link',
  shareToSocial: 'Share to Social Media',

  // AI Service Description
  aiServiceTitle: 'AI Image Generation Service',
  aiServiceDescription: 'Based on advanced AI technology, providing professional product image generation services:',
  feature1: 'Support text description to generate product images',
  feature2: 'Edit and optimize based on existing images',
  feature3: 'Comply with Amazon 2025 latest image specification requirements',
  feature4: 'Multiple style options: studio, lifestyle, scene, etc.',
  feature5: 'Smart background replacement and product highlighting'
};
