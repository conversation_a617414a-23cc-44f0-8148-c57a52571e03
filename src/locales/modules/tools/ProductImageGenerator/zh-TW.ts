// 產品圖片生成器 - 繁體中文翻譯
export const productImageGeneratorZhTW = {
  // 工具基本資訊
  toolName: 'AI產品圖片生成器',
  toolDescription: '基於先進AI技術，為您的亞馬遜產品生成專業的商品圖片',
  shortDescription: '智能生成亞馬遜產品圖片',

  // 主要功能區域
  uploadSection: '圖片上傳',
  configSection: '參數配置',
  previewSection: '預覽結果',
  
  // 上傳相關
  uploadPrompt: '點擊上傳產品圖片',
  uploadHint: '支援 JPG、PNG 格式，最大 10MB',
  changeImage: '更換圖片',
  uploadedImage: '上傳的產品圖片',
  dragDropHint: '拖拽圖片到此處或點擊上傳',
  
  // 圖片描述
  promptLabel: '圖片描述',
  promptRequired: '圖片描述 *',
  promptPlaceholder: '請描述您想要生成的產品圖片，例如：將這個藍牙耳機放在現代辦公桌上，背景是簡潔的白色...',
  promptEmpty: '請輸入圖片描述',

  // 亞馬遜圖片類型
  amazonImageTypeLabel: '亞馬遜圖片類型',
  amazonImageTypeDescription: '選擇您要製作的亞馬遜圖片類型，系統將自動匹配最佳規格和風格',

  // 亞馬遜圖片類型選項
  amazonMain: '主圖 - 白色背景產品圖 (1:1)',
  amazonLifestyle: '生活方式圖 - 使用場景展示 (1:1)',
  amazonDetail: '細節圖 - 產品特寫功能 (1:1)',
  amazonComparison: '對比圖 - 尺寸顏色對比 (1:1)',
  amazonInfographic: '資訊圖 - 圖文賣點展示 (1:1)',
  amazonAplus: 'A+內容圖 - 品牌故事 (16:9)',
  amazonBrandStore: '品牌店圖 - 店鋪裝修 (16:9)',

  // 當前配置
  currentConfig: '當前配置',
  aspectRatio: '比例',
  studioStyle: '工作室風格',
  lifestyleStyle: '生活方式風格',
  productStyle: '產品展示風格',
  sceneStyle: '場景化風格',
  amazonSpec: '亞馬遜規格',

  // 亞馬遜規格
  amazonSpecLabel: '亞馬遜圖片規格',
  specMain: '主圖 - 白色背景，產品佔比85%',
  specAdditional: '附圖 - 展示產品特性和使用場景',
  specAplus: 'A+內容圖 - 品牌故事和產品展示',
  specBrand: '品牌旗艦店圖 - Banner和宣傳圖',
  
  // 圖片風格
  styleLabel: '圖片風格',
  styleStudio: '工作室',
  styleLifestyle: '生活方式',
  styleProduct: '產品展示',
  styleScene: '場景化',
  
  // 寬高比
  aspectRatioLabel: '圖片比例',
  ratio1to1: '1:1 (正方形)',
  ratio3to4: '3:4 (豎版)',
  ratio4to3: '4:3 (橫版)',
  ratio9to16: '9:16 (手機豎屏)',
  ratio16to9: '16:9 (寬屏)',
  
  // 操作按鈕
  generateButton: '生成圖片',
  generating: '生成中...',
  clearButton: '清除',
  downloadButton: '下載圖片',
  retryButton: '重新生成',
  
  // 預覽區域
  previewTitle: '預覽結果',
  previewEmpty: '生成的圖片將在這裡顯示',
  generatedImage: '生成的產品圖片',
  
  // 狀態消息
  generateSuccess: '圖片生成成功！',
  downloadSuccess: '圖片下載成功！',
  generateError: '圖片生成失敗，請重試',
  downloadError: '圖片下載失敗，請重試',
  uploadError: '檔案上傳失敗，請重試',
  fileSizeError: '圖片檔案大小不能超過10MB',
  fileTypeError: '請上傳有效的圖片檔案',
  
  // 使用限制
  usageLimit: '使用限制',
  dailyLimit: '每日限制',
  remainingUses: '剩餘次數',
  usageExceeded: '今日使用次數已達上限',
  usageLimited: '使用受限，請稍後再試',
  freeUserLimit: '免費用戶每日限制5次生成',
  upgradePrompt: '升級到專業版獲得更多使用次數',
  
  // 使用說明
  instructionsTitle: '使用說明：',
  instruction1: '• 上傳您的產品圖片（可選），或直接通過文字描述生成',
  instruction2: '• 詳細描述您想要的圖片效果，包括背景、場景、風格等',
  instruction3: '• 選擇適合的亞馬遜圖片規格和寬高比',
  instruction4: '• 點擊生成按鈕，AI將為您創建專業的產品圖片',
  instruction5: '• 免費用戶每日限制5次生成，請合理使用',
  
  // 高級功能（預留）
  advancedOptions: '高級選項',
  backgroundRemoval: '背景移除',
  qualityEnhancement: '質量增強',
  batchProcessing: '批量處理',
  customTemplates: '自定義模板',
  
  // 錯誤處理
  networkError: '網路連接失敗，請檢查網路後重試',
  serverError: '伺服器錯誤，請稍後重試',
  timeoutError: 'AI請求超時，請稍後重試',
  quotaExceeded: '使用配額已用完，請明天再試',
  
  // 提示和幫助
  tips: '小貼士',
  tip1: '詳細的描述能幫助AI生成更準確的圖片',
  tip2: '主圖建議使用白色背景，符合亞馬遜要求',
  tip3: '生活方式圖片更容易吸引買家注意',
  tip4: '可以多次生成，選擇最滿意的結果',
  
  // 質量設置（預留）
  qualitySettings: '質量設置',
  qualityStandard: '標準質量',
  qualityHigh: '高質量',
  qualityUltra: '超高質量',
  
  // 導出選項
  exportOptions: '導出選項',
  exportPNG: '導出為PNG',
  exportJPG: '導出為JPG',
  exportWebP: '導出為WebP',
  
  // 歷史記錄（預留）
  history: '歷史記錄',
  recentGenerations: '最近生成',
  favorites: '收藏夾',
  
  // 分享功能（預留）
  share: '分享',
  copyLink: '複製連結',
  shareToSocial: '分享到社交媒體',

  // AI服務說明
  aiServiceTitle: 'AI圖片生成服務',
  aiServiceDescription: '基於先進的AI技術，為您提供專業的產品圖片生成服務：',
  feature1: '支援文字描述生成產品圖片',
  feature2: '基於現有圖片進行編輯和優化',
  feature3: '符合亞馬遜2025年最新圖片規格要求',
  feature4: '多種風格選擇：工作室、生活方式、場景等',
  feature5: '智能背景替換和產品突出顯示'
};
