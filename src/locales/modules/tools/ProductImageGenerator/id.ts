// Generator Gambar Produk - Terjemahan Indonesia
export const productImageGeneratorId = {
  // Informasi dasar alat
  toolName: 'Generator Gambar Produk AI',
  toolDescription: 'Hasilkan gambar produk profesional untuk listing Amazon Anda menggunakan teknologi AI canggih',
  shortDescription: 'Generasi gambar produk Amazon yang cerdas',

  // Area fungsional utama
  uploadSection: 'Upload Gambar',
  configSection: 'Konfigurasi',
  previewSection: 'Hasil Pratinjau',
  
  // Terkait upload
  uploadPrompt: 'Klik untuk upload gambar produk',
  uploadHint: 'Mendukung format JPG, PNG, maksimal 10MB',
  changeImage: 'Ganti Gambar',
  uploadedImage: 'Gambar produk yang diupload',
  dragDropHint: 'Seret gambar ke sini atau klik untuk upload',
  
  // Deskripsi gambar
  promptLabel: 'Deskripsi Gambar',
  promptRequired: 'Deskripsi Gambar *',
  promptPlaceholder: 'Deskripsikan gambar produk yang ingin Anda hasilkan, misalnya: Letakkan headset Bluetooth ini di meja kantor modern dengan latar belakang putih bersih...',
  promptEmpty: 'Silakan masukkan deskripsi gambar',

  // Tipe gambar Amazon
  amazonImageTypeLabel: 'Tipe Gambar Amazon',
  amazonImageTypeDescription: 'Pilih tipe gambar Amazon yang ingin Anda buat, sistem akan otomatis menyesuaikan spesifikasi dan gaya terbaik',

  // Opsi tipe gambar Amazon
  amazonMain: 'Gambar Utama - Produk latar putih (1:1)',
  amazonLifestyle: 'Gambar Gaya Hidup - Tampilan skenario penggunaan (1:1)',
  amazonDetail: 'Gambar Detail - Fitur produk close-up (1:1)',
  amazonComparison: 'Gambar Perbandingan - Perbandingan ukuran warna (1:1)',
  amazonInfographic: 'Gambar Infografis - Tampilan poin jual bergambar (1:1)',
  amazonAplus: 'Gambar Konten A+ - Cerita brand (16:9)',
  amazonBrandStore: 'Gambar Toko Brand - Dekorasi toko (16:9)',

  // Konfigurasi saat ini
  currentConfig: 'Konfigurasi Saat Ini',
  aspectRatio: 'Rasio',
  studioStyle: 'gaya studio',
  lifestyleStyle: 'gaya hidup',
  productStyle: 'gaya tampilan produk',
  sceneStyle: 'gaya adegan',
  amazonSpec: 'spesifikasi Amazon',

  // Spesifikasi Amazon
  amazonSpecLabel: 'Spesifikasi Gambar Amazon',
  specMain: 'Gambar Utama - Latar putih, produk 85% coverage',
  specAdditional: 'Gambar Tambahan - Tampilkan fitur produk dan skenario penggunaan',
  specAplus: 'Gambar Konten A+ - Cerita brand dan showcase produk',
  specBrand: 'Gambar Toko Brand - Banner dan gambar promosi',
  
  // Gaya gambar
  styleLabel: 'Gaya Gambar',
  styleStudio: 'Studio',
  styleLifestyle: 'Gaya Hidup',
  styleProduct: 'Produk',
  styleScene: 'Adegan',
  
  // Rasio aspek
  aspectRatioLabel: 'Rasio Aspek',
  ratio1to1: '1:1 (Persegi)',
  ratio3to4: '3:4 (Potret)',
  ratio4to3: '4:3 (Lanskap)',
  ratio9to16: '9:16 (Potret Mobile)',
  ratio16to9: '16:9 (Layar Lebar)',
  
  // Tombol aksi
  generateButton: 'Hasilkan Gambar',
  generating: 'Menghasilkan...',
  clearButton: 'Hapus',
  downloadButton: 'Download Gambar',
  retryButton: 'Hasilkan Ulang',
  
  // Area pratinjau
  previewTitle: 'Hasil Pratinjau',
  previewEmpty: 'Gambar yang dihasilkan akan muncul di sini',
  generatedImage: 'Gambar produk yang dihasilkan',
  
  // Pesan status
  generateSuccess: 'Gambar berhasil dihasilkan!',
  downloadSuccess: 'Gambar berhasil didownload!',
  generateError: 'Gagal menghasilkan gambar, silakan coba lagi',
  downloadError: 'Gagal mendownload gambar, silakan coba lagi',
  uploadError: 'Gagal upload file, silakan coba lagi',
  fileSizeError: 'Ukuran file gambar tidak boleh melebihi 10MB',
  fileTypeError: 'Silakan upload file gambar yang valid',
  
  // Batas penggunaan
  usageLimit: 'Batas Penggunaan',
  dailyLimit: 'Batas Harian',
  remainingUses: 'Sisa Penggunaan',
  usageExceeded: 'Batas penggunaan harian tercapai',
  usageLimited: 'Penggunaan terbatas, silakan coba lagi nanti',
  freeUserLimit: 'Pengguna gratis dibatasi 5 generasi per hari',
  upgradePrompt: 'Upgrade ke Pro untuk lebih banyak penggunaan',
  
  // Instruksi
  instructionsTitle: 'Instruksi:',
  instruction1: '• Upload gambar produk Anda (opsional) atau hasilkan langsung dari deskripsi teks',
  instruction2: '• Deskripsikan secara detail efek gambar yang Anda inginkan, termasuk latar belakang, adegan, gaya, dll.',
  instruction3: '• Pilih spesifikasi gambar Amazon dan rasio aspek yang sesuai',
  instruction4: '• Klik tombol hasilkan, AI akan membuat gambar produk profesional untuk Anda',
  instruction5: '• Pengguna gratis dibatasi 5 generasi per hari, gunakan dengan bijak',
  
  // Fitur lanjutan (dicadangkan)
  advancedOptions: 'Opsi Lanjutan',
  backgroundRemoval: 'Penghapusan Latar Belakang',
  qualityEnhancement: 'Peningkatan Kualitas',
  batchProcessing: 'Pemrosesan Batch',
  customTemplates: 'Template Kustom',
  
  // Penanganan error
  networkError: 'Koneksi jaringan gagal, silakan periksa koneksi Anda dan coba lagi',
  serverError: 'Error server, silakan coba lagi nanti',
  timeoutError: 'Permintaan AI timeout, silakan coba lagi nanti',
  quotaExceeded: 'Kuota penggunaan habis, silakan coba lagi besok',
  
  // Tips dan bantuan
  tips: 'Tips',
  tip1: 'Deskripsi yang detail membantu AI menghasilkan gambar yang lebih akurat',
  tip2: 'Gambar utama sebaiknya menggunakan latar putih untuk mematuhi persyaratan Amazon',
  tip3: 'Gambar gaya hidup lebih mungkin menarik perhatian pembeli',
  tip4: 'Anda dapat menghasilkan beberapa kali dan memilih hasil yang paling memuaskan',
  
  // Pengaturan kualitas (dicadangkan)
  qualitySettings: 'Pengaturan Kualitas',
  qualityStandard: 'Kualitas Standar',
  qualityHigh: 'Kualitas Tinggi',
  qualityUltra: 'Kualitas Ultra',
  
  // Opsi ekspor
  exportOptions: 'Opsi Ekspor',
  exportPNG: 'Ekspor sebagai PNG',
  exportJPG: 'Ekspor sebagai JPG',
  exportWebP: 'Ekspor sebagai WebP',
  
  // Riwayat (dicadangkan)
  history: 'Riwayat',
  recentGenerations: 'Generasi Terbaru',
  favorites: 'Favorit',
  
  // Berbagi (dicadangkan)
  share: 'Bagikan',
  copyLink: 'Salin Link',
  shareToSocial: 'Bagikan ke Media Sosial',

  // Deskripsi Layanan AI
  aiServiceTitle: 'Layanan Generasi Gambar AI',
  aiServiceDescription: 'Berdasarkan teknologi AI canggih, menyediakan layanan generasi gambar produk profesional:',
  feature1: 'Mendukung deskripsi teks untuk menghasilkan gambar produk',
  feature2: 'Edit dan optimalkan berdasarkan gambar yang ada',
  feature3: 'Mematuhi persyaratan spesifikasi gambar terbaru Amazon 2025',
  feature4: 'Pilihan gaya beragam: studio, gaya hidup, adegan, dll.',
  feature5: 'Penggantian latar belakang cerdas dan penyorotan produk'
};
