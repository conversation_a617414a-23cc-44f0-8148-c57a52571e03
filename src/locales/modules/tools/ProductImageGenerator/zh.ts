// 产品图片生成器 - 简体中文翻译
export const productImageGeneratorZh = {
  // 工具基本信息
  toolName: 'AI产品图片生成器',
  toolDescription: '基于先进AI技术，为您的亚马逊产品生成专业的商品图片',
  shortDescription: '智能生成亚马逊产品图片',

  // 主要功能区域
  uploadSection: '图片上传',
  configSection: '参数配置',
  previewSection: '预览结果',
  
  // 上传相关
  uploadPrompt: '点击上传产品图片',
  uploadHint: '支持 JPG、PNG 格式，最大 10MB',
  changeImage: '更换图片',
  uploadedImage: '上传的产品图片',
  dragDropHint: '拖拽图片到此处或点击上传',
  
  // 图片描述
  promptLabel: '图片描述',
  promptRequired: '图片描述 *',
  promptPlaceholder: '请描述您想要生成的产品图片，例如：将这个蓝牙耳机放在现代办公桌上，背景是简洁的白色...',
  promptEmpty: '请输入图片描述',
  
  // 亚马逊图片类型
  amazonImageTypeLabel: '亚马逊图片类型',
  amazonImageTypeDescription: '选择您要制作的亚马逊图片类型，系统将自动匹配最佳规格和风格',

  // 亚马逊图片类型选项
  amazonMain: '主图 - 白色背景产品图 (1:1)',
  amazonLifestyle: '生活方式图 - 使用场景展示 (1:1)',
  amazonDetail: '细节图 - 产品特写功能 (1:1)',
  amazonComparison: '对比图 - 尺寸颜色对比 (1:1)',
  amazonInfographic: '信息图 - 图文卖点展示 (1:1)',
  amazonAplus: 'A+内容图 - 品牌故事 (16:9)',
  amazonBrandStore: '品牌店图 - 店铺装修 (16:9)',

  // 当前配置
  currentConfig: '当前配置',
  aspectRatio: '比例',
  studioStyle: '工作室风格',
  lifestyleStyle: '生活方式风格',
  productStyle: '产品展示风格',
  sceneStyle: '场景化风格',
  amazonSpec: '亚马逊规格',

  // 图片风格（简化）
  styleLabel: '图片风格',
  styleStudio: '工作室',
  styleLifestyle: '生活方式',
  styleProduct: '产品展示',
  styleScene: '场景化',
  
  // 操作按钮
  generateButton: '生成图片',
  generating: '生成中...',
  clearButton: '清除',
  downloadButton: '下载图片',
  retryButton: '重新生成',
  
  // 预览区域
  previewTitle: '预览结果',
  previewEmpty: '生成的图片将在这里显示',
  generatedImage: '生成的产品图片',
  
  // 状态消息
  generateSuccess: '图片生成成功！',
  downloadSuccess: '图片下载成功！',
  generateError: '图片生成失败，请重试',
  downloadError: '图片下载失败，请重试',
  uploadError: '文件上传失败，请重试',
  fileSizeError: '图片文件大小不能超过10MB',
  fileTypeError: '请上传有效的图片文件',
  
  // 使用限制
  usageLimit: '使用限制',
  dailyLimit: '每日限制',
  remainingUses: '剩余次数',
  usageExceeded: '今日使用次数已达上限',
  usageLimited: '使用受限，请稍后再试',
  freeUserLimit: '免费用户每日限制5次生成（北京时间0点重置）',
  upgradePrompt: '升级到专业版获得更多使用次数',
  suspiciousActivity: '检测到异常活动，请稍后再试',

  // Turnstile验证
  securityVerification: '安全验证',
  verificationDescription: '为了防止滥用，我们需要验证您是真实用户',
  verificationFailed: '验证失败，请重试',
  verificationError: '验证过程出错，请重试',
  
  // 使用说明
  instructionsTitle: '使用说明：',
  instruction1: '• 上传您的产品图片（可选），或直接通过文字描述生成',
  instruction2: '• 详细描述您想要的图片效果，包括背景、场景、风格等',
  instruction3: '• 选择适合的亚马逊图片规格和宽高比',
  instruction4: '• 点击生成按钮，AI将为您创建专业的产品图片',
  instruction5: '• 免费用户每日限制5次生成，请合理使用',
  
  // 高级功能（预留）
  advancedOptions: '高级选项',
  backgroundRemoval: '背景移除',
  qualityEnhancement: '质量增强',
  batchProcessing: '批量处理',
  customTemplates: '自定义模板',
  
  // 错误处理
  networkError: '网络连接失败，请检查网络后重试',
  serverError: '服务器错误，请稍后重试',
  timeoutError: 'AI请求超时，请稍后重试',
  quotaExceeded: '使用配额已用完，请明天再试',
  
  // 提示和帮助
  tips: '小贴士',
  tip1: '详细的描述能帮助AI生成更准确的图片',
  tip2: '主图建议使用白色背景，符合亚马逊要求',
  tip3: '生活方式图片更容易吸引买家注意',
  tip4: '可以多次生成，选择最满意的结果',
  
  // 质量设置（预留）
  qualitySettings: '质量设置',
  qualityStandard: '标准质量',
  qualityHigh: '高质量',
  qualityUltra: '超高质量',
  
  // 导出选项
  exportOptions: '导出选项',
  exportPNG: '导出为PNG',
  exportJPG: '导出为JPG',
  exportWebP: '导出为WebP',
  
  // 历史记录（预留）
  history: '历史记录',
  recentGenerations: '最近生成',
  favorites: '收藏夹',
  
  // 分享功能（预留）
  share: '分享',
  copyLink: '复制链接',
  shareToSocial: '分享到社交媒体',

  // AI服务说明
  aiServiceTitle: 'AI图片生成服务',
  aiServiceDescription: '基于先进的AI技术，为您提供专业的产品图片生成服务：',
  feature1: '支持文字描述生成产品图片',
  feature2: '基于现有图片进行编辑和优化',
  feature3: '符合亚马逊2025年最新图片规格要求',
  feature4: '多种风格选择：工作室、生活方式、场景等',
  feature5: '智能背景替换和产品突出显示'
};
