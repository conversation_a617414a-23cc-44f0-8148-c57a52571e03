// Unit Converter translations - English
export const unitConverterEn = {
  // Unit Converter
  unitConverter: 'Unit Converter',
  unitConverterDesc: 'Quickly convert length, weight, volume and other units, supporting metric and imperial systems.',
  unitConverterHeader: 'Precisely convert various units, supporting length, weight, volume, area, temperature and power',

  // Unit Converter Categories
  ucCategoryLength: 'Length',
  ucCategoryWeight: 'Weight',
  ucCategoryVolume: 'Volume',
  ucCategoryArea: 'Area',
  ucCategoryTemperature: 'Temperature',
  ucCategoryPower: 'Power',

  // Length Units
  ucUnitMm: 'Millimeter',
  ucUnitCm: 'Centimeter',
  ucUnitM: 'Meter',
  ucUnitKm: 'Kilometer',
  ucUnitIn: 'Inch',
  ucUnitFt: 'Foot',
  ucUnitYd: 'Yard',
  ucUnitMil: 'Mil',

  // Weight Units
  ucUnitMg: 'Milligram',
  ucUnitG: 'Gram',
  ucUnitKg: 'Kilogram',
  ucUnitT: 'Ton',
  ucUnitTon: 'US Ton',
  ucUnitOz: 'Ounce',
  ucUnitLb: 'Pound',
  ucUnitSt: 'Stone',
  ucUnitGrain: 'Grain',

  // Volume Units
  ucUnitMl: 'Milliliter',
  ucUnitL: 'Liter',
  ucUnitM3: 'Cubic Meter',
  ucUnitCm3: 'Cubic Centimeter',
  ucUnitIn3: 'Cubic Inch',
  ucUnitFt3: 'Cubic Foot',
  ucUnitFlOz: 'Fluid Ounce',
  ucUnitFloz: 'Fluid Ounce',
  ucUnitCup: 'Cup',
  ucUnitGal: 'Gallon',
  ucUnitQt: 'Quart',
  ucUnitPt: 'Pint',

  // Area Units
  ucUnitMm2: 'Square Millimeter',
  ucUnitCm2: 'Square Centimeter',
  ucUnitM2: 'Square Meter',
  ucUnitKm2: 'Square Kilometer',
  ucUnitIn2: 'Square Inch',
  ucUnitFt2: 'Square Foot',
  ucUnitYd2: 'Square Yard',
  ucUnitAcre: 'Acre',

  // Temperature Units
  ucUnitC: 'Celsius',
  ucUnitF: 'Fahrenheit',
  ucUnitK: 'Kelvin',
  ucUnitR: 'Rankine',

  // Power Units
  ucUnitW: 'Watt',
  ucUnitKw: 'Kilowatt',
  ucUnitHp: 'Horsepower',
  ucUnitBtu: 'BTU/hour',
  ucUnitJ: 'Joule',
  ucUnitKj: 'Kilojoule',
  ucUnitCal: 'Calorie',
  ucUnitKcal: 'Kilocalorie',

  // Unit Converter Interface
  ucUnitConversion: 'Unit Conversion',
  ucInputValue: 'Input Value',
  ucInputPlaceholder: 'Enter value to convert',
  ucFrom: 'From',
  ucTo: 'To',
  ucSelectCategory: 'Select conversion category',
  ucRealTimeConversion: 'Real-time Conversion',
  ucSwapUnits: 'Swap Units',
  ucConversionResult: 'Conversion Result',
  ucCopy: 'Copy',
  ucCopied: 'Copied',
  ucSave: 'Save',
  ucClear: 'Clear',
  ucCommonReference: 'Common Reference',
  ucConversionHistory: 'Conversion History',

  // Amazon Seller Tips for Unit Converter
  ucAmazonSellerTips: 'Amazon Seller Tips',
  ucTip1: 'Product dimensions must be accurate, affecting FBA fee calculation and customer experience',
  ucTip2: 'Weight information directly affects shipping costs, recommend precision to two decimal places',
  ucTip3: 'Dimensional weight calculation: Length×Width×Height(cm)÷6000, take the larger value with actual weight(kg)',
  ucTip4: 'Temperature units are important in electronics specifications, pay attention to Fahrenheit and Celsius conversion',

  // Quick Conversions
  ucQuickConversions: 'Quick Conversions',
  ucQuick1Label: '1 inch',
  ucQuick1Value: '2.54 cm',
  ucQuick2Label: '1 pound',
  ucQuick2Value: '0.45 kg',
  ucQuick3Label: '1 gallon',
  ucQuick3Value: '3.79 liters',
  ucQuick4Label: '1 foot',
  ucQuick4Value: '30.48 cm',
  ucQuick5Label: '1 ounce',
  ucQuick5Value: '28.35 grams',
};
