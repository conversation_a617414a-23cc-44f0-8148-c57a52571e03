// Konverter Satuan terjemahan - Bahasa Indonesia
export const unitConverterId = {
  // Unit Converter
  unitConverter: 'Konverter Satuan',
  unitConverterDesc: 'Konversi cepat panjang, berat, volume dan satuan lainnya, mendukung sistem metrik dan imperial.',
  unitConverterHeader: 'Konversi berbagai satuan dengan presisi, mendukung panjang, berat, volume, luas, suhu dan daya',

  // Unit Converter Categories
  ucCategoryLength: 'Panjang',
  ucCategoryWeight: 'Berat',
  ucCategoryVolume: 'Volume',
  ucCategoryArea: 'Luas',
  ucCategoryTemperature: 'Suhu',
  ucCategoryPower: 'Daya',

  // Length Units
  ucUnitMm: 'Milimeter',
  ucUnitCm: 'Sentimeter',
  ucUnitM: 'Meter',
  ucUnitKm: 'Kilometer',
  ucUnitIn: 'Inci',
  ucUnitFt: 'Ka<PERSON>',
  ucUnitYd: 'Yard',
  ucUnitMil: 'Mil',

  // Weight Units
  ucUnitMg: 'Miligram',
  ucUnitG: 'Gram',
  ucUnitKg: 'Kilogram',
  ucUnitT: 'Ton',
  ucUnitTon: 'Ton AS',
  ucUnitOz: 'Ons',
  ucUnitLb: 'Pon',
  ucUnitSt: 'Stone',
  ucUnitGrain: 'Grain',

  // Volume Units
  ucUnitMl: 'Mililiter',
  ucUnitL: 'Liter',
  ucUnitM3: 'Meter Kubik',
  ucUnitCm3: 'Sentimeter Kubik',
  ucUnitIn3: 'Inci Kubik',
  ucUnitFt3: 'Kaki Kubik',
  ucUnitFlOz: 'Ons Cair',
  ucUnitFloz: 'Ons Cair',
  ucUnitCup: 'Cangkir',
  ucUnitGal: 'Galon',
  ucUnitQt: 'Quart',
  ucUnitPt: 'Pint',

  // Area Units
  ucUnitMm2: 'Milimeter Persegi',
  ucUnitCm2: 'Sentimeter Persegi',
  ucUnitM2: 'Meter Persegi',
  ucUnitKm2: 'Kilometer Persegi',
  ucUnitIn2: 'Inci Persegi',
  ucUnitFt2: 'Kaki Persegi',
  ucUnitYd2: 'Yard Persegi',
  ucUnitAcre: 'Acre',

  // Temperature Units
  ucUnitC: 'Celsius',
  ucUnitF: 'Fahrenheit',
  ucUnitK: 'Kelvin',
  ucUnitR: 'Rankine',

  // Power Units
  ucUnitW: 'Watt',
  ucUnitKw: 'Kilowatt',
  ucUnitHp: 'Tenaga Kuda',
  ucUnitBtu: 'BTU/jam',
  ucUnitJ: 'Joule',
  ucUnitKj: 'Kilojoule',
  ucUnitCal: 'Kalori',
  ucUnitKcal: 'Kilokalori',

  // Unit Converter Interface
  ucUnitConversion: 'Konversi Satuan',
  ucInputValue: 'Nilai Input',
  ucInputPlaceholder: 'Masukkan nilai untuk dikonversi',
  ucFrom: 'Dari',
  ucTo: 'Ke',
  ucSelectCategory: 'Pilih kategori konversi',
  ucRealTimeConversion: 'Konversi Real-time',
  ucSwapUnits: 'Tukar Satuan',
  ucConversionResult: 'Hasil Konversi',
  ucCopy: 'Salin',
  ucCopied: 'Disalin',
  ucSave: 'Simpan',
  ucClear: 'Hapus',
  ucCommonReference: 'Referensi Umum',
  ucConversionHistory: 'Riwayat Konversi',

  // Amazon Seller Tips for Unit Converter
  ucAmazonSellerTips: 'Tips Penjual Amazon',
  ucTip1: 'Dimensi produk harus akurat, mempengaruhi perhitungan biaya FBA dan pengalaman pelanggan',
  ucTip2: 'Informasi berat langsung mempengaruhi biaya pengiriman, disarankan akurat hingga dua desimal',
  ucTip3: 'Perhitungan berat volume: Panjang×Lebar×Tinggi(cm)÷6000, ambil nilai yang lebih besar dengan berat aktual(kg)',
  ucTip4: 'Satuan suhu penting dalam spesifikasi produk elektronik, perhatikan konversi Fahrenheit dan Celsius',

  // Quick Conversions
  ucQuickConversions: 'Konversi Cepat',
  ucQuick1Label: '1 inci',
  ucQuick1Value: '2,54 sentimeter',
  ucQuick2Label: '1 pon',
  ucQuick2Value: '0,45 kilogram',
  ucQuick3Label: '1 galon',
  ucQuick3Value: '3,79 liter',
  ucQuick4Label: '1 kaki',
  ucQuick4Value: '30,48 sentimeter',
  ucQuick5Label: '1 ons',
  ucQuick5Value: '28,35 gram',
};
