// 単位変換器翻訳 - 日本語
export const unitConverterJa = {
  // Unit Converter
  unitConverter: '単位変換器',
  unitConverterDesc: '長さ、重量、体積などの単位を素早く変換、メートル法とヤード・ポンド法をサポート。',
  unitConverterHeader: '長さ、重量、体積、面積、温度、電力の様々な単位を正確に変換',

  // Unit Converter Categories
  ucCategoryLength: '長さ',
  ucCategoryWeight: '重量',
  ucCategoryVolume: '体積',
  ucCategoryArea: '面積',
  ucCategoryTemperature: '温度',
  ucCategoryPower: '電力',

  // Length Units
  ucUnitMm: 'ミリメートル',
  ucUnitCm: 'センチメートル',
  ucUnitM: 'メートル',
  ucUnitKm: 'キロメートル',
  ucUnitIn: 'インチ',
  ucUnitFt: 'フィート',
  ucUnitYd: 'ヤード',
  ucUnitMil: 'ミル',

  // Weight Units
  ucUnitMg: 'ミリグラム',
  ucUnitG: 'グラム',
  ucUnitKg: 'キログラム',
  ucUnitT: 'トン',
  ucUnitTon: 'アメリカトン',
  ucUnitOz: 'オンス',
  ucUnitLb: 'ポンド',
  ucUnitSt: 'ストーン',
  ucUnitGrain: 'グレーン',

  // Volume Units
  ucUnitMl: 'ミリリットル',
  ucUnitL: 'リットル',
  ucUnitM3: '立方メートル',
  ucUnitCm3: '立方センチメートル',
  ucUnitIn3: '立方インチ',
  ucUnitFt3: '立方フィート',
  ucUnitFlOz: '液量オンス',
  ucUnitFloz: '液量オンス',
  ucUnitCup: 'カップ',
  ucUnitGal: 'ガロン',
  ucUnitQt: 'クォート',
  ucUnitPt: 'パイント',

  // Area Units
  ucUnitMm2: '平方ミリメートル',
  ucUnitCm2: '平方センチメートル',
  ucUnitM2: '平方メートル',
  ucUnitKm2: '平方キロメートル',
  ucUnitIn2: '平方インチ',
  ucUnitFt2: '平方フィート',
  ucUnitYd2: '平方ヤード',
  ucUnitAcre: 'エーカー',

  // Temperature Units
  ucUnitC: '摂氏',
  ucUnitF: '華氏',
  ucUnitK: 'ケルビン',
  ucUnitR: 'ランキン',

  // Power Units
  ucUnitW: 'ワット',
  ucUnitKw: 'キロワット',
  ucUnitHp: '馬力',
  ucUnitBtu: 'BTU/時',
  ucUnitJ: 'ジュール',
  ucUnitKj: 'キロジュール',
  ucUnitCal: 'カロリー',
  ucUnitKcal: 'キロカロリー',

  // Unit Converter Interface
  ucUnitConversion: '単位変換',
  ucInputValue: '入力値',
  ucInputPlaceholder: '変換する値を入力',
  ucFrom: 'から',
  ucTo: 'へ',
  ucSelectCategory: '変換カテゴリを選択',
  ucRealTimeConversion: 'リアルタイム変換',
  ucSwapUnits: '単位を交換',
  ucConversionResult: '変換結果',
  ucCopy: 'コピー',
  ucCopied: 'コピー済み',
  ucSave: '保存',
  ucClear: 'クリア',
  ucCommonReference: '一般的な参考',
  ucConversionHistory: '変換履歴',

  // Amazon Seller Tips for Unit Converter
  ucAmazonSellerTips: 'Amazon販売者のヒント',
  ucTip1: '商品寸法は正確である必要があり、FBA料金計算と顧客体験に影響します',
  ucTip2: '重量情報は配送コストに直接影響するため、小数点以下2桁まで正確にすることをお勧めします',
  ucTip3: '容積重量計算：長さ×幅×高さ(cm)÷6000、実重量(kg)との大きい方を取る',
  ucTip4: '電子製品の仕様では温度単位が重要です。華氏と摂氏の変換に注意してください',

  // Quick Conversions
  ucQuickConversions: 'クイック変換',
  ucQuick1Label: '1インチ',
  ucQuick1Value: '2.54センチメートル',
  ucQuick2Label: '1ポンド',
  ucQuick2Value: '0.45キログラム',
  ucQuick3Label: '1ガロン',
  ucQuick3Value: '3.79リットル',
  ucQuick4Label: '1フィート',
  ucQuick4Value: '30.48センチメートル',
  ucQuick5Label: '1オンス',
  ucQuick5Value: '28.35グラム',
};
