// Case Converter Translations - English
export const caseConverterEn = {
  // Case Converter
  caseConverter: 'Case Converter',
  caseConverterDesc: 'Convert product titles and descriptions case formats with one click, enhance Amazon listing professionalism.',
  caseConverterHeader: 'Quickly convert product titles and descriptions case formats to make your Amazon listings more professional',

  // Input Section
  ccInputText: 'Input Text',
  ccInputPlaceholder: 'Enter text to convert...',
  ccCharacters: 'characters',
  ccClear: 'Clear',
  ccNoText: 'Enter text to convert',

  // Conversion Types - Focus on Amazon Seller Needs
  ccTitleCase: 'Title Case',
  ccTitleCaseDesc: 'Capitalize important words, perfect for product titles',
  ccUpperCase: 'UPPERCASE',
  ccUpperCaseDesc: 'All letters uppercase, suitable for emphasis',
  ccLowerCase: 'lowercase',
  ccLowerCaseDesc: 'All letters lowercase, suitable for descriptive text',
  ccSentenceCase: 'Sentence case',
  ccSentenceCaseDesc: 'Only first letter capitalized, suitable for descriptions',
  ccInverseCase: 'iNVERSE cASE',
  ccInverseCaseDesc: 'Uppercase becomes lowercase, lowercase becomes uppercase',
  ccAlternatingCase: 'aLtErNaTiNg CaSe',
  ccAlternatingCaseDesc: 'Alternate case letters for creative display effects',

  // Actions
  ccCopy: 'Copy',
  ccCopied: 'Copied',
  ccHistory: 'Conversion History',

  // Amazon Seller Tips
  ccAmazonTips: 'Amazon Seller Tips',
  ccAmazonTip1Title: 'Product Title Optimization',
  ccAmazonTip1Desc: 'Use title case to make product titles more professional, capitalizing important words improves search rankings',
  ccAmazonTip2Title: 'Avoid All Uppercase',
  ccAmazonTip2Desc: 'Avoid all uppercase titles as Amazon may consider them spam, affecting listing quality',
  ccAmazonTip3Title: 'Brand Name Standards',
  ccAmazonTip3Desc: 'Keep brand names in their original case format to ensure brand consistency and professional image',
  ccAmazonTip4Title: 'Keyword Optimization',
  ccAmazonTip4Desc: 'Use case strategically to highlight keywords, but avoid over-stuffing that affects user experience',

  // Examples
  ccExample1: 'Example: Wireless Bluetooth Headphones',
  ccExample2: 'Example: Premium Quality Kitchen Knife Set',
  ccExample3: 'Example: Waterproof Phone Case for iPhone',
  ccExample4: 'Example: Organic Cotton Baby Clothes'
};
