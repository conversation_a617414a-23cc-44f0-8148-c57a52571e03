// Te<PERSON><PERSON><PERSON><PERSON> Konverter Huruf - Bahasa Indonesia
export const caseConverterId = {
  // Case Converter
  caseConverter: 'Konverter Huruf',
  caseConverterDesc: 'Konversi format huruf judul dan deskripsi produk dengan satu klik, tingkatkan profesionalitas listing Amazon.',
  caseConverterHeader: 'Konversi format huruf judul dan deskripsi produk dengan cepat untuk membuat listing Amazon Anda lebih profesional',

  // Input Section
  ccInputText: 'Teks Input',
  ccInputPlaceholder: 'Masukkan teks untuk dikonversi...',
  ccCharacters: 'karakter',
  ccClear: 'Hapus',
  ccNoText: 'Masukkan teks untuk dikonversi',

  // Conversion Types
  ccUpperCase: 'HURUF BESAR',
  ccUpperCaseDesc: 'Konversi semua huruf menjadi huruf besar',
  ccLowerCase: 'huruf kecil',
  ccLowerCaseDesc: 'Konversi semua huruf menjadi huruf kecil',
  ccTitleCase: 'Format Judul',
  ccTitleCaseDesc: 'Huruf pertama setiap kata menjadi huruf besar',
  ccSentenceCase: 'Format kalimat',
  ccSentenceCaseDesc: 'Hanya huruf pertama yang menjadi huruf besar',
  ccCamelCase: 'camelCase',
  ccCamelCaseDesc: 'Kata pertama huruf kecil, huruf pertama kata lainnya huruf besar',
  ccPascalCase: 'PascalCase',
  ccPascalCaseDesc: 'Huruf pertama semua kata huruf besar, tanpa spasi',
  ccSnakeCase: 'snake_case',
  ccSnakeCaseDesc: 'Hubungkan kata dengan garis bawah, semua huruf kecil',
  ccKebabCase: 'kebab-case',
  ccKebabCaseDesc: 'Hubungkan kata dengan tanda hubung, semua huruf kecil',
  ccConstantCase: 'CONSTANT_CASE',
  ccConstantCaseDesc: 'Hubungkan kata dengan garis bawah, semua huruf besar',
  ccAlternatingCase: 'hUrUf BeRgAnTiAn',
  ccAlternatingCaseDesc: 'Bergantian antara huruf besar dan kecil',
  ccInverseCase: 'hURUF tERBALIK',
  ccInverseCaseDesc: 'Huruf besar menjadi kecil, huruf kecil menjadi besar',

  // Actions
  ccCopy: 'Salin',
  ccCopied: 'Tersalin',
  ccHistory: 'Riwayat Konversi',

  // Amazon Seller Tips
  ccAmazonTips: 'Tips Penjual Amazon',
  ccAmazonTip1Title: 'Optimasi Judul Produk',
  ccAmazonTip1Desc: 'Gunakan format judul untuk membuat judul produk lebih profesional, kapitalisasi kata penting meningkatkan peringkat pencarian',
  ccAmazonTip2Title: 'Hindari Semua Huruf Besar',
  ccAmazonTip2Desc: 'Hindari judul semua huruf besar karena Amazon mungkin menganggapnya spam, mempengaruhi kualitas listing',
  ccAmazonTip3Title: 'Standar Nama Merek',
  ccAmazonTip3Desc: 'Pertahankan nama merek dalam format huruf aslinya untuk memastikan konsistensi merek dan citra profesional',
  ccAmazonTip4Title: 'Optimasi Kata Kunci',
  ccAmazonTip4Desc: 'Gunakan huruf besar-kecil secara strategis untuk menyoroti kata kunci, tapi hindari penumpukan berlebihan yang mempengaruhi pengalaman pengguna',

  // Examples
  ccExample1: 'Contoh: Wireless Bluetooth Headphones',
  ccExample2: 'Contoh: Premium Quality Kitchen Knife Set',
  ccExample3: 'Contoh: Waterproof Phone Case for iPhone',
  ccExample4: 'Contoh: Organic Cotton Baby Clothes'
};
