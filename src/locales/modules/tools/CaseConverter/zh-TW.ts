// 大小寫轉換器翻譯 - 繁體中文
export const caseConverterZhTW = {
  // Case Converter
  caseConverter: '大小寫轉換器',
  caseConverterDesc: '一鍵轉換產品標題和描述的大小寫格式，提升Amazon listing專業度。',
  caseConverterHeader: '快速轉換產品標題和描述的大小寫格式，讓您的Amazon listing更專業規範',

  // Input Section
  ccInputText: '輸入文字',
  ccInputPlaceholder: '請輸入需要轉換的文字...',
  ccCharacters: '字元',
  ccClear: '清空',
  ccNoText: '請輸入文字進行轉換',

  // Conversion Types
  ccUpperCase: '全大寫',
  ccUpperCaseDesc: '將所有字母轉換為大寫',
  ccLowerCase: '全小寫',
  ccLowerCaseDesc: '將所有字母轉換為小寫',
  ccTitleCase: '標題格式',
  ccTitleCaseDesc: '每個單字首字母大寫',
  ccSentenceCase: '句子格式',
  ccSentenceCaseDesc: '只有第一個字母大寫',
  ccCamelCase: '駝峰命名',
  ccCamelCaseDesc: '首單字小寫，其餘單字首字母大寫',
  ccPascalCase: '帕斯卡命名',
  ccPascalCaseDesc: '所有單字首字母大寫，無空格',
  ccSnakeCase: '底線命名',
  ccSnakeCaseDesc: '單字間用底線連接，全小寫',
  ccKebabCase: '短橫線命名',
  ccKebabCaseDesc: '單字間用短橫線連接，全小寫',
  ccConstantCase: '常數命名',
  ccConstantCaseDesc: '單字間用底線連接，全大寫',
  ccAlternatingCase: '交替大小寫',
  ccAlternatingCaseDesc: '字母交替大小寫顯示',
  ccInverseCase: '反轉大小寫',
  ccInverseCaseDesc: '大寫變小寫，小寫變大寫',

  // Actions
  ccCopy: '複製',
  ccCopied: '已複製',
  ccHistory: '轉換歷史',

  // Amazon Seller Tips
  ccAmazonTips: 'Amazon賣家使用技巧',
  ccAmazonTip1Title: '產品標題優化',
  ccAmazonTip1Desc: '使用標題格式讓產品標題更專業，每個重要單字首字母大寫可提升搜尋排名',
  ccAmazonTip2Title: '避免全大寫',
  ccAmazonTip2Desc: '避免全大寫標題，Amazon可能將其視為垃圾資訊，影響listing品質',
  ccAmazonTip3Title: '品牌名稱規範',
  ccAmazonTip3Desc: '品牌名稱保持原有大小寫格式，確保品牌一致性和專業形象',
  ccAmazonTip4Title: '關鍵字優化',
  ccAmazonTip4Desc: '合理使用大小寫突出關鍵字，但避免過度堆砌影響使用者體驗',

  // Examples
  ccExample1: '範例：Wireless Bluetooth Headphones',
  ccExample2: '範例：Premium Quality Kitchen Knife Set',
  ccExample3: '範例：Waterproof Phone Case for iPhone',
  ccExample4: '範例：Organic Cotton Baby Clothes'
};
