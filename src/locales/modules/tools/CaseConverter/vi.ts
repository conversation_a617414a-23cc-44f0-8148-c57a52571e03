// Bản dịch Bộ chuyển đổi chữ hoa chữ thường - Tiếng Việt
export const caseConverterVi = {
  // Case Converter
  caseConverter: 'B<PERSON> chuyển đổi chữ hoa/thường',
  caseConverterDesc: 'Chuyển đổi định dạng chữ hoa thường của tiêu đề và mô tả sản phẩm chỉ với một cú nhấp, nâng cao tính chuyên nghiệp của listing Amazon.',
  caseConverterHeader: '<PERSON>hanh chóng chuyển đổi định dạng chữ hoa thường của tiêu đề và mô tả sản phẩm để làm cho listing Amazon của bạn chuyên nghiệp hơn',

  // Input Section
  ccInputText: 'Văn bản đầu vào',
  ccInputPlaceholder: 'Nhập văn bản cần chuyển đổi...',
  ccCharacters: 'ký tự',
  ccClear: 'Xóa',
  ccNoText: 'Nhập văn bản để chuyển đổi',

  // Conversion Types
  ccUpperCase: 'CHỮ HOA',
  ccUpperCaseDesc: 'Chuyển tất cả chữ cái thành chữ hoa',
  ccLowerCase: 'chữ thường',
  ccLowerCaseDesc: 'Chuyển tất cả chữ cái thành chữ thường',
  ccTitleCase: 'Định dạng tiêu đề',
  ccTitleCaseDesc: 'Viết hoa chữ cái đầu của mỗi từ',
  ccSentenceCase: 'Định dạng câu',
  ccSentenceCaseDesc: 'Chỉ viết hoa chữ cái đầu tiên',
  ccCamelCase: 'camelCase',
  ccCamelCaseDesc: 'Từ đầu viết thường, viết hoa chữ cái đầu của các từ còn lại',
  ccPascalCase: 'PascalCase',
  ccPascalCaseDesc: 'Viết hoa chữ cái đầu của tất cả các từ, không có khoảng trắng',
  ccSnakeCase: 'snake_case',
  ccSnakeCaseDesc: 'Nối các từ bằng dấu gạch dưới, tất cả chữ thường',
  ccKebabCase: 'kebab-case',
  ccKebabCaseDesc: 'Nối các từ bằng dấu gạch ngang, tất cả chữ thường',
  ccConstantCase: 'CONSTANT_CASE',
  ccConstantCaseDesc: 'Nối các từ bằng dấu gạch dưới, tất cả chữ hoa',
  ccAlternatingCase: 'ChỮ XeN kẼ',
  ccAlternatingCaseDesc: 'Xen kẽ giữa chữ hoa và chữ thường',
  ccInverseCase: 'cHỮ ĐảO nGƯỢC',
  ccInverseCaseDesc: 'Chữ hoa thành chữ thường, chữ thường thành chữ hoa',

  // Actions
  ccCopy: 'Sao chép',
  ccCopied: 'Đã sao chép',
  ccHistory: 'Lịch sử chuyển đổi',

  // Amazon Seller Tips
  ccAmazonTips: 'Mẹo cho người bán Amazon',
  ccAmazonTip1Title: 'Tối ưu tiêu đề sản phẩm',
  ccAmazonTip1Desc: 'Sử dụng định dạng tiêu đề để làm cho tiêu đề sản phẩm chuyên nghiệp hơn, viết hoa các từ quan trọng giúp cải thiện thứ hạng tìm kiếm',
  ccAmazonTip2Title: 'Tránh toàn bộ chữ hoa',
  ccAmazonTip2Desc: 'Tránh tiêu đề toàn chữ hoa vì Amazon có thể coi là spam, ảnh hưởng đến chất lượng listing',
  ccAmazonTip3Title: 'Tiêu chuẩn tên thương hiệu',
  ccAmazonTip3Desc: 'Giữ nguyên định dạng chữ hoa thường gốc của tên thương hiệu để đảm bảo tính nhất quán và hình ảnh chuyên nghiệp',
  ccAmazonTip4Title: 'Tối ưu từ khóa',
  ccAmazonTip4Desc: 'Sử dụng chữ hoa thường một cách chiến lược để làm nổi bật từ khóa, nhưng tránh nhồi nhét quá mức ảnh hưởng đến trải nghiệm người dùng',

  // Examples
  ccExample1: 'Ví dụ: Wireless Bluetooth Headphones',
  ccExample2: 'Ví dụ: Premium Quality Kitchen Knife Set',
  ccExample3: 'Ví dụ: Waterproof Phone Case for iPhone',
  ccExample4: 'Ví dụ: Organic Cotton Baby Clothes'
};
