// 大小写转换器翻译 - 简体中文
export const caseConverterZh = {
  // Case Converter
  caseConverter: '大小写转换器',
  caseConverterDesc: '一键转换产品标题和描述的大小写格式，提升Amazon listing专业度。',
  caseConverterHeader: '快速转换产品标题和描述的大小写格式，让您的Amazon listing更专业规范',

  // Input Section
  ccInputText: '输入文本',
  ccInputPlaceholder: '请输入需要转换的文本...',
  ccCharacters: '字符',
  ccClear: '清空',
  ccNoText: '请输入文本进行转换',

  // Conversion Types - 专注Amazon卖家需求
  ccTitleCase: '标题格式',
  ccTitleCaseDesc: '每个重要单词首字母大写，最适合产品标题',
  ccUpperCase: '全大写',
  ccUpperCaseDesc: '所有字母大写，适合强调重点信息',
  ccLowerCase: '全小写',
  ccLowerCaseDesc: '所有字母小写，适合描述性文本',
  ccSentenceCase: '句子格式',
  ccSentenceCaseDesc: '只有第一个字母大写，适合产品描述',
  ccInverseCase: '反转大小写',
  ccInverseCaseDesc: '大写变小写，小写变大写',
  ccAlternatingCase: '交替大小写',
  ccAlternatingCaseDesc: '字母交替大小写，创意展示效果',

  // Actions
  ccCopy: '复制',
  ccCopied: '已复制',
  ccHistory: '转换历史',

  // Amazon Seller Tips
  ccAmazonTips: 'Amazon卖家使用技巧',
  ccAmazonTip1Title: '产品标题优化',
  ccAmazonTip1Desc: '使用标题格式让产品标题更专业，每个重要单词首字母大写可提升搜索排名',
  ccAmazonTip2Title: '避免全大写',
  ccAmazonTip2Desc: '避免全大写标题，Amazon可能将其视为垃圾信息，影响listing质量',
  ccAmazonTip3Title: '品牌名称规范',
  ccAmazonTip3Desc: '品牌名称保持原有大小写格式，确保品牌一致性和专业形象',
  ccAmazonTip4Title: '关键词优化',
  ccAmazonTip4Desc: '合理使用大小写突出关键词，但避免过度堆砌影响用户体验',

  // Examples
  ccExample1: '示例：Wireless Bluetooth Headphones',
  ccExample2: '示例：Premium Quality Kitchen Knife Set',
  ccExample3: '示例：Waterproof Phone Case for iPhone',
  ccExample4: '示例：Organic Cotton Baby Clothes'
};
