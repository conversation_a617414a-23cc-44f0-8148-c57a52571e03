// ケースコンバーター翻訳 - 日本語
export const caseConverterJa = {
  // Case Converter
  caseConverter: 'ケースコンバーター',
  caseConverterDesc: 'ワンクリックで商品タイトルと説明の大文字小文字形式を変換、Amazonリスティングの専門性を向上。',
  caseConverterHeader: '商品タイトルと説明の大文字小文字形式を素早く変換し、Amazonリスティングをより専門的に',

  // Input Section
  ccInputText: '入力テキスト',
  ccInputPlaceholder: '変換するテキストを入力してください...',
  ccCharacters: '文字',
  ccClear: 'クリア',
  ccNoText: '変換するテキストを入力してください',

  // Conversion Types
  ccUpperCase: '全て大文字',
  ccUpperCaseDesc: '全ての文字を大文字に変換',
  ccLowerCase: '全て小文字',
  ccLowerCaseDesc: '全ての文字を小文字に変換',
  ccTitleCase: 'タイトルケース',
  ccTitleCaseDesc: '各単語の最初の文字を大文字に',
  ccSentenceCase: 'センテンスケース',
  ccSentenceCaseDesc: '最初の文字のみ大文字に',
  ccCamelCase: 'キャメルケース',
  ccCamelCaseDesc: '最初の単語は小文字、残りの単語の最初の文字を大文字に',
  ccPascalCase: 'パスカルケース',
  ccPascalCaseDesc: '全ての単語の最初の文字を大文字に、スペースなし',
  ccSnakeCase: 'スネークケース',
  ccSnakeCaseDesc: '単語をアンダースコアで接続、全て小文字',
  ccKebabCase: 'ケバブケース',
  ccKebabCaseDesc: '単語をハイフンで接続、全て小文字',
  ccConstantCase: '定数ケース',
  ccConstantCaseDesc: '単語をアンダースコアで接続、全て大文字',
  ccAlternatingCase: '交互ケース',
  ccAlternatingCaseDesc: '文字を交互に大文字小文字で表示',
  ccInverseCase: '逆転ケース',
  ccInverseCaseDesc: '大文字を小文字に、小文字を大文字に',

  // Actions
  ccCopy: 'コピー',
  ccCopied: 'コピー済み',
  ccHistory: '変換履歴',

  // Amazon Seller Tips
  ccAmazonTips: 'Amazon販売者のコツ',
  ccAmazonTip1Title: '商品タイトル最適化',
  ccAmazonTip1Desc: 'タイトルケースを使用して商品タイトルをより専門的にし、重要な単語を大文字にして検索ランキングを向上',
  ccAmazonTip2Title: '全て大文字を避ける',
  ccAmazonTip2Desc: '全て大文字のタイトルは避ける、Amazonがスパム情報と見なしリスティング品質に影響する可能性',
  ccAmazonTip3Title: 'ブランド名の規範',
  ccAmazonTip3Desc: 'ブランド名は元の大文字小文字フォーマットを保持し、ブランドの一貫性と専門的なイメージを確保',
  ccAmazonTip4Title: 'キーワード最適化',
  ccAmazonTip4Desc: '大文字小文字を合理的に使用してキーワードを強調するが、ユーザー体験に影響する過度な詰め込みを避ける',

  // Examples
  ccExample1: '例：Wireless Bluetooth Headphones',
  ccExample2: '例：Premium Quality Kitchen Knife Set',
  ccExample3: '例：Waterproof Phone Case for iPhone',
  ccExample4: '例：Organic Cotton Baby Clothes'
};
