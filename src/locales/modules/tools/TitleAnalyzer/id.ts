// Penganalisis Judul terjemahan - Bahasa Indonesia
export const titleAnalyzerId = {
  // Title Analyzer
  titleAnalyzer: 'Penganalisis Judul',
  titleAnalyzerDesc: 'Analisis dan optimalkan judul produk Amazon Anda untuk visibilitas dan kepatuhan maksimum.',
  taTargetKeywordPlaceholder: 'contoh: wireless headphones, bluetooth speaker',
  taProductFeaturesPlaceholder: 'contoh: peredam bising, tahan air, baterai tahan lama, pengisian cepat',
  taToolDescription: 'Alat analisis dan optimasi judul cerdas berdasarkan kebijakan terbaru Amazon 2025',
  taMaxLength: 'Panjang Maksimal',
  taCharacters: '<PERSON><PERSON>er',
  taStartAnalyzingDesc: 'Masukkan judul produk untuk mendapatkan saran optimasi profesional dan pemeriksaan kepatuhan',
  taAiOptimizationSuggestions: 'Saran Optimasi AI',
  taOptimizedTitleSuggestions: 'Saran <PERSON> yang <PERSON>op<PERSON>',
  taWords: 'Kata',
  taKeywordSuggestions: 'Saran Kata Kunci',
  taComplianceScoreLabel: 'Skor Kepatuhan',
  taModificationSuggestions: 'Saran Modifikasi',
  taTitleStatistics: 'Statistik Judul',
  taOption: 'Opsi',
  taViolationItems: 'Item Pelanggaran',
  taAiSmartOptimization: 'Optimasi Cerdas AI',
  taAiServiceTip: 'Tips Layanan AI',
  taTitleOptimizedWell: 'Judul Dioptimalkan dengan Sangat Baik!',
  taTitleOptimizedDesc: 'Judul Anda mengikuti praktik terbaik Amazon 2025 dan membantu meningkatkan peringkat pencarian dan tingkat konversi.',
  taUse: 'Gunakan',
  taImprovementPoints: 'Poin Perbaikan',
  taBasicVersion: 'Versi Dasar',
  taKeywordInputNote: 'Masukkan kata kunci utama untuk hasil analisis yang lebih akurat',
  taStartAnalyzing: 'Mulai Analisis Judul',
  taAiEnhanced: 'Ditingkatkan AI',
  taProductFeatures: 'Fitur Produk',
  taOptional: '(Opsional)',
  taFeaturesNote: 'Masukkan fitur utama dan poin jual produk untuk membantu AI menghasilkan saran optimasi yang lebih akurat',
  taAiOptimizationError: 'Layanan optimasi AI sementara tidak tersedia, silakan coba lagi nanti',

  // Common Tool Elements
  targetKeyword: 'Kata Kunci Target',
  targetKeywordPlaceholder: 'Masukkan kata kunci utama Anda',
  productTitle: 'Judul Produk',
  titlePlaceholder: 'Masukkan judul produk Amazon Anda di sini...',
  overallScore: 'Skor Keseluruhan',
  titleOptimizationScore: 'Skor optimasi judul berdasarkan praktik terbaik Amazon',
  outOf100: 'dari 100',
  issuesFound: 'Masalah Ditemukan',
  suggestions: 'Saran',
  startAnalyzingTitle: 'Mulai analisis judul Anda',
  enterTitleLeft: 'Masukkan judul produk Anda di sebelah kiri, dan kami akan memberikan saran optimasi yang detail.',
  titleOptimizedWell: 'Judul dioptimalkan dengan baik!',
  titleFollowsBestPractices: 'Judul Anda mengikuti praktik terbaik Amazon dan membantu meningkatkan peringkat pencarian.',

  // Title Messages
  titleEmpty: 'Judul tidak boleh kosong',
  titleTooLong: 'Judul melebihi panjang maksimal {max} karakter',
  titleTooShort: 'Judul terlalu pendek. Pertimbangkan menambahkan detail deskriptif lebih banyak.',
  keywordMissing: 'Kata kunci target tidak ditemukan dalam judul',
  keywordTooLate: 'Kata kunci target muncul terlalu lambat dalam judul. Pindahkan ke dekat awal.',

  // Judul Bagian Informasi Detail Alat
  taToolDetailInfoTitle: 'Informasi Detail Alat',
  taToolDetailInfoSubtitle: 'Pelajari lebih lanjut tentang cara menggunakan Penganalisis Judul dan praktik terbaik',

  // Bagian Pengenalan Alat
  taToolIntroTitle: 'Pengenalan Alat',
  taToolIntroContent: 'Penganalisis Judul adalah alat analisis judul profesional berdasarkan teknologi AI dan algoritma terbaru Amazon. Ini dapat menganalisis judul produk Anda secara mendalam, mengidentifikasi masalah potensial, dan memberikan saran optimasi yang cerdas.',

  taToolFeaturesTitle: 'Fitur Inti',
  taFeatureAiAnalysis: 'Analisis AI Cerdas',
  taFeatureAiAnalysisDesc: 'Analisis mendalam struktur judul, distribusi kata kunci, dan potensi optimasi menggunakan algoritma pembelajaran mesin',
  taFeatureComplianceCheck: 'Pemeriksaan Kepatuhan',
  taFeatureComplianceCheckDesc: 'Deteksi real-time apakah judul mematuhi persyaratan kebijakan terbaru Amazon 2025',
  taFeatureKeywordOptimization: 'Optimasi Kata Kunci',
  taFeatureKeywordOptimizationDesc: 'Analisis cerdas kepadatan, posisi, dan relevansi kata kunci dengan saran optimasi yang tepat',
  taFeatureScoreSystem: 'Sistem Penilaian',
  taFeatureScoreSystemDesc: 'Skor optimasi judul yang detail dan arah perbaikan berdasarkan metrik multi-dimensi',

  // Bagian Panduan Penggunaan
  taUsageGuideTitle: 'Panduan Penggunaan',
  taStep1Title: 'Langkah 1: Masukkan Judul Produk',
  taStep1Content: 'Tempelkan judul produk Amazon Anda di kotak input judul. Alat mendukung analisis judul hingga 200 karakter dan secara otomatis mendeteksi masalah panjang judul, format, dan struktur dasar.',
  taStep2Title: 'Langkah 2: Tetapkan Kata Kunci Target',
  taStep2Content: 'Masukkan kata kunci utama yang ingin Anda optimalkan. Ini harus berupa kata-kata yang paling mungkin digunakan pelanggan saat mencari produk Anda, alat akan menganalisis posisi dan kepadatan kata kunci ini dalam judul.',
  taStep3Title: 'Langkah 3: Tambahkan Fitur Produk (Opsional)',
  taStep3Content: 'Masukkan fitur inti dan poin jual produk, seperti "tahan air", "nirkabel", "pengisian cepat", dll. Informasi ini membantu AI memahami produk Anda dengan lebih baik dan menghasilkan saran optimasi yang lebih tepat.',
  taStep4Title: 'Langkah 4: Pilih Mode Analisis',
  taStep4Content: 'Pilih analisis dasar atau analisis yang ditingkatkan AI. Analisis dasar menyediakan pemeriksaan kepatuhan standar dan saran optimasi, analisis yang ditingkatkan AI menyediakan solusi optimasi cerdas yang lebih mendalam.',
  taStep5Title: 'Langkah 5: Tinjau Hasil Analisis',
  taStep5Content: 'Dapatkan laporan analisis terperinci termasuk skor keseluruhan, masalah spesifik, saran optimasi, dan saran judul yang diperbaiki. Optimalkan judul Anda secara bertahap berdasarkan saran.',

  // Bagian FAQ
  taFaqTitle: 'Pertanyaan Umum',
  taFaq1Question: 'Mengapa skor judul saya rendah?',
  taFaq1Answer: 'Skor judul berdasarkan beberapa dimensi: posisi kata kunci (25%), panjang judul (20%), kepadatan kata kunci (20%), kepatuhan (20%), keterbacaan (15%). Skor rendah biasanya menunjukkan bahwa elemen kunci tertentu perlu optimasi, silakan perbaiki berdasarkan saran spesifik.',

  taFaq2Question: 'Apa perbedaan antara analisis yang ditingkatkan AI dan analisis dasar?',
  taFaq2Answer: 'Analisis dasar menyediakan pemeriksaan kepatuhan standar dan saran optimasi dasar. Analisis yang ditingkatkan AI menggunakan algoritma pembelajaran mesin, dapat memahami fitur produk, menghasilkan saran judul yang lebih personal, dan menyediakan analisis pesaing serta wawasan tren pasar.',

  taFaq3Question: 'Bagaimana cara memilih kata kunci target?',
  taFaq3Answer: 'Pilih kata-kata yang paling mungkin dicari pelanggan, termasuk kategori produk, merek, fungsi inti, dll. Disarankan untuk memilih 2-4 kata kunci utama, hindari penumpukan berlebihan. Anda dapat merujuk pada saran pencarian Amazon dan judul pesaing.',

  taFaq4Question: 'Mengapa ada batasan penggunaan AI?',
  taFaq4Answer: 'Analisis AI memerlukan pemanggilan algoritma canggih dengan biaya komputasi tinggi. Batasan 5 kali per hari memastikan stabilitas layanan, mencegah penyalahgunaan, dan memastikan semua pengguna dapat menggunakan secara adil. Disarankan untuk melengkapi informasi dasar terlebih dahulu sebelum menggunakan fitur AI.',

  taFaq5Question: 'Apakah judul yang dioptimalkan pasti akan meningkatkan peringkat?',
  taFaq5Answer: 'Alat ini memberikan saran berdasarkan praktik terbaik Amazon dan kasus sukses, dapat secara signifikan meningkatkan kualitas judul. Namun, peringkat juga dipengaruhi oleh kualitas produk, harga, ulasan, iklan, dan faktor lainnya. Disarankan untuk digunakan bersama dengan strategi optimasi lainnya.',

  taFaq6Question: 'Bagaimana memahami hasil pemeriksaan kepatuhan?',
  taFaq6Answer: 'Pemeriksaan kepatuhan berdasarkan kebijakan terbaru Amazon 2025, termasuk batasan karakter, kata-kata terlarang, persyaratan format, dll. Merah menunjukkan pelanggaran yang harus diperbaiki, kuning menunjukkan optimasi yang disarankan, hijau menunjukkan sesuai dengan persyaratan.',

  // Bagian Praktik Terbaik
  taBestPracticesTitle: 'Rekomendasi Praktik Terbaik',
  taBestPractice1: 'Tempatkan kata kunci terpenting dalam 50 karakter pertama judul untuk memastikan tampilan lengkap dalam hasil pencarian mobile',
  taBestPractice2: 'Gunakan struktur bahasa alami, hindari penumpukan kata kunci, pertahankan keterbacaan dan profesionalisme judul',
  taBestPractice3: 'Sertakan nama merek, jenis produk, fitur inti, dan poin jual utama untuk membangun hierarki informasi yang lengkap',
  taBestPractice4: 'Gunakan alat secara teratur untuk menganalisis judul pesaing, memahami tren pasar dan peluang optimasi',
  taBestPractice5: 'Kombinasikan dengan pengujian A/B untuk memverifikasi kinerja versi judul yang berbeda dan terus mengoptimalkan strategi judul',
  taBestPractice6: 'Perhatikan pembaruan kebijakan Amazon dan sesuaikan judul tepat waktu untuk mempertahankan kepatuhan dan daya saing',

  // Kunci terjemahan yang hilang
  taOptimizationSuggestions: 'Saran Optimasi',
  addMoreDetails: 'Tambahkan Lebih Banyak Detail',
  addMoreDescriptiveWords: 'Tambahkan Lebih Banyak Kata Deskriptif',

  // Kunci terjemahan dari kotak merah di gambar
  taBasedOn2025Policy: 'Berdasarkan Kebijakan 2025',
  taSeoScore: 'Skor SEO',
  taComplianceScore: 'Skor Kepatuhan',
  taMarketAdaptability: 'Adaptabilitas Pasar',
  taCategoryOptimization: 'Optimasi Kategori',
  taKeywordDensityLabel: 'Kepadatan Kata Kunci',
  taReadabilityLabel: 'Keterbacaan',
  taKeywordDensityHigh: 'Kepadatan kata kunci terlalu tinggi',
  taBrandInfoSuggestion: 'Sarankan menambahkan informasi merek',
  taMobileDisplaySuggestion: 'Sarankan mengoptimalkan tampilan mobile',
  taComplianceCheck: 'Pemeriksaan Kepatuhan',
  taComplianceAnalysisReport: 'Laporan Analisis Kepatuhan',
  taComplianceStatus: 'Status Kepatuhan',
  taCompliant: 'Patuh',
  taNeedsModification: 'Perlu Modifikasi',
  taComplianceReminder: 'Pengingat Kepatuhan',
  taAiComplianceError: 'Pemeriksaan kepatuhan AI gagal',

  // Kunci terjemahan lain yang mungkin hilang
  tooManyCapitalWords: 'Terlalu banyak kata kapital dalam judul',

  // Teks hard-coded dalam komponen UsageLimitInfo
  taAiUsageStatus: 'Status Penggunaan Optimasi AI',
  taTodayUsed: 'Digunakan Hari Ini',
  taTodayUsageProgress: 'Progres Penggunaan Hari Ini',
  taResetTomorrow: 'Besok 00:00',

  // Pesan kesalahan respons AI
  taAiResponseParseError: 'Parsing respons AI gagal',
  taManualComplianceCheck: 'Silakan periksa kepatuhan secara manual',

  // Saran pemasaran
  taMarketingAdvice: 'Saran Strategi Pemasaran',

  // Deskripsi fitur analisis dasar
  taBasicAnalysisDescription: 'Fitur analisis dasar mencakup penilaian judul lengkap, deteksi masalah, dan saran optimasi tanpa batasan penggunaan.',

  // Terjemahan terkait batasan penggunaan
  taUsageLimitExceeded: 'Batas penggunaan hari ini tercapai',
  taDailyLimitReached: 'Anda telah menghabiskan kuota optimasi AI hari ini (5 kali/hari)',
  taNextResetTime: 'Reset besok',
  taBasicAnalysisAvailable: 'Fitur analisis dasar masih tersedia',
  taRemainingUsage: 'Sisa penggunaan hari ini',
  taLimitReason: 'Mengapa ada batasan penggunaan?',
  taLimitExplanation1: 'Optimasi AI memerlukan algoritma canggih dengan biaya tinggi',
  taLimitExplanation2: 'Batasan penggunaan memastikan stabilitas layanan dan mencegah penyalahgunaan',
  taLimitExplanation3: 'Mendorong pengguna untuk melengkapi informasi dasar sebelum menggunakan optimasi AI',

  // Deteksi kata terlarang
  forbiddenWords: 'Kata terlarang terdeteksi: {words}',
  taLimitExplanation4: 'Memastikan semua pengguna dapat mengakses fitur AI secara adil',
  taOptimizationTips: 'Tips Optimasi',
  taOptimizationTip1: 'Lengkapi informasi kata kunci target dan fitur produk terlebih dahulu',
  taOptimizationTip2: 'Gunakan fitur analisis dasar untuk memeriksa masalah judul',
  taOptimizationTip3: 'Konfirmasi kebutuhan optimasi AI sebelum menggunakan kuota berharga',
  taUnderstanding: 'Terima kasih atas pengertian dan dukungan Anda!',
};
