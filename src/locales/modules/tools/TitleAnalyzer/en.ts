// Title Smart Optimizer translations - English
export const titleAnalyzerEn = {
  // Title Analyzer
  titleAnalyzer: 'Title Smart Optimizer',
  titleAnalyzerDesc: 'AI-powered analysis and optimization of your Amazon product titles for better search ranking and conversion.',
  taTargetKeywordPlaceholder: 'e.g: wireless headphones, bluetooth speaker',
  taProductFeaturesPlaceholder: 'e.g: noise cancelling, waterproof, long battery, fast charging',
  taToolDescription: 'Smart title analysis and optimization tool based on Amazon\'s latest 2025 policies',
  taMaxLength: 'Max Length',
  taCharacters: 'Characters',
  taStartAnalyzingDesc: 'Enter your product title to get professional optimization suggestions and compliance checks',
  taAiOptimizationSuggestions: 'AI Optimization Suggestions',
  taOptimizedTitleSuggestions: 'Optimized Title Suggestions',
  taWords: 'Words',
  taKeywordSuggestions: 'Keyword Suggestions',
  taComplianceScoreLabel: 'Compliance Score',
  taModificationSuggestions: 'Modification Suggestions',
  taTitleStatistics: 'Title Statistics',
  taOption: 'Option',
  taViolationItems: 'Violation Items',
  taAiSmartOptimization: 'AI Smart Optimization',
  taAiServiceTip: 'AI Service Tip',
  taTitleOptimizedWell: 'Title Optimized Excellently!',
  taTitleOptimizedDesc: 'Your title follows Amazon\'s 2025 best practices and helps improve search ranking and conversion rates.',
  taUse: 'Use',
  taImprovementPoints: 'Improvement Points',
  taBasicVersion: 'Basic Version',
  taKeywordInputNote: 'Enter main keywords for more precise analysis results',
  taStartAnalyzing: 'Start Analyzing Title',
  taAiEnhanced: 'AI Enhanced',
  taProductFeatures: 'Product Features',
  taOptional: '(Optional)',
  taFeaturesNote: 'Enter main product features and selling points to help AI generate more precise optimization suggestions',
  taAiOptimizationError: 'AI optimization service temporarily unavailable, please try again later',

  // AI usage limit related
  taUsageLimitTitle: 'AI Usage Limit',
  taUsageLimitExceeded: 'Daily usage limit reached',
  taDailyLimitReached: 'You have used up your daily AI optimization quota (5 times/day)',
  taLimitReason: 'Why usage limits?',
  taLimitExplanation1: '• AI optimization requires advanced algorithms with higher costs',
  taLimitExplanation2: '• Limits ensure service stability and prevent abuse',
  taLimitExplanation3: '• Encourages users to refine basic info before using AI optimization',
  taLimitExplanation4: '• Ensures fair access to AI features for all users',
  taRemainingUsage: 'Remaining uses today',
  taNextResetTime: 'Resets tomorrow',
  taBasicAnalysisAvailable: 'Basic analysis features remain fully available',
  taOptimizationTips: 'Optimization Tips',
  taOptimizationTip1: 'First complete target keywords and product features',
  taOptimizationTip2: 'Use basic analysis to check title issues',
  taOptimizationTip3: 'Confirm need for AI optimization before using precious quota',
  taUnderstanding: 'Thank you for your understanding and support!',

  // Common Tool Elements
  targetKeyword: 'Target Keyword',
  targetKeywordPlaceholder: 'Enter your main keyword',
  productTitle: 'Product Title',
  titlePlaceholder: 'Enter your Amazon product title here...',
  overallScore: 'Overall Score',
  titleOptimizationScore: 'Title optimization score based on Amazon best practices',
  outOf100: 'out of 100',
  issuesFound: 'Issues Found',
  suggestions: 'Suggestions',
  startAnalyzingTitle: 'Start analyzing your title',
  enterTitleLeft: 'Enter your product title on the left, and we\'ll provide detailed optimization suggestions.',
  titleOptimizedWell: 'Title optimized well!',
  titleFollowsBestPractices: 'Your title follows Amazon best practices and helps improve search ranking.',

  // Title Messages
  titleEmpty: 'Title cannot be empty',
  titleTooLong: 'Title exceeds maximum length of {max} characters',
  titleTooShort: 'Title is too short. Consider adding more descriptive details.',
  keywordMissing: 'Target keyword not found in title',
  keywordTooLate: 'Target keyword appears too late in title. Move it closer to the beginning.',

  // Tool Detail Info Section Titles
  taToolDetailInfoTitle: 'Tool Detailed Information',
  taToolDetailInfoSubtitle: 'Learn more about how to use the Title Analyzer and best practices',

  // Tool Introduction Section
  taToolIntroTitle: 'Tool Introduction',
  taToolIntroContent: 'The Title Analyzer is a professional title analysis tool based on AI technology and Amazon\'s latest algorithms. It can deeply analyze your product titles, identify potential issues, and provide intelligent optimization suggestions. The tool combines Amazon\'s latest 2025 policy requirements, SEO best practices, and big data analysis to help sellers create high-conversion product titles and achieve better search rankings and sales performance in the competitive Amazon marketplace.',

  taToolFeaturesTitle: 'Core Features',
  taFeatureAiAnalysis: 'AI Smart Analysis',
  taFeatureAiAnalysisDesc: 'Deep analysis of title structure, keyword distribution, and optimization potential using machine learning algorithms',
  taFeatureComplianceCheck: 'Compliance Check',
  taFeatureComplianceCheckDesc: 'Real-time detection of whether titles comply with Amazon\'s latest 2025 policy requirements',
  taFeatureKeywordOptimization: 'Keyword Optimization',
  taFeatureKeywordOptimizationDesc: 'Intelligent analysis of keyword density, position, and relevance with precise optimization suggestions',
  taFeatureScoreSystem: 'Scoring System',
  taFeatureScoreSystemDesc: 'Detailed title optimization scores and improvement directions based on multi-dimensional metrics',

  // Usage Guide Section
  taUsageGuideTitle: 'Usage Guide',
  taStep1Title: 'Step 1: Enter Product Title',
  taStep1Content: 'Paste your Amazon product title in the title input box. The tool supports analysis of titles up to 200 characters and automatically detects title length, format, and basic structural issues.',
  taStep2Title: 'Step 2: Set Target Keywords',
  taStep2Content: 'Enter the main keywords you want to optimize. These should be the words customers are most likely to use when searching for your product. The tool will analyze the position and density of these keywords in the title.',
  taStep3Title: 'Step 3: Add Product Features (Optional)',
  taStep3Content: 'Enter core product features and selling points, such as "waterproof," "wireless," "fast charging," etc. This information helps AI better understand your product and generate more precise optimization suggestions.',
  taStep4Title: 'Step 4: Choose Analysis Mode',
  taStep4Content: 'Select basic analysis or AI-enhanced analysis. Basic analysis provides standard compliance checks and optimization suggestions, while AI-enhanced analysis offers deeper intelligent optimization solutions.',
  taStep5Title: 'Step 5: Review Analysis Results',
  taStep5Content: 'Get a detailed analysis report including overall score, specific issues, optimization suggestions, and improved title recommendations. Optimize your title step by step based on the suggestions.',

  // FAQ Section
  taFaqTitle: 'Frequently Asked Questions',
  taFaq1Question: 'Why is my title score low?',
  taFaq1Answer: 'Title scores are based on multiple dimensions: keyword position (25%), title length (20%), keyword density (20%), compliance (20%), readability (15%). Low scores usually indicate that certain key elements need optimization. Please improve based on specific suggestions.',

  taFaq2Question: 'What\'s the difference between AI-enhanced and basic analysis?',
  taFaq2Answer: 'Basic analysis provides standard compliance checks and basic optimization suggestions. AI-enhanced analysis uses machine learning algorithms to understand product features, generate more personalized title suggestions, and provide competitor analysis and market trend insights.',

  taFaq3Question: 'How should I choose target keywords?',
  taFaq3Answer: 'Choose words customers are most likely to search for, including product categories, brands, core functions, etc. It\'s recommended to select 2-4 main keywords and avoid over-stuffing. You can refer to Amazon search suggestions and competitor titles.',

  taFaq4Question: 'Why is there a limit on AI usage?',
  taFaq4Answer: 'AI analysis requires calling advanced algorithms with high computational costs. The daily limit of 2 uses ensures service stability, prevents abuse, and ensures all users can use it fairly. It\'s recommended to complete basic information before using AI features.',

  taFaq5Question: 'Will optimized titles definitely improve rankings?',
  taFaq5Answer: 'The tool provides suggestions based on Amazon best practices and successful cases, which can significantly improve title quality. However, rankings are also affected by product quality, price, reviews, advertising, and other factors. It\'s recommended to use in combination with other optimization strategies.',

  taFaq6Question: 'How to understand compliance check results?',
  taFaq6Answer: 'Compliance checks are based on Amazon\'s latest 2025 policies, including character limits, prohibited words, format requirements, etc. Red indicates violations that must be fixed, yellow indicates recommended optimizations, and green indicates compliance.',

  // Best Practices Section
  taBestPracticesTitle: 'Best Practice Recommendations',
  taBestPractice1: 'Place the most important keywords within the first 50 characters of the title to ensure complete display in mobile search results',
  taBestPractice2: 'Use natural language structure, avoid keyword stuffing, and maintain title readability and professionalism',
  taBestPractice3: 'Include brand name, product type, core features, and main selling points to build a complete information hierarchy',
  taBestPractice4: 'Regularly use the tool to analyze competitor titles to understand market trends and optimization opportunities',
  taBestPractice5: 'Combine A/B testing to verify the performance of different title versions and continuously optimize title strategies',
  taBestPractice6: 'Pay attention to Amazon policy updates and adjust titles timely to maintain compliance and competitiveness',

  // Missing translation keys
  taOptimizationSuggestions: 'Optimization Suggestions',
  addMoreDetails: 'Add More Details',
  addMoreDescriptiveWords: 'Add More Descriptive Words',

  // Translation keys from red boxes in image
  taBasedOn2025Policy: 'Based on 2025 Policy',
  taSeoScore: 'SEO Score',
  taComplianceScore: 'Compliance Score',
  taMarketAdaptability: 'Market Adaptability',
  taCategoryOptimization: 'Category Optimization',
  taKeywordDensityLabel: 'Keyword Density',
  taReadabilityLabel: 'Readability',
  taKeywordDensityHigh: 'Keyword density too high',
  taBrandInfoSuggestion: 'Suggest adding brand information',
  taMobileDisplaySuggestion: 'Suggest optimizing mobile display',
  taComplianceCheck: 'Compliance Check',
  taComplianceAnalysisReport: 'Compliance Analysis Report',
  taComplianceStatus: 'Compliance Status',
  taCompliant: 'Compliant',
  taNeedsModification: 'Needs Modification',
  taComplianceReminder: 'Compliance Reminder',
  taAiComplianceError: 'AI compliance check failed',

  // Other potentially missing translation keys
  tooManyCapitalWords: 'Too many capital words in title',

  // Hard-coded text in UsageLimitInfo component
  taAiUsageStatus: 'AI Optimization Usage',
  taTodayUsed: 'Used Today',
  taTodayUsageProgress: 'Today\'s Usage Progress',
  taResetTomorrow: 'Tomorrow 00:00',

  // AI response error messages
  taAiResponseParseError: 'AI response parsing failed',
  taManualComplianceCheck: 'Please check compliance manually',

  // Marketing advice
  taMarketingAdvice: 'Marketing Strategy Advice',

  // Basic analysis description
  taBasicAnalysisDescription: 'Basic analysis includes complete title scoring, issue detection, and optimization suggestions with no usage limits.',

  // Forbidden words detection
  forbiddenWords: 'Forbidden words detected: {words}',
};
