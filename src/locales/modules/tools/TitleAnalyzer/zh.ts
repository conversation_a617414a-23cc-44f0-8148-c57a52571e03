// 标题智能优化器翻译 - 简体中文
export const titleAnalyzerZh = {
  // Title Analyzer
  titleAnalyzer: '标题智能优化器',
  titleAnalyzerDesc: 'AI智能分析和优化您的亚马逊产品标题，提升搜索排名和转化率。',
  taTargetKeywordPlaceholder: '例如: wireless headphones, bluetooth speaker',
  taProductFeaturesPlaceholder: '例如: 降噪, 防水, 长续航, 快充',
  taToolDescription: '基于2025年亚马逊最新政策的智能标题分析与优化工具',
  taMaxLength: '最大长度',
  taCharacters: '字符',
  taStartAnalyzingDesc: '输入产品标题，获得专业的优化建议和合规性检查',
  taAiOptimizationSuggestions: 'AI优化建议',
  taOptimizedTitleSuggestions: '优化后的标题建议',
  taWords: '单词',
  taKeywordSuggestions: '关键词建议',
  taComplianceScoreLabel: '合规评分',
  taModificationSuggestions: '修改建议',
  taTitleStatistics: '标题统计',
  taOption: '选项',
  taViolationItems: '违规项目',
  taAiSmartOptimization: 'AI智能优化',
  taAiServiceTip: 'AI服务提示',
  taTitleOptimizedWell: '标题优化优秀！',
  taTitleOptimizedDesc: '您的标题符合2025年亚马逊最佳实践，有助于提高搜索排名和转化率。',
  taUse: '使用',
  taImprovementPoints: '改进要点',
  taBasicVersion: '基础版本',
  taKeywordInputNote: '输入主要关键词以获得更精准的分析结果',
  taStartAnalyzing: '开始分析标题',
  taAiEnhanced: 'AI增强版',
  taProductFeatures: '产品特性',
  taOptional: '（可选）',
  taFeaturesNote: '输入产品的主要特性和卖点，帮助AI生成更精准的优化建议',
  taAiOptimizationError: 'AI优化服务暂时不可用，请稍后重试',

  // AI使用限制相关
  taUsageLimitTitle: 'AI使用限制',
  taUsageLimitExceeded: '今日使用次数已达上限',
  taDailyLimitReached: '您今日的AI优化次数已用完（5次/天）',
  taLimitReason: '为什么有使用限制？',
  taLimitExplanation1: '• AI优化需要调用高级算法，成本较高',
  taLimitExplanation2: '• 限制使用确保服务稳定，避免滥用',
  taLimitExplanation3: '• 鼓励用户先完善基础信息，再使用AI优化',
  taLimitExplanation4: '• 保证所有用户都能公平使用AI功能',
  taRemainingUsage: '今日剩余次数',
  taNextResetTime: '明日重置',
  taBasicAnalysisAvailable: '基础分析功能仍可正常使用',
  taOptimizationTips: '优化建议',
  taOptimizationTip1: '先完善目标关键词和产品特性信息',
  taOptimizationTip2: '使用基础分析功能检查标题问题',
  taOptimizationTip3: '确认需要AI优化后再使用宝贵的次数',
  taUnderstanding: '感谢您的理解与支持！',

  // Common Tool Elements (保留在工具中的特有元素)
  targetKeyword: '目标关键词',
  targetKeywordPlaceholder: '输入您的主要关键词',
  productTitle: '产品标题',
  titlePlaceholder: '在此输入您的亚马逊产品标题...',
  overallScore: '总体评分',
  titleOptimizationScore: '基于亚马逊最佳实践的标题优化评分',
  outOf100: '满分100',
  issuesFound: '发现的问题',
  suggestions: '建议',
  startAnalyzingTitle: '开始分析您的标题',
  enterTitleLeft: '在左侧输入您的产品标题，我们将为您提供详细的优化建议。',
  titleOptimizedWell: '标题优化良好！',
  titleFollowsBestPractices: '您的标题符合亚马逊最佳实践，有助于提高搜索排名。',

  // Title Messages
  titleEmpty: '标题不能为空',
  titleTooLong: '标题超过最大长度{max}字符',
  titleTooShort: '标题太短。考虑添加更多描述性细节。',
  keywordMissing: '标题中未找到目标关键词',
  keywordTooLate: '目标关键词在标题中出现太晚。将其移到开头附近。',

  // 工具详细信息部分标题
  taToolDetailInfoTitle: '工具详细信息',
  taToolDetailInfoSubtitle: '了解更多关于标题智能优化器的使用方法和最佳实践',

  // 工具介绍部分
  taToolIntroTitle: '工具介绍',
  taToolIntroContent: '标题智能优化器是一款基于AI技术和Amazon最新算法的专业标题分析工具。它能够深度分析您的产品标题，识别潜在问题，并提供智能优化建议。工具结合了2025年Amazon最新政策要求、SEO最佳实践和大数据分析，帮助卖家创建高转化率的产品标题，在竞争激烈的Amazon市场中获得更好的搜索排名和销售表现。',

  taToolFeaturesTitle: '核心功能',
  taFeatureAiAnalysis: 'AI智能分析',
  taFeatureAiAnalysisDesc: '基于机器学习算法深度分析标题结构、关键词分布和优化潜力',
  taFeatureComplianceCheck: '合规性检查',
  taFeatureComplianceCheckDesc: '实时检测标题是否符合Amazon 2025年最新政策要求',
  taFeatureKeywordOptimization: '关键词优化',
  taFeatureKeywordOptimizationDesc: '智能分析关键词密度、位置和相关性，提供精准优化建议',
  taFeatureScoreSystem: '评分系统',
  taFeatureScoreSystemDesc: '基于多维度指标提供详细的标题优化评分和改进方向',

  // 使用指南部分
  taUsageGuideTitle: '使用指南',
  taStep1Title: '第一步：输入产品标题',
  taStep1Content: '在标题输入框中粘贴您的Amazon产品标题。工具支持最长200字符的标题分析，会自动检测标题长度、格式和基础结构问题。',
  taStep2Title: '第二步：设置目标关键词',
  taStep2Content: '输入您希望优化的主要关键词。这些关键词应该是客户搜索您产品时最可能使用的词汇，工具会分析这些关键词在标题中的位置和密度。',
  taStep3Title: '第三步：添加产品特性（可选）',
  taStep3Content: '输入产品的核心特性和卖点，如"防水"、"无线"、"快充"等。这些信息帮助AI更好地理解您的产品，生成更精准的优化建议。',
  taStep4Title: '第四步：选择分析模式',
  taStep4Content: '选择基础分析或AI增强分析。基础分析提供标准的合规性检查和优化建议，AI增强分析提供更深度的智能优化方案。',
  taStep5Title: '第五步：查看分析结果',
  taStep5Content: '获得详细的分析报告，包括总体评分、具体问题、优化建议和改进后的标题建议。根据建议逐步优化您的标题。',

  // FAQ部分
  taFaqTitle: '常见问题',
  taFaq1Question: '为什么我的标题评分较低？',
  taFaq1Answer: '标题评分基于多个维度：关键词位置(25%)、标题长度(20%)、关键词密度(20%)、合规性(20%)、可读性(15%)。低分通常表示某些关键要素需要优化，请根据具体建议进行改进。',

  taFaq2Question: 'AI增强分析和基础分析有什么区别？',
  taFaq2Answer: '基础分析提供标准的合规性检查和基础优化建议。AI增强分析使用机器学习算法，能够理解产品特性，生成更个性化的标题建议，并提供竞争对手分析和市场趋势洞察。',

  taFaq3Question: '目标关键词应该如何选择？',
  taFaq3Answer: '选择客户最可能搜索的词汇，包括产品类别、品牌、核心功能等。建议选择2-4个主要关键词，避免过度堆砌。可以参考Amazon搜索建议和竞争对手标题。',

  taFaq4Question: '为什么有AI使用次数限制？',
  taFaq4Answer: 'AI分析需要调用高级算法，计算成本较高。每日5次的限制确保服务稳定，避免滥用，同时保证所有用户都能公平使用。建议先完善基础信息再使用AI功能。',

  taFaq5Question: '优化后的标题一定能提高排名吗？',
  taFaq5Answer: '工具基于Amazon最佳实践和成功案例提供建议，能显著提高标题质量。但排名还受产品质量、价格、评价、广告等多因素影响。建议结合其他优化策略使用。',

  taFaq6Question: '如何理解合规性检查结果？',
  taFaq6Answer: '合规性检查基于Amazon 2025年最新政策，包括字符限制、禁用词汇、格式要求等。红色表示违规必须修改，黄色表示建议优化，绿色表示符合要求。',

  // 最佳实践部分
  taBestPracticesTitle: '最佳实践建议',
  taBestPractice1: '将最重要的关键词放在标题前50个字符内，确保在移动端搜索结果中完整显示',
  taBestPractice2: '使用自然的语言结构，避免关键词堆砌，保持标题的可读性和专业性',
  taBestPractice3: '包含品牌名、产品类型、核心特性和主要卖点，构建完整的信息层次',
  taBestPractice4: '定期使用工具分析竞争对手标题，了解市场趋势和优化机会',
  taBestPractice5: '结合A/B测试验证不同标题版本的表现，持续优化标题策略',
  taBestPractice6: '关注Amazon政策更新，及时调整标题以保持合规性和竞争力',

  // 缺失的翻译键
  taOptimizationSuggestions: '优化建议',
  addMoreDetails: '添加更多细节',
  addMoreDescriptiveWords: '添加更多描述性词汇',

  // 图片中标红的翻译键
  taBasedOn2025Policy: '基于2025政策',
  taSeoScore: 'SEO评分',
  taComplianceScore: '合规评分',
  taMarketAdaptability: '市场适应性',
  taCategoryOptimization: '类别优化',
  taKeywordDensityLabel: '关键词密度',
  taReadabilityLabel: '可读性',
  taKeywordDensityHigh: '关键词密度过高',
  taBrandInfoSuggestion: '建议添加品牌信息',
  taMobileDisplaySuggestion: '建议优化移动端显示',
  taComplianceCheck: '合规检查',
  taComplianceAnalysisReport: '合规分析报告',
  taComplianceStatus: '合规状态',
  taCompliant: '合规',
  taNeedsModification: '需要修改',
  taComplianceReminder: '合规提醒',
  taAiComplianceError: 'AI合规检查失败',

  // 其他可能缺失的翻译键
  tooManyCapitalWords: '标题中大写单词过多',

  // UsageLimitInfo组件中的硬编码文本
  taAiUsageStatus: 'AI优化使用情况',
  taTodayUsed: '今日已用',
  taTodayUsageProgress: '今日使用进度',
  taResetTomorrow: '明天 00:00',

  // AI响应错误信息
  taAiResponseParseError: 'AI响应解析失败',
  taManualComplianceCheck: '请手动检查合规性',

  // 营销建议
  taMarketingAdvice: '营销策略建议',

  // 基础分析功能描述
  taBasicAnalysisDescription: '基础分析功能包含完整的标题评分、问题检测和优化建议，无使用限制。',

  // 禁用词汇检测
  forbiddenWords: '检测到禁用词汇: {words}',
};
