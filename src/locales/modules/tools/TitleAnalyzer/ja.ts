// タイトル分析器翻訳 - 日本語
export const titleAnalyzerJa = {
  // Title Analyzer
  titleAnalyzer: 'タイトル分析器',
  titleAnalyzerDesc: 'Amazonの商品タイトルを分析・最適化し、最大の可視性とコンプライアンスを実現します。',
  taTargetKeywordPlaceholder: '例: wireless headphones, bluetooth speaker',
  taProductFeaturesPlaceholder: '例: ノイズキャンセリング, 防水, 長時間バッテリー, 急速充電',
  taToolDescription: '2025年Amazon最新ポリシーに基づくスマートタイトル分析・最適化ツール',
  taMaxLength: '最大長',
  taCharacters: '文字',
  taStartAnalyzingDesc: '商品タイトルを入力して、プロの最適化提案とコンプライアンスチェックを取得',
  taAiOptimizationSuggestions: 'AI最適化提案',
  taOptimizedTitleSuggestions: '最適化されたタイトル提案',
  taWords: '単語',
  taKeywordSuggestions: 'キーワード提案',
  taComplianceScoreLabel: 'コンプライアンススコア',
  taModificationSuggestions: '修正提案',
  taTitleStatistics: 'タイトル統計',
  taOption: 'オプション',
  taViolationItems: '違反項目',
  taAiSmartOptimization: 'AIスマート最適化',
  taAiServiceTip: 'AIサービスヒント',
  taTitleOptimizedWell: 'タイトルが優秀に最適化されました！',
  taTitleOptimizedDesc: 'あなたのタイトルは2025年Amazonベストプラクティスに従っており、検索ランキングとコンバージョン率の向上に役立ちます。',
  taUse: '使用',
  taImprovementPoints: '改善ポイント',
  taBasicVersion: 'ベーシック版',
  taKeywordInputNote: 'より正確な分析結果を得るために主要キーワードを入力してください',
  taStartAnalyzing: 'タイトル分析を開始',
  taAiEnhanced: 'AI強化版',
  taProductFeatures: '商品特徴',
  taOptional: '（オプション）',
  taFeaturesNote: '商品の主要特徴とセールスポイントを入力して、AIがより正確な最適化提案を生成できるようにします',
  taAiOptimizationError: 'AI最適化サービスが一時的に利用できません。後でもう一度お試しください',

  // Common Tool Elements
  targetKeyword: 'ターゲットキーワード',
  targetKeywordPlaceholder: 'メインキーワードを入力',
  productTitle: '商品タイトル',
  titlePlaceholder: 'ここにAmazon商品タイトルを入力してください...',
  overallScore: '総合スコア',
  titleOptimizationScore: 'Amazonベストプラクティスに基づくタイトル最適化スコア',
  outOf100: '100点満点',
  issuesFound: '発見された問題',
  suggestions: '提案',
  startAnalyzingTitle: 'タイトルの分析を開始',
  enterTitleLeft: '左側に商品タイトルを入力すると、詳細な最適化提案を提供します。',
  titleOptimizedWell: 'タイトルがよく最適化されています！',
  titleFollowsBestPractices: 'あなたのタイトルはAmazonベストプラクティスに従っており、検索ランキングの向上に役立ちます。',

  // Title Messages
  titleEmpty: 'タイトルは空にできません',
  titleTooLong: 'タイトルが最大長{max}文字を超えています',
  titleTooShort: 'タイトルが短すぎます。より詳細な説明を追加することを検討してください。',
  keywordMissing: 'タイトルにターゲットキーワードが見つかりません',
  keywordTooLate: 'ターゲットキーワードがタイトルの後半に表示されています。先頭近くに移動してください。',

  // ツール詳細情報部分のタイトル
  taToolDetailInfoTitle: 'ツール詳細情報',
  taToolDetailInfoSubtitle: 'タイトル分析器の使用方法とベストプラクティスについて詳しく学ぶ',

  // ツール紹介部分
  taToolIntroTitle: 'ツール紹介',
  taToolIntroContent: 'タイトル分析器は、AI技術とAmazonの最新アルゴリズムに基づく専門的なタイトル分析ツールです。製品タイトルを深く分析し、潜在的な問題を特定し、インテリジェントな最適化提案を提供します。',

  taToolFeaturesTitle: 'コア機能',
  taFeatureAiAnalysis: 'AIスマート分析',
  taFeatureAiAnalysisDesc: '機械学習アルゴリズムを使用してタイトル構造、キーワード分布、最適化ポテンシャルを深く分析',
  taFeatureComplianceCheck: 'コンプライアンスチェック',
  taFeatureComplianceCheckDesc: 'タイトルがAmazonの2025年最新ポリシー要件に準拠しているかをリアルタイムで検出',
  taFeatureKeywordOptimization: 'キーワード最適化',
  taFeatureKeywordOptimizationDesc: 'キーワード密度、位置、関連性を智能分析し、精密な最適化提案を提供',
  taFeatureScoreSystem: 'スコアリングシステム',
  taFeatureScoreSystemDesc: '多次元指標に基づく詳細なタイトル最適化スコアと改善方向を提供',

  // 使用ガイド部分
  taUsageGuideTitle: '使用ガイド',
  taStep1Title: 'ステップ1：商品タイトルを入力',
  taStep1Content: 'タイトル入力ボックスにAmazon商品タイトルを貼り付けてください。ツールは最大200文字のタイトル分析をサポートし、タイトル長、フォーマット、基本構造の問題を自動検出します。',
  taStep2Title: 'ステップ2：ターゲットキーワードを設定',
  taStep2Content: '最適化したい主要キーワードを入力してください。これらは顧客が商品を検索する際に最も使用する可能性の高い語彙で、ツールはこれらのキーワードのタイトル内での位置と密度を分析します。',
  taStep3Title: 'ステップ3：商品特徴を追加（オプション）',
  taStep3Content: '「防水」「ワイヤレス」「急速充電」など、商品の核心特徴とセールスポイントを入力してください。この情報により、AIがより良く商品を理解し、より精密な最適化提案を生成できます。',
  taStep4Title: 'ステップ4：分析モードを選択',
  taStep4Content: '基本分析またはAI強化分析を選択してください。基本分析は標準的なコンプライアンスチェックと最適化提案を提供し、AI強化分析はより深いインテリジェント最適化ソリューションを提供します。',
  taStep5Title: 'ステップ5：分析結果を確認',
  taStep5Content: '総合スコア、具体的な問題、最適化提案、改善されたタイトル提案を含む詳細な分析レポートを取得します。提案に基づいてタイトルを段階的に最適化してください。',

  // FAQ部分
  taFaqTitle: 'よくある質問',
  taFaq1Question: 'なぜタイトルスコアが低いのですか？',
  taFaq1Answer: 'タイトルスコアは複数の次元に基づいています：キーワード位置(25%)、タイトル長(20%)、キーワード密度(20%)、コンプライアンス(20%)、可読性(15%)。低スコアは通常、特定の重要な要素が最適化を必要としていることを示します。具体的な提案に基づいて改善してください。',

  taFaq2Question: 'AI強化分析と基本分析の違いは何ですか？',
  taFaq2Answer: '基本分析は標準的なコンプライアンスチェックと基本的な最適化提案を提供します。AI強化分析は機械学習アルゴリズムを使用し、商品特徴を理解し、よりパーソナライズされたタイトル提案を生成し、競合他社分析と市場トレンド洞察を提供します。',

  taFaq3Question: 'ターゲットキーワードはどのように選択すべきですか？',
  taFaq3Answer: '顧客が最も検索する可能性の高い語彙を選択してください。商品カテゴリ、ブランド、核心機能などを含みます。2-4個の主要キーワードを選択し、過度な詰め込みを避けることをお勧めします。Amazon検索提案と競合他社のタイトルを参考にできます。',

  taFaq4Question: 'なぜAI使用回数制限があるのですか？',
  taFaq4Answer: 'AI分析は高度なアルゴリズムを呼び出す必要があり、計算コストが高いです。1日5回の制限により、サービスの安定性を確保し、乱用を防ぎ、すべてのユーザーが公平に使用できることを保証します。AI機能を使用する前に基本情報を完善することをお勧めします。',

  taFaq5Question: '最適化されたタイトルは必ずランキングを向上させますか？',
  taFaq5Answer: 'ツールはAmazonのベストプラクティスと成功事例に基づいて提案を提供し、タイトル品質を大幅に向上させることができます。しかし、ランキングは商品品質、価格、レビュー、広告などの多要因の影響を受けます。他の最適化戦略と組み合わせて使用することをお勧めします。',

  taFaq6Question: 'コンプライアンスチェック結果をどのように理解すべきですか？',
  taFaq6Answer: 'コンプライアンスチェックはAmazonの2025年最新ポリシーに基づいており、文字制限、禁止語彙、フォーマット要件などを含みます。赤色は違反で修正が必要、黄色は最適化推奨、緑色は要件に適合していることを示します。',

  // ベストプラクティス部分
  taBestPracticesTitle: 'ベストプラクティス推奨',
  taBestPractice1: '最も重要なキーワードをタイトルの最初の50文字以内に配置し、モバイル検索結果で完全に表示されることを確保する',
  taBestPractice2: '自然な言語構造を使用し、キーワードの詰め込みを避け、タイトルの可読性と専門性を保つ',
  taBestPractice3: 'ブランド名、商品タイプ、核心特徴、主要セールスポイントを含め、完全な情報階層を構築する',
  taBestPractice4: '定期的にツールを使用して競合他社のタイトルを分析し、市場トレンドと最適化機会を理解する',
  taBestPractice5: 'A/Bテストを組み合わせて異なるタイトルバージョンの性能を検証し、タイトル戦略を継続的に最適化する',
  taBestPractice6: 'Amazonポリシーの更新に注意し、コンプライアンスと競争力を維持するためにタイトルを適時調整する',

  // 不足している翻訳キー
  taOptimizationSuggestions: '最適化提案',
  addMoreDetails: 'より詳細を追加',
  addMoreDescriptiveWords: 'より説明的な単語を追加',

  // 画像で赤枠の翻訳キー
  taBasedOn2025Policy: '2025年ポリシーに基づく',
  taSeoScore: 'SEOスコア',
  taComplianceScore: 'コンプライアンススコア',
  taMarketAdaptability: '市場適応性',
  taCategoryOptimization: 'カテゴリ最適化',
  taKeywordDensityLabel: 'キーワード密度',
  taReadabilityLabel: '読みやすさ',
  taKeywordDensityHigh: 'キーワード密度が高すぎます',
  taBrandInfoSuggestion: 'ブランド情報の追加を提案',
  taMobileDisplaySuggestion: 'モバイル表示の最適化を提案',
  taComplianceCheck: 'コンプライアンスチェック',
  taComplianceAnalysisReport: 'コンプライアンス分析レポート',
  taComplianceStatus: 'コンプライアンス状況',
  taCompliant: '準拠',
  taNeedsModification: '修正が必要',
  taComplianceReminder: 'コンプライアンスリマインダー',
  taAiComplianceError: 'AIコンプライアンスチェックに失敗しました',

  // その他の不足している可能性のある翻訳キー
  tooManyCapitalWords: 'タイトルに大文字の単語が多すぎます',

  // UsageLimitInfoコンポーネントのハードコードされたテキスト
  taAiUsageStatus: 'AI最適化使用状況',
  taTodayUsed: '本日使用済み',
  taTodayUsageProgress: '本日の使用進捗',
  taResetTomorrow: '明日 00:00',

  // AI応答エラーメッセージ
  taAiResponseParseError: 'AI応答の解析に失敗しました',
  taManualComplianceCheck: '手動でコンプライアンスを確認してください',

  // マーケティングアドバイス
  taMarketingAdvice: 'マーケティング戦略アドバイス',

  // 基本分析機能の説明
  taBasicAnalysisDescription: '基本分析機能には完全なタイトル評価、問題検出、最適化提案が含まれており、使用制限はありません。',

  // 使用制限関連の翻訳
  taUsageLimitExceeded: '本日の使用回数が上限に達しました',
  taDailyLimitReached: '本日のAI最適化回数を使い切りました（5回/日）',
  taNextResetTime: '明日リセット',
  taBasicAnalysisAvailable: '基本分析機能は引き続き利用可能',
  taRemainingUsage: '本日の残り回数',
  taLimitReason: 'なぜ使用制限があるのですか？',
  taLimitExplanation1: 'AI最適化には高度なアルゴリズムが必要で、コストが高い',
  taLimitExplanation2: '使用制限によりサービスの安定性を確保し、乱用を防ぐ',
  taLimitExplanation3: 'ユーザーがAI最適化を使用する前に基本情報を完成させることを推奨',
  taLimitExplanation4: 'すべてのユーザーがAI機能を公平に使用できることを保証',
  taOptimizationTips: '最適化のヒント',
  taOptimizationTip1: 'まずターゲットキーワードと製品特性情報を完成させる',
  taOptimizationTip2: '基本分析機能を使用してタイトルの問題をチェック',
  taOptimizationTip3: 'AI最適化の必要性を確認してから貴重な回数を使用',
  taUnderstanding: 'ご理解とご支援をありがとうございます！',

  // 禁止語彙検出
  forbiddenWords: '禁止語彙が検出されました: {words}',
};
