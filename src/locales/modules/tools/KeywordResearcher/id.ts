// <PERSON><PERSON>ti Kata Kunci terjemahan - Bahasa Indonesia
export const keywordResearcherId = {
  // Keyword Researcher
  keywordResearcher: '<PERSON><PERSON><PERSON> Kata Kunci',
  keywordResearcherDesc: 'Temukan kata kunci bernilai tinggi untuk meningkatkan visibilitas produk',
  keywordResearchTips: 'Tips Penelitian Kata Kunci',
  keywordTip1: 'Menggunakan kata kunci ekor panjang dapat mengurangi persaingan dan meningkatkan tingkat konversi',
  keywordTip2: 'Analisis strategi kata kunci pesaing',
  keywordTip3: 'Pertimbangkan kata kunci musiman dan trending',
  keywordTip4: 'Gabungkan volume pencarian dan tingkat persaingan untuk memilih kata kunci optimal',
};
