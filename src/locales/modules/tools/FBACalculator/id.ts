// Kalkulator FBA terjemahan - Bahasa Indonesia
export const fbaCalculatorId = {
  // FBA Calculator
  fbaCalculator: 'Kalkulator FBA',
  fbaCalculatorDesc: 'Hitung biaya pengiriman dan penyimpanan Amazon FBA.',
  productDimensions: 'Dimensi Produk',
  weight: 'Berat',
  fbaFees: 'Biaya FBA',
  storageFees: 'Biaya Penyimpanan',
  fulfillmentFee: 'Biaya Pengiriman',
  fulfillmentFees: 'Biaya Pengiriman',
  monthlyStorageFee: 'Biaya Penyimpanan Bulanan',
  longTermStorageFee: 'Biaya Penyimpanan Jangka Panjang',
  removalFee: 'Biaya Penghapusan',
  returnProcessingFee: 'Biaya Pemrosesan Pengembalian',
  totalMonthlyFees: 'Total Biaya Bulanan',
  fbaOptimizationTips: 'Tips Optimasi FBA',
  fbaTip1: 'Optimalkan kemasan untuk mengurangi berat dimensi',
  fbaTip2: 'Pertimbangkan menggabungkan item kecil untuk meningkatkan tingkat ukuran',
  fbaTip3: 'Pantau tingkat inventaris untuk menghindari biaya penyimpanan jangka panjang',
  fbaTip4: 'Gunakan kalkulator biaya FBA sebelum mencari produk baru',

  // FBA Calculator Specific
  marketplace: 'Marketplace',
  productCategory: 'Kategori Produk',
  oversizeCategory: 'Ukuran Besar',
  dangerousGoods: 'Barang Berbahaya',
  apparelCategory: 'Kategori Pakaian',
  productInfo: 'Informasi Produk',
  sizeTier: 'Tingkat Ukuran',
  billableWeight: 'Berat yang Dapat Ditagih',
  volume: 'Volume',
  dimensionalWeight: 'Berat Dimensi',
  fbaFeeBreakdown: 'Rincian Biaya FBA',
  standardSize: 'Ukuran Standar',
  smallStandard: 'Standar Kecil',
  largeStandard: 'Standar Besar',
  largeBulky: 'Barang Besar',
  extraLarge: 'Sangat Besar',

  // Additional Tool Interface Elements
  length: 'Panjang',
  width: 'Lebar',
  height: 'Tinggi',
  fillProductInfo: 'Isi informasi produk dan kami akan memberikan saran optimasi yang komprehensif.',

  // Tool Info Sections
  fbaToolIntroTitle: 'Pengenalan Alat',
  fbaToolIntroContent: 'Kalkulator Biaya FBA adalah alat perhitungan biaya profesional yang dirancang khusus untuk penjual Amazon. Alat ini secara akurat menghitung berbagai biaya layanan Amazon FBA (Fulfillment by Amazon), termasuk biaya pengiriman, biaya penyimpanan, biaya penyimpanan jangka panjang, dan lainnya. Melalui estimasi biaya yang akurat, alat ini membantu penjual mengembangkan strategi penetapan harga yang wajar, mengoptimalkan dimensi kemasan produk, dan meningkatkan margin keuntungan. Alat ini mendukung beberapa marketplace Amazon, menyediakan layanan perhitungan biaya yang terlokalisasi untuk penjual global.',

  fbaToolFeaturesTitle: 'Fitur Inti',
  fbaFeatureAccurateCalculation: 'Perhitungan Biaya Akurat',
  fbaFeatureAccurateCalculationDesc: 'Menghitung semua biaya FBA secara tepat berdasarkan standar biaya resmi Amazon',
  fbaFeatureMultiMarketplace: 'Dukungan Multi-Marketplace',
  fbaFeatureMultiMarketplaceDesc: 'Mendukung marketplace Amazon utama termasuk AS, Inggris, Jerman, Kanada',
  fbaFeatureSizeTierAnalysis: 'Analisis Tingkat Ukuran',
  fbaFeatureSizeTierAnalysisDesc: 'Secara otomatis menentukan tingkat ukuran produk dan memberikan standar biaya yang sesuai',
  fbaFeatureOptimizationTips: 'Saran Optimasi',
  fbaFeatureOptimizationTipsDesc: 'Memberikan rekomendasi optimasi kemasan dan kontrol biaya yang profesional',

  fbaUsageGuideTitle: 'Panduan Penggunaan',
  fbaStep1Title: 'Langkah 1: Pilih Marketplace Target',
  fbaStep1Content: 'Pilih marketplace Amazon target Anda dari menu dropdown. Marketplace yang berbeda memiliki standar biaya FBA dan unit mata uang yang berbeda.',
  fbaStep2Title: 'Langkah 2: Masukkan Dimensi Produk',
  fbaStep2Content: 'Masukkan panjang, lebar, tinggi produk (inci) dan berat (pound) secara akurat. Kami merekomendasikan menggunakan dimensi aktual setelah dikemas untuk perhitungan.',
  fbaStep3Title: 'Langkah 3: Atur Atribut Produk',
  fbaStep3Content: 'Pilih opsi kategori yang sesuai berdasarkan karakteristik produk, seperti apakah berukuran besar, barang berbahaya, atau kategori pakaian.',
  fbaStep4Title: 'Langkah 4: Tinjau Rincian Biaya',
  fbaStep4Content: 'Sistem akan secara otomatis menghitung dan menampilkan rincian biaya yang detail, termasuk biaya pengiriman, biaya penyimpanan, biaya penghapusan, dan biaya lainnya.',
  fbaStep5Title: 'Langkah 5: Optimalkan Strategi Biaya',
  fbaStep5Content: 'Berdasarkan hasil perhitungan dan saran optimasi, sesuaikan kemasan produk atau strategi penetapan harga untuk mencapai margin keuntungan yang optimal.',

  fbaFaqTitle: 'Pertanyaan yang Sering Diajukan',
  fbaFaq1Question: 'Bagaimana cara mengukur dimensi produk secara akurat?',
  fbaFaq1Answer: 'Kami merekomendasikan mengukur dimensi aktual setelah dikemas, termasuk semua bahan kemasan. Gunakan penggaris standar atau pita pengukur untuk mengukur dimensi terpanjang, terlebar, dan tertinggi, akurat hingga 0,1 inci.',

  fbaFaq2Question: 'Apa itu berat dimensi? Bagaimana cara menghitungnya?',
  fbaFaq2Answer: 'Berat dimensi dihitung berdasarkan volume paket menggunakan rumus: Panjang × Lebar × Tinggi ÷ 139. Amazon menggunakan yang lebih besar antara berat aktual dan berat dimensi sebagai berat yang dapat ditagih.',

  fbaFaq3Question: 'Bagaimana cara menghindari biaya penyimpanan jangka panjang?',
  fbaFaq3Answer: 'Biaya penyimpanan jangka panjang berlaku untuk barang yang disimpan di gudang Amazon lebih dari 365 hari. Kami merekomendasikan untuk secara teratur memantau perputaran inventori, mengisi ulang tepat waktu atau membersihkan produk yang bergerak lambat, dan mempertahankan tingkat inventori yang wajar.',

  fbaBestPracticesTitle: 'Praktik Terbaik',
  fbaBestPractice1: 'Optimasi kemasan: Pilih bahan dan ukuran kemasan yang tepat, kurangi pemborosan ruang yang tidak perlu, dan turunkan berat dimensi',
  fbaBestPractice2: 'Manajemen tingkat ukuran: Pahami perbedaan biaya antara tingkat ukuran, kontrol produk dalam tingkat biaya yang lebih rendah melalui penyesuaian kemasan',
  fbaBestPractice3: 'Optimasi perputaran inventori: Rencanakan tingkat inventori secara wajar, hindari biaya penyimpanan jangka panjang, dan tingkatkan efisiensi perputaran modal',
  fbaBestPractice4: 'Perbandingan multi-marketplace: Bandingkan biaya FBA di berbagai marketplace Amazon dan pilih pasar penjualan yang paling hemat biaya',
  fbaBestPractice5: 'Tinjauan biaya berkala: Amazon secara berkala menyesuaikan standar biaya FBA, disarankan untuk secara teratur menggunakan kalkulator untuk menilai kembali biaya produk'
};
