// FBA Calculator translations - English
export const fbaCalculatorEn = {
  // FBA Calculator
  fbaCalculator: 'FBA Calculator',
  fbaCalculatorDesc: 'Calculate Amazon FBA fulfillment fees and storage costs.',
  productDimensions: 'Product Dimensions',
  weight: 'Weight',
  fbaFees: 'FBA Fees',
  storageFees: 'Storage Fees',
  fulfillmentFee: 'Fulfillment Fee',
  fulfillmentFees: 'Fulfillment Fees',
  monthlyStorageFee: 'Monthly Storage Fee',
  longTermStorageFee: 'Long-term Storage Fee',
  removalFee: 'Removal Fee',
  returnProcessingFee: 'Return Processing Fee',
  totalMonthlyFees: 'Total Monthly Fees',
  fbaOptimizationTips: 'FBA Optimization Tips',
  fbaTip1: 'Optimize packaging to reduce dimensional weight',
  fbaTip2: 'Consider bundling small items to improve size tier',
  fbaTip3: 'Monitor inventory levels to avoid long-term storage fees',
  fbaTip4: 'Use FBA fee calculator before sourcing new products',

  // FBA Calculator Specific
  marketplace: 'Marketplace',
  productCategory: 'Product Category',
  oversizeCategory: 'Oversize',
  dangerousGoods: 'Dangerous Goods',
  apparelCategory: 'Apparel Category',
  productInfo: 'Product Information',
  sizeTier: 'Size Tier',
  billableWeight: 'Billable Weight',
  volume: 'Volume',
  dimensionalWeight: 'Dimensional Weight',
  fbaFeeBreakdown: 'FBA Fee Breakdown',
  standardSize: 'Standard Size',
  smallStandard: 'Small Standard',
  largeStandard: 'Large Standard',
  largeBulky: 'Large Bulky',
  extraLarge: 'Extra Large',

  // Additional Tool Interface Elements
  length: 'Length',
  width: 'Width',
  height: 'Height',
  fillProductInfo: 'Fill in product information and we will provide comprehensive optimization suggestions.',

  // Tool Info Sections
  fbaToolIntroTitle: 'Tool Introduction',
  fbaToolIntroContent: 'The FBA Fee Calculator is a professional fee calculation tool designed specifically for Amazon sellers. It accurately calculates various Amazon FBA (Fulfillment by Amazon) service fees, including fulfillment fees, storage fees, long-term storage fees, and more. Through precise fee estimation, it helps sellers develop reasonable pricing strategies, optimize product packaging dimensions, and improve profit margins. The tool supports multiple Amazon marketplaces, providing localized fee calculation services for global sellers.',

  fbaToolFeaturesTitle: 'Core Features',
  fbaFeatureAccurateCalculation: 'Accurate Fee Calculation',
  fbaFeatureAccurateCalculationDesc: 'Precisely calculate all FBA fees based on Amazon official fee standards',
  fbaFeatureMultiMarketplace: 'Multi-Marketplace Support',
  fbaFeatureMultiMarketplaceDesc: 'Support major Amazon marketplaces including US, UK, Germany, Canada',
  fbaFeatureSizeTierAnalysis: 'Size Tier Analysis',
  fbaFeatureSizeTierAnalysisDesc: 'Automatically determine product size tier and provide corresponding fee standards',
  fbaFeatureOptimizationTips: 'Optimization Suggestions',
  fbaFeatureOptimizationTipsDesc: 'Provide professional packaging optimization and cost control recommendations',

  fbaUsageGuideTitle: 'Usage Guide',
  fbaStep1Title: 'Step 1: Select Target Marketplace',
  fbaStep1Content: 'Choose your target Amazon marketplace from the dropdown menu. Different marketplaces have different FBA fee standards and currency units.',
  fbaStep2Title: 'Step 2: Enter Product Dimensions',
  fbaStep2Content: 'Accurately enter the product length, width, height (inches) and weight (pounds). We recommend using actual dimensions after packaging for calculation.',
  fbaStep3Title: 'Step 3: Set Product Attributes',
  fbaStep3Content: 'Select appropriate category options based on product characteristics, such as whether it is oversized, dangerous goods, or apparel category.',
  fbaStep4Title: 'Step 4: Review Fee Breakdown',
  fbaStep4Content: 'The system will automatically calculate and display detailed fee breakdown, including fulfillment fees, storage fees, removal fees, and other charges.',
  fbaStep5Title: 'Step 5: Optimize Cost Strategy',
  fbaStep5Content: 'Based on calculation results and optimization suggestions, adjust product packaging or pricing strategy to achieve optimal profit margins.',

  fbaFaqTitle: 'Frequently Asked Questions',
  fbaFaq1Question: 'How to accurately measure product dimensions?',
  fbaFaq1Answer: 'We recommend measuring actual dimensions after packaging, including all packaging materials. Use a standard ruler or tape measure to measure the longest, widest, and highest dimensions, accurate to 0.1 inches.',

  fbaFaq2Question: 'What is dimensional weight? How is it calculated?',
  fbaFaq2Answer: 'Dimensional weight is calculated based on package volume using the formula: Length × Width × Height ÷ 139. Amazon uses the greater of actual weight and dimensional weight as the billable weight.',

  fbaFaq3Question: 'How to avoid long-term storage fees?',
  fbaFaq3Answer: 'Long-term storage fees apply to items stored in Amazon warehouses for more than 365 days. We recommend regularly monitoring inventory turnover, timely restocking or clearing slow-moving products, and maintaining reasonable inventory levels.',

  fbaBestPracticesTitle: 'Best Practices',
  fbaBestPractice1: 'Packaging optimization: Choose appropriate packaging materials and sizes, reduce unnecessary space waste, and lower dimensional weight',
  fbaBestPractice2: 'Size tier management: Understand fee differences between size tiers, control products in lower fee tiers through packaging adjustments',
  fbaBestPractice3: 'Inventory turnover optimization: Plan inventory levels reasonably, avoid long-term storage fees, and improve capital turnover efficiency',
  fbaBestPractice4: 'Multi-marketplace comparison: Compare FBA fees across different Amazon marketplaces and choose the most cost-effective sales market',
  fbaBestPractice5: 'Regular fee review: Amazon periodically adjusts FBA fee standards, recommend regularly using the calculator to reassess product costs'
};
