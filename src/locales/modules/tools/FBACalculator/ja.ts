// FBA計算器翻訳 - 日本語
export const fbaCalculatorJa = {
  // FBA Calculator
  fbaCalculator: 'FBA計算器',
  fbaCalculatorDesc: 'AmazonのFBA配送手数料と保管料を計算します。',
  productDimensions: '商品寸法',
  weight: '重量',
  fbaFees: 'FBA手数料',
  storageFees: '保管料',
  fulfillmentFee: '配送手数料',
  fulfillmentFees: '配送手数料',
  monthlyStorageFee: '月間保管料',
  longTermStorageFee: '長期保管料',
  removalFee: '撤去手数料',
  returnProcessingFee: '返品処理手数料',
  totalMonthlyFees: '月間総手数料',
  fbaOptimizationTips: 'FBA最適化のヒント',
  fbaTip1: 'パッケージを最適化して容積重量を削減する',
  fbaTip2: '小さなアイテムをバンドルしてサイズ階層を改善することを検討する',
  fbaTip3: '在庫レベルを監視して長期保管料を避ける',
  fbaTip4: '新商品を調達する前にFBA手数料計算器を使用する',

  // FBA Calculator Specific
  marketplace: 'マーケットプレイス',
  productCategory: '商品カテゴリ',
  oversizeCategory: '大型サイズ',
  dangerousGoods: '危険物',
  apparelCategory: 'アパレルカテゴリ',
  productInfo: '商品情報',
  sizeTier: 'サイズ階層',
  billableWeight: '請求重量',
  volume: '体積',
  dimensionalWeight: '容積重量',
  fbaFeeBreakdown: 'FBA手数料内訳',
  standardSize: '標準サイズ',
  smallStandard: '小型標準',
  largeStandard: '大型標準',
  largeBulky: '大型商品',
  extraLarge: '特大商品',

  // Additional Tool Interface Elements
  length: '長さ',
  width: '幅',
  height: '高さ',
  fillProductInfo: '商品情報を入力すると、包括的な最適化提案を提供します。',

  // Tool Info Sections
  fbaToolIntroTitle: 'ツール紹介',
  fbaToolIntroContent: 'FBA手数料計算器は、Amazon販売者向けに特別に設計された専門的な手数料計算ツールです。Amazon FBA（Fulfillment by Amazon）サービスの各種手数料を正確に計算し、配送手数料、保管料、長期保管料などを含みます。正確な手数料見積もりにより、販売者が合理的な価格戦略を策定し、商品パッケージサイズを最適化し、利益率を向上させることを支援します。このツールは複数のAmazonマーケットプレイスをサポートし、グローバル販売者にローカライズされた手数料計算サービスを提供します。',

  fbaToolFeaturesTitle: 'コア機能',
  fbaFeatureAccurateCalculation: '正確な手数料計算',
  fbaFeatureAccurateCalculationDesc: 'Amazon公式手数料基準に基づいて、各種FBA手数料を正確に計算',
  fbaFeatureMultiMarketplace: 'マルチマーケットプレイス対応',
  fbaFeatureMultiMarketplaceDesc: '米国、英国、ドイツ、カナダなど主要なAmazonマーケットプレイスをサポート',
  fbaFeatureSizeTierAnalysis: 'サイズ階層分析',
  fbaFeatureSizeTierAnalysisDesc: '商品サイズ階層を自動判定し、対応する手数料基準を提供',
  fbaFeatureOptimizationTips: '最適化提案',
  fbaFeatureOptimizationTipsDesc: '専門的なパッケージ最適化とコスト管理の推奨事項を提供',

  fbaUsageGuideTitle: '使用ガイド',
  fbaStep1Title: 'ステップ1：ターゲットマーケットプレイスの選択',
  fbaStep1Content: 'ドロップダウンメニューから販売予定のAmazonマーケットプレイスを選択します。マーケットプレイスによってFBA手数料基準と通貨単位が異なります。',
  fbaStep2Title: 'ステップ2：商品寸法の入力',
  fbaStep2Content: '商品の長さ、幅、高さ（インチ）と重量（ポンド）を正確に入力します。パッケージ後の実際の寸法を使用することをお勧めします。',
  fbaStep3Title: 'ステップ3：商品属性の設定',
  fbaStep3Content: '商品特性に基づいて適切なカテゴリオプションを選択します。特大サイズ、危険物、アパレルカテゴリなど。',
  fbaStep4Title: 'ステップ4：手数料明細の確認',
  fbaStep4Content: 'システムが自動的に計算し、配送手数料、保管料、撤去手数料などの詳細な手数料内訳を表示します。',
  fbaStep5Title: 'ステップ5：コスト戦略の最適化',
  fbaStep5Content: '計算結果と最適化提案に基づいて、商品パッケージまたは価格戦略を調整し、最適な利益率を実現します。',

  fbaFaqTitle: 'よくある質問',
  fbaFaq1Question: '商品寸法を正確に測定するには？',
  fbaFaq1Answer: 'すべてのパッケージ材料を含む、パッケージ後の実際の寸法を測定することをお勧めします。標準的な定規またはメジャーを使用して、最長、最幅、最高の3つの寸法を0.1インチまで正確に測定してください。',

  fbaFaq2Question: '容積重量とは何ですか？どのように計算されますか？',
  fbaFaq2Answer: '容積重量は荷物の体積に基づいて計算される重量で、公式は：長さ×幅×高さ÷139です。Amazonは実重量と容積重量のうち大きい方を課金重量として使用します。',

  fbaFaq3Question: '長期保管料を避けるには？',
  fbaFaq3Answer: '長期保管料は、Amazonの倉庫に365日以上保管されている商品に適用されます。在庫回転率を定期的に監視し、タイムリーに補充または売れ行きの悪い商品をクリアし、合理的な在庫レベルを維持することをお勧めします。',

  fbaBestPracticesTitle: 'ベストプラクティス',
  fbaBestPractice1: 'パッケージ最適化：適切なパッケージ材料とサイズを選択し、不要なスペースの無駄を減らし、容積重量を下げる',
  fbaBestPractice2: 'サイズ階層管理：異なるサイズ階層の手数料差を理解し、パッケージ調整により商品をより低い手数料階層に制御する',
  fbaBestPractice3: '在庫回転最適化：在庫レベルを合理的に計画し、長期保管料を避け、資本回転効率を向上させる',
  fbaBestPractice4: 'マルチマーケットプレイス比較：異なるAmazonマーケットプレイスのFBA手数料を比較し、最もコスト効率の良い販売市場を選択する',
  fbaBestPractice5: '定期的な手数料見直し：AmazonはFBA手数料基準を定期的に調整するため、計算器を定期的に使用して商品コストを再評価することをお勧めします'
};
