// M<PERSON><PERSON> tính FBA bản dịch - Tiếng Việt
export const fbaCalculatorVi = {
  // FBA Calculator
  fbaCalculator: '<PERSON><PERSON><PERSON> T<PERSON>h FBA',
  fbaCalculatorDesc: 'Tính phí vận chuyển và lưu trữ Amazon FBA.',
  productDimensions: '<PERSON>ích Thước Sản Phẩm',
  weight: 'Trọng Lượng',
  fbaFees: 'Phí FBA',
  storageFees: 'Phí Lưu T<PERSON>ữ',
  fulfillmentFee: 'Phí Vận Chuyển',
  fulfillmentFees: 'Phí Vận Chuyển',
  monthlyStorageFee: 'Phí Lưu Trữ Hàng Tháng',
  longTermStorageFee: 'Phí Lưu Trữ Dài Hạn',
  removalFee: 'Phí Loại Bỏ',
  returnProcessingFee: 'Phí Xử Lý Trả Hàng',
  totalMonthlyFees: 'Tổng Phí Hàng <PERSON>',
  fbaOptimizationTips: 'Mẹo Tối Ưu Hóa FBA',
  fbaTip1: 'Tối ưu hóa bao bì để giảm trọng lượng kích thước',
  fbaTip2: 'Xem xét việc gộp các mặt hàng nhỏ để cải thiện cấp độ kích thước',
  fbaTip3: 'Theo dõi mức tồn kho để tránh phí lưu trữ dài hạn',
  fbaTip4: 'Sử dụng máy tính phí FBA trước khi tìm nguồn sản phẩm mới',

  // FBA Calculator Specific
  marketplace: 'Thị Trường',
  productCategory: 'Danh Mục Sản Phẩm',
  oversizeCategory: 'Kích Thước Lớn',
  dangerousGoods: 'Hàng Hóa Nguy Hiểm',
  apparelCategory: 'Danh Mục Quần Áo',
  productInfo: 'Thông Tin Sản Phẩm',
  sizeTier: 'Cấp Độ Kích Thước',
  billableWeight: 'Trọng Lượng Tính Phí',
  volume: 'Thể Tích',
  dimensionalWeight: 'Trọng Lượng Kích Thước',
  fbaFeeBreakdown: 'Chi Tiết Phí FBA',
  standardSize: 'Kích Thước Tiêu Chuẩn',
  smallStandard: 'Tiêu Chuẩn Nhỏ',
  largeStandard: 'Tiêu Chuẩn Lớn',
  largeBulky: 'Hàng Cồng Kềnh',
  extraLarge: 'Siêu Lớn',

  // Additional Tool Interface Elements
  length: 'Chiều Dài',
  width: 'Chiều Rộng',
  height: 'Chiều Cao',
  fillProductInfo: 'Điền thông tin sản phẩm và chúng tôi sẽ cung cấp đề xuất tối ưu hóa toàn diện.',

  // Tool Info Sections
  fbaToolIntroTitle: 'Giới Thiệu Công Cụ',
  fbaToolIntroContent: 'Máy Tính Phí FBA là một công cụ tính phí chuyên nghiệp được thiết kế đặc biệt cho người bán Amazon. Nó tính toán chính xác các loại phí dịch vụ Amazon FBA (Fulfillment by Amazon), bao gồm phí vận chuyển, phí lưu trữ, phí lưu trữ dài hạn, và nhiều hơn nữa. Thông qua ước tính phí chính xác, nó giúp người bán phát triển chiến lược định giá hợp lý, tối ưu hóa kích thước đóng gói sản phẩm, và cải thiện biên lợi nhuận. Công cụ hỗ trợ nhiều thị trường Amazon, cung cấp dịch vụ tính phí được bản địa hóa cho người bán toàn cầu.',

  fbaToolFeaturesTitle: 'Tính Năng Cốt Lõi',
  fbaFeatureAccurateCalculation: 'Tính Toán Phí Chính Xác',
  fbaFeatureAccurateCalculationDesc: 'Tính toán chính xác tất cả phí FBA dựa trên tiêu chuẩn phí chính thức của Amazon',
  fbaFeatureMultiMarketplace: 'Hỗ Trợ Đa Thị Trường',
  fbaFeatureMultiMarketplaceDesc: 'Hỗ trợ các thị trường Amazon chính bao gồm Mỹ, Anh, Đức, Canada',
  fbaFeatureSizeTierAnalysis: 'Phân Tích Cấp Độ Kích Thước',
  fbaFeatureSizeTierAnalysisDesc: 'Tự động xác định cấp độ kích thước sản phẩm và cung cấp tiêu chuẩn phí tương ứng',
  fbaFeatureOptimizationTips: 'Đề Xuất Tối Ưu Hóa',
  fbaFeatureOptimizationTipsDesc: 'Cung cấp khuyến nghị tối ưu hóa đóng gói và kiểm soát chi phí chuyên nghiệp',

  fbaUsageGuideTitle: 'Hướng Dẫn Sử Dụng',
  fbaStep1Title: 'Bước 1: Chọn Thị Trường Mục Tiêu',
  fbaStep1Content: 'Chọn thị trường Amazon mục tiêu của bạn từ menu thả xuống. Các thị trường khác nhau có tiêu chuẩn phí FBA và đơn vị tiền tệ khác nhau.',
  fbaStep2Title: 'Bước 2: Nhập Kích Thước Sản Phẩm',
  fbaStep2Content: 'Nhập chính xác chiều dài, chiều rộng, chiều cao sản phẩm (inch) và trọng lượng (pound). Chúng tôi khuyến nghị sử dụng kích thước thực tế sau khi đóng gói để tính toán.',
  fbaStep3Title: 'Bước 3: Thiết Lập Thuộc Tính Sản Phẩm',
  fbaStep3Content: 'Chọn các tùy chọn danh mục phù hợp dựa trên đặc điểm sản phẩm, chẳng hạn như có phải kích thước lớn, hàng nguy hiểm, hoặc danh mục quần áo không.',
  fbaStep4Title: 'Bước 4: Xem Chi Tiết Phí',
  fbaStep4Content: 'Hệ thống sẽ tự động tính toán và hiển thị chi tiết phí, bao gồm phí vận chuyển, phí lưu trữ, phí loại bỏ, và các khoản phí khác.',
  fbaStep5Title: 'Bước 5: Tối Ưu Hóa Chiến Lược Chi Phí',
  fbaStep5Content: 'Dựa trên kết quả tính toán và đề xuất tối ưu hóa, điều chỉnh đóng gói sản phẩm hoặc chiến lược định giá để đạt được biên lợi nhuận tối ưu.',

  fbaFaqTitle: 'Câu Hỏi Thường Gặp',
  fbaFaq1Question: 'Làm thế nào để đo kích thước sản phẩm chính xác?',
  fbaFaq1Answer: 'Chúng tôi khuyến nghị đo kích thước thực tế sau khi đóng gói, bao gồm tất cả vật liệu đóng gói. Sử dụng thước kẻ tiêu chuẩn hoặc thước dây để đo kích thước dài nhất, rộng nhất, và cao nhất, chính xác đến 0,1 inch.',

  fbaFaq2Question: 'Trọng lượng kích thước là gì? Làm thế nào để tính toán?',
  fbaFaq2Answer: 'Trọng lượng kích thước được tính dựa trên thể tích gói hàng sử dụng công thức: Chiều dài × Chiều rộng × Chiều cao ÷ 139. Amazon sử dụng giá trị lớn hơn giữa trọng lượng thực tế và trọng lượng kích thước làm trọng lượng tính phí.',

  fbaFaq3Question: 'Làm thế nào để tránh phí lưu trữ dài hạn?',
  fbaFaq3Answer: 'Phí lưu trữ dài hạn áp dụng cho các mặt hàng được lưu trữ trong kho Amazon hơn 365 ngày. Chúng tôi khuyến nghị thường xuyên theo dõi vòng quay hàng tồn kho, bổ sung kịp thời hoặc dọn dẹp sản phẩm chậm tiêu thụ, và duy trì mức tồn kho hợp lý.',

  fbaBestPracticesTitle: 'Thực Hành Tốt Nhất',
  fbaBestPractice1: 'Tối ưu hóa đóng gói: Chọn vật liệu và kích thước đóng gói phù hợp, giảm lãng phí không gian không cần thiết, và giảm trọng lượng kích thước',
  fbaBestPractice2: 'Quản lý cấp độ kích thước: Hiểu sự khác biệt phí giữa các cấp độ kích thước, kiểm soát sản phẩm ở cấp độ phí thấp hơn thông qua điều chỉnh đóng gói',
  fbaBestPractice3: 'Tối ưu hóa vòng quay hàng tồn kho: Lập kế hoạch mức tồn kho hợp lý, tránh phí lưu trữ dài hạn, và cải thiện hiệu quả vòng quay vốn',
  fbaBestPractice4: 'So sánh đa thị trường: So sánh phí FBA trên các thị trường Amazon khác nhau và chọn thị trường bán hàng tiết kiệm chi phí nhất',
  fbaBestPractice5: 'Đánh giá phí định kỳ: Amazon định kỳ điều chỉnh tiêu chuẩn phí FBA, khuyến nghị thường xuyên sử dụng máy tính để đánh giá lại chi phí sản phẩm'
};
