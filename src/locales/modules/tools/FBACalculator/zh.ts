// FBA费用计算器翻译 - 简体中文
export const fbaCalculatorZh = {
  // FBA Calculator
  fbaCalculator: 'FBA费用计算器',
  fbaCalculatorDesc: '计算亚马逊FBA配送费用和存储费用。',
  productDimensions: '产品尺寸',
  weight: '重量',
  fbaFees: 'FBA费用',
  storageFees: '存储费用',
  fulfillmentFee: '配送费用',
  fulfillmentFees: '配送费用',
  monthlyStorageFee: '月度存储费',
  longTermStorageFee: '长期存储费',
  removalFee: '移除费',
  returnProcessingFee: '退货处理费',
  totalMonthlyFees: '月度总费用',
  fbaOptimizationTips: 'FBA优化技巧',
  fbaTip1: '优化包装以减少体积重量',
  fbaTip2: '考虑捆绑小物品以改善尺寸等级',
  fbaTip3: '监控库存水平以避免长期存储费',
  fbaTip4: '在采购新产品前使用FBA费用计算器',

  // FBA Calculator Specific
  marketplace: '市场',
  productCategory: '产品类别',
  oversizeCategory: '超大尺寸',
  dangerousGoods: '危险品',
  apparelCategory: '服装类别',
  productInfo: '产品信息',
  sizeTier: '尺寸等级',
  billableWeight: '计费重量',
  volume: '体积',
  dimensionalWeight: '体积重量',
  fbaFeeBreakdown: 'FBA费用明细',
  standardSize: '标准尺寸',
  smallStandard: '小标准尺寸',
  largeStandard: '大标准尺寸',
  largeBulky: '大件商品',
  extraLarge: '超大件商品',

  // Additional Tool Interface Elements
  length: '长度',
  width: '宽度',
  height: '高度',
  fillProductInfo: '填写产品信息，我们将为您提供全面的优化建议。',

  // Tool Info Sections
  fbaToolIntroTitle: '工具介绍',
  fbaToolIntroContent: 'FBA费用计算器是专为Amazon卖家设计的专业费用计算工具，能够精确计算Amazon FBA（Fulfillment by Amazon）服务的各项费用，包括配送费、存储费、长期存储费等。通过准确的费用预估，帮助卖家制定合理的定价策略，优化产品包装尺寸，提升利润空间。该工具支持多个Amazon市场，为全球卖家提供本地化的费用计算服务。',

  fbaToolFeaturesTitle: '核心功能',
  fbaFeatureAccurateCalculation: '精确费用计算',
  fbaFeatureAccurateCalculationDesc: '基于Amazon官方费用标准，精确计算各项FBA费用',
  fbaFeatureMultiMarketplace: '多市场支持',
  fbaFeatureMultiMarketplaceDesc: '支持美国、英国、德国、加拿大等主要Amazon市场',
  fbaFeatureSizeTierAnalysis: '尺寸等级分析',
  fbaFeatureSizeTierAnalysisDesc: '自动判断产品尺寸等级，提供相应的费用标准',
  fbaFeatureOptimizationTips: '优化建议',
  fbaFeatureOptimizationTipsDesc: '提供专业的包装优化和成本控制建议',

  fbaUsageGuideTitle: '使用指南',
  fbaStep1Title: '第一步：选择目标市场',
  fbaStep1Content: '从下拉菜单中选择您要销售的Amazon市场，不同市场的FBA费用标准和货币单位不同。',
  fbaStep2Title: '第二步：输入产品尺寸',
  fbaStep2Content: '准确输入产品的长度、宽度、高度（英寸）和重量（磅）。建议使用包装后的实际尺寸进行计算。',
  fbaStep3Title: '第三步：设置产品属性',
  fbaStep3Content: '根据产品特性选择相应的类别选项，如是否为超大尺寸、危险品或服装类别等。',
  fbaStep4Title: '第四步：查看费用明细',
  fbaStep4Content: '系统会自动计算并显示详细的费用分解，包括配送费、存储费、移除费等各项费用。',
  fbaStep5Title: '第五步：优化成本策略',
  fbaStep5Content: '根据计算结果和优化建议，调整产品包装或定价策略，以获得最佳的利润空间。',

  fbaFaqTitle: '常见问题',
  fbaFaq1Question: '如何准确测量产品尺寸？',
  fbaFaq1Answer: '建议使用包装后的实际尺寸进行测量，包括所有包装材料。使用标准尺子或卷尺，测量最长、最宽、最高的三个维度，精确到0.1英寸。',

  fbaFaq2Question: '什么是体积重量？如何计算？',
  fbaFaq2Answer: '体积重量是根据包裹体积计算的重量，公式为：长×宽×高÷139。Amazon会取实际重量和体积重量中的较大值作为计费重量。',

  fbaFaq3Question: '如何避免长期存储费？',
  fbaFaq3Answer: '长期存储费适用于在Amazon仓库存放超过365天的商品。建议定期监控库存周转率，及时补货或清理滞销商品，保持合理的库存水平。',

  fbaBestPracticesTitle: '最佳实践',
  fbaBestPractice1: '包装优化：选择合适的包装材料和尺寸，减少不必要的空间浪费，降低体积重量',
  fbaBestPractice2: '尺寸等级管理：了解不同尺寸等级的费用差异，通过调整包装将产品控制在较低的费用等级',
  fbaBestPractice3: '库存周转优化：合理规划库存量，避免长期存储费，提高资金周转效率',
  fbaBestPractice4: '多市场比较：比较不同Amazon市场的FBA费用，选择最具成本优势的销售市场',
  fbaBestPractice5: '定期费用审查：Amazon会定期调整FBA费用标准，建议定期使用计算器重新评估产品成本'
};
