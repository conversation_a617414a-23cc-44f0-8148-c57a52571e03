// FBA費用計算器翻譯 - 繁體中文
export const fbaCalculatorZhTW = {
  // FBA Calculator
  fbaCalculator: 'FBA費用計算器',
  fbaCalculatorDesc: '計算亞馬遜FBA配送費用和存儲費用。',
  productDimensions: '產品尺寸',
  weight: '重量',
  fbaFees: 'FBA費用',
  storageFees: '存儲費用',
  fulfillmentFee: '配送費用',
  fulfillmentFees: '配送費用',
  monthlyStorageFee: '月度存儲費',
  longTermStorageFee: '長期存儲費',
  removalFee: '移除費',
  returnProcessingFee: '退貨處理費',
  totalMonthlyFees: '月度總費用',
  fbaOptimizationTips: 'FBA優化技巧',
  fbaTip1: '優化包裝以減少體積重量',
  fbaTip2: '考慮捆綁小物品以改善尺寸等級',
  fbaTip3: '監控庫存水平以避免長期存儲費',
  fbaTip4: '在採購新產品前使用FBA費用計算器',
  marketplace: '市場',
  productCategory: '產品類別',
  oversizeCategory: '超大尺寸',
  dangerousGoods: '危險品',
  apparelCategory: '服裝類別',
  productInfo: '產品信息',
  sizeTier: '尺寸等級',
  billableWeight: '計費重量',
  volume: '體積',
  dimensionalWeight: '體積重量',
  fbaFeeBreakdown: 'FBA費用明細',
  standardSize: '標準尺寸',
  smallStandard: '小標準尺寸',
  largeStandard: '大標準尺寸',
  largeBulky: '大件商品',
  extraLarge: '超大件商品',
  length: '長度',
  width: '寬度',
  height: '高度',
  fillProductInfo: '填寫產品信息，我們將為您提供全面的優化建議。',

  // Tool Info Sections
  fbaToolIntroTitle: '工具介紹',
  fbaToolIntroContent: 'FBA費用計算器是專為Amazon賣家設計的專業費用計算工具，能夠精確計算Amazon FBA（Fulfillment by Amazon）服務的各項費用，包括配送費、存儲費、長期存儲費等。通過準確的費用預估，幫助賣家制定合理的定價策略，優化產品包裝尺寸，提升利潤空間。該工具支持多個Amazon市場，為全球賣家提供本地化的費用計算服務。',

  fbaToolFeaturesTitle: '核心功能',
  fbaFeatureAccurateCalculation: '精確費用計算',
  fbaFeatureAccurateCalculationDesc: '基於Amazon官方費用標準，精確計算各項FBA費用',
  fbaFeatureMultiMarketplace: '多市場支持',
  fbaFeatureMultiMarketplaceDesc: '支持美國、英國、德國、加拿大等主要Amazon市場',
  fbaFeatureSizeTierAnalysis: '尺寸等級分析',
  fbaFeatureSizeTierAnalysisDesc: '自動判斷產品尺寸等級，提供相應的費用標準',
  fbaFeatureOptimizationTips: '優化建議',
  fbaFeatureOptimizationTipsDesc: '提供專業的包裝優化和成本控制建議',

  fbaUsageGuideTitle: '使用指南',
  fbaStep1Title: '第一步：選擇目標市場',
  fbaStep1Content: '從下拉菜單中選擇您要銷售的Amazon市場，不同市場的FBA費用標準和貨幣單位不同。',
  fbaStep2Title: '第二步：輸入產品尺寸',
  fbaStep2Content: '準確輸入產品的長度、寬度、高度（英寸）和重量（磅）。建議使用包裝後的實際尺寸進行計算。',
  fbaStep3Title: '第三步：設置產品屬性',
  fbaStep3Content: '根據產品特性選擇相應的類別選項，如是否為超大尺寸、危險品或服裝類別等。',
  fbaStep4Title: '第四步：查看費用明細',
  fbaStep4Content: '系統會自動計算並顯示詳細的費用分解，包括配送費、存儲費、移除費等各項費用。',
  fbaStep5Title: '第五步：優化成本策略',
  fbaStep5Content: '根據計算結果和優化建議，調整產品包裝或定價策略，以獲得最佳的利潤空間。',

  fbaFaqTitle: '常見問題',
  fbaFaq1Question: '如何準確測量產品尺寸？',
  fbaFaq1Answer: '建議使用包裝後的實際尺寸進行測量，包括所有包裝材料。使用標準尺子或卷尺，測量最長、最寬、最高的三個維度，精確到0.1英寸。',

  fbaFaq2Question: '什麼是體積重量？如何計算？',
  fbaFaq2Answer: '體積重量是根據包裹體積計算的重量，公式為：長×寬×高÷139。Amazon會取實際重量和體積重量中的較大值作為計費重量。',

  fbaFaq3Question: '如何避免長期存儲費？',
  fbaFaq3Answer: '長期存儲費適用於在Amazon倉庫存放超過365天的商品。建議定期監控庫存周轉率，及時補貨或清理滯銷商品，保持合理的庫存水平。',

  fbaBestPracticesTitle: '最佳實踐',
  fbaBestPractice1: '包裝優化：選擇合適的包裝材料和尺寸，減少不必要的空間浪費，降低體積重量',
  fbaBestPractice2: '尺寸等級管理：了解不同尺寸等級的費用差異，通過調整包裝將產品控制在較低的費用等級',
  fbaBestPractice3: '庫存周轉優化：合理規劃庫存量，避免長期存儲費，提高資金周轉效率',
  fbaBestPractice4: '多市場比較：比較不同Amazon市場的FBA費用，選擇最具成本優勢的銷售市場',
  fbaBestPractice5: '定期費用審查：Amazon會定期調整FBA費用標準，建議定期使用計算器重新評估產品成本'
};
