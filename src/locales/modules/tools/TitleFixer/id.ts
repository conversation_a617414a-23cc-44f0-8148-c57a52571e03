// Perbaikan <PERSON>dul terjemahan - Bahasa Indonesia
export const titleFixerId = {
  // Title Fixer
  titleFixer: 'Perbaikan <PERSON>dul',
  titleFixerDesc: 'Perbaiki masalah kepatuhan judul Amazon sesuai kebijakan terbaru 2025.',
  titleFixerShortDesc: 'Perbaikan cerdas AI untuk judul yang tidak patuh, pastikan listing yang sesuai',
  titleFixerHeader: 'Perbaikan cerdas masalah kepatuhan judul Amazon berdasarkan kebijakan terbaru 2025',
  tfMaxLength: 'Panjang Maksimal',
  tfCharacters: 'Karakter',
  tfOriginalTitle: 'Ju<PERSON><PERSON>',
  tfRequired: '*',
  tfOriginalTitlePlaceholder: 'Tempel judul produk asli Anda...',
  tfCurrentLength: 'Panjang Saat Ini',
  tfMaxAllowed: '<PERSON><PERSON><PERSON><PERSON>',
  tfLengthNormal: 'Panjang Normal',
  tfAmazonError: 'Pesan Error Amazon',
  tfAmazonErrorPlaceholder: 'Tempel pesan error Amazon, contoh: item_name mengandung konten yang tidak patuh...',
  tfAmazonErrorHelp: 'Silakan salin lengkap pesan error yang ditampilkan Amazon agar AI dapat menganalisis dengan akurat',
  tfAmazonErrorExample: 'Contoh: item_name mengandung konten yang tidak patuh, panjang judul melebihi batas, mengandung karakter khusus, dll.',
  tfAiSmartFix: 'AI Perbaikan Cerdas',
  tfBasicVersion: 'Versi Dasar',
  tfApparelLimit: 'Batas Kategori Pakaian: 125 karakter',
  tfUsMarketLimit: 'Batas Pasar AS: 200 karakter',
  tfWords: 'kata',
  tfTitleTooLong: 'Panjang judul melebihi batas, silakan persingkat menjadi dalam {maxLength} karakter',
  tfOptional: 'opsional',
  tfStartFixing: 'Mulai Perbaikan Cerdas',
  tfFixingInProgress: 'Sedang memperbaiki...',
  tfFixResultScore: 'Skor Hasil Perbaikan',
  tfComplianceScoreOutOf: 'Skor Kepatuhan (dari 100)',
  tfVersionCount: 'versi',
  tfOptimizedTitle: 'Judul yang Dioptimalkan',
  tfCopyToClipboard: 'Salin Judul yang Dioptimalkan',
  tfCopiedToClipboard: 'Disalin ke Clipboard',
  tfChangesCount: 'perubahan',
  tfStartTitleFix: 'Mulai Perbaikan Judul',
  tfInputTitleAndError: 'Masukkan judul asli dan pesan error Amazon, klik tombol perbaiki untuk memulai',
  tfFixTitle: 'Perbaikan Judul Cerdas',
  tfFixingTitle: 'AI sedang memperbaiki judul...',
  tfWaitingForFix: 'Menunggu Perbaikan',
  tfWaitingMessage: 'AI sedang menganalisis judul Anda dan menghasilkan versi yang patuh, mohon tunggu...',
  tfAiUnavailable: 'Layanan AI sementara tidak tersedia, silakan periksa koneksi jaringan atau coba lagi nanti',
  tfInputRequired: 'Silakan masukkan judul asli dan pesan error',
  tfPolicyTips: 'Tips Kebijakan Amazon',
  tfCharacterLimit: 'Batas Karakter',
  tfCharacterLimitDesc: 'Panjang judul tidak boleh melebihi 200 karakter, disarankan dalam 150 karakter',
  tfSpecialCharacters: 'Karakter Khusus',
  tfSpecialCharactersDesc: 'Hindari menggunakan simbol khusus, emoji, dan karakter non-standar',
  tfRepeatedWords: 'Kata Berulang',
  tfRepeatedWordsDesc: 'Hindari mengulang kata atau frasa yang sama dalam judul',
  
  // Status layanan AI
  tfUsingGeminiService: 'Menggunakan Layanan Kecerdasan AI',
  
  tfBrandException: 'Pengecualian Merek',
  tfBrandExceptionDesc: 'Beberapa istilah merek mungkin memerlukan penanganan khusus, pastikan otorisasi merek',
  tfVersion: 'Versi',
  tfMinimalChanges: 'Perubahan Minimal',
  tfSeoOptimized: 'Dioptimalkan SEO',
  tfConversionOptimized: 'Dioptimalkan Konversi',
  tfMinimalVersion: 'Versi Perubahan Minimal',
  tfSeoVersion: 'Versi Dioptimalkan SEO',
  tfConversionVersion: 'Versi Dioptimalkan Konversi',
  tfSynonymReplacement: 'Penggantian Sinonim',
  tfStructurePreserved: 'Struktur Dipertahankan',
  tfComplianceScore: 'Skor Kepatuhan',
  tfFixedTitles: 'Judul yang Diperbaiki',
  tfVersions: 'versi',
  tfCopied: 'Disalin',
  tfCopySelected: 'Salin Terpilih',
  tfLength: 'Panjang',
  tfOptimized: 'Dioptimalkan',
  tfChanges: 'Perubahan Dibuat',
  tfDetailedExplanation: 'Penjelasan Detail',
  tfExceedsLimit: 'Melebihi Batas',
  tfAnalyzing: 'Menganalisis',
  tfAnalyzingMessage: 'AI sedang menganalisis judul Anda secara mendalam dan menghasilkan solusi optimal...',
  tfFixFailed: 'Perbaikan gagal, silakan coba lagi',
  tfNoExplanation: 'Tidak ada penjelasan detail tersedia',
  tfDefaultChange: 'Judul telah disesuaikan sesuai kebijakan Amazon',
  tfCategory: 'Kategori Produk',
  tfMarketplace: 'Marketplace Target',
  tfAiServiceUnavailable: 'Layanan perbaikan AI sementara tidak tersedia, silakan coba lagi nanti',

  // Pemeriksaan kepatuhan real-time
  tfRealTimeCheck: 'Pemeriksaan Kepatuhan Real-time',
  tfComplianceStatus: 'Status Kepatuhan',
  tfViolations: 'Pelanggaran',
  tfNoViolations: 'Tidak Ada Pelanggaran',
  tfLengthCheck: 'Pemeriksaan Panjang',
  tfSpecialCharsCheck: 'Pemeriksaan Karakter Khusus',
  tfRepeatedWordsCheck: 'Pemeriksaan Kata Berulang',
  tfSuggestionsList: 'Saran Optimasi',
  tfViolationDetails: 'Detail Pelanggaran',
  tfViolationItems: 'Item Pelanggaran',

  // Marketplaces
  marketplaceUS: 'Amerika Serikat',
  marketplaceUK: 'Inggris',
  marketplaceDE: 'Jerman',
  marketplaceFR: 'Prancis',
  marketplaceIT: 'Italia',
  marketplaceES: 'Spanyol',
  marketplaceJP: 'Jepang',
  marketplaceCA: 'Kanada',
  marketplaceAU: 'Australia',

  // Categories
  categoryGeneral: 'Barang Umum',
  categoryApparel: 'Pakaian & Aksesoris',
  categoryElectronics: 'Elektronik',
  categoryHome: 'Rumah & Taman',
  categoryBeauty: 'Kecantikan & Perawatan',
  categorySports: 'Olahraga & Outdoor',
  categoryToys: 'Mainan & Permainan',
  categoryBooks: 'Buku & Media',
  categoryAutomotive: 'Otomotif',

  // Baru: Tampilan Perbandingan Perubahan
  tfComparisonView: 'Perbandingan Perubahan',
  tfOriginalVsFixed: 'Judul Asli vs Diperbaiki',
  tfShowComparison: 'Tampilkan Perbandingan',
  tfHideComparison: 'Sembunyikan Perbandingan',
  tfChangesHighlight: 'Sorotan Perubahan',
  tfAddedText: 'Konten Ditambahkan',
  tfRemovedText: 'Konten Dihapus',
  tfModifiedText: 'Konten Dimodifikasi',

  // Baru: Visualisasi Pelanggaran
  tfViolationHighlight: 'Sorotan Pelanggaran',
  tfSpecialCharViolation: 'Pelanggaran Karakter Khusus',
  tfRepeatedWordViolation: 'Pelanggaran Kata Berulang',
  tfLengthViolation: 'Pelanggaran Panjang',
  tfClickToSeeDetails: 'Klik untuk melihat detail',

  // Pesan pemeriksaan kepatuhan
  tfLengthExceeded: 'Panjang judul melebihi batas: {current}/{max} karakter',
  tfLengthNearLimit: 'Panjang judul mendekati batas: {current}/{max} karakter',
  tfSpecialCharsFound: 'Mengandung karakter khusus terlarang: {chars}',
  tfRepeatedWordsFound: 'Kata berulang melebihi batas: {words}',
  tfPluralDuplicateFound: 'Ditemukan duplikasi tunggal/jamak: "{singular}" dan "{plural}"',

  // Pesan saran
  tfSugRemoveRepeated: 'Hapus kata berulang',
  tfSugSimplifyDesc: 'Sederhanakan deskripsi produk',
  tfSugRemoveUnnecessary: 'Hapus kata sifat yang tidak perlu',
  tfSugConsiderConcise: 'Pertimbangkan ekspresi yang ringkas',
  tfSugAvoidMoreContent: 'Hindari menambah konten lebih',
  tfSugRemoveSpecialChars: 'Hapus karakter khusus',
  tfSugReplaceWithText: 'Ganti simbol dengan teks',
  tfSugCheckBrandName: 'Periksa apakah nama merek perlu dipertahankan',
  tfSugRemoveExtraRepeated: 'Hapus kata berulang yang berlebihan',
  tfSugUseSynonyms: 'Gunakan sinonim sebagai pengganti',
  tfSugReorganizeStructure: 'Reorganisasi struktur judul',
  tfSugKeepOneForm: 'Pertahankan hanya satu bentuk',
  tfSugUsePreciseDesc: 'Gunakan deskripsi yang lebih tepat',
  tfSugFollowPolicy: 'Ikuti kebijakan judul Amazon 2025 terbaru',
  tfSugKeepConcise: 'Jaga judul tetap ringkas dan jelas',

  // Pesan tombol salin (hapus kunci duplikat)
  tfCopyOptimizedTitle: 'Salin judul yang dioptimalkan',

  // Judul Bagian Informasi Detail Alat
  tfToolDetailInfoTitle: 'Informasi Detail Alat',
  tfToolDetailInfoSubtitle: 'Pelajari lebih lanjut tentang cara menggunakan Title Compliance Fixer dan praktik terbaik',

  // Bagian Pengenalan Alat
  tfToolIntroTitle: 'Pengenalan Alat',
  tfToolIntroContent: 'Title Compliance Fixer adalah alat cerdas berbasis AI yang dirancang khusus untuk memperbaiki masalah kepatuhan judul produk Amazon.',

  tfToolFeaturesTitle: 'Fitur Inti',
  tfFeatureAiAnalysis: 'Analisis AI Cerdas',
  tfFeatureAiAnalysisDesc: 'Berdasarkan kebijakan Amazon terbaru, mengidentifikasi masalah pelanggaran judul secara cerdas',

  // Bagian Panduan Penggunaan
  tfUsageGuideTitle: 'Panduan Penggunaan',
  tfStep1Title: 'Langkah 1: Masukkan Judul Asli',
  tfStep1Content: 'Tempelkan judul produk asli Anda ke dalam kotak input. Alat akan secara otomatis mendeteksi panjang judul dan masalah kepatuhan dasar.',

  // Bagian FAQ
  tfFaqTitle: 'Pertanyaan Umum',
  tfFaq1Question: 'Mengapa perlu memasukkan informasi error Amazon?',
  tfFaq1Answer: 'Informasi error Amazon berisi alasan pelanggaran spesifik, setelah dimasukkan AI dapat lebih akurat mengidentifikasi masalah dan memberikan solusi perbaikan yang tepat sasaran.',

  // Bagian Praktik Terbaik
  tfBestPracticesTitle: 'Rekomendasi Praktik Terbaik',
  tfBestPractice1: 'Periksa kepatuhan judul secara berkala untuk menghindari akumulasi pelanggaran',
  tfBestPractice2: 'Simpan beberapa versi yang dioptimalkan untuk pengujian A/B',
  tfBestPractice3: 'Gabungkan dengan riset kata kunci untuk memilih versi judul terbaik',
  tfBestPractice4: 'Perhatikan pembaruan kebijakan Amazon dan sesuaikan strategi judul tepat waktu',
  tfBestPractice5: 'Gunakan fitur penilaian kepatuhan alat untuk memilih versi dengan skor tinggi',

  // Terkait Batas Penggunaan AI
  tfUsageLimitTitle: 'Batas Penggunaan AI',
  tfUsageLimitExceeded: 'Jumlah penggunaan telah mencapai batas',
  tfHourlyLimitReached: 'Anda telah mencapai batas penggunaan per jam (10 kali)',
  tfDailyLimitReached: 'Anda telah mencapai batas penggunaan harian (30 kali)',
  tfSessionLimitReached: 'Anda telah mencapai batas penggunaan sesi ini (5 kali)',
  tfCooldownActive: 'Harap tunggu 3 menit sebelum menggunakan lagi',
  tfRemainingQuota: 'Sisa kuota',
  tfNextResetTime: 'Waktu reset',
  tfUsageStats: 'Statistik penggunaan',
  tfTodayUsage: 'Digunakan hari ini',
  tfSuccessRate: 'Tingkat keberhasilan',
  tfRefreshToReset: 'Refresh halaman untuk reset batas sesi',
  tfUsageTips: 'Tips penggunaan',
  tfUsageTip1: 'Judul yang sama akan otomatis menggunakan hasil cache, tidak mengonsumsi kuota',
  tfUsageTip2: 'Disarankan menggunakan pemeriksaan kepatuhan dasar terlebih dahulu, lalu gunakan perbaikan AI jika diperlukan',
  tfUsageTip3: 'Setiap perbaikan menghasilkan 3 versi untuk Anda pilih',
  tfCacheHit: 'Menggunakan hasil cache',
  tfAiRequest: 'Perbaikan AI Cerdas',

  // 使用統計界面翻譯
  tfDailyUsageProgress: 'Progres Penggunaan Harian',

  // Kunci terjemahan umum
  loading: 'Memuat...',
  aiServiceUnavailable: 'Layanan AI sementara tidak tersedia',
};
