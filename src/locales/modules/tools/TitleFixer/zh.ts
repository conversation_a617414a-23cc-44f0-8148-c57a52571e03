// 标题合规修改器翻译 - 简体中文
export const titleFixerZh = {
  // Title Fixer
  titleFixer: '标题合规修改器',
  titleFixerDesc: '修复亚马逊标题合规性问题，符合2025年最新政策。',
  titleFixerShortDesc: 'AI智能修复违规标题，确保合规上架',
  titleFixerHeader: '基于2025年最新政策，智能修复亚马逊标题合规性问题',
  tfMaxLength: '最大长度',
  tfCharacters: '字符',
  tfOriginalTitle: '原标题',
  tfRequired: '*',
  tfOriginalTitlePlaceholder: '粘贴您的原始产品标题...',
  tfCurrentLength: '当前长度',
  tfMaxAllowed: '最大允许',
  tfLengthNormal: '长度正常',
  tfAmazonError: '亚马逊错误信息',
  tfAmazonErrorPlaceholder: '粘贴亚马逊返回的错误信息，例如：item_name包含不合规内容...',
  tfAmazonErrorHelp: '请完整复制亚马逊显示的错误信息，以便AI准确分析问题',
  tfAmazonErrorExample: '例如：item_name包含不合规内容、标题长度超限、包含特殊字符等',
  tfAiSmartFix: 'AI 智能修复',
  tfBasicVersion: '基础版本',
  tfApparelLimit: '服装类别限制: 125 字符',
  tfUsMarketLimit: '美国市场限制: 200 字符',
  tfWords: '词',
  tfTitleTooLong: '标题长度超出限制，请缩短至 {maxLength} 字符以内',
  tfOptional: '可选',
  tfStartFixing: '开始智能修复',
  tfFixingInProgress: '正在修复中...',
  tfFixResultScore: '修复结果评分',
  tfComplianceScoreOutOf: '合规评分 (满分100)',
  tfVersionCount: '个版本',
  tfOptimizedTitle: '优化后标题',
  tfCopyToClipboard: '复制优化后标题',
  tfCopiedToClipboard: '已复制到剪贴板',
  tfChangesCount: '项修改',
  tfStartTitleFix: '开始修复标题',
  tfInputTitleAndError: '输入原标题和Amazon错误信息，点击修复按钮开始',
  tfFixTitle: '智能修复标题',
  tfFixingTitle: 'AI正在修复标题...',
  tfWaitingForFix: '等待修复',
  tfWaitingMessage: 'AI正在分析您的标题并生成合规版本，请稍候...',
  tfAiUnavailable: 'AI服务暂时不可用，请检查网络连接或稍后重试',
  tfInputRequired: '请输入原标题和错误信息',
  tfPolicyTips: '亚马逊政策提示',
  tfCharacterLimit: '字符限制',
  tfCharacterLimitDesc: '标题长度不能超过200个字符，建议控制在150字符以内',
  tfSpecialCharacters: '特殊字符',
  tfSpecialCharactersDesc: '避免使用特殊符号、表情符号和非标准字符',
  tfRepeatedWords: '重复词汇',
  tfRepeatedWordsDesc: '避免在标题中重复使用相同的词汇或短语',

  // AI服务状态
  tfUsingGeminiService: '使用AI智能服务',
  tfBrandException: '品牌例外',
  tfBrandExceptionDesc: '某些品牌词汇可能需要特殊处理，请确认品牌授权',
  tfVersion: '版本',
  tfMinimalChanges: '最小修改',
  tfSeoOptimized: 'SEO优化',
  tfConversionOptimized: '转化优化',
  tfMinimalVersion: '最小化修改版本',
  tfSeoVersion: 'SEO优化版本',
  tfConversionVersion: '转化优化版本',
  tfSynonymReplacement: '同义词替换',
  tfStructurePreserved: '保持原结构',
  tfComplianceScore: '合规性评分',
  tfFixedTitles: '修复后的标题',
  tfVersions: '个版本',
  tfCopied: '已复制',
  tfCopySelected: '复制选中',
  tfLength: '长度',
  tfOptimized: '已优化',
  tfChanges: '修改内容',
  tfDetailedExplanation: '详细说明',
  tfExceedsLimit: '超出限制',
  tfAnalyzing: '正在分析',
  tfAnalyzingMessage: 'AI正在深度分析您的标题并生成最优解决方案...',
  tfFixFailed: '修复失败，请重试',
  tfNoExplanation: '暂无详细说明',
  tfDefaultChange: '标题已根据亚马逊政策进行调整',
  tfCategory: '产品类别',
  tfMarketplace: '目标市场',
  tfAiServiceUnavailable: 'AI修复服务暂时不可用，请稍后重试',

  // 实时合规检查
  tfRealTimeCheck: '实时合规检查',
  tfComplianceStatus: '合规状态',
  tfViolations: '违规项',
  tfNoViolations: '无违规项',
  tfLengthCheck: '长度检查',
  tfSpecialCharsCheck: '特殊字符检查',
  tfRepeatedWordsCheck: '重复词汇检查',
  tfSuggestionsList: '优化建议',
  tfViolationDetails: '违规详情',
  tfViolationItems: '违规项目',

  // Marketplaces (保留在工具中，因为是Amazon特有的)
  marketplaceUS: '美国',
  marketplaceUK: '英国',
  marketplaceDE: '德国',
  marketplaceFR: '法国',
  marketplaceIT: '意大利',
  marketplaceES: '西班牙',
  marketplaceJP: '日本',
  marketplaceCA: '加拿大',
  marketplaceAU: '澳大利亚',

  // Categories (保留在工具中，因为是Amazon特有的产品分类)
  categoryGeneral: '一般商品',
  categoryApparel: '服装配饰',
  categoryElectronics: '电子产品',
  categoryHome: '家居园艺',
  categoryBeauty: '美容个护',
  categorySports: '运动户外',
  categoryToys: '玩具游戏',
  categoryBooks: '图书音像',
  categoryAutomotive: '汽车用品',

  // 新增：修改对比视图
  tfComparisonView: '修改对比',
  tfOriginalVsFixed: '原标题 vs 修复后',
  tfShowComparison: '显示对比',
  tfHideComparison: '隐藏对比',
  tfChangesHighlight: '修改高亮',
  tfAddedText: '新增内容',
  tfRemovedText: '删除内容',
  tfModifiedText: '修改内容',

  // 新增：违规项可视化
  tfViolationHighlight: '违规标记',
  tfSpecialCharViolation: '特殊字符违规',
  tfRepeatedWordViolation: '重复词汇违规',
  tfLengthViolation: '长度违规',
  tfClickToSeeDetails: '点击查看详情',

  // 统计信息
  tfChangeStats: '修改统计',
  tfAdded: '新增',
  tfRemoved: '删除',
  tfModified: '修改',
  tfTotal: '总计',
  tfViolationCount: '个违规项',

  // 合规检查消息
  tfLengthExceeded: '标题长度超限：{current}/{max} 字符',
  tfLengthNearLimit: '标题长度接近上限：{current}/{max} 字符',
  tfSpecialCharsFound: '包含禁用特殊字符：{chars}',
  tfRepeatedWordsFound: '重复词汇超限：{words}',
  tfPluralDuplicateFound: '发现单复数重复："{singular}" 和 "{plural}"',

  // 建议消息
  tfSugRemoveRepeated: '删除重复词汇',
  tfSugSimplifyDesc: '简化产品描述',
  tfSugRemoveUnnecessary: '移除不必要的修饰词',
  tfSugConsiderConcise: '考虑精简表达',
  tfSugAvoidMoreContent: '避免添加更多内容',
  tfSugRemoveSpecialChars: '删除特殊字符',
  tfSugReplaceWithText: '用文字替代符号',
  tfSugCheckBrandName: '检查品牌名是否需要保留',
  tfSugRemoveExtraRepeated: '删除多余的重复词汇',
  tfSugUseSynonyms: '使用同义词替换',
  tfSugReorganizeStructure: '重新组织标题结构',
  tfSugKeepOneForm: '只保留一种形式',
  tfSugUsePreciseDesc: '使用更精确的描述',
  tfSugFollowPolicy: '遵循亚马逊2025年最新标题政策',
  tfSugKeepConcise: '保持标题简洁明了',

  // 复制按钮消息 (移除重复键)
  tfCopyOptimizedTitle: '复制优化后标题',

  // AI使用限制相关
  tfUsageLimitTitle: 'AI使用限制',
  tfUsageLimitExceeded: '使用次数已达上限',
  tfHourlyLimitReached: '您已达到每小时使用限制（10次）',
  tfDailyLimitReached: '您已达到每日使用限制（30次）',
  tfSessionLimitReached: '您已达到本次会话使用限制（5次）',
  tfCooldownActive: '请等待3分钟后再次使用',
  tfRemainingQuota: '剩余次数',
  tfNextResetTime: '重置时间',
  tfUsageStats: '使用统计',
  tfTodayUsage: '今日已用',
  tfSuccessRate: '成功率',
  tfRefreshToReset: '刷新页面重置会话限制',
  tfUsageTips: '使用小贴士',
  tfUsageTip1: '相同标题会自动使用缓存结果，不消耗次数',
  tfUsageTip2: '建议先使用基础合规检查，确认需要后再使用AI修复',
  tfUsageTip3: '每次修复会生成3个版本供您选择',
  tfCacheHit: '使用缓存结果',
  tfAiRequest: 'AI智能修复',

  // 使用统计界面翻译
  tfDailyUsageProgress: '今日使用进度',

  // 通用翻译键
  loading: '加载中...',
  aiServiceUnavailable: 'AI服务暂时不可用',

  // 工具详细信息部分标题
  tfToolDetailInfoTitle: '工具详细信息',
  tfToolDetailInfoSubtitle: '了解更多关于标题合规修改器的使用方法和最佳实践',

  // 工具介绍部分
  tfToolIntroTitle: '工具介绍',
  tfToolIntroContent: '标题合规修改器是一款基于AI技术的智能工具，专门用于修复亚马逊产品标题的合规性问题。该工具基于2025年最新的亚马逊政策规则，能够自动识别标题中的违规内容，并提供多个优化版本供您选择。无论是字符长度超限、包含特殊字符、重复词汇，还是其他合规性问题，我们的AI都能准确分析并给出专业的修复建议。',

  tfToolFeaturesTitle: '核心功能',
  tfFeatureAiAnalysis: 'AI智能分析',
  tfFeatureAiAnalysisDesc: '基于最新Amazon政策，智能识别标题违规问题',
  tfFeatureMultiVersions: '多版本生成',
  tfFeatureMultiVersionsDesc: '一次修复生成3个优化版本，满足不同需求',
  tfFeatureRealTimeCheck: '实时合规检查',
  tfFeatureRealTimeCheckDesc: '输入即检查，实时显示合规状态和问题',
  tfFeatureComplianceScore: '合规评分',
  tfFeatureComplianceScoreDesc: '为每个修复版本提供详细的合规评分',

  // 使用指南部分
  tfUsageGuideTitle: '使用指南',
  tfStep1Title: '第一步：输入原标题',
  tfStep1Content: '将您的原始产品标题粘贴到输入框中。工具会自动检测标题长度和基础合规性问题。',
  tfStep2Title: '第二步：添加错误信息（可选）',
  tfStep2Content: '如果Amazon返回了具体的错误信息，请完整复制粘贴到错误信息框中。这将帮助AI更准确地识别问题。',
  tfStep3Title: '第三步：选择产品类别',
  tfStep3Content: '选择正确的产品类别，不同类别有不同的字符限制（一般商品200字符，服装类125字符）。',
  tfStep4Title: '第四步：开始智能修复',
  tfStep4Content: '点击"AI智能修复"按钮，等待AI分析并生成优化版本。通常需要5-10秒时间。',
  tfStep5Title: '第五步：选择最佳版本',
  tfStep5Content: '从生成的多个版本中选择最适合的一个，查看详细的修改说明和合规评分。',

  // FAQ部分
  tfFaqTitle: '常见问题',
  tfFaq1Question: '为什么需要输入Amazon错误信息？',
  tfFaq1Answer: 'Amazon错误信息包含了具体的违规原因，输入后AI能更精准地定位问题并提供针对性的修复方案。如果没有错误信息，AI会进行全面的合规性检查。',

  tfFaq2Question: '生成的标题版本有什么区别？',
  tfFaq2Answer: '每个版本都采用不同的优化策略：第一个版本注重保持原意，第二个版本更注重SEO优化，第三个版本则更加简洁明了。您可以根据需要选择最合适的版本。',

  tfFaq3Question: '工具支持哪些语言的标题？',
  tfFaq3Answer: '目前主要支持英文标题的修复，因为大部分Amazon违规问题都出现在英文标题中。中文标题相对较少出现合规性问题。',

  tfFaq4Question: '修复后的标题一定能通过Amazon审核吗？',
  tfFaq4Answer: '我们的AI基于最新政策规则进行修复，大大提高了通过率。但Amazon政策可能随时更新，建议在使用修复后的标题前，先进行小批量测试。',

  tfFaq5Question: '为什么有时候修复速度较慢？',
  tfFaq5Answer: '修复速度取决于AI服务的响应时间和标题的复杂程度。复杂的违规问题需要更多时间分析。我们已经加入了缓存机制，相同标题的二次修复会更快。',

  tfFaq6Question: '工具是否收费？',
  tfFaq6Answer: '基础功能完全免费使用，每日有一定的使用次数限制。未来可能会推出高级版本，提供更多专业功能和更高的使用限额。',

  // 最佳实践部分
  tfBestPracticesTitle: '最佳实践建议',
  tfBestPractice1: '定期检查标题合规性，避免违规积累',
  tfBestPractice2: '保存多个优化版本，用于A/B测试',
  tfBestPractice3: '结合关键词研究，选择最佳标题版本',
  tfBestPractice4: '关注Amazon政策更新，及时调整标题策略',
  tfBestPractice5: '使用工具的合规评分功能，选择高分版本',
};
