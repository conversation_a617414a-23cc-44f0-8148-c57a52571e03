// Title Compliance Fixer translations - English
export const titleFixerEn = {
  // Title Fixer
  titleFixer: 'Title Compliance Fixer',
  titleFixerDesc: 'Fix Amazon title compliance issues according to 2025 latest policies.',
  titleFixerShortDesc: 'AI smart fix for non-compliant titles, ensure compliant listing',
  titleFixerHeader: 'Smart fix for Amazon title compliance issues based on 2025 latest policies',
  tfMaxLength: 'Max Length',
  tfCharacters: 'Characters',
  tfOriginalTitle: 'Original Title',
  tfRequired: '*',
  tfOriginalTitlePlaceholder: 'Paste your original product title...',
  tfCurrentLength: 'Current Length',
  tfMaxAllowed: 'Max Allowed',
  tfLengthNormal: 'Length Normal',
  tfAmazonError: 'Amazon Error Message',
  tfAmazonErrorPlaceholder: 'Paste Amazon error message, e.g.: item_name contains non-compliant content...',
  tfAmazonErrorHelp: 'Please copy the complete error message displayed by Amazon for AI to analyze accurately',
  tfAmazonErrorExample: 'e.g.: item_name contains non-compliant content, title length exceeds limit, contains special characters, etc.',
  tfAiSmartFix: 'AI Smart Fix',
  tfBasicVersion: 'Basic Version',
  tfApparelLimit: 'Apparel Category Limit: 125 characters',
  tfUsMarketLimit: 'US Market Limit: 200 characters',
  tfWords: 'words',
  tfTitleTooLong: 'Title length exceeds limit, please shorten to within {maxLength} characters',
  tfOptional: 'optional',
  tfStartFixing: 'Start Smart Fix',
  tfFixingInProgress: 'Fixing in progress...',
  tfFixResultScore: 'Fix Result Score',
  tfComplianceScoreOutOf: 'Compliance Score (out of 100)',
  tfVersionCount: 'versions',
  tfOptimizedTitle: 'Optimized Title',
  tfCopyToClipboard: 'Copy Optimized Title',
  tfCopiedToClipboard: 'Copied to Clipboard',
  tfChangesCount: 'changes',
  tfStartTitleFix: 'Start Title Fix',
  tfInputTitleAndError: 'Enter original title and Amazon error message, click fix button to start',
  tfFixTitle: 'Smart Fix Title',
  tfFixingTitle: 'AI is fixing title...',
  tfWaitingForFix: 'Waiting for Fix',
  tfWaitingMessage: 'AI is analyzing your title and generating compliant versions, please wait...',
  tfAiUnavailable: 'AI service temporarily unavailable, please check network connection or try again later',
  tfInputRequired: 'Please enter original title and error message',
  tfPolicyTips: 'Amazon Policy Tips',
  tfCharacterLimit: 'Character Limit',
  tfCharacterLimitDesc: 'Title length cannot exceed 200 characters, recommend keeping within 150 characters',
  tfSpecialCharacters: 'Special Characters',
  tfSpecialCharactersDesc: 'Avoid using special symbols, emojis and non-standard characters',
  tfRepeatedWords: 'Repeated Words',
  tfRepeatedWordsDesc: 'Avoid repeating the same words or phrases in the title',
  
  // AI service status
  tfUsingGeminiService: 'Using AI Intelligence Service',
  tfBrandException: 'Brand Exception',
  tfBrandExceptionDesc: 'Some brand terms may need special handling, please confirm brand authorization',
  tfVersion: 'Version',
  tfMinimalChanges: 'Minimal Changes',
  tfSeoOptimized: 'SEO Optimized',
  tfConversionOptimized: 'Conversion Optimized',
  tfMinimalVersion: 'Minimal Change Version',
  tfSeoVersion: 'SEO Optimized Version',
  tfConversionVersion: 'Conversion Optimized Version',
  tfSynonymReplacement: 'Synonym Replacement',
  tfStructurePreserved: 'Structure Preserved',
  tfComplianceScore: 'Compliance Score',
  tfFixedTitles: 'Fixed Titles',
  tfVersions: 'versions',
  tfCopied: 'Copied',
  tfCopySelected: 'Copy Selected',
  tfLength: 'Length',
  tfOptimized: 'Optimized',
  tfChanges: 'Changes Made',
  tfDetailedExplanation: 'Detailed Explanation',
  tfExceedsLimit: 'Exceeds Limit',
  tfAnalyzing: 'Analyzing',
  tfAnalyzingMessage: 'AI is deeply analyzing your title and generating optimal solutions...',
  tfFixFailed: 'Fix failed, please try again',
  tfNoExplanation: 'No detailed explanation available',
  tfDefaultChange: 'Title has been adjusted according to Amazon policies',
  tfCategory: 'Product Category',
  tfMarketplace: 'Target Marketplace',
  tfAiServiceUnavailable: 'AI fix service is temporarily unavailable, please try again later',

  // Real-time compliance check
  tfRealTimeCheck: 'Real-time Compliance Check',
  tfComplianceStatus: 'Compliance Status',
  tfViolations: 'Violations',
  tfNoViolations: 'No Violations',
  tfLengthCheck: 'Length Check',
  tfSpecialCharsCheck: 'Special Characters Check',
  tfRepeatedWordsCheck: 'Repeated Words Check',
  tfSuggestionsList: 'Optimization Suggestions',
  tfViolationDetails: 'Violation Details',
  tfViolationItems: 'Violation Items',

  // Marketplaces
  marketplaceUS: 'United States',
  marketplaceUK: 'United Kingdom',
  marketplaceDE: 'Germany',
  marketplaceFR: 'France',
  marketplaceIT: 'Italy',
  marketplaceES: 'Spain',
  marketplaceJP: 'Japan',
  marketplaceCA: 'Canada',
  marketplaceAU: 'Australia',

  // Categories
  categoryGeneral: 'General Merchandise',
  categoryApparel: 'Clothing & Accessories',
  categoryElectronics: 'Electronics',
  categoryHome: 'Home & Garden',
  categoryBeauty: 'Beauty & Personal Care',
  categorySports: 'Sports & Outdoors',
  categoryToys: 'Toys & Games',
  categoryBooks: 'Books & Media',
  categoryAutomotive: 'Automotive',

  // New: Comparison View
  tfComparisonView: 'Change Comparison',
  tfOriginalVsFixed: 'Original vs Fixed',
  tfShowComparison: 'Show Comparison',
  tfHideComparison: 'Hide Comparison',
  tfChangesHighlight: 'Changes Highlight',
  tfAddedText: 'Added Content',
  tfRemovedText: 'Removed Content',
  tfModifiedText: 'Modified Content',

  // New: Violation Visualization
  tfViolationHighlight: 'Violation Highlight',
  tfSpecialCharViolation: 'Special Character Violation',
  tfRepeatedWordViolation: 'Repeated Word Violation',
  tfLengthViolation: 'Length Violation',
  tfClickToSeeDetails: 'Click to see details',

  // Statistics
  tfChangeStats: 'Change Statistics',
  tfAdded: 'Added',
  tfRemoved: 'Removed',
  tfModified: 'Modified',
  tfTotal: 'Total',
  tfViolationCount: 'violations',

  // Compliance check messages
  tfLengthExceeded: 'Title length exceeded: {current}/{max} characters',
  tfLengthNearLimit: 'Title length near limit: {current}/{max} characters',
  tfSpecialCharsFound: 'Contains forbidden special characters: {chars}',
  tfRepeatedWordsFound: 'Repeated words exceeded: {words}',
  tfPluralDuplicateFound: 'Found singular/plural duplication: "{singular}" and "{plural}"',

  // Suggestion messages
  tfSugRemoveRepeated: 'Remove repeated words',
  tfSugSimplifyDesc: 'Simplify product description',
  tfSugRemoveUnnecessary: 'Remove unnecessary modifiers',
  tfSugConsiderConcise: 'Consider concise expression',
  tfSugAvoidMoreContent: 'Avoid adding more content',
  tfSugRemoveSpecialChars: 'Remove special characters',
  tfSugReplaceWithText: 'Replace symbols with text',
  tfSugCheckBrandName: 'Check if brand name needs to be preserved',
  tfSugRemoveExtraRepeated: 'Remove extra repeated words',
  tfSugUseSynonyms: 'Use synonyms for replacement',
  tfSugReorganizeStructure: 'Reorganize title structure',
  tfSugKeepOneForm: 'Keep only one form',
  tfSugUsePreciseDesc: 'Use more precise description',
  tfSugFollowPolicy: 'Follow Amazon 2025 latest title policy',
  tfSugKeepConcise: 'Keep title concise and clear',

  // Copy button messages (remove duplicate key)
  tfCopyOptimizedTitle: 'Copy optimized title',

  // AI usage limit related
  tfUsageLimitTitle: 'AI Usage Limit',
  tfUsageLimitExceeded: 'Usage limit exceeded',
  tfHourlyLimitReached: 'You have reached the hourly usage limit (10 times)',
  tfDailyLimitReached: 'You have reached the daily usage limit (30 times)',
  tfSessionLimitReached: 'You have reached the session usage limit (5 times)',
  tfCooldownActive: 'Please wait 3 minutes before using again',
  tfRemainingQuota: 'Remaining quota',
  tfNextResetTime: 'Reset time',
  tfUsageStats: 'Usage statistics',
  tfTodayUsage: 'Used today',
  tfSuccessRate: 'Success rate',
  tfRefreshToReset: 'Refresh page to reset session limit',
  tfUsageTips: 'Usage tips',
  tfUsageTip1: 'Same titles automatically use cached results, no quota consumed',
  tfUsageTip2: 'Recommend using basic compliance check first, then AI fix if needed',
  tfUsageTip3: 'Each fix generates 3 versions for you to choose from',
  tfCacheHit: 'Using cached result',
  tfAiRequest: 'AI smart fix',

  // Usage statistics interface translations
  tfDailyUsageProgress: 'Daily Usage Progress',

  // Common translation keys
  loading: 'Loading...',
  aiServiceUnavailable: 'AI service temporarily unavailable',

  // Tool Detail Info Section Titles
  tfToolDetailInfoTitle: 'Tool Detailed Information',
  tfToolDetailInfoSubtitle: 'Learn more about how to use the Title Compliance Fixer and best practices',

  // Tool Introduction Section
  tfToolIntroTitle: 'Tool Introduction',
  tfToolIntroContent: 'The Title Compliance Fixer is an AI-powered intelligent tool specifically designed to fix Amazon product title compliance issues. Based on the latest 2025 Amazon policy rules, it can automatically identify non-compliant content in titles and provide multiple optimized versions for your selection. Whether it\'s character length limits, special characters, repeated words, or other compliance issues, our AI can accurately analyze and provide professional fixing suggestions.',

  tfToolFeaturesTitle: 'Core Features',
  tfFeatureAiAnalysis: 'AI Smart Analysis',
  tfFeatureAiAnalysisDesc: 'Based on latest Amazon policies, intelligently identify title violation issues',
  tfFeatureMultiVersions: 'Multi-version Generation',
  tfFeatureMultiVersionsDesc: 'Generate 3 optimized versions in one fix to meet different needs',
  tfFeatureRealTimeCheck: 'Real-time Compliance Check',
  tfFeatureRealTimeCheckDesc: 'Check as you type, real-time display of compliance status and issues',
  tfFeatureComplianceScore: 'Compliance Scoring',
  tfFeatureComplianceScoreDesc: 'Provide detailed compliance scores for each fixed version',

  // Usage Guide Section
  tfUsageGuideTitle: 'Usage Guide',
  tfStep1Title: 'Step 1: Input Original Title',
  tfStep1Content: 'Paste your original product title into the input box. The tool will automatically detect title length and basic compliance issues.',
  tfStep2Title: 'Step 2: Add Error Information (Optional)',
  tfStep2Content: 'If Amazon returned specific error messages, please copy and paste them completely into the error information box. This will help AI identify issues more accurately.',
  tfStep3Title: 'Step 3: Select Product Category',
  tfStep3Content: 'Choose the correct product category, different categories have different character limits (general products 200 characters, apparel 125 characters).',
  tfStep4Title: 'Step 4: Start Smart Fix',
  tfStep4Content: 'Click the "AI Smart Fix" button and wait for AI to analyze and generate optimized versions. Usually takes 5-10 seconds.',
  tfStep5Title: 'Step 5: Select Best Version',
  tfStep5Content: 'Choose the most suitable version from the generated options, review detailed modification explanations and compliance scores.',

  // FAQ Section
  tfFaqTitle: 'Frequently Asked Questions',
  tfFaq1Question: 'Why do I need to input Amazon error information?',
  tfFaq1Answer: 'Amazon error messages contain specific violation reasons. With this input, AI can more precisely locate issues and provide targeted fixing solutions. Without error information, AI will perform comprehensive compliance checks.',

  tfFaq2Question: 'What are the differences between generated title versions?',
  tfFaq2Answer: 'Each version uses different optimization strategies: the first version focuses on maintaining original meaning, the second version emphasizes SEO optimization, and the third version is more concise and clear. You can choose the most suitable version based on your needs.',

  tfFaq3Question: 'Which languages does the tool support for titles?',
  tfFaq3Answer: 'Currently mainly supports English title fixes, as most Amazon violation issues occur in English titles. Chinese titles relatively rarely have compliance issues.',

  tfFaq4Question: 'Will fixed titles definitely pass Amazon review?',
  tfFaq4Answer: 'Our AI fixes based on the latest policy rules, greatly improving pass rates. However, Amazon policies may update at any time, so we recommend small-batch testing before using fixed titles.',

  tfFaq5Question: 'Why is the fixing process sometimes slow?',
  tfFaq5Answer: 'Fixing speed depends on AI service response time and title complexity. Complex violation issues require more analysis time. We have added caching mechanism, so second fixes of the same title will be faster.',

  tfFaq6Question: 'Is the tool free to use?',
  tfFaq6Answer: 'Basic features are completely free with daily usage limits. We may introduce premium versions in the future with more professional features and higher usage quotas.',

  // Best Practices Section
  tfBestPracticesTitle: 'Best Practice Recommendations',
  tfBestPractice1: 'Regularly check title compliance to avoid violation accumulation',
  tfBestPractice2: 'Save multiple optimized versions for A/B testing',
  tfBestPractice3: 'Combine with keyword research to select the best title version',
  tfBestPractice4: 'Follow Amazon policy updates and adjust title strategies timely',
  tfBestPractice5: 'Use the tool\'s compliance scoring feature to select high-scoring versions',
};
