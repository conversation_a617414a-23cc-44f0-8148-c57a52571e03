// Trình sửa tuân thủ tiêu đề bản dịch - Tiếng Việt
export const titleFixerVi = {
  // Title Fixer
  titleFixer: 'Trình Sửa Tuân Thủ Tiêu Đề',
  titleFixerDesc: 'Sửa các vấn đề tuân thủ tiêu đề Amazon theo chính sách mới nhất 2025.',
  titleFixerShortDesc: 'Sửa chữa thông minh AI cho tiêu đề không tuân thủ, đảm bảo listing tuân thủ',
  titleFixerHeader: 'Sửa chữa thông minh các vấn đề tuân thủ tiêu đề Amazon dựa trên chính sách mới nhất 2025',
  tfMaxLength: 'Độ Dài <PERSON>ố<PERSON>',
  tfCharacters: '<PERSON><PERSON> Tự',
  tfOriginalTitle: 'Tiêu <PERSON>ố<PERSON>',
  tfRequired: '*',
  tfOriginalTitlePlaceholder: '<PERSON><PERSON> tiêu đề sản phẩm gốc của bạn...',
  tfCurrentLength: '<PERSON><PERSON> Dài Hiện Tại',
  tfMaxAllowed: 'Tối Đa Cho Phép',
  tfLengthNormal: 'Độ Dài Bình Thường',
  tfAmazonError: 'Thông Báo Lỗi Amazon',
  tfAmazonErrorPlaceholder: 'Dán thông báo lỗi Amazon, ví dụ: item_name chứa nội dung không tuân thủ...',
  tfAmazonErrorHelp: 'Vui lòng sao chép đầy đủ thông báo lỗi mà Amazon hiển thị để AI có thể phân tích chính xác',
  tfAmazonErrorExample: 'Ví dụ: item_name chứa nội dung không tuân thủ, độ dài tiêu đề vượt quá giới hạn, chứa ký tự đặc biệt, v.v.',
  tfAiSmartFix: 'AI Sửa Thông Minh',
  tfBasicVersion: 'Phiên Bản Cơ Bản',
  tfApparelLimit: 'Giới Hạn Danh Mục Thời Trang: 125 ký tự',
  tfUsMarketLimit: 'Giới Hạn Thị Trường Mỹ: 200 ký tự',
  tfWords: 'từ',
  tfTitleTooLong: 'Độ dài tiêu đề vượt quá giới hạn, vui lòng rút ngắn xuống trong {maxLength} ký tự',
  tfOptional: 'tùy chọn',
  tfStartFixing: 'Bắt Đầu Sửa Thông Minh',
  tfFixingInProgress: 'Đang sửa...',
  tfFixResultScore: 'Điểm Kết Quả Sửa',
  tfComplianceScoreOutOf: 'Điểm Tuân Thủ (trên 100)',
  tfVersionCount: 'phiên bản',
  tfOptimizedTitle: 'Tiêu Đề Được Tối Ưu',
  tfCopyToClipboard: 'Sao Chép Tiêu Đề Được Tối Ưu',
  tfCopiedToClipboard: 'Đã Sao Chép Vào Clipboard',
  tfChangesCount: 'thay đổi',
  tfStartTitleFix: 'Bắt Đầu Sửa Tiêu Đề',
  tfInputTitleAndError: 'Nhập tiêu đề gốc và thông báo lỗi Amazon, nhấp nút sửa để bắt đầu',
  tfFixTitle: 'Sửa Tiêu Đề Thông Minh',
  tfFixingTitle: 'AI đang sửa tiêu đề...',
  tfWaitingForFix: 'Đang Chờ Sửa',
  tfWaitingMessage: 'AI đang phân tích tiêu đề của bạn và tạo phiên bản tuân thủ, vui lòng đợi...',
  tfAiUnavailable: 'Dịch vụ AI tạm thời không khả dụng, vui lòng kiểm tra kết nối mạng hoặc thử lại sau',
  tfInputRequired: 'Vui lòng nhập tiêu đề gốc và thông báo lỗi',
  tfPolicyTips: 'Mẹo Chính Sách Amazon',
  tfCharacterLimit: 'Giới Hạn Ký Tự',
  tfCharacterLimitDesc: 'Độ dài tiêu đề không được vượt quá 200 ký tự, khuyến nghị trong vòng 150 ký tự',
  tfSpecialCharacters: 'Ký Tự Đặc Biệt',
  tfSpecialCharactersDesc: 'Tránh sử dụng ký hiệu đặc biệt, emoji và ký tự không chuẩn',
  tfRepeatedWords: 'Từ Lặp Lại',
  tfRepeatedWordsDesc: 'Tránh lặp lại cùng một từ hoặc cụm từ trong tiêu đề',
  
  // Trạng thái dịch vụ AI
  tfUsingGeminiService: 'Sử dụng Dịch vụ Trí tuệ AI',
  
  tfBrandException: 'Ngoại Lệ Thương Hiệu',
  tfBrandExceptionDesc: 'Một số thuật ngữ thương hiệu có thể cần xử lý đặc biệt, vui lòng xác nhận ủy quyền thương hiệu',
  tfVersion: 'Phiên Bản',
  tfMinimalChanges: 'Thay Đổi Tối Thiểu',
  tfSeoOptimized: 'Tối Ưu SEO',
  tfConversionOptimized: 'Tối Ưu Chuyển Đổi',
  tfMinimalVersion: 'Phiên Bản Thay Đổi Tối Thiểu',
  tfSeoVersion: 'Phiên Bản Tối Ưu SEO',
  tfConversionVersion: 'Phiên Bản Tối Ưu Chuyển Đổi',
  tfSynonymReplacement: 'Thay Thế Từ Đồng Nghĩa',
  tfStructurePreserved: 'Bảo Tồn Cấu Trúc',
  tfComplianceScore: 'Điểm Tuân Thủ',
  tfFixedTitles: 'Tiêu Đề Đã Sửa',
  tfVersions: 'phiên bản',
  tfCopied: 'Đã Sao Chép',
  tfCopySelected: 'Sao Chép Đã Chọn',
  tfLength: 'Độ Dài',
  tfOptimized: 'Đã Tối Ưu',
  tfChanges: 'Thay Đổi Đã Thực Hiện',
  tfDetailedExplanation: 'Giải Thích Chi Tiết',
  tfExceedsLimit: 'Vượt Quá Giới Hạn',
  tfAnalyzing: 'Đang Phân Tích',
  tfAnalyzingMessage: 'AI đang phân tích sâu tiêu đề của bạn và tạo ra giải pháp tối ưu...',
  tfFixFailed: 'Sửa thất bại, vui lòng thử lại',
  tfNoExplanation: 'Không có giải thích chi tiết',
  tfDefaultChange: 'Tiêu đề đã được điều chỉnh theo chính sách Amazon',
  tfCategory: 'Danh Mục Sản Phẩm',
  tfMarketplace: 'Thị Trường Mục Tiêu',
  tfAiServiceUnavailable: 'Dịch vụ sửa chữa AI tạm thời không khả dụng, vui lòng thử lại sau',

  // Kiểm tra tuân thủ thời gian thực
  tfRealTimeCheck: 'Kiểm Tra Tuân Thủ Thời Gian Thực',
  tfComplianceStatus: 'Trạng Thái Tuân Thủ',
  tfViolations: 'Vi Phạm',
  tfNoViolations: 'Không Có Vi Phạm',
  tfLengthCheck: 'Kiểm Tra Độ Dài',
  tfSpecialCharsCheck: 'Kiểm Tra Ký Tự Đặc Biệt',
  tfRepeatedWordsCheck: 'Kiểm Tra Từ Lặp Lại',
  tfSuggestionsList: 'Đề Xuất Tối Ưu',
  tfViolationDetails: 'Chi Tiết Vi Phạm',
  tfViolationItems: 'Các Mục Vi Phạm',

  // Marketplaces
  marketplaceUS: 'Hoa Kỳ',
  marketplaceUK: 'Vương Quốc Anh',
  marketplaceDE: 'Đức',
  marketplaceFR: 'Pháp',
  marketplaceIT: 'Ý',
  marketplaceES: 'Tây Ban Nha',
  marketplaceJP: 'Nhật Bản',
  marketplaceCA: 'Canada',
  marketplaceAU: 'Úc',

  // Categories
  categoryGeneral: 'Hàng Hóa Tổng Hợp',
  categoryApparel: 'Quần Áo & Phụ Kiện',
  categoryElectronics: 'Điện Tử',
  categoryHome: 'Nhà Cửa & Vườn',
  categoryBeauty: 'Làm Đẹp & Chăm Sóc',
  categorySports: 'Thể Thao & Ngoài Trời',
  categoryToys: 'Đồ Chơi & Trò Chơi',
  categoryBooks: 'Sách & Truyền Thông',
  categoryAutomotive: 'Ô Tô',

  // Mới: Chế Độ Xem So Sánh Thay Đổi
  tfComparisonView: 'So Sánh Thay Đổi',
  tfOriginalVsFixed: 'Tiêu Đề Gốc vs Đã Sửa',
  tfShowComparison: 'Hiển Thị So Sánh',
  tfHideComparison: 'Ẩn So Sánh',
  tfChangesHighlight: 'Làm Nổi Bật Thay Đổi',
  tfAddedText: 'Nội Dung Được Thêm',
  tfRemovedText: 'Nội Dung Bị Xóa',
  tfModifiedText: 'Nội Dung Được Sửa Đổi',

  // Mới: Trực Quan Hóa Vi Phạm
  tfViolationHighlight: 'Làm Nổi Bật Vi Phạm',
  tfSpecialCharViolation: 'Vi Phạm Ký Tự Đặc Biệt',
  tfRepeatedWordViolation: 'Vi Phạm Từ Lặp Lại',
  tfLengthViolation: 'Vi Phạm Độ Dài',
  tfClickToSeeDetails: 'Nhấp để xem chi tiết',

  // Thông điệp kiểm tra tuân thủ
  tfLengthExceeded: 'Độ dài tiêu đề vượt quá: {current}/{max} ký tự',
  tfLengthNearLimit: 'Độ dài tiêu đề gần giới hạn: {current}/{max} ký tự',
  tfSpecialCharsFound: 'Chứa ký tự đặc biệt bị cấm: {chars}',
  tfRepeatedWordsFound: 'Từ lặp lại vượt quá: {words}',
  tfPluralDuplicateFound: 'Phát hiện trùng lặp số ít/số nhiều: "{singular}" và "{plural}"',

  // Thông điệp đề xuất
  tfSugRemoveRepeated: 'Xóa từ lặp lại',
  tfSugSimplifyDesc: 'Đơn giản hóa mô tả sản phẩm',
  tfSugRemoveUnnecessary: 'Loại bỏ từ bổ nghĩa không cần thiết',
  tfSugConsiderConcise: 'Xem xét biểu đạt ngắn gọn',
  tfSugAvoidMoreContent: 'Tránh thêm nội dung',
  tfSugRemoveSpecialChars: 'Xóa ký tự đặc biệt',
  tfSugReplaceWithText: 'Thay thế ký hiệu bằng văn bản',
  tfSugCheckBrandName: 'Kiểm tra xem có cần giữ tên thương hiệu',
  tfSugRemoveExtraRepeated: 'Xóa từ lặp lại thừa',
  tfSugUseSynonyms: 'Sử dụng từ đồng nghĩa thay thế',
  tfSugReorganizeStructure: 'Tổ chức lại cấu trúc tiêu đề',
  tfSugKeepOneForm: 'Chỉ giữ một dạng',
  tfSugUsePreciseDesc: 'Sử dụng mô tả chính xác hơn',
  tfSugFollowPolicy: 'Tuân theo chính sách tiêu đề Amazon 2025 mới nhất',
  tfSugKeepConcise: 'Giữ tiêu đề ngắn gọn và rõ ràng',

  // Thông điệp nút sao chép (xóa khóa trùng lặp)
  tfCopyOptimizedTitle: 'Sao chép tiêu đề đã tối ưu',

  // Tiêu đề Phần Thông tin Chi tiết Công cụ
  tfToolDetailInfoTitle: 'Thông tin Chi tiết Công cụ',
  tfToolDetailInfoSubtitle: 'Tìm hiểu thêm về cách sử dụng Title Compliance Fixer và các thực hành tốt nhất',

  // Phần Giới thiệu Công cụ
  tfToolIntroTitle: 'Giới thiệu Công cụ',
  tfToolIntroContent: 'Title Compliance Fixer là một công cụ thông minh dựa trên AI được thiết kế đặc biệt để khắc phục các vấn đề tuân thủ tiêu đề sản phẩm Amazon.',

  tfToolFeaturesTitle: 'Tính năng Cốt lõi',
  tfFeatureAiAnalysis: 'Phân tích AI Thông minh',
  tfFeatureAiAnalysisDesc: 'Dựa trên chính sách Amazon mới nhất, xác định thông minh các vấn đề vi phạm tiêu đề',

  // Phần Hướng dẫn Sử dụng
  tfUsageGuideTitle: 'Hướng dẫn Sử dụng',
  tfStep1Title: 'Bước 1: Nhập Tiêu đề Gốc',
  tfStep1Content: 'Dán tiêu đề sản phẩm gốc của bạn vào hộp nhập. Công cụ sẽ tự động phát hiện độ dài tiêu đề và các vấn đề tuân thủ cơ bản.',

  // Phần FAQ
  tfFaqTitle: 'Câu hỏi Thường gặp',
  tfFaq1Question: 'Tại sao cần nhập thông tin lỗi Amazon?',
  tfFaq1Answer: 'Thông tin lỗi Amazon chứa lý do vi phạm cụ thể, sau khi nhập AI có thể xác định vấn đề chính xác hơn và cung cấp giải pháp khắc phục có mục tiêu.',

  // Phần Thực hành Tốt nhất
  tfBestPracticesTitle: 'Khuyến nghị Thực hành Tốt nhất',
  tfBestPractice1: 'Kiểm tra tuân thủ tiêu đề định kỳ để tránh tích lũy vi phạm',
  tfBestPractice2: 'Lưu nhiều phiên bản tối ưu hóa để thử nghiệm A/B',
  tfBestPractice3: 'Kết hợp với nghiên cứu từ khóa để chọn phiên bản tiêu đề tốt nhất',
  tfBestPractice4: 'Chú ý cập nhật chính sách Amazon và điều chỉnh chiến lược tiêu đề kịp thời',
  tfBestPractice5: 'Sử dụng tính năng chấm điểm tuân thủ của công cụ để chọn phiên bản điểm cao',

  // Liên quan đến Giới hạn Sử dụng AI
  tfUsageLimitTitle: 'Giới hạn Sử dụng AI',
  tfUsageLimitExceeded: 'Số lần sử dụng đã đạt giới hạn',
  tfHourlyLimitReached: 'Bạn đã đạt giới hạn sử dụng mỗi giờ (10 lần)',
  tfDailyLimitReached: 'Bạn đã đạt giới hạn sử dụng hàng ngày (30 lần)',
  tfSessionLimitReached: 'Bạn đã đạt giới hạn sử dụng phiên này (5 lần)',
  tfCooldownActive: 'Vui lòng đợi 3 phút trước khi sử dụng lại',
  tfRemainingQuota: 'Số lần còn lại',
  tfNextResetTime: 'Thời gian đặt lại',
  tfUsageStats: 'Thống kê sử dụng',
  tfTodayUsage: 'Đã sử dụng hôm nay',
  tfSuccessRate: 'Tỷ lệ thành công',
  tfRefreshToReset: 'Làm mới trang để đặt lại giới hạn phiên',
  tfUsageTips: 'Mẹo sử dụng',
  tfUsageTip1: 'Tiêu đề giống nhau sẽ tự động sử dụng kết quả cache, không tiêu thụ quota',
  tfUsageTip2: 'Khuyến nghị sử dụng kiểm tra tuân thủ cơ bản trước, sau đó sử dụng sửa chữa AI nếu cần',
  tfUsageTip3: 'Mỗi lần sửa chữa tạo ra 3 phiên bản để bạn lựa chọn',
  tfCacheHit: 'Sử dụng kết quả cache',
  tfAiRequest: 'Sửa chữa AI Thông minh',

  // 使用統計界面翻譯
  tfDailyUsageProgress: 'Tiến độ sử dụng hàng ngày',

  // Khóa dịch thuật chung
  loading: 'Đang tải...',
  aiServiceUnavailable: 'Dịch vụ AI tạm thời không khả dụng',
};
