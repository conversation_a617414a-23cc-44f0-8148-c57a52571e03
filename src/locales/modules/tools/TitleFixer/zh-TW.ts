// 標題合規修改器翻譯 - 繁體中文
export const titleFixerZhTW = {
  // Title Fixer
  titleFixer: '標題合規修改器',
  titleFixerDesc: '修復亞馬遜標題合規性問題，符合2025年最新政策。',
  titleFixerShortDesc: 'AI智能修復違規標題，確保合規上架',
  titleFixerHeader: '基於2025年最新政策，智能修復亞馬遜標題合規性問題',
  tfMaxLength: '最大長度',
  tfCharacters: '字符',
  tfOriginalTitle: '原標題',
  tfRequired: '*',
  tfOriginalTitlePlaceholder: '粘貼您的原始產品標題...',
  tfCurrentLength: '當前長度',
  tfMaxAllowed: '最大允許',
  tfLengthNormal: '長度正常',
  tfAmazonError: '亞馬遜錯誤信息',
  tfAmazonErrorPlaceholder: '粘貼亞馬遜返回的錯誤信息，例如：item_name包含不合規內容...',
  tfAmazonErrorHelp: '請完整複製亞馬遜顯示的錯誤信息，以便AI準確分析問題',
  tfAmazonErrorExample: '例如：item_name包含不合規內容、標題長度超限、包含特殊字符等',
  tfAiSmartFix: 'AI 智能修復',
  tfBasicVersion: '基礎版本',
  tfApparelLimit: '服裝類別限制: 125 字符',
  tfUsMarketLimit: '美國市場限制: 200 字符',
  tfWords: '詞',
  tfTitleTooLong: '標題長度超出限制，請縮短至 {maxLength} 字符以內',
  tfOptional: '可選',
  tfStartFixing: '開始智能修復',
  tfFixingInProgress: '正在修復中...',
  tfFixResultScore: '修復結果評分',
  tfComplianceScoreOutOf: '合規評分 (滿分100)',
  tfVersionCount: '個版本',
  tfOptimizedTitle: '優化後標題',
  tfCopyToClipboard: '複製優化後標題',
  tfCopiedToClipboard: '已複製到剪貼板',
  tfChangesCount: '項修改',
  tfStartTitleFix: '開始修復標題',
  tfInputTitleAndError: '輸入原標題和Amazon錯誤信息，點擊修復按鈕開始',
  tfFixTitle: '智能修復標題',
  tfFixingTitle: 'AI正在修復標題...',
  tfWaitingForFix: '等待修復',
  tfWaitingMessage: 'AI正在分析您的標題並生成合規版本，請稍候...',
  tfAiUnavailable: 'AI服務暫時不可用，請檢查網絡連接或稍後重試',
  tfInputRequired: '請輸入原標題和錯誤信息',
  tfPolicyTips: '亞馬遜政策提示',
  tfCharacterLimit: '字符限制',
  tfCharacterLimitDesc: '標題長度不能超過200個字符，建議控制在150字符以內',
  tfSpecialCharacters: '特殊字符',
  tfSpecialCharactersDesc: '避免使用特殊符號、表情符號和非標準字符',
  tfRepeatedWords: '重複詞彙',
  tfRepeatedWordsDesc: '避免在標題中重複使用相同的詞彙或短語',

  // AI服務狀態
  tfUsingGeminiService: '使用AI智能服務',

  tfBrandException: '品牌例外',
  tfBrandExceptionDesc: '某些品牌詞彙可能需要特殊處理，請確認品牌授權',
  tfVersion: '版本',
  tfMinimalChanges: '最小修改',
  tfSeoOptimized: 'SEO優化',
  tfConversionOptimized: '轉化優化',
  tfMinimalVersion: '最小化修改版本',
  tfSeoVersion: 'SEO優化版本',
  tfConversionVersion: '轉化優化版本',
  tfSynonymReplacement: '同義詞替換',
  tfStructurePreserved: '保持原結構',
  tfComplianceScore: '合規性評分',
  tfFixedTitles: '修復後的標題',
  tfVersions: '個版本',
  tfCopied: '已複製',
  tfCopySelected: '複製選中',
  tfLength: '長度',
  tfOptimized: '已優化',
  tfChanges: '修改內容',
  tfDetailedExplanation: '詳細說明',
  tfExceedsLimit: '超出限制',
  tfAnalyzing: '正在分析',
  tfAnalyzingMessage: 'AI正在深度分析您的標題並生成最優解決方案...',
  tfFixFailed: '修復失敗，請重試',
  tfNoExplanation: '暫無詳細說明',
  tfDefaultChange: '標題已根據亞馬遜政策進行調整',
  tfCategory: '產品類別',
  tfMarketplace: '目標市場',
  tfAiServiceUnavailable: 'AI修復服務暫時不可用，請稍後重試',

  // 實時合規檢查
  tfRealTimeCheck: '實時合規檢查',
  tfComplianceStatus: '合規狀態',
  tfViolations: '違規項',
  tfNoViolations: '無違規項',
  tfLengthCheck: '長度檢查',
  tfSpecialCharsCheck: '特殊字符檢查',
  tfRepeatedWordsCheck: '重複詞彙檢查',
  tfSuggestionsList: '優化建議',
  tfViolationDetails: '違規詳情',
  tfViolationItems: '違規項目',

  // Marketplaces
  marketplaceUS: '美國',
  marketplaceUK: '英國',
  marketplaceDE: '德國',
  marketplaceFR: '法國',
  marketplaceIT: '意大利',
  marketplaceES: '西班牙',
  marketplaceJP: '日本',
  marketplaceCA: '加拿大',
  marketplaceAU: '澳大利亞',

  // Categories
  categoryGeneral: '一般商品',
  categoryApparel: '服裝配飾',
  categoryElectronics: '電子產品',
  categoryHome: '家居園藝',
  categoryBeauty: '美容個護',
  categorySports: '運動戶外',
  categoryToys: '玩具遊戲',
  categoryBooks: '圖書音像',
  categoryAutomotive: '汽車用品',

  // 新增：修改對比視圖
  tfComparisonView: '修改對比',
  tfOriginalVsFixed: '原標題 vs 修復後',
  tfShowComparison: '顯示對比',
  tfHideComparison: '隱藏對比',
  tfChangesHighlight: '修改高亮',
  tfAddedText: '新增內容',
  tfRemovedText: '刪除內容',
  tfModifiedText: '修改內容',

  // 新增：違規項可視化
  tfViolationHighlight: '違規標記',
  tfSpecialCharViolation: '特殊字符違規',
  tfRepeatedWordViolation: '重複詞彙違規',
  tfLengthViolation: '長度違規',
  tfClickToSeeDetails: '點擊查看詳情',

  // 合規檢查消息
  tfLengthExceeded: '標題長度超限：{current}/{max} 字符',
  tfLengthNearLimit: '標題長度接近上限：{current}/{max} 字符',
  tfSpecialCharsFound: '包含禁用特殊字符：{chars}',
  tfRepeatedWordsFound: '重複詞彙超限：{words}',
  tfPluralDuplicateFound: '發現單複數重複："{singular}" 和 "{plural}"',

  // 建議消息
  tfSugRemoveRepeated: '刪除重複詞彙',
  tfSugSimplifyDesc: '簡化產品描述',
  tfSugRemoveUnnecessary: '移除不必要的修飾詞',
  tfSugConsiderConcise: '考慮精簡表達',
  tfSugAvoidMoreContent: '避免添加更多內容',
  tfSugRemoveSpecialChars: '刪除特殊字符',
  tfSugReplaceWithText: '用文字替代符號',
  tfSugCheckBrandName: '檢查品牌名是否需要保留',
  tfSugRemoveExtraRepeated: '刪除多餘的重複詞彙',
  tfSugUseSynonyms: '使用同義詞替換',
  tfSugReorganizeStructure: '重新組織標題結構',
  tfSugKeepOneForm: '只保留一種形式',
  tfSugUsePreciseDesc: '使用更精確的描述',
  tfSugFollowPolicy: '遵循亞馬遜2025年最新標題政策',
  tfSugKeepConcise: '保持標題簡潔明了',

  // 複製按鈕訊息 (移除重複鍵)
  tfCopyOptimizedTitle: '複製優化後標題',

  // AI使用限制相關
  tfUsageLimitTitle: 'AI使用限制',
  tfUsageLimitExceeded: '使用次數已達上限',
  tfHourlyLimitReached: '您已達到每小時使用限制（10次）',
  tfDailyLimitReached: '您已達到每日使用限制（30次）',
  tfSessionLimitReached: '您已達到本次會話使用限制（5次）',
  tfCooldownActive: '請等待3分鐘後再次使用',
  tfRemainingQuota: '剩餘次數',
  tfNextResetTime: '重置時間',
  tfUsageStats: '使用統計',
  tfTodayUsage: '今日已用',
  tfSuccessRate: '成功率',
  tfRefreshToReset: '刷新頁面重置會話限制',
  tfUsageTips: '使用小貼士',
  tfUsageTip1: '相同標題會自動使用緩存結果，不消耗次數',
  tfUsageTip2: '建議先使用基礎合規檢查，確認需要後再使用AI修復',
  tfUsageTip3: '每次修復會生成3個版本供您選擇',
  tfCacheHit: '使用緩存結果',
  tfAiRequest: 'AI智能修復',

  // 使用統計界面翻譯
  tfDailyUsageProgress: '今日使用進度',

  // 通用翻譯鍵
  loading: '加載中...',
  aiServiceUnavailable: 'AI服務暫時不可用',

  // 工具詳細信息部分標題
  tfToolDetailInfoTitle: '工具詳細信息',
  tfToolDetailInfoSubtitle: '了解更多關於標題合規修改器的使用方法和最佳實踐',

  // 工具介紹部分
  tfToolIntroTitle: '工具介紹',
  tfToolIntroContent: '標題合規修改器是一款基於AI技術的智能工具，專門用於修復亞馬遜產品標題的合規性問題。該工具基於2025年最新的亞馬遜政策規則，能夠自動識別標題中的違規內容，並提供多個優化版本供您選擇。',

  tfToolFeaturesTitle: '核心功能',
  tfFeatureAiAnalysis: 'AI智能分析',
  tfFeatureAiAnalysisDesc: '基於最新Amazon政策，智能識別標題違規問題',
  tfFeatureMultiVersions: '多版本生成',
  tfFeatureMultiVersionsDesc: '一次修復生成3個優化版本，滿足不同需求',

  // 使用指南部分
  tfUsageGuideTitle: '使用指南',
  tfStep1Title: '第一步：輸入原標題',
  tfStep1Content: '將您的原始產品標題粘貼到輸入框中。工具會自動檢測標題長度和基礎合規性問題。',
  tfStep2Title: '第二步：添加錯誤信息（可選）',
  tfStep2Content: '如果Amazon返回了具體的錯誤信息，請完整複製粘貼到錯誤信息框中。',

  // FAQ部分
  tfFaqTitle: '常見問題',
  tfFaq1Question: '為什麼需要輸入Amazon錯誤信息？',
  tfFaq1Answer: 'Amazon錯誤信息包含了具體的違規原因，輸入後AI能更精準地定位問題並提供針對性的修復方案。',

  tfFaq2Question: '工具是否收費？',
  tfFaq2Answer: '基礎功能完全免費使用，每日有一定的使用次數限制。未來可能會推出高級版本。',
};
