// タイトルコンプライアンス修正器翻訳 - 日本語
export const titleFixerJa = {
  // Title Fixer
  titleFixer: 'タイトルコンプライアンス修正器',
  titleFixerDesc: '2025年最新ポリシーに従ってAmazonタイトルのコンプライアンス問題を修正します。',
  titleFixerShortDesc: 'AIスマート修正で非準拠タイトルを修正し、準拠リスティングを確保',
  titleFixerHeader: '2025年最新ポリシーに基づくAmazonタイトルコンプライアンス問題のスマート修正',
  tfMaxLength: '最大長',
  tfCharacters: '文字',
  tfOriginalTitle: '元のタイトル',
  tfRequired: '*',
  tfOriginalTitlePlaceholder: '元の商品タイトルを貼り付けてください...',
  tfCurrentLength: '現在の長さ',
  tfMaxAllowed: '最大許可',
  tfLengthNormal: '長さ正常',
  tfAmazonError: 'Amazonエラーメッセージ',
  tfAmazonErrorPlaceholder: 'Amazonエラーメッセージを貼り付けてください。例：item_nameに非準拠コンテンツが含まれています...',
  tfAmazonErrorHelp: 'AIが正確に分析できるよう、Amazonが表示する完全なエラーメッセージをコピーしてください',
  tfAmazonErrorExample: '例：item_nameに非準拠コンテンツが含まれている、タイトル長が制限を超えている、特殊文字が含まれているなど',
  tfAiSmartFix: 'AI スマート修正',
  tfBasicVersion: 'ベーシック版',
  tfApparelLimit: 'アパレルカテゴリ制限: 125文字',
  tfUsMarketLimit: '米国市場制限: 200文字',
  tfWords: '語',
  tfTitleTooLong: 'タイトル長が制限を超えています。{maxLength}文字以内に短縮してください',
  tfOptional: 'オプション',
  tfStartFixing: 'スマート修正開始',
  tfFixingInProgress: '修正中...',
  tfFixResultScore: '修正結果スコア',
  tfComplianceScoreOutOf: 'コンプライアンススコア (100点満点)',
  tfVersionCount: 'バージョン',
  tfOptimizedTitle: '最適化されたタイトル',
  tfCopyToClipboard: '最適化されたタイトルをコピー',
  tfCopiedToClipboard: 'クリップボードにコピーしました',
  tfChangesCount: '変更',
  tfStartTitleFix: 'タイトル修正開始',
  tfInputTitleAndError: '元のタイトルとAmazonエラーメッセージを入力し、修正ボタンをクリックして開始',
  tfFixTitle: 'スマートタイトル修正',
  tfFixingTitle: 'AIがタイトルを修正中...',
  tfWaitingForFix: '修正待ち',
  tfWaitingMessage: 'AIがあなたのタイトルを分析し、準拠バージョンを生成しています。お待ちください...',
  tfAiUnavailable: 'AIサービスが一時的に利用できません。ネットワーク接続を確認するか、後でもう一度お試しください',
  tfInputRequired: '元のタイトルとエラーメッセージを入力してください',
  tfPolicyTips: 'Amazonポリシーヒント',
  tfCharacterLimit: '文字制限',
  tfCharacterLimitDesc: 'タイトルの長さは200文字を超えることはできません。150文字以内に抑えることをお勧めします',
  tfSpecialCharacters: '特殊文字',
  tfSpecialCharactersDesc: '特殊記号、絵文字、非標準文字の使用を避けてください',
  tfRepeatedWords: '重複語',
  tfRepeatedWordsDesc: 'タイトル内で同じ単語やフレーズを繰り返し使用することを避けてください',
  tfBrandException: 'ブランド例外',
  tfBrandExceptionDesc: '一部のブランド用語は特別な処理が必要な場合があります。ブランド認証を確認してください',
  tfVersion: 'バージョン',
  tfMinimalChanges: '最小変更',
  tfSeoOptimized: 'SEO最適化',
  tfConversionOptimized: 'コンバージョン最適化',
  tfMinimalVersion: '最小変更版',
  tfSeoVersion: 'SEO最適化版',
  tfConversionVersion: 'コンバージョン最適化版',
  tfSynonymReplacement: '同義語置換',
  
  // AI service status
  tfUsingGeminiService: 'AIインテリジェンスサービスを使用',
  tfStructurePreserved: '構造保持',
  tfComplianceScore: 'コンプライアンススコア',
  tfFixedTitles: '修正されたタイトル',
  tfVersions: 'バージョン',
  tfCopied: 'コピー済み',
  tfCopySelected: '選択をコピー',
  tfLength: '長さ',
  tfOptimized: '最適化済み',
  tfChanges: '変更内容',
  tfDetailedExplanation: '詳細説明',
  tfExceedsLimit: '制限超過',
  tfAnalyzing: '分析中',
  tfAnalyzingMessage: 'AIがあなたのタイトルを深く分析し、最適なソリューションを生成しています...',
  tfFixFailed: '修正に失敗しました。もう一度お試しください',
  tfNoExplanation: '詳細な説明はありません',
  tfDefaultChange: 'タイトルはAmazonポリシーに従って調整されました',
  tfCategory: '商品カテゴリ',
  tfMarketplace: 'ターゲットマーケットプレイス',
  tfAiServiceUnavailable: 'AI修正サービスが一時的に利用できません。後でもう一度お試しください',

  // リアルタイムコンプライアンスチェック
  tfRealTimeCheck: 'リアルタイムコンプライアンスチェック',
  tfComplianceStatus: 'コンプライアンス状態',
  tfViolations: '違反項目',
  tfNoViolations: '違反なし',
  tfLengthCheck: '長さチェック',
  tfSpecialCharsCheck: '特殊文字チェック',
  tfRepeatedWordsCheck: '重複語チェック',
  tfSuggestionsList: '最適化提案',
  tfViolationDetails: '違反詳細',
  tfViolationItems: '違反項目',

  // Marketplaces
  marketplaceUS: 'アメリカ',
  marketplaceUK: 'イギリス',
  marketplaceDE: 'ドイツ',
  marketplaceFR: 'フランス',
  marketplaceIT: 'イタリア',
  marketplaceES: 'スペイン',
  marketplaceJP: '日本',
  marketplaceCA: 'カナダ',
  marketplaceAU: 'オーストラリア',

  // Categories
  categoryGeneral: '一般商品',
  categoryApparel: '衣類・アクセサリー',
  categoryElectronics: '電子機器',
  categoryHome: 'ホーム・ガーデン',
  categoryBeauty: '美容・パーソナルケア',
  categorySports: 'スポーツ・アウトドア',
  categoryToys: 'おもちゃ・ゲーム',
  categoryBooks: '本・メディア',
  categoryAutomotive: '自動車用品',

  // 新機能：変更比較ビュー
  tfComparisonView: '変更比較',
  tfOriginalVsFixed: '元のタイトル vs 修正後',
  tfShowComparison: '比較を表示',
  tfHideComparison: '比較を非表示',
  tfChangesHighlight: '変更のハイライト',
  tfAddedText: '追加されたコンテンツ',
  tfRemovedText: '削除されたコンテンツ',
  tfModifiedText: '変更されたコンテンツ',

  // 新機能：違反項目の可視化
  tfViolationHighlight: '違反のハイライト',
  tfSpecialCharViolation: '特殊文字違反',
  tfRepeatedWordViolation: '重複語句違反',
  tfLengthViolation: '長さ違反',
  tfClickToSeeDetails: 'クリックして詳細を表示',

  // コンプライアンスチェックメッセージ
  tfLengthExceeded: 'タイトル長制限超過：{current}/{max} 文字',
  tfLengthNearLimit: 'タイトル長が上限に近い：{current}/{max} 文字',
  tfSpecialCharsFound: '禁止特殊文字を含む：{chars}',
  tfRepeatedWordsFound: '重複語句制限超過：{words}',
  tfPluralDuplicateFound: '単複数重複発見："{singular}" と "{plural}"',

  // 提案メッセージ
  tfSugRemoveRepeated: '重複語句を削除',
  tfSugSimplifyDesc: '商品説明を簡素化',
  tfSugRemoveUnnecessary: '不要な修飾語を削除',
  tfSugConsiderConcise: '簡潔な表現を検討',
  tfSugAvoidMoreContent: 'より多くのコンテンツの追加を避ける',
  tfSugRemoveSpecialChars: '特殊文字を削除',
  tfSugReplaceWithText: '記号をテキストで置換',
  tfSugCheckBrandName: 'ブランド名の保持が必要か確認',
  tfSugRemoveExtraRepeated: '余分な重複語句を削除',
  tfSugUseSynonyms: '同義語で置換',
  tfSugReorganizeStructure: 'タイトル構造を再編成',
  tfSugKeepOneForm: '一つの形式のみ保持',
  tfSugUsePreciseDesc: 'より正確な説明を使用',
  tfSugFollowPolicy: 'Amazon 2025年最新タイトルポリシーに従う',
  tfSugKeepConcise: 'タイトルを簡潔明瞭に保つ',

  // コピーボタンメッセージ (重複キーを削除)
  tfCopyOptimizedTitle: '最適化されたタイトルをコピー',

  // ツール詳細情報部分のタイトル
  tfToolDetailInfoTitle: 'ツール詳細情報',
  tfToolDetailInfoSubtitle: 'タイトルコンプライアンス修正器の使用方法とベストプラクティスについて詳しく学ぶ',

  // 工具介绍部分
  tfToolIntroTitle: 'ツール紹介',
  tfToolIntroContent: 'タイトルコンプライアンス修正器は、AI技術に基づくスマートツールで、Amazonの商品タイトルのコンプライアンス問題を修正するために特別に設計されています。',

  tfToolFeaturesTitle: 'コア機能',
  tfFeatureAiAnalysis: 'AIスマート分析',
  tfFeatureAiAnalysisDesc: '最新のAmazonポリシーに基づき、タイトルの違反問題をスマートに識別',

  // 使用指南部分
  tfUsageGuideTitle: '使用ガイド',
  tfStep1Title: 'ステップ1：元のタイトルを入力',
  tfStep1Content: '元の商品タイトルを入力ボックスに貼り付けてください。ツールが自動的にタイトルの長さと基本的なコンプライアンス問題を検出します。',

  // FAQ部分
  tfFaqTitle: 'よくある質問',
  tfFaq1Question: 'なぜAmazonエラー情報を入力する必要があるのですか？',
  tfFaq1Answer: 'Amazonエラー情報には具体的な違反理由が含まれており、入力することでAIがより正確に問題を特定し、的確な修正方案を提供できます。',

  // 最佳実践部分
  tfBestPracticesTitle: 'ベストプラクティス推奨',
  tfBestPractice1: 'タイトルコンプライアンスを定期的にチェックし、違反の蓄積を避ける',
  tfBestPractice2: '複数の最適化バージョンを保存し、A/Bテストに使用する',
  tfBestPractice3: 'キーワード研究と組み合わせて、最適なタイトルバージョンを選択する',
  tfBestPractice4: 'Amazonポリシーの更新に注意し、タイトル戦略を適時調整する',
  tfBestPractice5: 'ツールのコンプライアンススコア機能を使用して、高スコアバージョンを選択する',

  // AI使用限制相関
  tfUsageLimitTitle: 'AI使用制限',
  tfUsageLimitExceeded: '使用回数が上限に達しました',
  tfHourlyLimitReached: '1時間の使用制限（10回）に達しました',
  tfDailyLimitReached: '1日の使用制限（30回）に達しました',
  tfSessionLimitReached: 'セッション使用制限（5回）に達しました',
  tfCooldownActive: '3分後に再度使用してください',
  tfRemainingQuota: '残り回数',
  tfNextResetTime: 'リセット時間',
  tfUsageStats: '使用統計',
  tfTodayUsage: '今日の使用',
  tfSuccessRate: '成功率',
  tfRefreshToReset: 'ページを更新してセッション制限をリセット',
  tfUsageTips: '使用のヒント',
  tfUsageTip1: '同じタイトルは自動的にキャッシュ結果を使用し、回数を消費しません',
  tfUsageTip2: '基本コンプライアンスチェックを先に使用し、必要に応じてAI修正を使用することをお勧めします',
  tfUsageTip3: '各修正で3つのバージョンが生成され、選択できます',
  tfCacheHit: 'キャッシュ結果を使用',
  tfAiRequest: 'AIスマート修正',

  // 使用統計界面翻譯
  tfDailyUsageProgress: '今日の使用進捗',

  // 通用翻訳キー
  loading: '読み込み中...',
  aiServiceUnavailable: 'AIサービスが一時的に利用できません',
};
