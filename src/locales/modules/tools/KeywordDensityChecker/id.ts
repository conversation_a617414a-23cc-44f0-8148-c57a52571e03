// Keyword Density Checker translations - Indonesian
export const keywordDensityCheckerId = {
  // Keyword Density Checker
  keywordDensityChecker: 'Pemeriksa Kepadatan Kata Kunci',
  keywordDensityDesc: 'Menganalisis kepadatan kata kunci dalam daftar produk, dioptimalkan untuk pencarian Amazon.',
  targetKeywordsLabel: 'Kata Kunci Target',
  keywordsPlaceholder: 'headphone nirkabel, headphone bluetooth, pembatal kebisingan',
  separateKeywordsComma: 'Pisahkan beberapa kata kunci dengan koma',
  contentToAnalyze: 'Konten untuk Dianalisis',
  contentPlaceholder: 'Tempel judul produk, poin penting, dan deskripsi di sini...',
  sentences: '<PERSON><PERSON>',
  keywordAnalysisResults: 'Hasil Analisis Kata Kunci',
  keywordDensityOptimal: 'Kepadatan kata kunci optimal',
  increaseKeywordUsage: 'Sarankan peningkatan frekuensi penggunaan kata kunci',
  reduceKeywordUsage: 'Sarankan pengurangan penggunaan kata kunci untuk menghindari over-optimasi',
  startKeywordAnalysis: '<PERSON><PERSON>',
  enterKeywordsAndContent: 'Masukkan kata kunci dan konten, kami akan menganalisis kepadatan kata kunci untuk Anda',
  
  // Tool Documentation
  kdcToolIntroTitle: 'Pengenalan Alat',
  kdcToolIntroContent: 'Pemeriksa Kepadatan Kata Kunci adalah alat khusus untuk penjual Amazon mengoptimalkan daftar produk. Dengan menganalisis frekuensi kata kunci dalam daftar, membantu memastikan kepadatan kata kunci berada dalam rentang optimal untuk meningkatkan peringkat produk dalam hasil pencarian.',
  kdcToolFeaturesTitle: 'Fitur Inti',
  kdcFeatureKeywordAnalysis: 'Analisis Kata Kunci',
  kdcFeatureKeywordAnalysisDesc: 'Menghitung secara akurat jumlah kemunculan dan kepadatan setiap kata kunci dalam konten',
  kdcFeatureDensityCalculation: 'Perhitungan Kepadatan',
  kdcFeatureDensityCalculationDesc: 'Secara otomatis menghitung persentase kepadatan kata kunci dan menampilkan hasil secara visual',
  kdcFeatureRecommendations: 'Saran Optimasi',
  kdcFeatureRecommendationsDesc: 'Memberikan saran optimasi spesifik berdasarkan hasil analisis',
  kdcFeatureContentStats: 'Statistik Konten',
  kdcFeatureContentStatsDesc: 'Menampilkan jumlah kata, jumlah karakter, dan jumlah kalimat secara real-time',
  
  kdcUsageGuideTitle: 'Panduan Penggunaan',
  kdcStep1Title: 'Masukkan Kata Kunci Target',
  kdcStep1Content: 'Masukkan kata kunci yang ingin Anda optimalkan di kolom "Kata Kunci Target", pisahkan beberapa kata kunci dengan koma. Misalnya: headphone nirkabel, headphone bluetooth, headphone pembatal kebisingan',
  kdcStep2Title: 'Tempel Konten Produk',
  kdcStep2Content: 'Tempel judul produk, poin penting, dan deskripsi produk ke kotak teks "Konten untuk Dianalisis". Alat akan secara otomatis menganalisis kepadatan kata kunci dalam konten ini.',
  kdcStep3Title: 'Lihat Hasil Analisis',
  kdcStep3Content: 'Alat akan segera menampilkan jumlah kemunculan, persentase kepadatan, dan saran optimasi untuk setiap kata kunci. Diagram batang kepadatan membantu Anda memahami penggunaan kata kunci secara visual.',
  kdcStep4Title: 'Optimalkan Berdasarkan Saran',
  kdcStep4Content: 'Sesuaikan konten daftar Anda sesuai dengan saran optimasi yang disediakan oleh alat untuk menjaga kepadatan kata kunci dalam rentang ideal 1%-3%.',

  kdcFaqTitle: 'Pertanyaan Umum',
  kdcFaq1Question: 'Apa itu kepadatan kata kunci?',
  kdcFaq1Answer: 'Kepadatan kata kunci mengacu pada frekuensi kemunculan kata kunci tertentu dalam teks, biasanya dinyatakan dalam persentase. Dalam SEO Amazon, kepadatan kata kunci yang sesuai membantu meningkatkan peringkat produk, tetapi penggunaan berlebihan dianggap sebagai penumpukan kata kunci.',
  kdcFaq2Question: 'Berapa kepadatan kata kunci ideal?',
  kdcFaq2Answer: 'Umumnya, kepadatan kata kunci 1%-3% dianggap optimal. Terlalu rendah mungkin tidak dapat menyampaikan informasi produk secara efektif kepada mesin pencari, sedangkan terlalu tinggi mungkin dikenai sanksi oleh Amazon sebagai penumpukan kata kunci.',
  kdcFaq3Question: 'Bagaimana cara menghindari penumpukan kata kunci?',
  kdcFaq3Answer: 'Hindari penggunaan kata kunci yang sama berulang kali, gunakan sinonim dan istilah terkait, dan jaga konten tetap alami dan lancar. Kata kunci harus diintegrasikan secara alami ke dalam judul, poin penting, dan deskripsi, bukan dimasukkan secara paksa.',
  kdcFaq4Question: 'Jenis konten apa yang didukung oleh alat ini?',
  kdcFaq4Answer: 'Alat ini mendukung analisis konten teks apa pun, terutama cocok untuk judul produk Amazon, poin penting, dan deskripsi produk. Anda dapat menganalisis konten daftar lengkap sekaligus.',
  kdcFaq5Question: 'Mengapa hasil perhitungan kepadatan mungkin tidak akurat?',
  kdcFaq5Answer: 'Perhitungan kepadatan berdasarkan statistik frekuensi kata sederhana dan mungkin tidak sepenuhnya memahami semantik. Disarankan untuk menggabungkan dengan penilaian manusia untuk memastikan konten alami dan sesuai dengan kebijakan Amazon.',

  kdcBestPracticesTitle: 'Praktik Terbaik',
  kdcBestPractice1: 'Fokus pada kata kunci inti: Utamakan menganalisis kata kunci inti yang paling mewakili produk',
  kdcBestPractice2: 'Jaga konten tetap alami: Hindari mengorbankan keterbacaan dan naturalitas demi kepadatan yang lebih tinggi',
  kdcBestPractice3: 'Gunakan istilah varian: Gunakan sinonim, bentuk jamak, dan varian lain dari kata kunci secara wajar',
  kdcBestPractice4: 'Seimbangkan beberapa kata kunci: Jika menggunakan beberapa kata kunci, pastikan kepadatannya semua berada dalam rentang wajar',
  kdcBestPractice5: 'Periksa dan perbarui secara teratur: Seiring optimasi produk dan perubahan pasar, periksa dan sesuaikan kepadatan kata kunci secara teratur',

  kdcToolDetailInfoTitle: 'Mendalami Pemeriksa Kepadatan Kata Kunci',
  kdcToolDetailInfoSubtitle: 'Kuasai teknik optimasi kata kunci untuk meningkatkan peringkat pencarian produk',
};