// Keyword Density Checker translations - English
export const keywordDensityCheckerEn = {
  // Keyword Density Checker
  keywordDensityChecker: 'Keyword Density Checker',
  keywordDensityDesc: 'Analyze keyword density in product listings, optimized for Amazon search.',
  targetKeywordsLabel: 'Target Keywords',
  keywordsPlaceholder: 'wireless headphones, bluetooth headphones, noise cancelling',
  separateKeywordsComma: 'Separate multiple keywords with commas',
  contentToAnalyze: 'Content to Analyze',
  contentPlaceholder: 'Paste your product title, bullet points and description here...',
  sentences: 'Sentences',
  keywordAnalysisResults: 'Keyword Analysis Results',
  keywordDensityOptimal: 'Keyword density is optimal',
  increaseKeywordUsage: 'Recommend increasing keyword usage frequency',
  reduceKeywordUsage: 'Recommend reducing keyword usage to avoid over-optimization',
  startKeywordAnalysis: 'Start Keyword Analysis',
  enterKeywordsAndContent: 'Enter keywords and content, we will analyze keyword density for you',
  
  // Tool Documentation
  kdcToolIntroTitle: 'Tool Introduction',
  kdcToolIntroContent: 'The Keyword Density Checker is a specialized tool for Amazon sellers to optimize product listings. By analyzing the frequency of keywords in listings, it helps ensure keyword density is within the optimal range to improve product rankings in search results.',
  kdcToolFeaturesTitle: 'Core Features',
  kdcFeatureKeywordAnalysis: 'Keyword Analysis',
  kdcFeatureKeywordAnalysisDesc: 'Accurately counts the occurrences and density of each keyword in the content',
  kdcFeatureDensityCalculation: 'Density Calculation',
  kdcFeatureDensityCalculationDesc: 'Automatically calculates keyword density percentage and visually displays results',
  kdcFeatureRecommendations: 'Optimization Suggestions',
  kdcFeatureRecommendationsDesc: 'Provides specific optimization suggestions based on analysis results',
  kdcFeatureContentStats: 'Content Statistics',
  kdcFeatureContentStatsDesc: 'Displays word count, character count, and sentence count in real-time',
  
  kdcUsageGuideTitle: 'Usage Guide',
  kdcStep1Title: 'Enter Target Keywords',
  kdcStep1Content: 'Enter the keywords you want to optimize in the "Target Keywords" field, separating multiple keywords with commas. For example: wireless headphones, bluetooth headphones, noise cancelling headphones',
  kdcStep2Title: 'Paste Product Content',
  kdcStep2Content: 'Paste your product title, bullet points, and product description into the "Content to Analyze" text box. The tool will automatically analyze keyword density in this content.',
  kdcStep3Title: 'View Analysis Results',
  kdcStep3Content: 'The tool will immediately display the occurrence count, density percentage, and optimization suggestions for each keyword. The density bar chart helps you visually understand keyword usage.',
  kdcStep4Title: 'Optimize Based on Suggestions',
  kdcStep4Content: 'Adjust your listing content according to the optimization suggestions provided by the tool to keep keyword density within the ideal range of 1%-3%.',

  kdcFaqTitle: 'Frequently Asked Questions',
  kdcFaq1Question: 'What is keyword density?',
  kdcFaq1Answer: 'Keyword density refers to the frequency at which a specific keyword appears in text, usually expressed as a percentage. In Amazon SEO, appropriate keyword density helps improve product rankings, but overuse is considered keyword stuffing.',
  kdcFaq2Question: 'What is the ideal keyword density?',
  kdcFaq2Answer: 'Generally, a keyword density of 1%-3% is considered optimal. Too low may not effectively convey product information to search engines, while too high may be penalized by Amazon as keyword stuffing.',
  kdcFaq3Question: 'How to avoid keyword stuffing?',
  kdcFaq3Answer: 'Avoid repeatedly using the same keywords, use synonyms and related terms, and keep the content natural and fluent. Keywords should be organically integrated into titles, bullet points, and descriptions, rather than forcibly inserted.',
  kdcFaq4Question: 'What types of content does the tool support?',
  kdcFaq4Answer: 'The tool supports analysis of any text content, especially suitable for Amazon product titles, bullet points, and product descriptions. You can analyze complete listing content at once.',
  kdcFaq5Question: 'Why might density calculation results be inaccurate?',
  kdcFaq5Answer: 'Density calculation is based on simple word frequency statistics and may not fully understand semantics. It is recommended to combine with human judgment to ensure content is natural and complies with Amazon policies.',

  kdcBestPracticesTitle: 'Best Practices',
  kdcBestPractice1: 'Focus on core keywords: Prioritize analyzing core keywords that best represent the product',
  kdcBestPractice2: 'Keep content natural: Avoid sacrificing readability and naturalness for higher density',
  kdcBestPractice3: 'Use variant terms: Reasonably use synonyms, plural forms, and other variants of keywords',
  kdcBestPractice4: 'Balance multiple keywords: If using multiple keywords, ensure their densities are all within reasonable ranges',
  kdcBestPractice5: 'Regularly check and update: As products are optimized and markets change, regularly check and adjust keyword density',

  kdcToolDetailInfoTitle: 'Deep Dive into Keyword Density Checker',
  kdcToolDetailInfoSubtitle: 'Master keyword optimization techniques to improve product search rankings',
};