// Bản dịch Công cụ Tìm kiếm Bằng sáng chế Thiết kế - Tiếng Việt
export const designPatentSearchVi = {
  // Thông tin cơ bản của công cụ
  designPatentSearch: 'Tìm kiếm Bằng sáng chế Thiết kế',
  designPatentSearchDesc: 'Tìm kiếm thông tin bằng sáng chế thiết kế nhanh chóng với hỗ trợ từ khóa, số bằng sáng chế, người nộp đơn và nhiều phương pháp tìm kiếm khác để giúp bạn nghiên cứu và phân tích bằng sáng chế.',
  designPatentSearchShortDesc: 'Tìm kiếm thông tin bằng sáng chế thiết kế với nhiều phương pháp tìm kiếm',

  // Thông báo trạng thái phát triển
  developmentNotice: 'Tính năng đang Phát triển',
  developmentNoticeDesc: 'Công cụ này hiện đang được phát triển và hiển thị dữ liệu mô phỏng. Chức năng thực tế sẽ kết nối với cơ sở dữ liệu bằng sáng chế thực.',

  // Liên quan đến tìm kiếm
  searchPatents: 'Tìm kiếm Bằng sáng chế',
  searchType: 'Loại Tìm kiếm',
  searchByKeyword: 'Tìm kiếm theo Từ khóa',
  searchByNumber: 'Tìm kiếm theo Số Bằng sáng chế',
  searchByApplicant: 'Tìm kiếm theo Người nộp đơn',
  searchQuery: 'Truy vấn Tìm kiếm',
  searchPlaceholder: 'Nhập từ khóa, số bằng sáng chế, hoặc tên người nộp đơn...',
  search: 'Tìm kiếm',
  searching: 'Đang tìm kiếm...',
  searchQueryRequired: 'Vui lòng nhập truy vấn tìm kiếm',
  searchCompleted: 'Tìm kiếm hoàn tất, tìm thấy {count} kết quả',
  searchError: 'Tìm kiếm thất bại, vui lòng thử lại sau',

  // Kết quả tìm kiếm
  searchResults: 'Kết quả Tìm kiếm',
  patentNumber: 'Số Bằng sáng chế',
  applicant: 'Người nộp đơn',
  filingDate: 'Ngày nộp đơn',
  publicationDate: 'Ngày công bố',
  status: 'Trạng thái',
  classification: 'Phân loại',
  description: 'Mô tả',
  
  // Trạng thái bằng sáng chế
  statusGranted: 'Đã cấp',
  statusPending: 'Đang chờ',
  statusExpired: 'Đã hết hạn',

  // Nút hành động
  viewDetails: 'Xem Chi tiết',
  viewOriginal: 'Xem Bản gốc',
  close: 'Đóng',
  downloadReport: 'Tải xuống Báo cáo',

  // Chi tiết bằng sáng chế
  patentDetails: 'Chi tiết Bằng sáng chế',

  // Dữ liệu mô phỏng
  mockPatent1Title: 'Thiết kế Smartphone',
  mockPatent1Desc: 'Thiết kế smartphone với màn hình hình chữ nhật góc tròn và khung kim loại, có tính thẩm mỹ sạch sẽ và hiện đại.',
  mockPatent2Title: 'Thiết kế Ốp lưng Bảo vệ Tablet',
  mockPatent2Desc: 'Thiết kế ốp lưng bảo vệ tablet có thể gập với chức năng giá đỡ và tính năng điều chỉnh đa góc.',
  mockPatent3Title: 'Hộp Sạc Tai nghe Không dây',
  mockPatent3Desc: 'Thiết kế hộp sạc tai nghe không dây hình oval với xử lý bề mặt nhám.',
};
