// 外观专利查询工具翻译 - 简体中文
export const designPatentSearchZh = {
  // 工具基本信息
  designPatentSearch: '外观专利查询',
  designPatentSearchDesc: '快速查询外观专利信息，支持关键词、专利号、申请人等多种搜索方式，帮助您进行专利检索和分析。',
  designPatentSearchShortDesc: '查询外观专利信息，支持多种搜索方式',

  // 开发状态提示
  developmentNotice: '开发中功能',
  developmentNoticeDesc: '此工具正在开发中，当前显示的是模拟数据。实际功能将连接真实的专利数据库。',

  // 搜索相关
  searchPatents: '搜索专利',
  searchType: '搜索类型',
  searchByKeyword: '关键词搜索',
  searchByNumber: '专利号搜索',
  searchByApplicant: '申请人搜索',
  searchQuery: '搜索内容',
  searchPlaceholder: '请输入搜索关键词、专利号或申请人名称...',
  search: '搜索',
  searching: '搜索中...',
  searchQueryRequired: '请输入搜索内容',
  searchCompleted: '搜索完成，找到 {count} 条结果',
  searchError: '搜索失败，请稍后重试',

  // 搜索结果
  searchResults: '搜索结果',
  patentNumber: '专利号',
  applicant: '申请人',
  filingDate: '申请日期',
  publicationDate: '公布日期',
  status: '状态',
  classification: '分类号',
  description: '描述',
  
  // 专利状态
  statusGranted: '已授权',
  statusPending: '审查中',
  statusExpired: '已失效',

  // 操作按钮
  viewDetails: '查看详情',
  viewOriginal: '查看原文',
  close: '关闭',
  downloadReport: '下载报告',

  // 专利详情
  patentDetails: '专利详情',

  // 模拟数据
  mockPatent1Title: '智能手机外观设计',
  mockPatent1Desc: '一种具有圆角矩形屏幕和金属边框的智能手机外观设计，具有简洁现代的视觉效果。',
  mockPatent2Title: '平板电脑保护套设计',
  mockPatent2Desc: '一种可折叠的平板电脑保护套设计，具有支架功能和多角度调节特性。',
  mockPatent3Title: '无线耳机充电盒外观',
  mockPatent3Desc: '一种椭圆形无线耳机充电盒的外观设计，表面采用磨砂质感处理。',
};
