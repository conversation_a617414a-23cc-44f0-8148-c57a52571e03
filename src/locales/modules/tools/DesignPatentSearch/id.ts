// Ter<PERSON><PERSON>an Alat Pencarian Paten Desain - Bahasa Indonesia
export const designPatentSearchId = {
  // Informasi dasar alat
  designPatentSearch: 'Pencarian Paten Desain',
  designPatentSearchDesc: 'Cari informasi paten desain dengan cepat menggunakan kata kunci, nomor paten, pemohon dan berbagai metode pencarian lainnya untuk membantu penelitian dan analisis paten Anda.',
  designPatentSearchShortDesc: 'Cari informasi paten desain dengan berbagai metode pencarian',

  // Pemberitahuan status pengembangan
  developmentNotice: 'Fitur dalam Pengembangan',
  developmentNoticeDesc: 'Alat ini sedang dalam pengembangan dan menampilkan data simulasi. Fungsi sebenarnya akan terhubung ke database paten yang nyata.',

  // Terkait pencarian
  searchPatents: 'Cari Paten',
  searchType: '<PERSON><PERSON>',
  searchByKeyword: 'Pencarian Kat<PERSON>',
  searchByNumber: 'Pencarian Nomor Paten',
  searchByApplicant: 'Pencar<PERSON> Pemohon',
  searchQuery: 'Query Pencarian',
  searchPlaceholder: '<PERSON><PERSON>kka<PERSON> kata kunci, nomor paten, atau nama pemohon...',
  search: 'Cari',
  searching: 'Mencari...',
  searchQueryRequired: 'Silakan masukkan query pencarian',
  searchCompleted: 'Pencarian selesai, ditemukan {count} hasil',
  searchError: 'Pencarian gagal, silakan coba lagi nanti',

  // Hasil pencarian
  searchResults: 'Hasil Pencarian',
  patentNumber: 'Nomor Paten',
  applicant: 'Pemohon',
  filingDate: 'Tanggal Pengajuan',
  publicationDate: 'Tanggal Publikasi',
  status: 'Status',
  classification: 'Klasifikasi',
  description: 'Deskripsi',
  
  // Status paten
  statusGranted: 'Diberikan',
  statusPending: 'Tertunda',
  statusExpired: 'Kedaluwarsa',

  // Tombol aksi
  viewDetails: 'Lihat Detail',
  viewOriginal: 'Lihat Asli',
  close: 'Tutup',
  downloadReport: 'Unduh Laporan',

  // Detail paten
  patentDetails: 'Detail Paten',

  // Data simulasi
  mockPatent1Title: 'Desain Smartphone',
  mockPatent1Desc: 'Desain smartphone dengan layar persegi panjang sudut membulat dan bingkai logam, menampilkan daya tarik visual yang bersih dan modern.',
  mockPatent2Title: 'Desain Casing Pelindung Tablet',
  mockPatent2Desc: 'Desain casing pelindung tablet yang dapat dilipat dengan fungsi penyangga dan fitur penyesuaian multi-sudut.',
  mockPatent3Title: 'Casing Pengisi Daya Earphone Nirkabel',
  mockPatent3Desc: 'Desain casing pengisi daya earphone nirkabel berbentuk oval dengan perlakuan permukaan matte.',
};
