// 外觀專利查詢工具翻譯 - 繁體中文
export const designPatentSearchZhTW = {
  // 工具基本信息
  designPatentSearch: '外觀專利查詢',
  designPatentSearchDesc: '快速查詢外觀專利資訊，支援關鍵詞、專利號、申請人等多種搜尋方式，幫助您進行專利檢索和分析。',
  designPatentSearchShortDesc: '查詢外觀專利資訊，支援多種搜尋方式',

  // 開發狀態提示
  developmentNotice: '開發中功能',
  developmentNoticeDesc: '此工具正在開發中，目前顯示的是模擬資料。實際功能將連接真實的專利資料庫。',

  // 搜索相關
  searchPatents: '搜尋專利',
  searchType: '搜尋類型',
  searchByKeyword: '關鍵詞搜尋',
  searchByNumber: '專利號搜尋',
  searchByApplicant: '申請人搜尋',
  searchQuery: '搜尋內容',
  searchPlaceholder: '請輸入搜尋關鍵詞、專利號或申請人名稱...',
  search: '搜尋',
  searching: '搜尋中...',
  searchQueryRequired: '請輸入搜尋內容',
  searchCompleted: '搜尋完成，找到 {count} 條結果',
  searchError: '搜尋失敗，請稍後重試',

  // 搜索結果
  searchResults: '搜尋結果',
  patentNumber: '專利號',
  applicant: '申請人',
  filingDate: '申請日期',
  publicationDate: '公佈日期',
  status: '狀態',
  classification: '分類號',
  description: '描述',
  
  // 專利狀態
  statusGranted: '已授權',
  statusPending: '審查中',
  statusExpired: '已失效',

  // 操作按鈕
  viewDetails: '查看詳情',
  viewOriginal: '查看原文',
  close: '關閉',
  downloadReport: '下載報告',

  // 專利詳情
  patentDetails: '專利詳情',

  // 模擬數據
  mockPatent1Title: '智慧型手機外觀設計',
  mockPatent1Desc: '一種具有圓角矩形螢幕和金屬邊框的智慧型手機外觀設計，具有簡潔現代的視覺效果。',
  mockPatent2Title: '平板電腦保護套設計',
  mockPatent2Desc: '一種可摺疊的平板電腦保護套設計，具有支架功能和多角度調節特性。',
  mockPatent3Title: '無線耳機充電盒外觀',
  mockPatent3Desc: '一種橢圓形無線耳機充電盒的外觀設計，表面採用磨砂質感處理。',
};
