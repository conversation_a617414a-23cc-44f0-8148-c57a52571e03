// 意匠特許検索ツール翻訳 - 日本語
export const designPatentSearchJa = {
  // ツール基本情報
  designPatentSearch: '意匠特許検索',
  designPatentSearchDesc: 'キーワード、特許番号、出願人など複数の検索方法で意匠特許情報を素早く検索し、特許調査と分析をサポートします。',
  designPatentSearchShortDesc: '意匠特許情報を検索、複数の検索方法をサポート',

  // 開発状況通知
  developmentNotice: '開発中機能',
  developmentNoticeDesc: 'このツールは現在開発中で、模擬データを表示しています。実際の機能では実際の特許データベースに接続します。',

  // 検索関連
  searchPatents: '特許検索',
  searchType: '検索タイプ',
  searchByKeyword: 'キーワード検索',
  searchByNumber: '特許番号検索',
  searchByApplicant: '出願人検索',
  searchQuery: '検索内容',
  searchPlaceholder: 'キーワード、特許番号、または出願人名を入力してください...',
  search: '検索',
  searching: '検索中...',
  searchQueryRequired: '検索内容を入力してください',
  searchCompleted: '検索完了、{count}件の結果が見つかりました',
  searchError: '検索に失敗しました。後でもう一度お試しください',

  // 検索結果
  searchResults: '検索結果',
  patentNumber: '特許番号',
  applicant: '出願人',
  filingDate: '出願日',
  publicationDate: '公開日',
  status: 'ステータス',
  classification: '分類番号',
  description: '説明',
  
  // 特許ステータス
  statusGranted: '登録済み',
  statusPending: '審査中',
  statusExpired: '失効',

  // アクションボタン
  viewDetails: '詳細を見る',
  viewOriginal: '原文を見る',
  close: '閉じる',
  downloadReport: 'レポートダウンロード',

  // 特許詳細
  patentDetails: '特許詳細',

  // 模擬データ
  mockPatent1Title: 'スマートフォンデザイン',
  mockPatent1Desc: '角の丸い矩形スクリーンと金属フレームを持つスマートフォンデザイン、シンプルでモダンな視覚効果を持つ。',
  mockPatent2Title: 'タブレット保護ケースデザイン',
  mockPatent2Desc: '折りたたみ可能なタブレット保護ケースデザイン、スタンド機能と多角度調整特性を持つ。',
  mockPatent3Title: 'ワイヤレスイヤホン充電ケース外観',
  mockPatent3Desc: '楕円形のワイヤレスイヤホン充電ケースの外観デザイン、表面にマット質感処理を採用。',
};
