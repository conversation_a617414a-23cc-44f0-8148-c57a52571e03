// Design Patent Search Tool Translation - English
export const designPatentSearchEn = {
  // Tool basic information
  designPatentSearch: 'Design Patent Search',
  designPatentSearchDesc: 'Quickly search design patent information with support for keywords, patent numbers, applicants and more search methods to help you with patent research and analysis.',
  designPatentSearchShortDesc: 'Search design patent information with multiple search methods',

  // Development status notice
  developmentNotice: 'Feature in Development',
  developmentNoticeDesc: 'This tool is currently under development and shows mock data. The actual functionality will connect to real patent databases.',

  // Search related
  searchPatents: 'Search Patents',
  searchType: 'Search Type',
  searchByKeyword: 'Keyword Search',
  searchByNumber: 'Patent Number Search',
  searchByApplicant: 'Applicant Search',
  searchQuery: 'Search Query',
  searchPlaceholder: 'Enter keywords, patent number, or applicant name...',
  search: 'Search',
  searching: 'Searching...',
  searchQueryRequired: 'Please enter search query',
  searchCompleted: 'Search completed, found {count} results',
  searchError: 'Search failed, please try again later',

  // Search results
  searchResults: 'Search Results',
  patentNumber: 'Patent Number',
  applicant: 'Applicant',
  filingDate: 'Filing Date',
  publicationDate: 'Publication Date',
  status: 'Status',
  classification: 'Classification',
  description: 'Description',
  
  // Patent status
  statusGranted: 'Granted',
  statusPending: 'Pending',
  statusExpired: 'Expired',

  // Action buttons
  viewDetails: 'View Details',
  viewOriginal: 'View Original',
  close: 'Close',
  downloadReport: 'Download Report',

  // Patent details
  patentDetails: 'Patent Details',

  // Mock data
  mockPatent1Title: 'Smartphone Design',
  mockPatent1Desc: 'A smartphone design with rounded rectangular screen and metal frame, featuring clean and modern visual appeal.',
  mockPatent2Title: 'Tablet Protective Case Design',
  mockPatent2Desc: 'A foldable tablet protective case design with stand functionality and multi-angle adjustment features.',
  mockPatent3Title: 'Wireless Earphone Charging Case',
  mockPatent3Desc: 'An oval-shaped wireless earphone charging case design with matte surface treatment.',
};
