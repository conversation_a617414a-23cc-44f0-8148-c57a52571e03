// Reference Table - English Translation
export const referenceTableEn = {
  // Tool basic information
  referenceTable: 'Reference Tables',
  referenceTableDesc: 'Comprehensive reference tables for Amazon sellers, including geography, sizing, logistics and more categories with quick search and data export features',
  referenceTableShortDesc: 'Common reference table lookup tool',

  // Categories
  allCategories: 'All Categories',
  geographyCategory: 'Geography',
  sizingCategory: 'Sizing',
  logisticsCategory: 'Logistics',
  financialCategory: 'Financial',
  technicalCategory: 'Technical',

  // Interface operations
  backToTables: 'Back to Tables',
  searchInTable: 'Search in table...',
  exportCSV: 'Export CSV',
  exporting: 'Exporting...',
  clickToCopy: 'Click to copy',
  copyRow: 'Copy row',
  actions: 'Actions',
  entries: 'entries',
  viewTable: 'View Table',
  searchResults: 'Search Results',
  noSearchResults: 'No matching results found',
  noDataAvailable: 'No data available',
  clearSearch: 'Clear search',

  // US States table
  usStatesTable: 'US States Abbreviation Table',
  usStatesTableDesc: 'Reference table of major US states with English names, local names, 2-letter codes, 3-letter codes and capitals',
  stateName: 'State Name',
  localName: 'Local Name',
  abbreviation2Letter: '2-Letter Code',
  abbreviation3Letter: '3-Letter Code',
  capital: 'Capital',

  // Global shoe sizes table
  shoeSizesTable: 'Global Shoe Sizes Table',
  shoeSizesTableDesc: '2025 updated international shoe size conversion table with foot length reference',
  usShoeSizes: 'US Size',
  euShoeSizes: 'EU Size',
  ukShoeSizes: 'UK Size',
  cnShoeSizes: 'CN Size',
  jpShoeSizes: 'JP Size',
  footLength: 'Foot Length',

  // Incoterms table
  incotermsTable: 'Incoterms Reference Table',
  incotermsTableDesc: 'Incoterms 2020 international commercial terms reference table',
  incotermCode: 'Term Code',
  incotermFullName: 'Full Name',
  sellerResponsibility: 'Seller Responsibility',
  riskTransferPoint: 'Risk Transfer Point',

  // Currency codes table
  currencyCodesTable: 'Currency Codes Table',
  currencyCodesTableDesc: '2025 updated international currency names, local names, ISO codes and symbols reference table',
  currencyName: 'Currency Name',
  currencyCode: 'Currency Code',
  currencySymbol: 'Currency Symbol',
  primaryCountry: 'Primary Country',

  // Paper sizes table
  paperSizesTable: 'Paper Sizes Table',
  paperSizesTableDesc: 'International standard paper sizes reference table with millimeter and inch measurements',
  paperSizeName: 'Paper Size',
  widthMm: 'Width (mm)',
  heightMm: 'Height (mm)',
  widthInches: 'Width (inches)',
  heightInches: 'Height (inches)',

  // Country codes table
  countryCodesTable: 'Country Codes Table',
  countryCodesTableDesc: '2025 updated ISO country codes reference table with local names',
  countryName: 'Country Name',
  iso2Code: 'ISO2 Code',
  iso3Code: 'ISO3 Code',
  numericCode: 'Numeric Code',

  // Clothing sizes table
  clothingSizesTable: 'Clothing Sizes Table',
  clothingSizesTableDesc: '2025 international clothing size conversion table with chest and waist measurements',
  sizeLabel: 'Size Label',
  usClothingSize: 'US Size',
  euClothingSize: 'EU Size',
  ukClothingSize: 'UK Size',
  chestSize: 'Chest Size',
  waistSize: 'Waist Size',

  // Battery types table
  batteryTypesTable: 'Battery Types Table',
  batteryTypesTableDesc: '2025 common battery types and specifications with voltage and dimensions',
  commonName: 'Common Name',
  iecName: 'IEC Standard',
  ansiName: 'ANSI Standard',
  voltage: 'Voltage',
  dimensions: 'Dimensions',

  // Screw specifications table
  screwSpecsTable: 'Screw Specifications Table',
  screwSpecsTableDesc: '2025 metric and imperial screw specifications with thread pitch and head types',
  screwType: 'Screw Type',
  metricSize: 'Metric Size',
  imperialSize: 'Imperial Size',
  threadPitch: 'Thread Pitch',
  headType: 'Head Type',

  // Usage tips
  tips: {
    title: 'Usage Tips',
    tip1: 'Click any cell to quickly copy its content',
    tip2: 'Use search function to quickly locate information',
    tip3: 'Export to CSV format for offline use',
    tip4: 'Data is updated in real-time for accuracy'
  }
};
