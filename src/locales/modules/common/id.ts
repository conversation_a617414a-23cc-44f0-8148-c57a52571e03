// Ter<PERSON><PERSON>an umum - Bahasa Indonesia
export const commonId = {
  // Header
  features: 'Fitur',
  pricing: 'Harga',
  reviews: '<PERSON><PERSON><PERSON>',
  contact: '<PERSON>nta<PERSON>',
  feedback: 'Umpan Bali<PERSON>',
  startFreeTrial: '<PERSON><PERSON>',
  tools: 'Alat',
  freeTools: 'Alat Gratis',
  freeRegistration: 'Registrasi Gratis',
  siteSubtitle: 'Toolkit Penjual Lintas Batas',
  headerAnnouncement: '🎉 Alat Amazon Gratis · Tanpa Registrasi · Gunakan Sekarang',
  feedbackButton: 'Umpan Balik',
  
  // Footer
  aboutAuthor: 'Tentang Penulis',
  authorIntro1: '<PERSON>a adalah mantan <strong>arsitek</strong> dari perusahaan internet besar, sangat familiar dengan operasi e-commerce Amazon.',
  authorIntro2: 'Saya menggabungkan pengalaman teknis bertahun-tahun dengan pengetahuan operasional e-commerce untuk mengembangkan toolkit operasional yang <strong style="color: #ea580c;">gratis dan terbuka</strong> ini.',
  authorIntro3: '<PERSON>a berharap dapat membantu penjual kecil dan menengah meningkatkan efisiensi operasional melalui toolifikasi, dan <strong>mengeksplorasi serta berkembang bersama</strong> dengan semua orang.',
  freeAndOpen: 'Gratis & Terbuka',
  freeAndOpenDesc: 'Semua alat sepenuhnya gratis\nTanpa registrasi diperlukan',
  growTogether: 'Berkembang Bersama',
  growTogetherDesc: 'Jelajahi praktik terbaik\ndengan penjual kecil dan menengah',
  techDriven: 'Didorong Teknologi',
  techDrivenDesc: 'Gunakan teknologi untuk\nmenyelesaikan masalah operasional',
  communicationAndFeedback: 'Komunikasi & Umpan Balik',
  communicationDesc: 'Selamat datang untuk bertukar pengalaman operasional Amazon dengan saya, atau menyarankan perbaikan untuk alat-alat ini',
  emailContact: 'Kontak Email',
  forumDiscussion: 'Diskusi Forum',
  onlineFeedback: 'Umpan Balik Online',
  forumComingSoon: 'Fitur diskusi forum sedang dalam pengembangan, nantikan!',
  copyright: '© 2025 SellerBox.asia. Toolkit operasional gratis yang dirancang untuk penjual lintas batas',
  companyName: 'Shenzhen Yunduan Silue Technology Co., Ltd.',
  icpMainRecord: '粤ICP备**********号',
  icpWebsiteRecord: '粤ICP备**********号-2',

  // Common UI elements
  backToHome: 'Kembali ke Beranda',
  hot: 'Populer',
  developing: 'Dalam Pengembangan',
  freeToUse: 'Gratis Digunakan',
  optional: 'Opsional',

  // Tools Navigation
  toolsList: 'Daftar Alat',
  backToTools: 'Kembali ke Alat',

  // Categories
  generalCategory: 'Umum',
  electronicsCategory: 'Elektronik',
  clothingCategory: 'Pakaian & Aksesoris',
  homeCategory: 'Rumah & Taman',
  booksCategory: 'Buku',
  healthCategory: 'Kesehatan & Perawatan Pribadi',

  // Legal
  termsOfService: 'Syarat Layanan',
  privacyPolicy: 'Kebijakan Privasi',

  // Kategori Alat (dipindahkan dari modul tools)
  optimizationTools: 'Alat Optimasi',
  financialTools: 'Alat Keuangan',
  researchTools: 'Alat Riset',
  managementTools: 'Alat Manajemen',
  analyticsTools: 'Alat Analitik',

  // Status Alat (dipindahkan dari modul tools)
  useToolNow: 'Gunakan Sekarang',
  comingSoon: 'Segera Hadir',
  hotTools: '🔥 Alat Populer',
  allTools: 'Semua Alat',
  toolsCount: '{count} Alat',

  // Elemen Antarmuka Dasar (dipindahkan dari modul tools)
  marketplace: 'Marketplace',
  category: 'Kategori',
  maxLength: 'Panjang Maksimal',
  words: 'Kata',
  characters: 'Karakter',
  analysisResults: 'Hasil Analisis',
  optimizationSuggestions: 'Saran Optimasi',
  complianceCheck: 'Pemeriksaan Kepatuhan',
  performanceMetrics: 'Metrik Kinerja',

  // Status Layanan AI
  aiServiceStatus: 'Status Layanan AI',
  aiServiceName: 'Layanan Kecerdasan AI',
  serviceAvailable: 'Layanan Tersedia',
  serviceUnavailable: 'Layanan Tidak Tersedia',
  serviceLimited: 'Layanan Terbatas',
  serviceHealthy: 'Layanan Sehat',
  serviceUnhealthy: 'Layanan Bermasalah',
  serviceConnecting: 'Menghubungkan',
  statusHealthyDesc: 'Layanan AI berjalan normal dengan waktu respons yang baik',
  statusUnhealthyDesc: 'Layanan AI sementara tidak tersedia, silakan coba lagi nanti',
  statusConnectingDesc: 'Menghubungkan ke layanan AI...',
  statusUnavailableDesc: 'Layanan AI sementara tidak dapat diakses',
  showDetails: 'Tampilkan Detail',
  hideDetails: 'Sembunyikan Detail',
  refreshStatus: 'Perbarui Status',
  aiServiceSettings: 'Pengaturan Layanan AI',
  responseTime: 'Waktu Respons',
  lastChecked: 'Terakhir Diperiksa',
  connectionError: 'Kesalahan Koneksi',

  // Layanan AI (sekarang menggunakan proxy Gemini terpadu)
  aiServiceProvider: 'Layanan Proxy Gemini AI',

  // Statistik Layanan AI
  totalRequests: 'Total Permintaan',
  successfulRequests: 'Permintaan Berhasil',
  failedRequests: 'Permintaan Gagal',
  successRate: 'Tingkat Keberhasilan',
  averageResponseTime: 'Waktu Respons Rata-rata',
  tokenUsage: 'Penggunaan Token',
  estimatedCost: 'Perkiraan Biaya',
  lastUsed: 'Terakhir Digunakan',

  // Peringatan Layanan AI
  freeQuotaExceeded: 'Kuota gratis mungkin telah terlampaui',
  freeQuotaWarning: 'Penggunaan berkelanjutan dapat menimbulkan biaya. Silakan periksa saldo akun Anda.',
  apiKeyNotConfigured: 'Kunci API tidak dikonfigurasi',
  checkApiKeyConfiguration: 'Silakan periksa konfigurasi kunci API',

  // Layanan AI Terkait
  aiProvider: 'Penyedia Layanan AI',
  aiProviderAuto: 'Pilihan Cerdas',
  aiServiceUnavailable: 'Layanan AI sementara tidak tersedia',
  aiServiceError: 'Peringatan Layanan AI',
  allAiServicesUnavailable: 'Semua layanan AI tidak tersedia',
};
