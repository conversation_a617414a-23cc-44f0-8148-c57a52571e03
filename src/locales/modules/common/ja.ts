// 共通翻訳 - 日本語
export const commonJa = {
  // Header
  features: '機能',
  pricing: '料金',
  reviews: 'レビュー',
  contact: 'お問い合わせ',
  feedback: 'フィードバック',
  startFreeTrial: '無料トライアル開始',
  tools: 'ツール',
  freeTools: '無料ツール',
  freeRegistration: '無料登録',
  siteSubtitle: '越境ECセラーツールキット',
  headerAnnouncement: '🎉 無料Amazonツール · 登録不要 · 今すぐ使用',
  feedbackButton: 'フィードバック',
  
  // Footer
  aboutAuthor: '作者について',
  authorIntro1: '私は大手インターネット企業の元<strong>アーキテクト</strong>で、Amazonのeコマース運営に精通しています。',
  authorIntro2: '長年の技術経験とeコマース運営知識を組み合わせて、この<strong style="color: #ea580c;">無料でオープン</strong>な運営ツールセットを開発しました。',
  authorIntro3: 'ツール化により中小セラーの運営効率向上を支援し、皆様と<strong>共に探求し、共に進歩</strong>したいと思います。',
  freeAndOpen: '無料・オープン',
  freeAndOpenDesc: 'すべてのツールが完全無料\n登録不要で使用可能',
  growTogether: '共に成長',
  growTogetherDesc: '中小セラーと一緒に\nベストプラクティスを探求',
  techDriven: '技術主導',
  techDrivenDesc: '技術で運営の\n課題を解決',
  communicationAndFeedback: 'コミュニケーション・フィードバック',
  communicationDesc: 'Amazon運営経験の交換やツールの改善提案をお待ちしています',
  emailContact: 'メール連絡',
  forumDiscussion: 'フォーラム議論',
  onlineFeedback: 'オンラインフィードバック',
  forumComingSoon: 'フォーラム議論機能は開発中です。お楽しみに！',
  copyright: '© 2025 SellerBox.asia. 越境セラー向け無料運営ツールキット',
  companyName: '深圳市雲端思略科技有限公司',
  icpMainRecord: '粤ICP备**********号',
  icpWebsiteRecord: '粤ICP备**********号-2',

  // Common UI elements
  backToHome: 'ホームに戻る',
  hot: 'ホット',
  developing: '開発中',
  freeToUse: '無料使用',
  optional: 'オプション',

  // Tools Navigation
  toolsList: 'ツールリスト',
  backToTools: 'ツールに戻る',

  // Categories
  generalCategory: '一般',
  electronicsCategory: '電子機器',
  clothingCategory: '衣類・アクセサリー',
  homeCategory: 'ホーム・ガーデン',
  booksCategory: '書籍',
  healthCategory: 'ヘルス・パーソナルケア',

  // Legal
  termsOfService: '利用規約',
  privacyPolicy: 'プライバシーポリシー',

  // ツールカテゴリ (toolsモジュールから移動)
  optimizationTools: '最適化ツール',
  financialTools: '財務ツール',
  researchTools: '調査ツール',
  managementTools: '管理ツール',
  analyticsTools: '分析ツール',

  // ツールステータス (toolsモジュールから移動)
  useToolNow: '今すぐ使用',
  comingSoon: '近日公開',
  hotTools: '🔥 人気ツール',
  allTools: 'すべてのツール',
  toolsCount: '{count} ツール',

  // 基本インターフェース要素 (toolsモジュールから移動)
  marketplace: 'マーケットプレイス',
  category: 'カテゴリ',
  maxLength: '最大長',
  words: '単語',
  characters: '文字',
  analysisResults: '分析結果',
  optimizationSuggestions: '最適化提案',
  complianceCheck: 'コンプライアンスチェック',
  performanceMetrics: 'パフォーマンス指標',

  // AIサービス状態
  aiServiceStatus: 'AIサービス状態',
  aiServiceName: 'AIインテリジェンスサービス',
  serviceAvailable: 'サービス利用可能',
  serviceUnavailable: 'サービス利用不可',
  serviceLimited: 'サービス制限中',
  serviceHealthy: 'サービス正常',
  serviceUnhealthy: 'サービス異常',
  serviceConnecting: '接続中',
  statusHealthyDesc: 'AIサービスは正常に動作しており、応答速度も良好です',
  statusUnhealthyDesc: 'AIサービスは一時的に利用できません。しばらくしてから再試行してください',
  statusConnectingDesc: 'AIサービスに接続中...',
  statusUnavailableDesc: 'AIサービスに一時的にアクセスできません',
  showDetails: '詳細を表示',
  hideDetails: '詳細を非表示',
  refreshStatus: 'ステータス更新',
  aiServiceSettings: 'AIサービス設定',
  responseTime: '応答時間',
  lastChecked: '最終確認',
  connectionError: '接続エラー',

  // AIサービス（現在は統一Geminiプロキシを使用）
  aiServiceProvider: 'Gemini AIプロキシサービス',

  // AIサービス統計
  totalRequests: '総リクエスト数',
  successfulRequests: '成功リクエスト',
  failedRequests: '失敗リクエスト',
  successRate: '成功率',
  averageResponseTime: '平均応答時間',
  tokenUsage: 'トークン使用量',
  estimatedCost: '推定コスト',
  lastUsed: '最終使用時刻',

  // AIサービス警告
  freeQuotaExceeded: '無料枠を超過している可能性があります',
  freeQuotaWarning: '継続使用により料金が発生する可能性があります。アカウント残高をご確認ください。',
  apiKeyNotConfigured: 'APIキーが設定されていません',
  checkApiKeyConfiguration: 'APIキーの設定をご確認ください',

  // AIサービス関連
  aiProvider: 'AIサービスプロバイダー',
  aiProviderAuto: 'スマート選択',
  aiServiceUnavailable: 'AIサービスが一時的に利用できません',
  aiServiceError: 'AIサービス通知',
  allAiServicesUnavailable: 'すべてのAIサービスが利用できません',
};
