// 通用翻译 - 简体中文
export const commonZh = {
  // Header
  features: '功能特色',
  pricing: '价格方案',
  reviews: '用户评价',
  contact: '联系我们',
  feedback: '用户反馈',
  startFreeTrial: '开始免费试用',
  tools: '工具',
  freeTools: '免费工具',
  freeRegistration: '免费注册',
  siteSubtitle: '亚马逊运营工具箱',
  headerAnnouncement: '🎉 免费亚马逊运营工具 · 无需注册 · 立即使用',
  feedbackButton: '意见反馈',
  
  // Footer
  aboutAuthor: '关于作者',
  authorIntro1: '我是一名前互联网大厂的<strong>架构师</strong>，深度熟悉亚马逊电商运营流程。',
  authorIntro2: '我将多年的技术经验与电商运营知识相结合，开发了这套<strong style="color: #ea580c;">免费开放</strong>的运营工具集。',
  authorIntro3: '希望通过工具化的方式，帮助中小卖家提升运营效率，与大家<strong>共同探讨、共同进步</strong>。',
  freeAndOpen: '免费开放',
  freeAndOpenDesc: '所有工具完全免费\n无需注册即可使用',
  growTogether: '共同成长',
  growTogetherDesc: '与中小卖家一起\n探讨最佳实践',
  techDriven: '技术驱动',
  techDrivenDesc: '用技术解决\n运营痛点',
  communicationAndFeedback: '交流与反馈',
  communicationDesc: '欢迎与我交流亚马逊运营经验，或对工具提出改进建议',
  emailContact: '邮件联系',
  forumDiscussion: '论坛讨论',
  onlineFeedback: '在线反馈',
  forumComingSoon: '论坛讨论功能开发中，敬请期待！',
  copyright: '© 2025 AmzOva.com. 专为亚马逊卖家打造的免费运营工具集',

  // Common UI elements
  backToHome: '返回首页',
  hot: '热门',
  developing: '开发中',
  freeToUse: '免费使用',
  optional: '可选',

  // Tools Navigation
  toolsList: '工具列表',
  backToTools: '返回工具',
  
  // Categories
  generalCategory: '一般',
  electronicsCategory: '电子产品',
  clothingCategory: '服装配饰',

  // AI Service Status
  aiServiceStatus: 'AI 服务状态',
  aiServiceName: 'AI 智能服务',
  serviceAvailable: '服务可用',
  serviceUnavailable: '服务不可用',
  serviceLimited: '服务受限',
  serviceHealthy: '服务正常',
  serviceUnhealthy: '服务异常',
  serviceConnecting: '连接中',
  statusHealthyDesc: 'AI 服务运行正常，响应速度良好',
  statusUnhealthyDesc: 'AI 服务暂时不可用，请稍后重试',
  statusConnectingDesc: '正在连接 AI 服务...',
  statusUnavailableDesc: 'AI 服务暂时无法访问',
  showDetails: '显示详情',
  hideDetails: '隐藏详情',
  refreshStatus: '刷新状态',
  aiServiceSettings: 'AI 服务设置',
  responseTime: '响应时间',
  lastChecked: '最后检查',
  connectionError: '连接错误',

  // AI Service (通用AI服务，不显示具体提供商)
  aiServiceProvider: 'AI 智能服务',

  // AI Service Statistics
  totalRequests: '总请求数',
  successfulRequests: '成功请求',
  failedRequests: '失败请求',
  successRate: '成功率',
  averageResponseTime: '平均响应时间',
  tokenUsage: 'Token 使用量',
  estimatedCost: '估算成本',
  lastUsed: '最后使用时间',

  // AI Service Warnings
  freeQuotaExceeded: '免费额度可能已用完',
  freeQuotaWarning: '继续使用可能产生费用，请检查您的账户余额。',
  apiKeyNotConfigured: 'API 密钥未配置',
  checkApiKeyConfiguration: '请检查 API 密钥配置',
  homeCategory: '家居园艺',
  booksCategory: '图书',
  healthCategory: '健康个护',

  // Legal
  termsOfService: '服务条款',
  privacyPolicy: '隐私政策',

  // 工具分类 (从 tools 模块移入)
  optimizationTools: '优化工具',
  financialTools: '财务工具',
  researchTools: '研究工具',
  managementTools: '管理工具',
  analyticsTools: '分析工具',

  // 工具状态 (从 tools 模块移入)
  useToolNow: '立即使用',
  comingSoon: '敬请期待',
  hotTools: '🔥 热门工具',
  allTools: '所有工具',
  toolsCount: '{count} 个工具',

  // 基础界面元素 (从 tools 模块移入)
  marketplace: '市场',
  category: '类别',
  maxLength: '最大长度',
  words: '单词',
  characters: '字符',
  analysisResults: '分析结果',
  optimizationSuggestions: '优化建议',
  complianceCheck: '合规检查',
  performanceMetrics: '性能指标',

  // AI 服务相关
  aiProvider: 'AI 服务提供商',
  aiProviderAuto: '智能选择',
  aiServiceUnavailable: 'AI 服务暂时不可用',
  aiServiceError: 'AI 服务提示',
  allAiServicesUnavailable: '所有 AI 服务都不可用',
};
