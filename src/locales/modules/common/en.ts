// Common translations - English
export const commonEn = {
  // Header
  features: 'Features',
  pricing: 'Pricing',
  reviews: 'Reviews',
  contact: 'Contact',
  feedback: 'Feedback',
  startFreeTrial: 'Start Free Trial',
  tools: 'Tools',
  freeTools: 'Free Tools',
  freeRegistration: 'Free Registration',
  siteSubtitle: 'Amazon Seller Toolkit',
  headerAnnouncement: '🎉 Free Amazon Tools · No Registration · Use Now',
  feedbackButton: 'Feedback',
  
  // Footer
  aboutAuthor: 'About Author',
  authorIntro1: 'I am a former <strong>architect</strong> from a major internet company, deeply familiar with Amazon e-commerce operations.',
  authorIntro2: 'I combine years of technical experience with e-commerce operational knowledge to develop this <strong style="color: #ea580c;">free and open</strong> operational toolkit.',
  authorIntro3: 'I hope to help small and medium sellers improve operational efficiency through toolification, and <strong>explore and progress together</strong> with everyone.',
  freeAndOpen: 'Free & Open',
  freeAndOpenDesc: 'All tools completely free\nNo registration required',
  growTogether: 'Grow Together',
  growTogetherDesc: 'Explore best practices\nwith small and medium sellers',
  techDriven: 'Tech Driven',
  techDrivenDesc: 'Use technology to solve\noperational pain points',
  communicationAndFeedback: 'Communication & Feedback',
  communicationDesc: 'Welcome to exchange Amazon operational experience with me, or suggest improvements to the tools',
  emailContact: 'Email Contact',
  forumDiscussion: 'Forum Discussion',
  onlineFeedback: 'Online Feedback',
  forumComingSoon: 'Forum discussion feature under development, stay tuned!',
  copyright: '© 2025 AmzOva.com. Free operational toolkit designed for Amazon sellers',

  // Common UI elements
  backToHome: 'Back to Home',
  hot: 'Hot',
  developing: 'Developing',
  freeToUse: 'Free to Use',
  optional: 'Optional',

  // Tools Navigation
  toolsList: 'Tools List',
  backToTools: 'Back to Tools',
  
  // Categories
  generalCategory: 'General',
  electronicsCategory: 'Electronics',
  clothingCategory: 'Clothing & Accessories',
  homeCategory: 'Home & Garden',
  booksCategory: 'Books',
  healthCategory: 'Health & Personal Care',

  // Legal
  termsOfService: 'Terms of Service',
  privacyPolicy: 'Privacy Policy',

  // Tool Categories (moved from tools module)
  optimizationTools: 'Optimization Tools',
  financialTools: 'Financial Tools',
  researchTools: 'Research Tools',
  managementTools: 'Management Tools',
  analyticsTools: 'Analytics Tools',

  // Tool Status (moved from tools module)
  useToolNow: 'Use Now',
  comingSoon: 'Coming Soon',
  hotTools: '🔥 Hot Tools',
  allTools: 'All Tools',
  toolsCount: '{count} Tools',

  // Basic Interface Elements (moved from tools module)
  marketplace: 'Marketplace',
  category: 'Category',
  maxLength: 'Max Length',
  words: 'Words',
  characters: 'Characters',
  analysisResults: 'Analysis Results',
  optimizationSuggestions: 'Optimization Suggestions',
  complianceCheck: 'Compliance Check',
  performanceMetrics: 'Performance Metrics',

  // AI Service Status
  aiServiceStatus: 'AI Service Status',
  aiServiceName: 'AI Intelligence Service',
  serviceAvailable: 'Service Available',
  serviceUnavailable: 'Service Unavailable',
  serviceLimited: 'Service Limited',
  serviceHealthy: 'Service Healthy',
  serviceUnhealthy: 'Service Unhealthy',
  serviceConnecting: 'Connecting',
  statusHealthyDesc: 'AI service is running normally with good response time',
  statusUnhealthyDesc: 'AI service is temporarily unavailable, please try again later',
  statusConnectingDesc: 'Connecting to AI service...',
  statusUnavailableDesc: 'AI service is temporarily inaccessible',
  showDetails: 'Show Details',
  hideDetails: 'Hide Details',
  refreshStatus: 'Refresh Status',
  aiServiceSettings: 'AI Service Settings',
  responseTime: 'Response Time',
  lastChecked: 'Last Checked',
  connectionError: 'Connection Error',

  // AI Service (now using unified Gemini proxy)
  aiServiceProvider: 'Gemini AI Proxy Service',

  // AI Service Statistics
  totalRequests: 'Total Requests',
  successfulRequests: 'Successful Requests',
  failedRequests: 'Failed Requests',
  successRate: 'Success Rate',
  averageResponseTime: 'Average Response Time',
  tokenUsage: 'Token Usage',
  estimatedCost: 'Estimated Cost',
  lastUsed: 'Last Used',

  // AI Service Warnings
  freeQuotaExceeded: 'Free quota may be exceeded',
  freeQuotaWarning: 'Continued use may incur charges. Please check your account balance.',
  apiKeyNotConfigured: 'API key not configured',
  checkApiKeyConfiguration: 'Please check API key configuration',

  // AI Service Related
  aiProvider: 'AI Service Provider',
  aiProviderAuto: 'Smart Selection',
  aiServiceUnavailable: 'AI service temporarily unavailable',
  aiServiceError: 'AI Service Alert',
  allAiServicesUnavailable: 'All AI services are unavailable',
};
