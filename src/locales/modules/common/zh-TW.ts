// 通用翻譯 - 繁體中文
export const commonZhTW = {
  // Header
  features: '功能特色',
  pricing: '價格方案',
  reviews: '用戶評價',
  contact: '聯繫我們',
  feedback: '用戶反饋',
  startFreeTrial: '開始免費試用',
  tools: '工具',
  freeTools: '免費工具',
  freeRegistration: '免費註冊',
  siteSubtitle: '跨境賣家工具箱',
  headerAnnouncement: '🎉 免費亞馬遜運營工具 · 無需註冊 · 立即使用',
  feedbackButton: '意見反饋',
  
  // Footer
  aboutAuthor: '關於作者',
  authorIntro1: '我是一名前互聯網大廠的<strong>架構師</strong>，深度熟悉亞馬遜電商運營流程。',
  authorIntro2: '我將多年的技術經驗與電商運營知識相結合，開發了這套<strong style="color: #ea580c;">免費開放</strong>的運營工具集。',
  authorIntro3: '希望通過工具化的方式，幫助中小賣家提升運營效率，與大家<strong>共同探討、共同進步</strong>。',
  freeAndOpen: '免費開放',
  freeAndOpenDesc: '所有工具完全免費\n無需註冊即可使用',
  growTogether: '共同成長',
  growTogetherDesc: '與中小賣家一起\n探討最佳實踐',
  techDriven: '技術驅動',
  techDrivenDesc: '用技術解決\n運營痛點',
  communicationAndFeedback: '交流與反饋',
  communicationDesc: '歡迎與我交流亞馬遜運營經驗，或對工具提出改進建議',
  emailContact: '郵件聯繫',
  forumDiscussion: '論壇討論',
  onlineFeedback: '在線反饋',
  forumComingSoon: '論壇討論功能開發中，敬請期待！',
  copyright: '© 2025 SellerBox.asia. 專為跨境賣家打造的免費運營工具集',
  companyName: '深圳市雲端思略科技有限公司',
  icpMainRecord: '粵ICP備**********號',
  icpWebsiteRecord: '粵ICP備**********號-2',

  // Common UI elements
  backToHome: '返回首頁',
  hot: '熱門',
  developing: '開發中',
  freeToUse: '免費使用',
  optional: '可選',

  // Tools Navigation
  toolsList: '工具列表',
  backToTools: '返回工具',

  // Categories
  generalCategory: '一般',
  electronicsCategory: '電子產品',
  clothingCategory: '服裝配飾',
  homeCategory: '家居園藝',
  booksCategory: '圖書',
  healthCategory: '健康個護',

  // Legal
  termsOfService: '服務條款',
  privacyPolicy: '隱私政策',

  // 工具分類 (從 tools 模組移入)
  optimizationTools: '優化工具',
  financialTools: '財務工具',
  researchTools: '研究工具',
  managementTools: '管理工具',
  analyticsTools: '分析工具',

  // 工具狀態 (從 tools 模組移入)
  useToolNow: '立即使用',
  comingSoon: '敬請期待',
  hotTools: '🔥 熱門工具',
  allTools: '所有工具',
  toolsCount: '{count} 個工具',

  // 基礎介面元素 (從 tools 模組移入)
  marketplace: '市場',
  category: '類別',
  maxLength: '最大長度',
  words: '單詞',
  characters: '字符',
  analysisResults: '分析結果',
  optimizationSuggestions: '優化建議',
  complianceCheck: '合規檢查',
  performanceMetrics: '性能指標',

  // AI 服務狀態
  aiServiceStatus: 'AI 服務狀態',
  aiServiceName: 'AI 智能服務',
  serviceAvailable: '服務可用',
  serviceUnavailable: '服務不可用',
  serviceLimited: '服務受限',
  serviceHealthy: '服務正常',
  serviceUnhealthy: '服務異常',
  serviceConnecting: '連接中',
  statusHealthyDesc: 'AI 服務運行正常，響應速度良好',
  statusUnhealthyDesc: 'AI 服務暫時不可用，請稍後重試',
  statusConnectingDesc: '正在連接 AI 服務...',
  statusUnavailableDesc: 'AI 服務暫時無法訪問',
  showDetails: '顯示詳情',
  hideDetails: '隱藏詳情',
  refreshStatus: '刷新狀態',
  aiServiceSettings: 'AI 服務設定',
  responseTime: '響應時間',
  lastChecked: '最後檢查',
  connectionError: '連接錯誤',

  // AI 服務（現在使用統一Gemini代理）
  aiServiceProvider: 'Gemini AI代理服務',

  // AI 服務統計
  totalRequests: '總請求數',
  successfulRequests: '成功請求',
  failedRequests: '失敗請求',
  successRate: '成功率',
  averageResponseTime: '平均響應時間',
  tokenUsage: 'Token 使用量',
  estimatedCost: '估算成本',
  lastUsed: '最後使用時間',

  // AI 服務警告
  freeQuotaExceeded: '免費額度可能已用完',
  freeQuotaWarning: '繼續使用可能產生費用，請檢查您的帳戶餘額。',
  apiKeyNotConfigured: 'API 密鑰未配置',
  checkApiKeyConfiguration: '請檢查 API 密鑰配置',

  // AI 服務相關
  aiProvider: 'AI 服務提供商',
  aiProviderAuto: '智能選擇',
  aiServiceUnavailable: 'AI 服務暫時不可用',
  aiServiceError: 'AI 服務提示',
  allAiServicesUnavailable: '所有 AI 服務都不可用',
};
