// Page translations - English
export const pagesEn = {
  // User Feedback
  userFeedbackTitle: 'Share Your Feedback',
  userFeedbackDescription: 'Help us improve our tools and build features that matter to you. Your feedback drives our development roadmap.',
  weValueYourFeedback: 'We Value Your Feedback',
  selectFeedbackType: 'What would you like to share?',
  generalFeedback: 'General Feedback',
  generalFeedbackDesc: 'Share your thoughts, suggestions, or overall experience',
  bugReport: 'Bug Report',
  bugReportDesc: 'Report technical issues or errors you encountered',
  featureRequest: 'Feature Request',
  featureRequestDesc: 'Suggest new features or tool improvements',
  improvement: 'Improvement Suggestion',
  improvementDesc: 'Suggest how to improve existing features',
  compliment: 'Compliment',
  complaint: 'Complaint',
  howWasExperience: 'How was your experience?',
  excellent: 'Excellent',
  good: 'Good',
  average: 'Average',
  poor: 'Poor',
  terrible: 'Terrible',
  yourMessage: 'Your Message',
  messagePlaceholder: 'Tell us about your experience, suggestions, or any issues you encountered...',
  emailOptional: 'Email (Optional)',
  emailPlaceholder: '<EMAIL>',
  emailNote: 'We\'ll only use this email to follow up on your feedback if needed.',
  submitFeedback: 'Submit Feedback',
  submittingFeedback: 'Submitting Feedback...',
  submitting: 'Submitting...',
  characters: 'characters',
  feedbackSubmitted: 'Feedback Submitted',
  feedbackSubmittedDesc: 'Thank you for your feedback! We\'ll review it carefully and use it to improve our tools.',
  feedbackThankYou: 'Thank you for your feedback! We\'ll review it carefully and use it to improve our tools.',
  feedbackError: 'Submission Failed',
  feedbackErrorDesc: 'Sorry, there was an error submitting your feedback. Please try again later.',
  tryAgain: 'Try Again',
  backToHome: 'Back to Home',

  // Feedback Stats
  feedbackStats: 'Feedback Statistics',
  totalFeedback: 'Total Feedback',
  avgRating: 'Average Rating',
  overallRating: 'Overall Rating',

  // Recent Feedback Highlights
  recentFeedbackHighlights: 'Recent Feedback Highlights',
  feedbackExample1: 'Hope to add more currency conversion options',
  feedbackExample2: 'Great interface design, but hope it loads faster',
  feedbackExample3: 'Title optimizer tool sometimes gets stuck',
  implemented: 'Implemented',
  inProgress: 'In Progress',
  fixed: 'Fixed',

  // Privacy Note
  privacyNote: 'We value your privacy. Your feedback will be used to improve our services and will not be shared with third parties.',
  
  // Error Pages
  pageNotFound: 'Page Not Found',
  pageNotFoundDesc: 'Sorry, the page you are looking for does not exist.',
  goHome: 'Go Home',
  
  // Loading States
  loading: 'Loading...',
  pleaseWait: 'Please wait',
  
  // Success Messages
  success: 'Success',
  operationCompleted: 'Operation completed',
  
  // Error Messages
  error: 'Error',
  somethingWentWrong: 'Something went wrong',
  networkError: 'Network error',
  serverError: 'Server error',
  
  // Form Validation
  fieldRequired: 'This field is required',
  invalidEmail: 'Please enter a valid email address',
  messageTooShort: 'Message must be at least 10 characters',
  messageTooLong: 'Message cannot exceed 1000 characters',
  
  // Common Actions
  submit: 'Submit',
  cancel: 'Cancel',
  save: 'Save',
  delete: 'Delete',
  edit: 'Edit',
  view: 'View',
  close: 'Close',
  confirm: 'Confirm',
  
  // Time and Date
  justNow: 'Just now',
  minutesAgo: '{minutes} minutes ago',
  hoursAgo: '{hours} hours ago',
  daysAgo: '{days} days ago',
  weeksAgo: '{weeks} weeks ago',
  monthsAgo: '{months} months ago',
  
  // Search
  search: 'Search',
  searchPlaceholder: 'Search...',
  noResults: 'No results found',
  searchResults: 'Search Results',
};
