// Ter<PERSON><PERSON><PERSON> halaman - Bahasa Indonesia
export const pagesId = {
  // User Feedback
  userFeedbackTitle: 'Bagikan Umpan Balik Anda',
  userFeedbackDescription: 'Bantu kami meningkatkan alat dan membangun fitur yang penting bagi Anda. Umpan balik Anda mendorong peta jalan pengembangan kami.',
  weValueYourFeedback: '<PERSON><PERSON> Bali<PERSON> And<PERSON>',
  selectFeedbackType: 'Apa yang ingin Anda bagikan?',
  generalFeedback: 'Um<PERSON>k Um<PERSON>',
  generalFeedbackDesc: 'Bagikan pemikiran, saran, atau pengalaman keseluruhan Anda',
  bugReport: 'Laporan Bug',
  bugReportDesc: 'Laporkan masalah teknis atau kesalahan yang Anda temui',
  featureRequest: 'Permintaan Fitur',
  featureRequestDesc: 'Sarankan fitur baru atau perbaikan alat',
  improvement: 'Saran Perbaikan',
  improvementDesc: 'Sarankan cara meningkatkan fitur yang ada',
  compliment: 'Pujian',
  complaint: '<PERSON><PERSON><PERSON>',
  howWasExperience: 'Bagaimana pengalaman Anda?',
  excellent: 'Sangat Baik',
  good: 'Baik',
  average: 'Rata-rata',
  poor: 'Buruk',
  terrible: 'Sangat Buruk',
  yourMessage: 'Pesan Anda',
  messagePlaceholder: 'Ceritakan kepada kami tentang pengalaman, saran, atau masalah yang Anda temui...',
  emailOptional: 'Email (Opsional)',
  emailPlaceholder: '<EMAIL>',
  emailNote: 'Kami hanya akan menggunakan email ini untuk menindaklanjuti umpan balik Anda jika diperlukan.',
  submitFeedback: 'Kirim Umpan Balik',
  submittingFeedback: 'Mengirim Umpan Balik...',
  submitting: 'Mengirim...',
  characters: 'karakter',
  feedbackSubmitted: 'Umpan Balik Terkirim',
  feedbackSubmittedDesc: 'Terima kasih atas umpan balik Anda! Kami akan meninjau dengan cermat dan menggunakannya untuk meningkatkan alat kami.',
  feedbackThankYou: 'Terima kasih atas umpan balik Anda! Kami akan meninjau dengan cermat dan menggunakannya untuk meningkatkan alat kami.',
  feedbackError: 'Pengiriman Gagal',
  feedbackErrorDesc: 'Maaf, terjadi kesalahan saat mengirim umpan balik Anda. Silakan coba lagi nanti.',
  tryAgain: 'Coba Lagi',
  backToHome: 'Kembali ke Beranda',

  // Feedback Stats
  feedbackStats: 'Statistik Umpan Balik',
  totalFeedback: 'Total Umpan Balik',
  avgRating: 'Rating Rata-rata',
  overallRating: 'Rating Keseluruhan',

  // Recent Feedback Highlights
  recentFeedbackHighlights: 'Sorotan Umpan Balik Terbaru',
  feedbackExample1: 'Berharap dapat menambahkan lebih banyak opsi konversi mata uang',
  feedbackExample2: 'Desain antarmuka bagus, tapi berharap bisa memuat lebih cepat',
  feedbackExample3: 'Alat pengoptimal judul kadang macet',
  implemented: 'Diimplementasikan',
  inProgress: 'Sedang Berlangsung',
  fixed: 'Diperbaiki',

  // Privacy Note
  privacyNote: 'Kami menghargai privasi Anda. Umpan balik Anda akan digunakan untuk meningkatkan layanan kami dan tidak akan dibagikan kepada pihak ketiga.',

  // Error Pages
  pageNotFound: 'Halaman Tidak Ditemukan',
  pageNotFoundDesc: 'Maaf, halaman yang Anda cari tidak ada.',
  goHome: 'Kembali ke Beranda',

  // Loading States
  loading: 'Memuat...',
  pleaseWait: 'Harap tunggu',

  // Success Messages
  success: 'Berhasil',
  operationCompleted: 'Operasi selesai',

  // Error Messages
  error: 'Kesalahan',
  somethingWentWrong: 'Terjadi kesalahan',
  networkError: 'Kesalahan jaringan',
  serverError: 'Kesalahan server',

  // Form Validation
  fieldRequired: 'Bidang ini wajib diisi',
  invalidEmail: 'Harap masukkan alamat email yang valid',
  messageTooShort: 'Pesan harus minimal 10 karakter',
  messageTooLong: 'Pesan tidak boleh melebihi 1000 karakter',

  // Common Actions
  submit: 'Kirim',
  cancel: 'Batal',
  save: 'Simpan',
  delete: 'Hapus',
  edit: 'Edit',
  view: 'Lihat',
  close: 'Tutup',
  confirm: 'Konfirmasi',

  // Time and Date
  justNow: 'Baru saja',
  minutesAgo: '{minutes} menit yang lalu',
  hoursAgo: '{hours} jam yang lalu',
  daysAgo: '{days} hari yang lalu',
  weeksAgo: '{weeks} minggu yang lalu',
  monthsAgo: '{months} bulan yang lalu',

  // Pagination
  previous: 'Sebelumnya',
  next: 'Selanjutnya',
  page: 'Halaman {page}',
  of: 'dari {total}',

  // Search
  search: 'Cari',
  searchPlaceholder: 'Cari...',
  noResults: 'Tidak ada hasil ditemukan',
  searchResults: 'Hasil Pencarian',

  // Filters
  filter: 'Filter',
  filterBy: 'Filter berdasarkan {field}',
  clearFilters: 'Hapus Filter',
  applyFilters: 'Terapkan Filter',

  // Sorting
  sortBy: 'Urutkan berdasarkan',
  sortAscending: 'Naik',
  sortDescending: 'Turun',

  // Data Display
  noData: 'Tidak ada data',
  dataLoading: 'Memuat data...',
  dataError: 'Gagal memuat data',
  refreshData: 'Refresh data',
};
