// 页面翻译 - 简体中文
export const pagesZh = {
  // User Feedback
  userFeedbackTitle: '分享您的反馈',
  userFeedbackDescription: '帮助我们改进工具并构建对您重要的功能。您的反馈推动我们的开发路线图。',
  weValueYourFeedback: '我们重视您的反馈',
  selectFeedbackType: '您想分享什么？',
  generalFeedback: '一般反馈',
  generalFeedbackDesc: '分享您的想法、建议或整体体验',
  bugReport: '错误报告',
  bugReportDesc: '报告您遇到的技术问题或错误',
  featureRequest: '功能请求',
  featureRequestDesc: '建议新功能或工具改进',
  improvement: '改进建议',
  improvementDesc: '建议如何改进现有功能',
  compliment: '表扬',
  complaint: '投诉',
  howWasExperience: '您的体验如何？',
  excellent: '优秀',
  good: '良好',
  average: '一般',
  poor: '较差',
  terrible: '很差',
  yourMessage: '您的消息',
  messagePlaceholder: '告诉我们您的体验、建议或遇到的任何问题...',
  emailOptional: '邮箱（可选）',
  emailPlaceholder: '<EMAIL>',
  emailNote: '我们只会在需要时用此邮箱跟进您的反馈。',
  submitFeedback: '提交反馈',
  submittingFeedback: '正在提交反馈...',
  submitting: '提交中...',
  characters: '字符',
  feedbackSubmitted: '反馈已提交',
  feedbackSubmittedDesc: '感谢您的反馈！我们会仔细审阅并用于改进我们的工具。',
  feedbackThankYou: '感谢您的反馈！我们会仔细审阅并用于改进我们的工具。',
  feedbackError: '提交失败',
  feedbackErrorDesc: '抱歉，提交反馈时出现错误。请稍后重试。',
  tryAgain: '重试',
  backToHome: '返回首页',

  // Feedback Stats
  feedbackStats: '反馈统计',
  totalFeedback: '总反馈数',
  avgRating: '平均评分',
  overallRating: '总体评分',

  // Recent Feedback Highlights
  recentFeedbackHighlights: '最近反馈亮点',
  feedbackExample1: '希望能添加更多货币转换选项',
  feedbackExample2: '界面设计很棒，但希望能更快加载',
  feedbackExample3: '标题优化工具有时会卡住',
  implemented: '已实现',
  inProgress: '进行中',
  fixed: '已修复',

  // Privacy Note
  privacyNote: '我们重视您的隐私。您的反馈将用于改进我们的服务，不会与第三方分享。',
  
  // Error Pages
  pageNotFound: '页面未找到',
  pageNotFoundDesc: '抱歉，您访问的页面不存在。',
  goHome: '返回首页',
  
  // Loading States
  loading: '加载中...',
  pleaseWait: '请稍候',
  
  // Success Messages
  success: '成功',
  operationCompleted: '操作已完成',
  
  // Error Messages
  error: '错误',
  somethingWentWrong: '出现了一些问题',
  networkError: '网络错误',
  serverError: '服务器错误',
  
  // Form Validation
  fieldRequired: '此字段为必填项',
  invalidEmail: '请输入有效的邮箱地址',
  messageTooShort: '消息至少需要10个字符',
  messageTooLong: '消息不能超过1000个字符',
  
  // Common Actions
  submit: '提交',
  cancel: '取消',
  save: '保存',
  delete: '删除',
  edit: '编辑',
  view: '查看',
  close: '关闭',
  confirm: '确认',
  
  // Time and Date
  justNow: '刚刚',
  minutesAgo: '{minutes}分钟前',
  hoursAgo: '{hours}小时前',
  daysAgo: '{days}天前',
  weeksAgo: '{weeks}周前',
  monthsAgo: '{months}个月前',
  
  // Pagination
  previous: '上一页',
  next: '下一页',
  page: '第{page}页',
  of: '共{total}页',
  
  // Search
  search: '搜索',
  searchPlaceholder: '搜索...',
  noResults: '没有找到结果',
  searchResults: '搜索结果',
  
  // Filters
  filter: '筛选',
  filterBy: '按{field}筛选',
  clearFilters: '清除筛选',
  applyFilters: '应用筛选',
  
  // Sorting
  sortBy: '排序方式',
  sortAscending: '升序',
  sortDescending: '降序',
  
  // Data Display
  noData: '暂无数据',
  dataLoading: '数据加载中...',
  dataError: '数据加载失败',
  refreshData: '刷新数据',
};
