// ページ翻訳 - 日本語
export const pagesJa = {
  // User Feedback
  userFeedbackTitle: 'フィードバックを共有',
  userFeedbackDescription: 'ツールの改善と、あなたにとって重要な機能の構築にご協力ください。あなたのフィードバックが開発ロードマップを推進します。',
  weValueYourFeedback: 'あなたのフィードバックを大切にします',
  selectFeedbackType: '何を共有したいですか？',
  generalFeedback: '一般的なフィードバック',
  generalFeedbackDesc: 'あなたの考え、提案、または全体的な体験を共有してください',
  bugReport: 'バグレポート',
  bugReportDesc: '遭遇した技術的な問題やエラーを報告してください',
  featureRequest: '機能リクエスト',
  featureRequestDesc: '新機能やツールの改善を提案してください',
  improvement: '改善提案',
  improvementDesc: '既存機能の改善方法を提案してください',
  compliment: '称賛',
  complaint: '苦情',
  howWasExperience: 'あなたの体験はいかがでしたか？',
  excellent: '優秀',
  good: '良い',
  average: '普通',
  poor: '悪い',
  terrible: 'ひどい',
  yourMessage: 'あなたのメッセージ',
  messagePlaceholder: 'あなたの体験、提案、または遭遇した問題について教えてください...',
  emailOptional: 'メール（オプション）',
  emailPlaceholder: '<EMAIL>',
  emailNote: '必要に応じてフィードバックのフォローアップにのみこのメールを使用します。',
  submitFeedback: 'フィードバックを送信',
  submittingFeedback: 'フィードバックを送信中...',
  submitting: '送信中...',
  characters: '文字',
  feedbackSubmitted: 'フィードバックが送信されました',
  feedbackSubmittedDesc: 'フィードバックをありがとうございます！慎重に検討し、ツールの改善に活用させていただきます。',
  feedbackThankYou: 'フィードバックをありがとうございます！慎重に検討し、ツールの改善に活用させていただきます。',
  feedbackError: '送信に失敗しました',
  feedbackErrorDesc: '申し訳ございませんが、フィードバックの送信中にエラーが発生しました。後でもう一度お試しください。',
  tryAgain: '再試行',
  backToHome: 'ホームに戻る',

  // Feedback Stats
  feedbackStats: 'フィードバック統計',
  totalFeedback: '総フィードバック数',
  avgRating: '平均評価',
  overallRating: '総合評価',

  // Recent Feedback Highlights
  recentFeedbackHighlights: '最近のフィードバックハイライト',
  feedbackExample1: 'より多くの通貨変換オプションを追加してほしい',
  feedbackExample2: 'インターフェースデザインは素晴らしいが、もっと速く読み込んでほしい',
  feedbackExample3: 'タイトル最適化ツールが時々固まる',
  implemented: '実装済み',
  inProgress: '進行中',
  fixed: '修正済み',

  // Privacy Note
  privacyNote: 'プライバシーを大切にしています。フィードバックはサービス改善のために使用され、第三者と共有されることはありません。',

  // Error Pages
  pageNotFound: 'ページが見つかりません',
  pageNotFoundDesc: '申し訳ございませんが、お探しのページは存在しません。',
  goHome: 'ホームに戻る',

  // Loading States
  loading: '読み込み中...',
  pleaseWait: 'お待ちください',

  // Success Messages
  success: '成功',
  operationCompleted: '操作が完了しました',

  // Error Messages
  error: 'エラー',
  somethingWentWrong: '何かが間違っています',
  networkError: 'ネットワークエラー',
  serverError: 'サーバーエラー',

  // Form Validation
  fieldRequired: 'この項目は必須です',
  invalidEmail: '有効なメールアドレスを入力してください',
  messageTooShort: 'メッセージは10文字以上である必要があります',
  messageTooLong: 'メッセージは1000文字を超えることはできません',

  // Common Actions
  submit: '送信',
  cancel: 'キャンセル',
  save: '保存',
  delete: '削除',
  edit: '編集',
  view: '表示',
  close: '閉じる',
  confirm: '確認',

  // Time and Date
  justNow: 'たった今',
  minutesAgo: '{minutes}分前',
  hoursAgo: '{hours}時間前',
  daysAgo: '{days}日前',
  weeksAgo: '{weeks}週間前',
  monthsAgo: '{months}ヶ月前',

  // Pagination
  previous: '前へ',
  next: '次へ',
  page: '{page}ページ目',
  of: '全{total}ページ',

  // Search
  search: '検索',
  searchPlaceholder: '検索...',
  noResults: '結果が見つかりません',
  searchResults: '検索結果',

  // Filters
  filter: 'フィルター',
  filterBy: '{field}でフィルター',
  clearFilters: 'フィルターをクリア',
  applyFilters: 'フィルターを適用',

  // Sorting
  sortBy: '並び順',
  sortAscending: '昇順',
  sortDescending: '降順',

  // Data Display
  noData: 'データがありません',
  dataLoading: 'データを読み込み中...',
  dataError: 'データの読み込みに失敗しました',
  refreshData: 'データを更新',
};
