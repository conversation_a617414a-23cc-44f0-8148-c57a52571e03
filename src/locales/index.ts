// 导入模块化翻译
import { commonZh } from './modules/common/zh';
import { commonEn } from './modules/common/en';
import { commonZhTW } from './modules/common/zh-TW';
import { commonJa } from './modules/common/ja';
import { commonId } from './modules/common/id';
import { commonVi } from './modules/common/vi';

import { componentsZh } from './modules/components/zh';
import { componentsEn } from './modules/components/en';
import { componentsZhTW } from './modules/components/zh-TW';
import { componentsJa } from './modules/components/ja';
import { componentsId } from './modules/components/id';
import { componentsVi } from './modules/components/vi';

// AI提供商翻译已移除（现在使用统一的Gemini代理）

// 导入各工具翻译 - 中文
import { titleAnalyzerZh } from './modules/tools/TitleAnalyzer/zh';
import { titleFixerZh } from './modules/tools/TitleFixer/zh';
import { unitConverterZh } from './modules/tools/UnitConverter/zh';
import { currencyConverterZh } from './modules/tools/CurrencyConverter/zh';
import { fbaCalculatorZh } from './modules/tools/FBACalculator/zh';
import { keywordDensityCheckerZh } from './modules/tools/KeywordDensityChecker/zh';
import { listingOptimizerZh } from './modules/tools/ListingOptimizer/zh';
import { profitCalculatorZh } from './modules/tools/ProfitCalculator/zh';
import { keywordResearcherZh } from './modules/tools/KeywordResearcher/zh';
import { competitorAnalyzerZh } from './modules/tools/CompetitorAnalyzer/zh';
import { reviewAnalyzerZh } from './modules/tools/ReviewAnalyzer/zh';
import { packingCalculatorZh } from './modules/tools/PackingCalculator/zh';
import { worldClockZh } from './modules/tools/WorldClock/zh';
import { amzSellerCalendarZh } from './modules/tools/AmzSellerCalendar/zh';
import { referenceTableZh } from './modules/tools/ReferenceTable/zh';
import { caseConverterZh } from './modules/tools/CaseConverter/zh';
import { keywordCombinerZh } from './modules/tools/KeywordCombiner/zh';
import { wordFrequencyAnalyzerZh } from './modules/tools/WordFrequencyAnalyzer/zh';
import { designPatentSearchZh } from './modules/tools/DesignPatentSearch/zh';
import { productImageGeneratorZh } from './modules/tools/ProductImageGenerator/zh';

// 导入各工具翻译 - 繁体中文
import { titleAnalyzerZhTW } from './modules/tools/TitleAnalyzer/zh-TW';
import { titleFixerZhTW } from './modules/tools/TitleFixer/zh-TW';
import { unitConverterZhTW } from './modules/tools/UnitConverter/zh-TW';
import { currencyConverterZhTW } from './modules/tools/CurrencyConverter/zh-TW';
import { fbaCalculatorZhTW } from './modules/tools/FBACalculator/zh-TW';
import { listingOptimizerZhTW } from './modules/tools/ListingOptimizer/zh-TW';
import { keywordDensityCheckerZhTW } from './modules/tools/KeywordDensityChecker/zh-TW';
import { profitCalculatorZhTW } from './modules/tools/ProfitCalculator/zh-TW';
import { keywordResearcherZhTW } from './modules/tools/KeywordResearcher/zh-TW';
import { competitorAnalyzerZhTW } from './modules/tools/CompetitorAnalyzer/zh-TW';
import { reviewAnalyzerZhTW } from './modules/tools/ReviewAnalyzer/zh-TW';
import { packingCalculatorZhTW } from './modules/tools/PackingCalculator/zh-TW';
import { worldClockZhTW } from './modules/tools/WorldClock/zh-TW';
import { amzSellerCalendarZhTW } from './modules/tools/AmzSellerCalendar/zh-TW';
import { referenceTableZhTW } from './modules/tools/ReferenceTable/zh-TW';
import { caseConverterZhTW } from './modules/tools/CaseConverter/zh-TW';
import { keywordCombinerZhTW } from './modules/tools/KeywordCombiner/zh-TW';
import { wordFrequencyAnalyzerZhTW } from './modules/tools/WordFrequencyAnalyzer/zh-TW';
import { designPatentSearchZhTW } from './modules/tools/DesignPatentSearch/zh-TW';
import { productImageGeneratorZhTW } from './modules/tools/ProductImageGenerator/zh-TW';

// 导入各工具翻译 - 英文
import { titleAnalyzerEn } from './modules/tools/TitleAnalyzer/en';
import { titleFixerEn } from './modules/tools/TitleFixer/en';
import { unitConverterEn } from './modules/tools/UnitConverter/en';
import { currencyConverterEn } from './modules/tools/CurrencyConverter/en';
import { fbaCalculatorEn } from './modules/tools/FBACalculator/en';
import { keywordDensityCheckerEn } from './modules/tools/KeywordDensityChecker/en';
import { listingOptimizerEn } from './modules/tools/ListingOptimizer/en';
import { profitCalculatorEn } from './modules/tools/ProfitCalculator/en';
import { keywordResearcherEn } from './modules/tools/KeywordResearcher/en';
import { competitorAnalyzerEn } from './modules/tools/CompetitorAnalyzer/en';
import { reviewAnalyzerEn } from './modules/tools/ReviewAnalyzer/en';
import { packingCalculatorEn } from './modules/tools/PackingCalculator/en';
import { worldClockEn } from './modules/tools/WorldClock/en';
import { amzSellerCalendarEn } from './modules/tools/AmzSellerCalendar/en';
import { referenceTableEn } from './modules/tools/ReferenceTable/en';
import { caseConverterEn } from './modules/tools/CaseConverter/en';
import { keywordCombinerEn } from './modules/tools/KeywordCombiner/en';
import { wordFrequencyAnalyzerEn } from './modules/tools/WordFrequencyAnalyzer/en';
import { designPatentSearchEn } from './modules/tools/DesignPatentSearch/en';
import { productImageGeneratorEn } from './modules/tools/ProductImageGenerator/en';

// 导入各工具翻译 - 日文
import { titleAnalyzerJa } from './modules/tools/TitleAnalyzer/ja';
import { titleFixerJa } from './modules/tools/TitleFixer/ja';
import { unitConverterJa } from './modules/tools/UnitConverter/ja';
import { currencyConverterJa } from './modules/tools/CurrencyConverter/ja';
import { fbaCalculatorJa } from './modules/tools/FBACalculator/ja';
import { keywordDensityCheckerJa } from './modules/tools/KeywordDensityChecker/ja';
import { listingOptimizerJa } from './modules/tools/ListingOptimizer/ja';
import { profitCalculatorJa } from './modules/tools/ProfitCalculator/ja';
import { keywordResearcherJa } from './modules/tools/KeywordResearcher/ja';
import { competitorAnalyzerJa } from './modules/tools/CompetitorAnalyzer/ja';
import { reviewAnalyzerJa } from './modules/tools/ReviewAnalyzer/ja';
import { packingCalculatorJa } from './modules/tools/PackingCalculator/ja';
import { worldClockJa } from './modules/tools/WorldClock/ja';
import { amzSellerCalendarJa } from './modules/tools/AmzSellerCalendar/ja';
import { referenceTableJa } from './modules/tools/ReferenceTable/ja';
import { caseConverterJa } from './modules/tools/CaseConverter/ja';
import { keywordCombinerJa } from './modules/tools/KeywordCombiner/ja';
import { wordFrequencyAnalyzerJa } from './modules/tools/WordFrequencyAnalyzer/ja';
import { designPatentSearchJa } from './modules/tools/DesignPatentSearch/ja';
import { productImageGeneratorJa } from './modules/tools/ProductImageGenerator/ja';

// 导入各工具翻译 - 印尼文
import { titleAnalyzerId } from './modules/tools/TitleAnalyzer/id';
import { titleFixerId } from './modules/tools/TitleFixer/id';
import { unitConverterId } from './modules/tools/UnitConverter/id';
import { currencyConverterId } from './modules/tools/CurrencyConverter/id';
import { fbaCalculatorId } from './modules/tools/FBACalculator/id';
import { keywordDensityCheckerId } from './modules/tools/KeywordDensityChecker/id';
import { listingOptimizerId } from './modules/tools/ListingOptimizer/id';
import { profitCalculatorId } from './modules/tools/ProfitCalculator/id';
import { keywordResearcherId } from './modules/tools/KeywordResearcher/id';
import { competitorAnalyzerId } from './modules/tools/CompetitorAnalyzer/id';
import { reviewAnalyzerId } from './modules/tools/ReviewAnalyzer/id';
import { packingCalculatorId } from './modules/tools/PackingCalculator/id';
import { worldClockId } from './modules/tools/WorldClock/id';
import { amzSellerCalendarId } from './modules/tools/AmzSellerCalendar/id';
import { referenceTableId } from './modules/tools/ReferenceTable/id';
import { caseConverterId } from './modules/tools/CaseConverter/id';
import { keywordCombinerId } from './modules/tools/KeywordCombiner/id';
import { wordFrequencyAnalyzerId } from './modules/tools/WordFrequencyAnalyzer/id';
import { designPatentSearchId } from './modules/tools/DesignPatentSearch/id';
import { productImageGeneratorId } from './modules/tools/ProductImageGenerator/id';

// 导入各工具翻译 - 越南文
import { titleAnalyzerVi } from './modules/tools/TitleAnalyzer/vi';
import { titleFixerVi } from './modules/tools/TitleFixer/vi';
import { unitConverterVi } from './modules/tools/UnitConverter/vi';
import { currencyConverterVi } from './modules/tools/CurrencyConverter/vi';
import { fbaCalculatorVi } from './modules/tools/FBACalculator/vi';
import { keywordDensityCheckerVi } from './modules/tools/KeywordDensityChecker/vi';
import { listingOptimizerVi } from './modules/tools/ListingOptimizer/vi';
import { profitCalculatorVi } from './modules/tools/ProfitCalculator/vi';
import { keywordResearcherVi } from './modules/tools/KeywordResearcher/vi';
import { competitorAnalyzerVi } from './modules/tools/CompetitorAnalyzer/vi';
import { reviewAnalyzerVi } from './modules/tools/ReviewAnalyzer/vi';
import { packingCalculatorVi } from './modules/tools/PackingCalculator/vi';
import { worldClockVi } from './modules/tools/WorldClock/vi';
import { amzSellerCalendarVi } from './modules/tools/AmzSellerCalendar/vi';
import { referenceTableVi } from './modules/tools/ReferenceTable/vi';
import { caseConverterVi } from './modules/tools/CaseConverter/vi';
import { keywordCombinerVi } from './modules/tools/KeywordCombiner/vi';
import { wordFrequencyAnalyzerVi } from './modules/tools/WordFrequencyAnalyzer/vi';
import { designPatentSearchVi } from './modules/tools/DesignPatentSearch/vi';
import { productImageGeneratorVi } from './modules/tools/ProductImageGenerator/vi';
import { qrCodeGeneratorZh } from './modules/tools/QRCodeGenerator/zh';
import { qrCodeGeneratorEn } from './modules/tools/QRCodeGenerator/en';
import { qrCodeGeneratorZhTW } from './modules/tools/QRCodeGenerator/zh-TW';
import { qrCodeGeneratorJa } from './modules/tools/QRCodeGenerator/ja';
import { qrCodeGeneratorId } from './modules/tools/QRCodeGenerator/id';
import { qrCodeGeneratorVi } from './modules/tools/QRCodeGenerator/vi';



import { pagesZh } from './modules/pages/zh';
import { pagesEn } from './modules/pages/en';
import { pagesZhTW } from './modules/pages/zh-TW';
import { pagesJa } from './modules/pages/ja';
import { pagesId } from './modules/pages/id';
import { pagesVi } from './modules/pages/vi';

// 导入SEO翻译
import { seoZh } from './modules/seo/zh';
import { seoEn } from './modules/seo/en';
import { seoZhTW } from './modules/seo/zh-TW';
import { seoJa } from './modules/seo/ja';
import { seoId } from './modules/seo/id';
import { seoVi } from './modules/seo/vi';

// 旧的翻译文件已被模块化翻译替代

export type Language = 'zh' | 'zh-TW' | 'en' | 'ja' | 'id' | 'vi';

// 合并工具翻译
const mergedToolsZh = {
  ...titleAnalyzerZh,
  ...titleFixerZh,
  ...unitConverterZh,
  ...currencyConverterZh,
  ...fbaCalculatorZh,
  ...keywordDensityCheckerZh,
  ...listingOptimizerZh,
  ...profitCalculatorZh,
  ...keywordResearcherZh,
  ...competitorAnalyzerZh,
  ...reviewAnalyzerZh,
  ...packingCalculatorZh,
  ...worldClockZh,
  ...amzSellerCalendarZh,
  ...referenceTableZh,
  ...caseConverterZh,
  ...keywordCombinerZh,
  ...wordFrequencyAnalyzerZh,
  ...designPatentSearchZh,
  ...qrCodeGeneratorZh,
  ...productImageGeneratorZh,
};

const mergedToolsZhTW = {
  ...titleAnalyzerZhTW,
  ...titleFixerZhTW,
  ...unitConverterZhTW,
  ...currencyConverterZhTW,
  ...fbaCalculatorZhTW,
  ...listingOptimizerZhTW,
  ...keywordDensityCheckerZhTW,
  ...profitCalculatorZhTW,
  ...keywordResearcherZhTW,
  ...competitorAnalyzerZhTW,
  ...reviewAnalyzerZhTW,
  ...packingCalculatorZhTW,
  ...worldClockZhTW,
  ...amzSellerCalendarZhTW,
  ...referenceTableZhTW,
  ...caseConverterZhTW,
  ...keywordCombinerZhTW,
  ...wordFrequencyAnalyzerZhTW,
  ...designPatentSearchZhTW,
  ...qrCodeGeneratorZhTW,
  ...productImageGeneratorZhTW,
};

const mergedToolsEn = {
  ...titleAnalyzerEn,
  ...titleFixerEn,
  ...unitConverterEn,
  ...currencyConverterEn,
  ...fbaCalculatorEn,
  ...keywordDensityCheckerEn,
  ...listingOptimizerEn,
  ...profitCalculatorEn,
  ...keywordResearcherEn,
  ...competitorAnalyzerEn,
  ...reviewAnalyzerEn,
  ...packingCalculatorEn,
  ...worldClockEn,
  ...amzSellerCalendarEn,
  ...referenceTableEn,
  ...caseConverterEn,
  ...keywordCombinerEn,
  ...wordFrequencyAnalyzerEn,
  ...designPatentSearchEn,
  ...qrCodeGeneratorEn,
  ...productImageGeneratorEn,
};

const mergedToolsJa = {
  ...titleAnalyzerJa,
  ...titleFixerJa,
  ...unitConverterJa,
  ...currencyConverterJa,
  ...fbaCalculatorJa,
  ...keywordDensityCheckerJa,
  ...listingOptimizerJa,
  ...profitCalculatorJa,
  ...keywordResearcherJa,
  ...competitorAnalyzerJa,
  ...reviewAnalyzerJa,
  ...packingCalculatorJa,
  ...worldClockJa,
  ...amzSellerCalendarJa,
  ...referenceTableJa,
  ...caseConverterJa,
  ...keywordCombinerJa,
  ...wordFrequencyAnalyzerJa,
  ...designPatentSearchJa,
  ...qrCodeGeneratorJa,
  ...productImageGeneratorJa,
};

const mergedToolsId = {
  ...titleAnalyzerId,
  ...titleFixerId,
  ...unitConverterId,
  ...currencyConverterId,
  ...fbaCalculatorId,
  ...keywordDensityCheckerId,
  ...listingOptimizerId,
  ...profitCalculatorId,
  ...keywordResearcherId,
  ...competitorAnalyzerId,
  ...reviewAnalyzerId,
  ...packingCalculatorId,
  ...worldClockId,
  ...amzSellerCalendarId,
  ...referenceTableId,
  ...caseConverterId,
  ...keywordCombinerId,
  ...wordFrequencyAnalyzerId,
  ...designPatentSearchId,
  ...qrCodeGeneratorId,
  ...productImageGeneratorId,
};

const mergedToolsVi = {
  ...titleAnalyzerVi,
  ...titleFixerVi,
  ...unitConverterVi,
  ...currencyConverterVi,
  ...fbaCalculatorVi,
  ...keywordDensityCheckerVi,
  ...listingOptimizerVi,
  ...profitCalculatorVi,
  ...keywordResearcherVi,
  ...competitorAnalyzerVi,
  ...reviewAnalyzerVi,
  ...packingCalculatorVi,
  ...worldClockVi,
  ...amzSellerCalendarVi,
  ...referenceTableVi,
  ...caseConverterVi,
  ...keywordCombinerVi,
  ...wordFrequencyAnalyzerVi,
  ...designPatentSearchVi,
  ...qrCodeGeneratorVi,
  ...productImageGeneratorVi,
};

// 合并模块化翻译（已移除AIProvider，现在使用统一Gemini代理）
const mergedZh = { ...commonZh, ...componentsZh, ...mergedToolsZh, ...pagesZh, ...seoZh };
const mergedEn = { ...commonEn, ...componentsEn, ...mergedToolsEn, ...pagesEn, ...seoEn };
const mergedZhTW = { ...commonZhTW, ...componentsZhTW, ...mergedToolsZhTW, ...pagesZhTW, ...seoZhTW };
const mergedJa = { ...commonJa, ...componentsJa, ...mergedToolsJa, ...pagesJa, ...seoJa };
const mergedId = { ...commonId, ...componentsId, ...mergedToolsId, ...pagesId, ...seoId };
const mergedVi = { ...commonVi, ...componentsVi, ...mergedToolsVi, ...pagesVi, ...seoVi };

export const translations = {
  zh: mergedZh,
  'zh-TW': mergedZhTW,
  en: mergedEn,
  ja: mergedJa,
  id: mergedId,
  vi: mergedVi,
};

export const getTranslation = (language: Language, key: string): string => {
  const translation = translations[language];
  if (!translation) {
    console.warn(`Translation for language "${language}" not found, falling back to Chinese`);
    return translations.zh[key as keyof typeof translations.zh] || key;
  }
  
  const value = translation[key as keyof typeof translation];
  if (value === undefined) {
    console.warn(`Translation key "${key}" not found for language "${language}", falling back to Chinese`);
    return translations.zh[key as keyof typeof translations.zh] || key;
  }
  
  return value;
};

// 语言配置
export const languageConfig = {
  zh: { name: '简体中文', flag: '🇨🇳', direction: 'ltr' },
  'zh-TW': { name: '繁體中文', flag: '🇭🇰', direction: 'ltr' },
  en: { name: 'English', flag: '🇺🇸', direction: 'ltr' },
  ja: { name: '日本語', flag: '🇯🇵', direction: 'ltr' },
  id: { name: 'Bahasa Indonesia', flag: '🇮🇩', direction: 'ltr' },
  vi: { name: 'Tiếng Việt', flag: '🇻🇳', direction: 'ltr' },
};

// 默认语言
export const DEFAULT_LANGUAGE: Language = 'zh';

// 支持的语言列表
export const SUPPORTED_LANGUAGES: Language[] = ['zh', 'zh-TW', 'en', 'ja', 'id', 'vi'];
