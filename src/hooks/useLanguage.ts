import { useState, useEffect, useCallback } from 'react';

export type Language = 'zh' | 'zh-TW' | 'en' | 'ja' | 'id' | 'vi';

// Create a simple event emitter for language changes
const languageChangeListeners = new Set<(language: Language) => void>();

const emitLanguageChange = (language: Language) => {
  languageChangeListeners.forEach(listener => listener(language));
};

const addLanguageChangeListener = (listener: (language: Language) => void) => {
  languageChangeListeners.add(listener);
  return () => languageChangeListeners.delete(listener);
};

export const useLanguage = () => {
  const [currentLanguage, setCurrentLanguage] = useState<Language>(() => {
    // Initialize from localStorage if available
    const saved = localStorage.getItem('language') as Language;
    return saved && ['zh', 'zh-TW', 'en', 'ja', 'id', 'vi'].includes(saved) ? saved : 'zh';
  });

  // Listen for language changes from other components
  useEffect(() => {
    const unsubscribe = addLanguageChangeListener((newLanguage) => {
      setCurrentLanguage(newLanguage);
    });
    return unsubscribe;
  }, []);

  const changeLanguage = useCallback((language: Language) => {
    setCurrentLanguage(language);
    localStorage.setItem('language', language);
    // Emit the change to all other components
    emitLanguageChange(language);
  }, []);

  return { currentLanguage, changeLanguage };
};