import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useLanguage } from './useLanguage';
import { Language } from '../locales';

export const useSEOLanguage = () => {
  const { currentLanguage, changeLanguage } = useLanguage();
  const location = useLocation();
  const navigate = useNavigate();

  // 从URL参数中获取语言设置
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const langParam = urlParams.get('lang') as Language;
    
    if (langParam && langParam !== currentLanguage) {
      // 支持的语言列表
      const supportedLanguages: Language[] = ['zh', 'zh-TW', 'en', 'ja', 'id', 'vi'];
      
      if (supportedLanguages.includes(langParam)) {
        changeLanguage(langParam);
      }
    }
  }, [location.search, currentLanguage, changeLanguage]);

  // 更新URL中的语言参数
  const updateLanguageInURL = (newLanguage: Language) => {
    const urlParams = new URLSearchParams(location.search);
    
    if (newLanguage === 'zh') {
      // 默认语言不需要在URL中显示
      urlParams.delete('lang');
    } else {
      urlParams.set('lang', newLanguage);
    }
    
    const newSearch = urlParams.toString();
    const newUrl = `${location.pathname}${newSearch ? `?${newSearch}` : ''}`;
    
    // 使用replace而不是push，避免在浏览器历史中创建过多条目
    navigate(newUrl, { replace: true });
  };

  // 获取当前页面的多语言URL
  const getLanguageUrls = () => {
    const supportedLanguages: Language[] = ['zh', 'zh-TW', 'en', 'ja', 'id', 'vi'];
    const baseUrl = 'https://sellerbox.asia';
    const currentPath = location.pathname;
    
    return supportedLanguages.reduce((urls, lang) => {
      const urlParams = new URLSearchParams();
      if (lang !== 'zh') {
        urlParams.set('lang', lang);
      }
      
      const search = urlParams.toString();
      const url = `${baseUrl}${currentPath}${search ? `?${search}` : ''}`;
      
      urls[lang] = url;
      return urls;
    }, {} as Record<Language, string>);
  };

  // 获取规范URL（不带语言参数）
  const getCanonicalUrl = () => {
    const baseUrl = 'https://sellerbox.asia';
    return `${baseUrl}${location.pathname}`;
  };

  // 获取当前语言的完整URL
  const getCurrentLanguageUrl = () => {
    const baseUrl = 'https://sellerbox.asia';
    const urlParams = new URLSearchParams(location.search);
    const search = urlParams.toString();
    return `${baseUrl}${location.pathname}${search ? `?${search}` : ''}`;
  };

  return {
    currentLanguage,
    updateLanguageInURL,
    getLanguageUrls,
    getCanonicalUrl,
    getCurrentLanguageUrl
  };
};
