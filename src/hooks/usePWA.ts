import { useEffect, useState } from 'react';

interface PWAInstallPrompt {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

interface PWAState {
  isInstallable: boolean;
  isInstalled: boolean;
  isOffline: boolean;
  installPrompt: PWAInstallPrompt | null;
}

export const usePWA = () => {
  const [pwaState, setPwaState] = useState<PWAState>({
    isInstallable: false,
    isInstalled: false,
    isOffline: !navigator.onLine,
    installPrompt: null,
  });

  // 检查是否已安装为 PWA
  const checkIfInstalled = () => {
    // 检查是否在独立模式下运行
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
    // 检查是否在 iOS Safari 的全屏模式
    const isIOSStandalone = (window.navigator as any).standalone === true;
    
    return isStandalone || isIOSStandalone;
  };

  // 注册 Service Worker
  const registerServiceWorker = async () => {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js');

        console.log('Service Worker registered successfully:', registration);

        // 监听更新
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;
          if (newWorker) {
            console.log('Service Worker: New version found');
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed') {
                if (navigator.serviceWorker.controller) {
                  // 有现有控制器，这是一个更新
                  console.log('Service Worker: New version available (update)');
                } else {
                  // 没有现有控制器，这是首次安装
                  console.log('Service Worker: First installation complete');
                }
              }
            });
          }
        });

        // 检查是否有等待中的 Service Worker
        if (registration.waiting) {
          console.log('Service Worker: Waiting worker found');
        }

        return registration;
      } catch (error) {
        console.error('Service Worker registration failed:', error);
        return null;
      }
    }
    return null;
  };

  // 安装 PWA
  const installPWA = async () => {
    if (pwaState.installPrompt) {
      try {
        await pwaState.installPrompt.prompt();
        const choiceResult = await pwaState.installPrompt.userChoice;
        
        if (choiceResult.outcome === 'accepted') {
          console.log('PWA installation accepted');
          setPwaState(prev => ({ ...prev, isInstallable: false, installPrompt: null }));
        } else {
          console.log('PWA installation dismissed');
        }
      } catch (error) {
        console.error('PWA installation failed:', error);
      }
    }
  };

  // 检查网络状态
  const updateOnlineStatus = () => {
    setPwaState(prev => ({ ...prev, isOffline: !navigator.onLine }));
  };

  useEffect(() => {
    // 检查是否已安装
    const isInstalled = checkIfInstalled();
    setPwaState(prev => ({ ...prev, isInstalled }));

    // 注册 Service Worker
    registerServiceWorker();

    // 监听安装提示事件
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      const installPrompt = e as any as PWAInstallPrompt;
      setPwaState(prev => ({ 
        ...prev, 
        isInstallable: true, 
        installPrompt 
      }));
    };

    // 监听应用安装事件
    const handleAppInstalled = () => {
      console.log('PWA was installed');
      setPwaState(prev => ({ 
        ...prev, 
        isInstalled: true, 
        isInstallable: false, 
        installPrompt: null 
      }));
    };

    // 监听网络状态变化
    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  // 获取 PWA 安装指导
  const getInstallInstructions = () => {
    const userAgent = navigator.userAgent.toLowerCase();
    
    if (userAgent.includes('chrome') && !userAgent.includes('edg')) {
      return {
        browser: 'Chrome',
        steps: [
          '点击地址栏右侧的安装图标',
          '或者点击菜单 → 更多工具 → 创建快捷方式',
          '勾选"在窗口中打开"选项',
          '点击"创建"按钮'
        ]
      };
    } else if (userAgent.includes('firefox')) {
      return {
        browser: 'Firefox',
        steps: [
          '点击地址栏右侧的安装图标',
          '或者点击菜单 → 安装此站点为应用',
          '点击"安装"按钮'
        ]
      };
    } else if (userAgent.includes('safari')) {
      return {
        browser: 'Safari',
        steps: [
          '点击分享按钮（方框带箭头图标）',
          '选择"添加到主屏幕"',
          '编辑应用名称（可选）',
          '点击"添加"按钮'
        ]
      };
    } else if (userAgent.includes('edg')) {
      return {
        browser: 'Edge',
        steps: [
          '点击地址栏右侧的安装图标',
          '或者点击菜单 → 应用 → 安装此站点为应用',
          '点击"安装"按钮'
        ]
      };
    }
    
    return {
      browser: '浏览器',
      steps: [
        '查找浏览器菜单中的"安装应用"或"添加到主屏幕"选项',
        '按照提示完成安装'
      ]
    };
  };

  return {
    ...pwaState,
    installPWA,
    getInstallInstructions,
    registerServiceWorker,
  };
};
