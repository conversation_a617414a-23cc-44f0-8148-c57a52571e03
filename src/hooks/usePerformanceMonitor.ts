import { useEffect, useCallback } from 'react';

interface PerformanceMetrics {
  fcp?: number; // First Contentful Paint
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  ttfb?: number; // Time to First Byte
}

interface NavigationTiming {
  dns: number;
  tcp: number;
  request: number;
  response: number;
  processing: number;
  onload: number;
}

export const usePerformanceMonitor = () => {
  // 获取导航时间
  const getNavigationTiming = useCallback((): NavigationTiming | null => {
    if (!window.performance || !window.performance.timing) {
      return null;
    }

    const timing = window.performance.timing;
    const navigationStart = timing.navigationStart;

    return {
      dns: timing.domainLookupEnd - timing.domainLookupStart,
      tcp: timing.connectEnd - timing.connectStart,
      request: timing.responseStart - timing.requestStart,
      response: timing.responseEnd - timing.responseStart,
      processing: timing.domComplete - timing.domLoading,
      onload: timing.loadEventEnd - timing.loadEventStart,
    };
  }, []);

  // 获取Web Vitals指标
  const getWebVitals = useCallback((): Promise<PerformanceMetrics> => {
    return new Promise((resolve) => {
      const metrics: PerformanceMetrics = {};

      // First Contentful Paint
      const fcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
        if (fcpEntry) {
          metrics.fcp = fcpEntry.startTime;
        }
      });

      // Largest Contentful Paint
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        if (lastEntry) {
          metrics.lcp = lastEntry.startTime;
        }
      });

      // First Input Delay
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const fidEntry = entries[0];
        if (fidEntry) {
          metrics.fid = fidEntry.processingStart - fidEntry.startTime;
        }
      });

      // Cumulative Layout Shift
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        }
        metrics.cls = clsValue;
      });

      try {
        fcpObserver.observe({ entryTypes: ['paint'] });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        fidObserver.observe({ entryTypes: ['first-input'] });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
      } catch (error) {
        console.warn('Performance Observer not supported:', error);
      }

      // 获取TTFB
      if (window.performance && window.performance.timing) {
        const timing = window.performance.timing;
        metrics.ttfb = timing.responseStart - timing.navigationStart;
      }

      // 延迟解析以收集更多指标
      setTimeout(() => {
        resolve(metrics);
      }, 3000);
    });
  }, []);

  // 监控资源加载性能
  const getResourceTiming = useCallback(() => {
    if (!window.performance || !window.performance.getEntriesByType) {
      return [];
    }

    const resources = window.performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    return resources.map(resource => ({
      name: resource.name,
      duration: resource.duration,
      size: resource.transferSize || 0,
      type: resource.initiatorType,
    }));
  }, []);

  // 计算性能分数
  const calculatePerformanceScore = useCallback((metrics: PerformanceMetrics): number => {
    let score = 100;

    // FCP评分 (目标: < 1.8s)
    if (metrics.fcp) {
      if (metrics.fcp > 3000) score -= 20;
      else if (metrics.fcp > 1800) score -= 10;
    }

    // LCP评分 (目标: < 2.5s)
    if (metrics.lcp) {
      if (metrics.lcp > 4000) score -= 25;
      else if (metrics.lcp > 2500) score -= 15;
    }

    // FID评分 (目标: < 100ms)
    if (metrics.fid) {
      if (metrics.fid > 300) score -= 20;
      else if (metrics.fid > 100) score -= 10;
    }

    // CLS评分 (目标: < 0.1)
    if (metrics.cls) {
      if (metrics.cls > 0.25) score -= 15;
      else if (metrics.cls > 0.1) score -= 8;
    }

    // TTFB评分 (目标: < 600ms)
    if (metrics.ttfb) {
      if (metrics.ttfb > 1500) score -= 20;
      else if (metrics.ttfb > 600) score -= 10;
    }

    return Math.max(0, score);
  }, []);

  // 发送性能数据到分析服务
  const reportPerformance = useCallback(async (metrics: PerformanceMetrics) => {
    if (process.env.NODE_ENV !== 'production') {
      console.log('Performance Metrics:', metrics);
      return;
    }

    try {
      // 这里可以发送到你的分析服务
      // await fetch('/api/analytics/performance', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(metrics)
      // });
    } catch (error) {
      console.warn('Failed to report performance metrics:', error);
    }
  }, []);

  // 初始化性能监控
  useEffect(() => {
    // 页面加载完成后收集指标
    const collectMetrics = async () => {
      try {
        const webVitals = await getWebVitals();
        const navigationTiming = getNavigationTiming();
        const resourceTiming = getResourceTiming();

        const performanceData = {
          ...webVitals,
          navigation: navigationTiming,
          resources: resourceTiming,
          score: calculatePerformanceScore(webVitals),
          timestamp: Date.now(),
          url: window.location.href,
          userAgent: navigator.userAgent,
        };

        // 报告性能数据
        await reportPerformance(performanceData);

        // 在开发环境中显示性能信息
        if (process.env.NODE_ENV === 'development') {
          console.group('🚀 Performance Metrics');
          console.log('Web Vitals:', webVitals);
          console.log('Navigation Timing:', navigationTiming);
          console.log('Performance Score:', performanceData.score);
          console.groupEnd();
        }
      } catch (error) {
        console.warn('Performance monitoring error:', error);
      }
    };

    // 等待页面完全加载
    if (document.readyState === 'complete') {
      collectMetrics();
    } else {
      window.addEventListener('load', collectMetrics);
      return () => window.removeEventListener('load', collectMetrics);
    }
  }, [getWebVitals, getNavigationTiming, getResourceTiming, calculatePerformanceScore, reportPerformance]);

  return {
    getNavigationTiming,
    getWebVitals,
    getResourceTiming,
    calculatePerformanceScore,
  };
};
