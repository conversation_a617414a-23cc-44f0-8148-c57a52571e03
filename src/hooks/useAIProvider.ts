// AI 服务状态管理 Hook - 适配新的统一 Gemini 代理架构
import { useState, useEffect, useCallback } from 'react';
import { isAIAvailable } from '../lib/ai';

export type AIProvider = 'gemini' | 'auto';

interface AIProviderStatus {
  provider: AIProvider;
  isAvailable: boolean;
  statusMessage: string;
}

interface UseAIProviderReturn {
  currentProvider: AIProvider;
  setProvider: (provider: AIProvider) => void;
  providerStatus: Record<AIProvider, AIProviderStatus>;
  refreshStatus: () => void;
  isLoading: boolean;
  isAvailable: boolean;
}

export const useAIProvider = (): UseAIProviderReturn => {
  const [currentProvider, setCurrentProvider] = useState<AIProvider>('gemini');
  const [isLoading, setIsLoading] = useState(false);
  const [providerStatus, setProviderStatus] = useState<Record<AIProvider, AIProviderStatus>>({
    gemini: {
      provider: 'gemini',
      isAvailable: true,
      statusMessage: 'Gemini AI 服务正常'
    },
    auto: {
      provider: 'auto',
      isAvailable: true,
      statusMessage: '智能选择最佳服务'
    }
  });

  // 检查 AI 服务状态
  const checkAIStatus = useCallback(async (): Promise<AIProviderStatus> => {
    try {
      const available = await isAIAvailable();
      return {
        provider: 'gemini',
        isAvailable: available,
        statusMessage: available ? 'Gemini AI 服务正常' : 'AI 服务暂时不可用'
      };
    } catch (error) {
      return {
        provider: 'gemini',
        isAvailable: false,
        statusMessage: 'AI 服务检查失败'
      };
    }
  }, []);

  // 刷新服务状态
  const refreshStatus = useCallback(async () => {
    setIsLoading(true);
    
    try {
      const geminiStatus = await checkAIStatus();
      
      setProviderStatus({
        gemini: geminiStatus,
        auto: {
          provider: 'auto',
          isAvailable: geminiStatus.isAvailable,
          statusMessage: geminiStatus.isAvailable ? 'Gemini AI' : '服务不可用'
        }
      });
    } catch (error) {
      console.error('Failed to refresh AI service status:', error);
    } finally {
      setIsLoading(false);
    }
  }, [checkAIStatus]);

  // 设置提供商
  const setProvider = useCallback((provider: AIProvider) => {
    setCurrentProvider(provider);
    
    if (import.meta.env.DEV) {
      console.log(`[AI Provider] 用户选择: ${provider}`);
    }
  }, []);

  // 初始化状态
  useEffect(() => {
    refreshStatus();
  }, [refreshStatus]);

  // 计算当前提供商是否可用
  const isAvailable = providerStatus[currentProvider]?.isAvailable ?? true;

  return {
    currentProvider,
    setProvider,
    providerStatus,
    refreshStatus,
    isLoading,
    isAvailable
  };
};

// 获取推荐的提供商
export const getRecommendedProvider = (
  providerStatus: Record<AIProvider, AIProviderStatus>
): AIProvider => {
  return providerStatus.gemini.isAvailable ? 'gemini' : 'auto';
};

// 获取提供商显示名称
export const getProviderDisplayName = (provider: AIProvider): string => {
  switch (provider) {
    case 'gemini':
      return 'Gemini AI';
    case 'auto':
      return '智能选择';
    default:
      return provider;
  }
};

// 获取提供商描述
export const getProviderDescription = (provider: AIProvider): string => {
  switch (provider) {
    case 'gemini':
      return '统一 Gemini AI 代理服务，高质量智能分析';
    case 'auto':
      return '自动选择最佳可用的 AI 服务';
    default:
      return '';
  }
};
