/* 全局字体设置 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* 防止HTML和Body的溢出 */
  html, body {
    overflow-x: hidden;
    height: 100%;
  }

  #root {
    min-height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f5f9;
  }

  ::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  /* 选择文本样式 */
  ::selection {
    background: #fed7aa;
    color: #9a3412;
  }
}

@layer components {
  /* 按钮样式 - 亚马逊橙黑风格 */
  .btn-primary {
    @apply inline-flex items-center justify-center px-6 py-3 text-base font-medium text-white border border-transparent rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200;
    background-color: #FF9900;
    border-color: #FF9900;
  }

  .btn-primary:hover {
    background-color: #E6890A;
    border-color: #E6890A;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  }

  .btn-primary:focus {
    ring-color: #FF9900;
  }

  .btn-secondary {
    @apply inline-flex items-center justify-center px-6 py-3 text-base font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200;
    background-color: #FFF5E6;
    color: #232F3E;
    border: 1px solid #FFD699;
  }

  .btn-secondary:hover {
    background-color: #FFEBCC;
    border-color: #FFCC66;
  }

  .btn-outline {
    @apply inline-flex items-center justify-center px-6 py-3 text-base font-medium bg-white rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200;
    color: #232F3E;
    border: 1px solid #E5E5E5;
  }

  .btn-outline:hover {
    background-color: #F8F8F8;
    border-color: #FF9900;
    color: #FF9900;
  }

  .btn-sm {
    @apply px-4 py-2 text-sm;
  }

  .btn-outline-sm {
    @apply inline-flex items-center justify-center px-3 py-1 text-sm font-medium bg-white rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200;
    color: #232F3E;
    border: 1px solid #E5E5E5;
  }

  .btn-outline-sm:hover {
    background-color: #F8F8F8;
    border-color: #FF9900;
    color: #FF9900;
  }

  .btn-lg {
    @apply px-8 py-4 text-lg;
  }

  /* 卡片样式 */
  .card {
    @apply bg-white rounded-xl border border-gray-200 shadow-sm;
  }

  /* 输入框样式 */
  .input {
    @apply w-full px-4 py-3 text-base border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 bg-white placeholder-gray-400;
  }

  .select {
    @apply w-full px-4 py-3 text-base border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 bg-white;
  }

  .label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }

  /* 徽章样式 - 亚马逊橙黑风格 */
  .badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;
  }

  .badge-primary {
    background-color: #FFF5E6;
    color: #232F3E;
    border: 1px solid #FFD699;
  }

  .badge-secondary {
    background-color: #F8F9FA;
    color: #232F3E;
    border: 1px solid #E5E5E5;
  }

  .badge-success {
    @apply bg-green-100 text-green-800;
  }

  .badge-warning {
    background-color: #FFF5E6;
    color: #FF9900;
  }

  .badge-error {
    @apply bg-red-100 text-red-800;
  }

  /* 工具页面头部 - 亚马逊橙黑风格 */
  .tool-header {
    @apply text-white p-8;
    background: linear-gradient(135deg, #FF9900 0%, #E6890A 100%);
  }

  .tool-header-icon {
    @apply w-12 h-12 bg-white bg-opacity-20 backdrop-blur-sm rounded-xl flex items-center justify-center mr-4;
  }

  .tool-header-title {
    @apply text-3xl font-bold tracking-tight;
    color: #FFFFFF;
  }

  .tool-header-description {
    @apply mt-2 leading-relaxed;
    color: rgba(255, 255, 255, 0.9);
  }

  /* 侧边栏样式 */
  .sidebar {
    @apply bg-white border-r border-gray-200 h-full flex flex-col;
  }

  .sidebar-header {
    @apply p-4 border-b border-gray-200 flex items-center justify-between;
  }

  .sidebar-item {
    @apply w-full px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 flex items-center;
  }

  .sidebar-item-active {
    background-color: #FFF5E6;
    color: #232F3E;
    border: 1px solid #FFD699;
  }

  .sidebar-item-inactive {
    color: #232F3E;
    @apply hover:bg-gray-50;
  }

  /* 工具详细信息展开动画 */
  .animate-fadeIn {
    animation: fadeIn 0.3s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
