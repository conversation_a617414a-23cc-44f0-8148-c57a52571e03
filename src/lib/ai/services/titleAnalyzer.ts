// 标题智能分析AI服务 - 使用Gemini代理

import { aiService, ModelType } from '../index';
import { ModelSelector } from '../models';

export interface TitleAnalysisResult {
  optimizedTitles: string[];
  improvements: string[];
  keywordSuggestions: string[];
  seoTips: string[];
  marketingAdvice: string[];
}

export class TitleAnalyzerService {
  /**
   * 智能分析和优化亚马逊产品标题
   */
  async analyzeAndOptimize(
    title: string,
    targetKeyword: string,
    marketplace: string = 'US',
    category: string = 'general',
    productFeatures: string[] = [],
    modelType: ModelType = 'STANDARD',
    language: string = 'zh' // 新增语言参数
  ): Promise<TitleAnalysisResult> {
    
    // 根据语言生成系统提示词
    const getSystemPrompt = (lang: string) => {
      switch (lang) {
        case 'en':
          return `You are an Amazon SEO and marketing expert specializing in analyzing and optimizing product titles to improve search rankings, click-through rates, and conversion rates.

【Core Optimization Goals】:
1. Search Ranking Optimization: keyword layout, density, relevance
2. User Experience Enhancement: readability, attractiveness, information completeness
3. Conversion Rate Optimization: purchase intent, trust, differentiation
4. Market Competitiveness: trend awareness, user habits, competitor analysis

【2025 Amazon Title Optimization Strategy】:
✓ Keyword Front-loading: Place core keywords within the first 50 characters
✓ Natural Language: Avoid keyword stuffing, maintain readability
✓ Information Hierarchy: Brand → Product Type → Core Features → Differentiating Selling Points
✓ User Search Habits: Match target market expression patterns
✓ Mobile Optimization: Consider mobile screen display effects
✓ Seasonal Adjustments: Incorporate seasonal and holiday demands

【Market-Specific Optimization】:
• US Market: Concise and direct, highlight functionality and value
• European Market: Focus on quality and environmental protection, comply with local regulations
• Japanese Market: Detailed specifications, emphasize brand and craftsmanship
• Other Markets: Localized expression, cultural adaptability

【Category-Specific Optimization】:
• Electronics: Technical specs, compatibility, performance parameters
• Apparel & Accessories: Materials, sizes, style, applicable occasions
• Home & Garden: Functionality, materials, dimensions, usage scenarios
• Health & Beauty: Ingredients, efficacy, target audience, safety
• Sports & Outdoors: Performance, durability, applicable environments, professionalism

Please return strictly in JSON format without any explanatory text or code block markers.`;

        case 'ja':
          return `あなたはAmazonのSEOとマーケティングの専門家で、検索ランキング、クリック率、コンバージョン率を向上させるために商品タイトルの分析と最適化を専門としています。

【コア最適化目標】:
1. 検索ランキング最適化：キーワード配置、密度、関連性
2. ユーザーエクスペリエンス向上：読みやすさ、魅力、情報の完全性
3. コンバージョン率最適化：購入意図、信頼性、差別化
4. 市場競争力：トレンド把握、ユーザー習慣、競合分析

【2025年Amazonタイトル最適化戦略】:
✓ キーワード前置：コアキーワードを最初の50文字以内に配置
✓ 自然言語：キーワードの詰め込みを避け、読みやすさを保つ
✓ 情報階層：ブランド→商品タイプ→コア機能→差別化セールスポイント
✓ ユーザー検索習慣：ターゲット市場の表現パターンに合わせる
✓ モバイル最適化：モバイル画面表示効果を考慮
✓ 季節調整：季節や祝日の需要を取り入れる

【市場特化最適化】:
• 米国市場：簡潔で直接的、機能性と価値を強調
• 欧州市場：品質と環境保護に焦点、現地規制に準拠
• 日本市場：詳細仕様、ブランドと職人技を重視
• その他市場：現地化表現、文化適応性

【カテゴリ特化最適化】:
• 電子機器：技術仕様、互換性、性能パラメータ
• アパレル・アクセサリー：素材、サイズ、スタイル、適用場面
• ホーム・ガーデン：機能性、素材、寸法、使用シナリオ
• ヘルス・ビューティー：成分、効能、対象者、安全性
• スポーツ・アウトドア：性能、耐久性、適用環境、専門性

JSON形式で厳密に返し、説明文やコードブロックマーカーは一切追加しないでください。`;

        default: // 中文和其他语言
          return `你是亚马逊SEO和营销专家，专门分析和优化产品标题以提升搜索排名、点击率和转化率。

【核心优化目标】:
1. 搜索排名优化：关键词布局、密度、相关性
2. 用户体验提升：可读性、吸引力、信息完整性
3. 转化率优化：购买意图、信任度、差异化
4. 市场竞争力：趋势把握、用户习惯、竞品分析

【2025年亚马逊标题优化策略】:
✓ 关键词前置：核心关键词放在前50字符内
✓ 自然语言：避免关键词堆砌，保持可读性
✓ 信息层次：品牌→产品类型→核心特性→差异化卖点
✓ 用户搜索习惯：符合目标市场的表达方式
✓ 移动端优化：考虑手机屏幕显示效果
✓ 季节性调整：结合时令和节日需求

【市场特定优化】:
• 美国市场：简洁直接，突出功能和价值
• 欧洲市场：注重品质和环保，符合当地法规
• 日本市场：详细规格，注重品牌和工艺
• 其他市场：本土化表达，文化适应性

【类别专业优化】:
• 电子产品：技术规格、兼容性、性能参数
• 服装配饰：材质、尺寸、风格、适用场合
• 家居用品：功能、材质、尺寸、使用场景
• 健康美容：成分、功效、适用人群、安全性
• 运动户外：性能、耐用性、适用环境、专业性

请严格按照JSON格式返回，不要添加任何解释文字或代码块标记。`;
      }
    };

    const systemPrompt = getSystemPrompt(language);

    // 根据语言生成用户提示词
    const getUserPrompt = (lang: string) => {
      switch (lang) {
        case 'en':
          return `【Original Title】: "${title}"
【Target Keywords】: "${targetKeyword}"
【Target Market】: ${marketplace}
【Product Category】: ${category}
【Product Features】: ${productFeatures.length > 0 ? productFeatures.join(', ') : 'Not provided'}

【Analysis Dimensions】:
1. Keyword Analysis: position, density, relevance, search potential
2. Structure Analysis: information hierarchy, logical order, readability
3. Competitive Analysis: differentiation, advantage highlighting, market positioning
4. User Experience: attractiveness, trustworthiness, purchase intent
5. SEO Optimization: search ranking, traffic acquisition, conversion potential

【Optimization Strategy】:
Based on ${marketplace} market and ${category} category characteristics:
${marketplace === 'US' ? `
→ US Market Strategy: Concise and powerful, highlight core value and differentiation advantages
→ Keyword front-loading, function-oriented, data-supported, user review-oriented` : ''}
${marketplace === 'UK' || marketplace === 'DE' || marketplace === 'FR' ? `
→ European Market Strategy: Quality assurance, environmental concepts, compliance standards, localized expression
→ Focus on craftsmanship and materials, comply with local consumption habits and regulatory requirements` : ''}
${marketplace === 'JP' ? `
→ Japanese Market Strategy: Precise specifications, brand reputation, craftsmanship details, service guarantee
→ Detailed parameters, quality commitment, ease of use, after-sales service` : ''}

Please provide 3 optimized versions, strictly return in the following JSON format:
{
  "optimizedTitles": [
    "Version 1: SEO Optimized - Keyword front-loaded, search ranking oriented complete title",
    "Version 2: Conversion Optimized - User experience oriented, click and purchase enhancing complete title",
    "Version 3: Differentiated - Unique selling points highlighted, competitive advantage clear complete title"
  ],
  "improvements": [
    "SEO Improvement: Front-load core keyword '${targetKeyword}', optimize search weight, improve organic ranking",
    "Conversion Improvement: Enhance user purchase intent expression, highlight core value and usage scenarios",
    "Differentiation Improvement: Strengthen unique selling points, create clear distinction from competitors, enhance brand memorability"
  ],
  "keywordSuggestions": [
    "Primary Keywords: Core terms based on search volume and relevance",
    "Long-tail Keywords: Specific scenario and user demand precise terms",
    "Trending Keywords: Market hotspots and seasonal demand terms"
  ],
  "seoTips": [
    "Keyword Layout: Place most important keywords within first 50 characters of title, ensure complete mobile display",
    "Information Hierarchy: Organize information in order of Brand → Product Type → Core Features → Differentiating Selling Points",
    "Natural Language: Maintain title readability and naturalness, avoid keyword stuffing affecting user experience"
  ],
  "marketingAdvice": [
    "Target Users: Identify core user groups, optimize titles for their needs and pain points",
    "Competitive Strategy: Analyze competitor title characteristics, find differentiation opportunities and advantage highlights",
    "Conversion Optimization: Combine with product images and pricing, ensure title consistency with overall listing"
  ]
}`;

        case 'ja':
          return `【元のタイトル】: "${title}"
【ターゲットキーワード】: "${targetKeyword}"
【ターゲット市場】: ${marketplace}
【商品カテゴリ】: ${category}
【商品特徴】: ${productFeatures.length > 0 ? productFeatures.join(', ') : '提供されていません'}

【分析次元】:
1. キーワード分析：位置、密度、関連性、検索ポテンシャル
2. 構造分析：情報階層、論理順序、読みやすさ
3. 競合分析：差別化、優位性強調、市場ポジショニング
4. ユーザーエクスペリエンス：魅力、信頼性、購入意図
5. SEO最適化：検索ランキング、トラフィック獲得、コンバージョンポテンシャル

【最適化戦略】:
${marketplace}市場と${category}カテゴリの特性に基づいて：

3つの最適化バージョンを提供し、以下のJSON形式で厳密に返してください：
{
  "optimizedTitles": [
    "バージョン1：SEO最適化版 - キーワード前置、検索ランキング指向の完全タイトル",
    "バージョン2：コンバージョン最適化版 - ユーザーエクスペリエンス指向、クリックと購入を促進する完全タイトル",
    "バージョン3：差別化版 - 独自のセールスポイントを強調、競争優位性が明確な完全タイトル"
  ],
  "improvements": [
    "SEO改善：コアキーワード'${targetKeyword}'を前置、検索重みを最適化、オーガニックランキングを向上",
    "コンバージョン改善：ユーザーの購入意図表現を強化、コア価値と使用シナリオを強調",
    "差別化改善：独自のセールスポイントを強化、競合との明確な区別を作り、ブランド記憶度を向上"
  ],
  "keywordSuggestions": [
    "主要キーワード：検索ボリュームと関連性に基づくコア用語",
    "ロングテールキーワード：具体的なシナリオとユーザー需要の精密用語",
    "トレンドキーワード：市場ホットスポットと季節需要用語"
  ],
  "seoTips": [
    "キーワード配置：最も重要なキーワードをタイトルの最初の50文字以内に配置、モバイル完全表示を確保",
    "情報階層：ブランド→商品タイプ→コア機能→差別化セールスポイントの順序で情報を整理",
    "自然言語：タイトルの読みやすさと自然性を保持、キーワード詰め込みがユーザーエクスペリエンスに影響することを避ける"
  ],
  "marketingAdvice": [
    "ターゲットユーザー：コアユーザーグループを特定、彼らのニーズと痛点に対してタイトルを最適化",
    "競争戦略：競合タイトルの特徴を分析、差別化機会と優位性ハイライトを見つける",
    "コンバージョン最適化：商品画像と価格と組み合わせ、タイトルと全体リスティングの一貫性を確保"
  ]
}`;

        default: // 中文
          return `【原标题】: "${title}"
【目标关键词】: "${targetKeyword}"
【目标市场】: ${marketplace}
【产品类别】: ${category}
【产品特性】: ${productFeatures.length > 0 ? productFeatures.join(', ') : '未提供'}

【分析维度】:
1. 关键词分析：位置、密度、相关性、搜索潜力
2. 结构分析：信息层次、逻辑顺序、可读性
3. 竞争分析：差异化、优势突出、市场定位
4. 用户体验：吸引力、信任度、购买意图
5. SEO优化：搜索排名、流量获取、转化潜力

【优化策略】:
基于${marketplace}市场和${category}类别的特点：
${marketplace === 'US' ? `
→ 美国市场策略：简洁有力，突出核心价值和差异化优势
→ 关键词前置，功能导向，数据支撑，用户评价导向` : ''}
${marketplace === 'UK' || marketplace === 'DE' || marketplace === 'FR' ? `
→ 欧洲市场策略：品质保证，环保理念，合规标准，本土化表达
→ 注重工艺和材质，符合当地消费习惯和法规要求` : ''}
${marketplace === 'JP' ? `
→ 日本市场策略：精确规格，品牌信誉，工艺细节，服务保障
→ 详细参数，品质承诺，使用便利性，售后服务` : ''}

请提供3个优化版本，严格按照以下JSON格式返回：
{
  "optimizedTitles": [
    "版本1：SEO优化版 - 关键词前置，搜索排名导向的完整标题",
    "版本2：转化优化版 - 用户体验导向，提升点击和购买的完整标题",
    "版本3：差异化版 - 突出独特卖点，竞争优势明显的完整标题"
  ],
  "improvements": [
    "SEO改进：将核心关键词'${targetKeyword}'前置，优化搜索权重，提升自然排名",
    "转化改进：增强用户购买意图表达，突出核心价值和使用场景",
    "差异化改进：强化独特卖点，与竞品形成明显区别，提升品牌记忆度"
  ],
  "keywordSuggestions": [
    "主要关键词：基于搜索热度和相关性的核心词汇",
    "长尾关键词：具体场景和用户需求的精准词汇", 
    "趋势关键词：市场热点和季节性需求词汇"
  ],
  "seoTips": [
    "关键词布局：将最重要的关键词放在标题前50字符内，确保移动端完整显示",
    "信息层次：按照品牌→产品类型→核心特性→差异化卖点的顺序组织信息",
    "自然语言：保持标题的可读性和自然性，避免关键词堆砌影响用户体验"
  ],
  "marketingAdvice": [
    "目标用户：明确核心用户群体，针对其需求和痛点进行标题优化",
    "竞争策略：分析竞品标题特点，找出差异化机会和优势突出点",
    "转化优化：结合产品图片和价格，确保标题与整体listing的一致性"
  ]
}`;
      }
    };

    const prompt = getUserPrompt(language);

    try {
      // 使用智能模型选择器为标题分析选择最佳配置
      const modelConfig = ModelSelector.forTitleAnalysis();
      
      const response = await aiService.chat({
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: modelConfig.temperature,
        maxTokens: modelConfig.maxTokens
      }, 'title-analysis');

      // 解析JSON响应，处理可能的markdown格式
      let jsonResult: TitleAnalysisResult;
      try {
        // 先尝试直接解析
        jsonResult = JSON.parse(response.content) as TitleAnalysisResult;
      } catch {
        // 尝试提取JSON（处理markdown代码块）
        const jsonMatch = response.content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          jsonResult = JSON.parse(jsonMatch[0]) as TitleAnalysisResult;
        } else {
          throw new Error('无法解析AI响应为JSON格式');
        }
      }

      // 验证返回结果
      if (!jsonResult?.optimizedTitles || !Array.isArray(jsonResult.optimizedTitles)) {
        throw new Error('AI返回结果不完整');
      }

      if (jsonResult.optimizedTitles.length < 3) {
        throw new Error('AI返回的优化版本不足');
      }

      return {
        optimizedTitles: jsonResult.optimizedTitles,
        improvements: jsonResult.improvements || ['改进建议1', '改进建议2', '改进建议3'],
        keywordSuggestions: jsonResult.keywordSuggestions || ['关键词1', '关键词2', '关键词3'],
        seoTips: jsonResult.seoTips || ['SEO建议1', 'SEO建议2', 'SEO建议3'],
        marketingAdvice: jsonResult.marketingAdvice || ['营销建议1', '营销建议2', '营销建议3']
      };

    } catch (error) {
      console.error('Title analysis error:', error);

      if (error instanceof Error) {
        if (error.message.includes('AI服务不可用') || error.message.includes('网络连接失败')) {
          throw new Error('AI服务连接失败，请检查网络连接后重试');
        } else if (error.message.includes('超时') || error.message.includes('timeout')) {
          throw new Error('AI服务响应超时，请稍后重试');
        } else if (error.message.includes('格式') || error.message.includes('解析')) {
          throw new Error('AI响应解析失败，请重试');
        } else {
          throw error;
        }
      } else {
        throw new Error('AI服务调用失败，请稍后重试');
      }
    }
  }
}

// 单例实例
export const titleAnalyzerService = new TitleAnalyzerService();
