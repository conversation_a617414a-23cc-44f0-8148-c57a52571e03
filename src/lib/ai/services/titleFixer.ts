// 标题修复AI服务 - 使用Gemini代理

import { aiService, ModelType } from '../index';
import { ModelSelector } from '../models';

export interface TitleFixResult {
  fixedTitles: string[];
  changes: string[];
  explanation: string;
  complianceScore: number;
}

export interface TitleOptimizationResult {
  optimizedTitles: string[];
  improvements: string[];
  keywordSuggestions: string[];
  complianceNotes: string[];
}

export interface ComplianceAnalysisResult {
  isCompliant: boolean;
  violations: string[];
  suggestions: string[];
  score: number;
}

export class TitleFixerService {
  /**
   * 修复亚马逊标题合规性问题
   */
  async fixCompliance(
    originalTitle: string,
    errorMessage: string,
    marketplace: string = 'US',
    category: string = 'general',
    violations: any[] = [],
    modelType: ModelType = 'STANDARD',
    language: string = 'zh' // 新增语言参数
  ): Promise<TitleFixResult> {
    // 生成基于违规项的详细分析
    const violationAnalysis = violations.length > 0
      ? violations.map((v: any) => `- ${v.type}: ${v.message}`).join('\n')
      : language === 'en' ? 'No specific violations detected' : 
        language === 'ja' ? '具体的な違反は検出されませんでした' : '无具体违规项检测到';

    // 根据语言生成系统提示词
    const getSystemPrompt = (lang: string) => {
      switch (lang) {
        case 'en':
          return `You are an Amazon title compliance expert specializing in fixing title violations based on Amazon's latest July 2025 policies.

【Core Fixing Principles】:
1. Precise Fixing: Only modify specific detected violation issues
2. Maintain SEO Value: Preserve original keyword order and important terms as much as possible
3. Minimize Changes: Maintain original title structure and style
4. Ensure Compliance: Fixed titles must 100% comply with Amazon 2025 policies
5. Optimize Conversion: Enhance user experience while maintaining compliance

【July 2025 Amazon Title Policies】:
✓ Character Limits: General categories 200 characters, Apparel 125 characters
✓ Prohibited Special Characters: ! $ ? _ { } ^ ¬ ¦ (can be retained in brand names)
✓ Repeated Words: Same word maximum 2 times (except prepositions/conjunctions/articles)
✓ Singular/Plural Check: "shoe" and "shoes" count as repetition
✓ Brand Exception: Brand name words don't count as repetition with descriptive words

【Synonym Replacement Guide】:
• Length Exceeded: Replace long words with shorter synonyms
  - "Professional" → "Pro"
  - "Waterproof" → "Water-Resistant"
  - "Comfortable" → "Comfy"
  - "Adjustable" → "Flex"
• Repeated Words: Replace repeated items with similar meaning words
  - "Strong" → "Durable", "Sturdy", "Robust"
  - "Soft" → "Smooth", "Gentle", "Plush"
  - "Large" → "Big", "Spacious", "Roomy"
• Special Characters: Replace with standard expressions
  - "!" → Remove or express with words like "Great"
  - "$" → "Dollar" or remove
  - "&" → "and" or "+"

【Three-Version Fix Strategy】:
Version 1: 【Minimal Changes】- Synonym replacement strategy, maintain original title structure and style
Version 2: 【SEO Optimization】- Optimize keyword layout and search ranking while maintaining compliance
Version 3: 【Conversion Optimization】- Enhance user experience and purchase conversion while maintaining compliance

Please return strictly in JSON format without any explanatory text or code block markers.`;

        case 'ja':
          return `あなたはAmazonのタイトルコンプライアンス専門家で、Amazon の2025年7月最新ポリシーに基づいてタイトル違反を修正することを専門としています。

【コア修正原則】:
1. 精密修正：検出された具体的な違反問題のみを修正
2. SEO価値維持：元のキーワード順序と重要な用語を可能な限り保持
3. 変更最小化：元のタイトル構造とスタイルを維持
4. コンプライアンス確保：修正後のタイトルは100%Amazon 2025ポリシーに準拠
5. コンバージョン最適化：コンプライアンスを維持しながらユーザーエクスペリエンスを向上

【2025年7月Amazonタイトルポリシー】:
✓ 文字制限：一般カテゴリ200文字、アパレル125文字
✓ 禁止特殊文字：! $ ? _ { } ^ ¬ ¦ （ブランド名では保持可能）
✓ 重複語：同じ語は最大2回（前置詞/接続詞/冠詞を除く）
✓ 単数/複数チェック：「shoe」と「shoes」は重複とみなす
✓ ブランド例外：ブランド名の語は説明語との重複にカウントしない

【類義語置換ガイド】:
• 長さ超過：長い語をより短い類義語に置換
• 重複語：重複項目を類似意味の語に置換
• 特殊文字：標準表現に置換

【3バージョン修正戦略】:
バージョン1：【最小変更】- 類義語置換戦略、元のタイトル構造とスタイルを維持
バージョン2：【SEO最適化】- コンプライアンスを維持しながらキーワード配置と検索ランキングを最適化
バージョン3：【コンバージョン最適化】- コンプライアンスを維持しながらユーザーエクスペリエンスと購入コンバージョンを向上

JSON形式で厳密に返し、説明文やコードブロックマーカーは一切追加しないでください。`;

        default: // 中文和其他语言
          return `你是亚马逊标题合规专家，专门根据2025年7月最新政策修复标题违规问题。

【核心修复原则】:
1. 精准修复：只针对检测到的具体违规问题进行修改
2. 保持SEO价值：尽可能保留原有关键词顺序和重要术语
3. 最小化改动：保持原标题的结构和风格
4. 确保合规：修复后必须100%符合亚马逊2025年政策
5. 优化转化：在合规基础上提升用户体验

【2025年7月亚马逊标题政策】:
✓ 字符限制：一般类别200字符，服装类125字符
✓ 禁用特殊字符：! $ ? _ { } ^ ¬ ¦ （品牌名中可保留）
✓ 重复词汇：同一词汇最多2次（介词/连词/冠词除外）
✓ 单复数检查：shoe和shoes算重复
✓ 品牌例外：品牌名词汇与描述词汇不算重复

【同义词替换指导】:
• 长度超限：用更短的同义词替换长词汇
  - "Professional" → "Pro"
  - "Waterproof" → "Water-Resistant"
  - "Comfortable" → "Comfy"
  - "Adjustable" → "Flex"
• 重复词汇：用意思相近的词替换重复项
  - "Strong" → "Durable", "Sturdy", "Robust"
  - "Soft" → "Smooth", "Gentle", "Plush"
  - "Large" → "Big", "Spacious", "Roomy"
• 特殊字符：用标准表达替换
  - "!" → 删除或用"Great"等词汇表达
  - "$" → "Dollar" 或删除
  - "&" → "and" 或 "+"

【三版本修复策略】:
版本1：【最小化修改】- 同义词替换策略，保持原标题结构和风格
版本2：【SEO优化】- 在合规基础上优化关键词布局和搜索排名
版本3：【转化优化】- 在合规基础上提升用户体验和购买转化

请严格按照JSON格式返回，不要添加任何解释文字或代码块标记。`;
      }
    };

    const systemPrompt = getSystemPrompt(language);

    // 根据语言生成用户提示词
    const getUserPrompt = (lang: string) => {
      const getViolationStrategy = (violations: any[]) => {
        if (violations.length === 0) {
          return lang === 'en' ? '→ General optimization based on error message' :
                 lang === 'ja' ? '→ エラーメッセージに基づく一般的な最適化' : '→ 基于错误信息进行通用优化';
        }
        
        return violations.map((v: any) => {
          switch (v.type) {
            case 'length':
              return lang === 'en' ? `→ Length Issue: Prioritize synonym shortening, remove repeated words, retain core keywords` :
                     lang === 'ja' ? `→ 長さ問題：類義語短縮を優先、重複語削除、コアキーワード保持` : 
                     `→ 长度问题：优先使用同义词缩短，删除重复词汇，保留核心关键词`;
            case 'special_chars':
              return lang === 'en' ? `→ Special Characters: Replace prohibited characters ${v.details?.foundChars?.map((f: any) => f.char).join(', ') || ''} with standard characters, maintain original meaning` :
                     lang === 'ja' ? `→ 特殊文字：禁止文字 ${v.details?.foundChars?.map((f: any) => f.char).join(', ') || ''} を標準文字に置換、元の意味を保持` :
                     `→ 特殊字符：用标准字符替换禁用字符 ${v.details?.foundChars?.map((f: any) => f.char).join(', ') || ''}，保持原意`;
            case 'repeated_words':
              return lang === 'en' ? `→ Repeated Words: Replace repeated words with synonyms, maintain semantic integrity` :
                     lang === 'ja' ? `→ 重複語：重複語を類義語に置換、意味的完全性を維持` :
                     `→ 重复词汇：用同义词替换重复词汇，保持语义完整性`;
            default:
              return `→ ${v.type}：${v.message}`;
          }
        }).join('\n');
      };

      switch (lang) {
        case 'en':
          return `【Original Title】: "${originalTitle}"
【Amazon Error Message】: "${errorMessage}"
【Target Market】: ${marketplace}
【Product Category】: ${category}
【Detected Violations】:
${violationAnalysis}

【Category-Specific Requirements】:
${category === 'apparel' ? `
Apparel Category (125 character limit):
- Must Retain: Brand name, product type, core materials
- Optimize: Size information simplification, standard color names
- Remove: Redundant modifiers, repeated descriptions` : `
General Category (200 character limit):
- Maintain: Brand name, core functions, main features
- Optimize: Keyword density, user search habits
- Remove: Marketing terms, repeated expressions`}

【Smart Fix Strategy】:
Based on detected violations, apply precise fixes:
${getViolationStrategy(violations)}

Please provide 3 fix versions, strictly return in the following JSON format:
{
  "fixedTitles": [
    "Version 1: Minimal changes, complete title with only synonym replacements for violating words",
    "Version 2: SEO optimized, complete title with rearranged keywords",
    "Version 3: Conversion optimized, complete title with enhanced user experience"
  ],
  "changes": [
    "Version 1 Changes: Replaced 'X' with synonym 'Y', maintained original structure",
    "Version 2 Changes: Rearranged keyword order, optimized search weight",
    "Version 3 Changes: Optimized expression, enhanced user comprehension"
  ],
  "explanation": "Detailed explanation: The main issue with the original title is X. Version 1 minimizes changes through synonym replacement, Version 2 optimizes SEO layout, Version 3 enhances conversion effects. All versions comply with 2025 Amazon policies.",
  "complianceScore": 95
}`;

        case 'ja':
          return `【元のタイトル】: "${originalTitle}"
【Amazonエラーメッセージ】: "${errorMessage}"
【ターゲット市場】: ${marketplace}
【商品カテゴリ】: ${category}
【検出された違反】:
${violationAnalysis}

【カテゴリ特有の要件】:
${category === 'apparel' ? `
アパレルカテゴリ（125文字制限）:
- 必須保持：ブランド名、商品タイプ、コア素材
- 最適化：サイズ情報の簡素化、標準色名
- 削除：冗長な修飾語、重複説明` : `
一般カテゴリ（200文字制限）:
- 維持：ブランド名、コア機能、主要特徴
- 最適化：キーワード密度、ユーザー検索習慣
- 削除：マーケティング用語、重複表現`}

【スマート修正戦略】:
検出された違反に基づいて、精密修正を適用：
${getViolationStrategy(violations)}

3つの修正バージョンを提供し、以下のJSON形式で厳密に返してください：
{
  "fixedTitles": [
    "バージョン1：最小変更、違反語の類義語置換のみの完全タイトル",
    "バージョン2：SEO最適化、キーワード再配置の完全タイトル",
    "バージョン3：コンバージョン最適化、ユーザーエクスペリエンス向上の完全タイトル"
  ],
  "changes": [
    "バージョン1変更：'X'を類義語'Y'に置換、元の構造を維持",
    "バージョン2変更：キーワード順序を再配置、検索重みを最適化",
    "バージョン3変更：表現を最適化、ユーザー理解度を向上"
  ],
  "explanation": "詳細説明：元のタイトルの主な問題はXです。バージョン1は類義語置換で変更を最小化、バージョン2はSEOレイアウトを最適化、バージョン3はコンバージョン効果を向上。すべてのバージョンは2025年Amazonポリシーに準拠。",
  "complianceScore": 95
}`;

        default: // 中文
          return `【原标题】: "${originalTitle}"
【亚马逊错误信息】: "${errorMessage}"
【目标市场】: ${marketplace}
【产品类别】: ${category}
【检测到的违规项】:
${violationAnalysis}

【类别特殊要求】:
${category === 'apparel' ? `
服装类(125字符限制)：
- 必保留：品牌名、产品类型、核心材质
- 优化：尺寸信息简洁化、标准颜色名称
- 删除：冗余修饰词、重复描述` : `
一般类别(200字符限制)：
- 保持：品牌名、核心功能、主要特征
- 优化：关键词密度、用户搜索习惯
- 删除：营销词汇、重复表述`}

【智能修复策略】:
基于检测到的违规项，采用精准修复：
${getViolationStrategy(violations)}

请提供3个修复版本，严格按照以下JSON格式返回：
{
  "fixedTitles": [
    "版本1：最小化修改，仅用同义词替换违规词汇的完整标题",
    "版本2：SEO优化，重新排列关键词的完整标题", 
    "版本3：转化优化，提升用户体验的完整标题"
  ],
  "changes": [
    "版本1修改：将'X'替换为同义词'Y'，保持原结构不变",
    "版本2修改：重新排列关键词顺序，优化搜索权重",
    "版本3修改：优化表达方式，提升用户理解度"
  ],
  "explanation": "详细说明：原标题的主要问题是X，版本1通过同义词替换最小化修改，版本2优化SEO布局，版本3提升转化效果。所有版本均符合2025年亚马逊政策。",
  "complianceScore": 95
}`;
      }
    };

    const prompt = getUserPrompt(language);

    try {
      // 使用智能模型选择器为标题修复选择最佳配置
      const modelConfig = ModelSelector.forTitleFix();
      
      const response = await aiService.chat({
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: modelConfig.temperature,
        maxTokens: modelConfig.maxTokens
      }, 'title-fix');

      // 解析JSON响应，处理可能的markdown格式
      let jsonResult: any;
      try {
        // 先尝试直接解析
        jsonResult = JSON.parse(response.content);
      } catch {
        // 尝试提取JSON（处理markdown代码块）
        const jsonMatch = response.content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          jsonResult = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error(language === 'en' ? 'Unable to parse AI response as JSON format' :
                         language === 'ja' ? 'AI応答をJSON形式として解析できません' : '无法解析AI响应为JSON格式');
        }
      }

      // 验证返回结果
      if (!jsonResult?.fixedTitles || !Array.isArray(jsonResult.fixedTitles)) {
        throw new Error(language === 'en' ? 'AI returned incomplete results: missing fixedTitles array' :
                       language === 'ja' ? 'AIが不完全な結果を返しました：fixedTitles配列が不足' : 'AI返回结果不完整：缺少fixedTitles数组');
      }

      if (jsonResult.fixedTitles.length < 3) {
        throw new Error(language === 'en' ? `AI returned insufficient fix versions: expected 3, got ${jsonResult.fixedTitles.length}` :
                       language === 'ja' ? `AIが返した修正バージョンが不足：期待3、実際${jsonResult.fixedTitles.length}` : 
                       `AI返回的修复版本不足：期望3个，实际${jsonResult.fixedTitles.length}个`);
      }

      const defaultChanges = language === 'en' ? ['Change 1', 'Change 2', 'Change 3'] :
                            language === 'ja' ? ['変更1', '変更2', '変更3'] : ['修改1', '修改2', '修改3'];
      
      const defaultExplanation = language === 'en' ? 'Fixed according to Amazon policies' :
                                language === 'ja' ? 'Amazonポリシーに従って修正されました' : '已根据亚马逊政策进行修复';

      return {
        fixedTitles: jsonResult.fixedTitles,
        changes: jsonResult.changes || defaultChanges,
        explanation: jsonResult.explanation || defaultExplanation,
        complianceScore: jsonResult.complianceScore || 90
      };

    } catch (error) {
      console.error('Title fix error:', error);

      if (error instanceof Error) {
        if (error.message.includes('AI服务不可用') || error.message.includes('网络连接失败') || 
            error.message.includes('AI service unavailable') || error.message.includes('network connection failed') ||
            error.message.includes('AIサービスが利用できません') || error.message.includes('ネットワーク接続に失敗')) {
          throw new Error(language === 'en' ? 'AI service connection failed, please check network connection and try again' :
                         language === 'ja' ? 'AIサービス接続に失敗しました。ネットワーク接続を確認して再試行してください' : 
                         'AI服务连接失败，请检查网络连接后重试');
        } else if (error.message.includes('格式') || error.message.includes('解析') ||
                  error.message.includes('format') || error.message.includes('parse') ||
                  error.message.includes('形式') || error.message.includes('解析')) {
          throw new Error(language === 'en' ? 'AI response parsing failed, please try again' :
                         language === 'ja' ? 'AI応答の解析に失敗しました。再試行してください' : 
                         'AI响应解析失败，请重试');
        }
      }

      throw error;
    }
  }

  /**
   * 生成标题优化建议
   */
  async generateOptimization(
    title: string,
    targetKeyword: string,
    marketplace: string,
    category: string,
    productFeatures?: string[],
    modelType: ModelType = 'STANDARD',
    language: string = 'zh' // 新增语言参数
  ): Promise<TitleOptimizationResult> {
    const systemPrompt = `你是亚马逊SEO专家，专门优化产品标题以提高搜索排名和转化率。

请根据2025年亚马逊最新政策和最佳实践进行优化：
- 遵循亚马逊标题政策（无主观性词汇、正确大小写、无特殊符号滥用）
- 关键词前置，提高搜索排名
- 包含核心产品特性和卖点
- 符合目标市场的语言习惯
- 优化转化率和点击率`;

    const prompt = `原标题: "${title}"
目标关键词: "${targetKeyword}"
市场: ${marketplace}
类别: ${category}
产品特性: ${productFeatures?.join(', ') || '未提供'}

请提供：
1. 3个优化后的标题建议（每个不超过200字符）
2. 具体改进建议
3. 相关关键词建议
4. 合规性注意事项

请以JSON格式返回：
{
  "optimizedTitles": ["标题1", "标题2", "标题3"],
  "improvements": ["改进建议1", "改进建议2", "改进建议3"],
  "keywordSuggestions": ["关键词1", "关键词2", "关键词3"],
  "complianceNotes": ["合规注意1", "合规注意2", "合规注意3"]
}`;

    try {
      const modelConfig = ModelSelector.forTitleAnalysis();
      
      const response = await aiService.chat({
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: modelConfig.temperature,
        maxTokens: modelConfig.maxTokens
      }, 'title-optimization');

      // 解析JSON响应
      let jsonResult: any;
      try {
        jsonResult = JSON.parse(response.content);
      } catch {
        const jsonMatch = response.content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          jsonResult = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('AI响应格式不正确');
        }
      }

      return jsonResult;
    } catch (parseError) {
      console.warn('Failed to parse AI response as JSON:', parseError);
      throw new Error('AI响应格式错误，请重试');
    }
  }

  /**
   * 分析标题合规性
   */
  async analyzeCompliance(
    title: string,
    marketplace: string,
    category: string,
    modelType: ModelType = 'ECONOMY'
  ): Promise<ComplianceAnalysisResult> {
    const systemPrompt = `你是亚马逊合规专家，专门分析产品标题的合规性。

请根据亚马逊2025年最新标题政策检查：
1. 禁用词汇（best, amazing, perfect, ultimate, premium等主观性词汇）
2. 大小写规范（避免全大写，使用正确的标题大小写）
3. 特殊符号使用（™®©限制使用，避免过多标点符号）
4. 字符长度限制（不同市场的限制）
5. 品牌名称位置和格式
6. 产品信息准确性和完整性
7. 搜索相关性和关键词使用
8. 类别特定要求

评分标准：
- 90-100分：完全合规，优秀
- 70-89分：基本合规，有改进空间
- 50-69分：部分违规，需要修改
- 0-49分：严重违规，必须重写`;

    const prompt = `标题: "${title}"
市场: ${marketplace}
类别: ${category}

请以JSON格式返回：
{
  "isCompliant": true,
  "violations": [],
  "suggestions": ["建议1", "建议2", "建议3"],
  "score": 95
}`;

    try {
      const modelConfig = ModelSelector.forTitleAnalysis();
      
      const response = await aiService.chat({
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        maxTokens: 1500
      }, 'compliance-analysis');

      // 解析JSON响应
      let jsonResult: any;
      try {
        jsonResult = JSON.parse(response.content);
      } catch {
        const jsonMatch = response.content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          jsonResult = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('AI响应格式不正确');
        }
      }

      return jsonResult;
    } catch (parseError) {
      console.warn('Failed to parse compliance analysis:', parseError);
      throw new Error('AI响应格式错误，请重试');
    }
  }
}

// 单例实例
export const titleFixerService = new TitleFixerService();
