// 产品图片生成服务 - 基于Gemini 2.0 Flash图片生成模型
// 专门用于亚马逊产品图片的AI生成和编辑
// 对接 https://gemini-balance-c1w2.onrender.com 代理服务

import { checkAIUsageLimit, recordAIUsage, type UsageLimits } from '../../../utils/aiUsageLimit';
import { validateAmazonImage, convertImageFormat, type ImageQualityConfig } from '../../../utils/imageProcessing';
import {
  UserTier,
  DEFAULT_USER_TIER,
  getUserLimits
} from '../../../config/productImageGenerator';
import { aiService } from '../index';

// 图片生成相关类型定义
export interface ImageGenerationRequest {
  prompt: string;
  baseImage?: string; // base64编码的图片
  aspectRatio?: '1:1' | '3:4' | '4:3' | '9:16' | '16:9';
  style?: 'product' | 'lifestyle' | 'studio' | 'scene';
  amazonSpec?: 'main' | 'additional' | 'aplus' | 'brand';
  userTier?: UserTier; // 用户等级
  preferredModel?: string; // 首选模型
}

export interface ImageGenerationResult {
  imageData: string; // base64编码的生成图片
  prompt: string;
  metadata: {
    model: string;
    aspectRatio: string;
    processingTime: number;
    timestamp: string;
    width?: number;
    height?: number;
    fileSize?: number;
    optimized?: boolean;
  };
}

export interface ImageEditRequest {
  baseImage: string; // base64编码的原始图片
  editPrompt: string;
  preserveProduct?: boolean; // 是否保持产品主体不变
  backgroundOnly?: boolean; // 是否只编辑背景
}

// 产品图片生成器使用限制配置（免费用户默认）
const getImageGeneratorLimits = (userTier: UserTier = DEFAULT_USER_TIER): UsageLimits => {
  const limits = getUserLimits(userTier);
  return {
    hourlyLimit: limits.hourlyGenerations,
    dailyLimit: limits.dailyGenerations === -1 ? 999999 : limits.dailyGenerations,
    sessionLimit: limits.sessionGenerations,
    cooldownMinutes: limits.cooldownMinutes
  };
};

// 亚马逊图片规格配置 - 基于2025年最新要求
export const AMAZON_IMAGE_SPECS = {
  main: {
    aspectRatio: '1:1' as const,
    description: '主图 - 纯白背景，产品占比85%',
    requirements: '纯白背景(RGB 255,255,255)，产品居中占85%，无文字水印，最小1000x1000像素',
    dimensions: { min: 1000, recommended: 2000, max: 10000 },
    fileSize: { max: 10 }, // MB
    formats: ['JPEG', 'PNG', 'TIFF', 'GIF']
  },
  additional: {
    aspectRatio: '1:1' as const,
    description: '附图 - 展示产品特性和使用场景',
    requirements: '可以有背景，展示产品功能和细节，最小1000x1000像素',
    dimensions: { min: 1000, recommended: 2000, max: 10000 },
    fileSize: { max: 10 }, // MB
    formats: ['JPEG', 'PNG', 'TIFF', 'GIF']
  },
  aplus: {
    aspectRatio: '16:9' as const,
    description: 'A+内容图 - 品牌故事和产品展示',
    requirements: '高质量展示，可包含文字和品牌元素，RGB色彩空间，最小72dpi',
    dimensions: { min: 970, recommended: 1464, max: 10000 },
    fileSize: { max: 2 }, // MB
    formats: ['JPEG', 'PNG', 'BMP']
  },
  brand: {
    aspectRatio: '16:9' as const,
    description: '品牌旗舰店图 - Banner和宣传图',
    requirements: '品牌化设计，可包含营销元素，高分辨率',
    dimensions: { min: 1200, recommended: 1920, max: 10000 },
    fileSize: { max: 5 }, // MB
    formats: ['JPEG', 'PNG', 'TIFF']
  }
};

/**
 * 产品图片生成服务类
 */
export class ProductImageGenerator {
  
  /**
   * 检查使用限制
   */
  checkUsageLimit(userTier: UserTier = DEFAULT_USER_TIER): {
    canUse: boolean;
    reason?: string;
    stats: any;
    userTier: UserTier;
    limits: any;
  } {
    const limits = getImageGeneratorLimits(userTier);
    const stats = checkAIUsageLimit('product-image-generator', limits);

    return {
      canUse: !stats.isLimited,
      reason: stats.limitReason,
      stats,
      userTier,
      limits
    };
  }

  /**
   * 获取用户可用的模型
   */
  getAvailableModels(): string[] {
    // 当前只有图片生成模型可用
    return ['gemini-2.0-flash-preview-image-generation'];
  }

  /**
   * 生成产品图片 - 使用真实的Gemini 2.0 Flash图片生成API
   */
  async generateProductImage(request: ImageGenerationRequest): Promise<ImageGenerationResult> {
    const userTier = request.userTier || DEFAULT_USER_TIER;

    // 检查使用限制
    const usageCheck = this.checkUsageLimit(userTier);
    if (!usageCheck.canUse) {
      throw new Error(`使用受限: ${this.getUsageLimitMessage(usageCheck.reason || '', userTier)}`);
    }

    // 检查AI服务可用性
    if (!aiService.isAvailable()) {
      throw new Error('AI图片生成服务暂时不可用，请稍后重试');
    }

    const startTime = Date.now();

    // 根据亚马逊规格调整提示词
    const enhancedPrompt = this.enhancePromptForAmazon(request);
    console.log('🎨 Enhanced prompt:', enhancedPrompt);

    try {
      // 调用AI图片生成API
      const response = await this.callAIImageGeneration(enhancedPrompt, request);

      // 记录成功使用
      recordAIUsage('product-image-generator', true);

      return {
        imageData: response.imageData,
        prompt: enhancedPrompt,
        metadata: {
          model: 'advanced-ai-image-generation',
          aspectRatio: request.aspectRatio || '1:1',
          processingTime: Date.now() - startTime,
          timestamp: new Date().toISOString(),
          width: response.width,
          height: response.height,
          fileSize: response.fileSize,
          optimized: response.optimized || false
        }
      };

    } catch (error) {
      console.error('图片生成失败:', error);
      // 记录失败使用
      recordAIUsage('product-image-generator', false);

      // 如果是API限制或网络错误，提供更友好的错误信息
      if (error instanceof Error) {
        if (error.message.includes('quota') || error.message.includes('limit')) {
          throw new Error('AI服务使用量已达上限，请稍后重试或升级账户');
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          throw new Error('网络连接失败，请检查网络后重试');
        }
      }

      throw new Error(`图片生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 编辑现有产品图片
   */
  async editProductImage(request: ImageEditRequest): Promise<ImageGenerationResult> {
    const startTime = Date.now();
    
    // 构建编辑提示词
    const editPrompt = this.buildEditPrompt(request);

    try {
      // 演示模式：直接返回占位符图片
      console.log('演示模式：图片编辑功能使用占位符');

      // 模拟处理时间
      await new Promise(resolve => setTimeout(resolve, 1500));

      const imageData = this.getPlaceholderImage();

      return {
        imageData,
        prompt: editPrompt,
        metadata: {
          model: 'gemini-2.0-flash-demo',
          aspectRatio: '1:1', // 默认保持原始比例
          processingTime: Date.now() - startTime,
          timestamp: new Date().toISOString()
        }
      };

    } catch (error) {
      console.error('图片编辑失败:', error);
      throw new Error(`图片编辑失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 批量生成产品图片
   */
  async batchGenerateImages(requests: ImageGenerationRequest[]): Promise<ImageGenerationResult[]> {
    const results: ImageGenerationResult[] = [];
    
    // 为了避免API限制，添加延迟
    for (let i = 0; i < requests.length; i++) {
      try {
        const result = await this.generateProductImage(requests[i]);
        results.push(result);
        
        // 添加延迟避免频率限制
        if (i < requests.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      } catch (error) {
        console.error(`批量生成第${i + 1}张图片失败:`, error);
        // 继续处理其他图片
      }
    }
    
    return results;
  }

  /**
   * 根据亚马逊规格增强提示词
   */
  private enhancePromptForAmazon(request: ImageGenerationRequest): string {
    const spec = request.amazonSpec ? AMAZON_IMAGE_SPECS[request.amazonSpec] : null;
    let enhancedPrompt = request.prompt;

    // 根据亚马逊规格添加要求
    if (spec) {
      enhancedPrompt += `\n\nAmazon Requirements: ${spec.requirements}`;
      
      if (request.amazonSpec === 'main') {
        enhancedPrompt += '\nEnsure pure white background (RGB 255,255,255), product centered and occupying 85% of the image area, no text overlays, no watermarks, high resolution, professional studio lighting.';
      }
    }

    // 根据风格添加描述
    switch (request.style) {
      case 'studio':
        enhancedPrompt += '\nStyle: Professional studio photography with clean lighting and minimal shadows.';
        break;
      case 'lifestyle':
        enhancedPrompt += '\nStyle: Lifestyle photography showing the product in real-world usage scenarios.';
        break;
      case 'scene':
        enhancedPrompt += '\nStyle: Contextual scene showing the product in its intended environment.';
        break;
      default:
        enhancedPrompt += '\nStyle: Clean, professional product photography.';
    }

    // 添加宽高比要求 - 更明确的描述
    if (request.aspectRatio) {
      const ratioDescriptions = {
        '1:1': 'Create a square image with 1:1 aspect ratio',
        '3:4': 'Create a portrait image with 3:4 aspect ratio (vertical)',
        '4:3': 'Create a landscape image with 4:3 aspect ratio (horizontal)',
        '9:16': 'Create a vertical mobile image with 9:16 aspect ratio (portrait)',
        '16:9': 'Create a widescreen image with 16:9 aspect ratio (landscape)'
      };
      enhancedPrompt += `\n${ratioDescriptions[request.aspectRatio] || `Aspect ratio: ${request.aspectRatio}`}`;
    }

    return enhancedPrompt;
  }

  /**
   * 构建图片编辑提示词
   */
  private buildEditPrompt(request: ImageEditRequest): string {
    let prompt = `Edit the provided product image: ${request.editPrompt}`;

    if (request.preserveProduct) {
      prompt += '\nIMPORTANT: Keep the main product exactly as it is, only modify the specified elements.';
    }

    if (request.backgroundOnly) {
      prompt += '\nIMPORTANT: Only change the background, keep the product and its positioning unchanged.';
    }

    prompt += '\nMaintain high image quality and ensure the result looks professional and natural.';

    return prompt;
  }



  /**
   * 获取使用限制消息
   */
  private getUsageLimitMessage(limitReason: string, userTier: UserTier = DEFAULT_USER_TIER): string {
    const limits = getImageGeneratorLimits(userTier);

    switch (limitReason) {
      case 'daily_limit':
        return `您今日的图片生成次数已用完（${limits.dailyLimit}次），请明天再试`;
      case 'hourly_limit':
        return `您本小时的图片生成次数已用完（${limits.hourlyLimit}次），请稍后再试`;
      case 'session_limit':
        return `您本次会话的图片生成次数已用完（${limits.sessionLimit}次），请刷新页面后继续`;
      case 'cooldown':
        return `请等待${limits.cooldownMinutes}分钟后再次使用图片生成功能`;
      default:
        return userTier === UserTier.FREE
          ? '免费用户使用受限，升级到专业版获得更多使用次数'
          : '使用受限，请稍后再试';
    }
  }

  /**
   * 调用AI图片生成API
   */
  private async callAIImageGeneration(prompt: string, request: ImageGenerationRequest): Promise<{
    imageData: string;
    width?: number;
    height?: number;
    fileSize?: number;
    optimized?: boolean;
  }> {
    // 使用环境变量中的API基础URL，优先使用自定义域名
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'https://api.amzova.com';

    // 构建图片生成请求
    const requestBody = {
      model: 'gemini-2.0-flash-preview-image-generation',
      messages: [
        {
          role: 'system',
          content: 'You are an AI image generation assistant. When asked to generate images, you should create visual content based on the user\'s description. Always respond with the generated image data or a detailed description if image generation fails.'
        },
        {
          role: 'user',
          content: `Please generate an image based on this description: ${prompt}\n\nImportant: This is for Amazon product listing, so ensure high quality and professional appearance.`
        }
      ],
      max_tokens: 1000,
      temperature: 0.7
    };

    console.log('🚀 Sending image generation request:', JSON.stringify(requestBody, null, 2));

    // 如果有基础图片，添加到请求中
    if (request.baseImage) {
      requestBody.messages.unshift({
        role: 'user',
        content: [
          {
            type: 'image',
            image: request.baseImage
          },
          {
            type: 'text',
            text: '请基于这张图片进行编辑：'
          }
        ]
      });
    }

    try {
      // 临时解决方案：由于当前Gemini代理不支持真正的图片生成，
      // 我们先尝试API调用，如果失败则使用演示模式
      console.log('🎨 尝试调用图片生成API...');

      const response = await fetch(`${apiBaseUrl}/api/ai/image-generation`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody),
        signal: AbortSignal.timeout(30000) // 减少到30秒超时
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.warn('⚠️ API调用失败，使用演示模式:', response.status, errorText);
        throw new Error(`API调用失败: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success) {
        console.warn('⚠️ API返回失败，使用演示模式:', result.error);
        throw new Error(result.error || '图片生成失败');
      }

      // 提取生成的图片数据
      const imageData = result.data.image || result.data.images?.[0];
      if (!imageData) {
        console.warn('⚠️ API未返回图片数据，使用演示模式');
        throw new Error('API返回的图片数据为空');
      }

      console.log('✅ 成功获取AI生成的图片');
      return {
        imageData,
        width: result.data.width || 1024,
        height: result.data.height || 1024,
        fileSize: result.data.fileSize || 2048,
        optimized: result.data.optimized || false
      };

    } catch (error) {
      console.warn('⚠️ AI图片生成API不可用，切换到演示模式:', error);

      // 使用演示模式生成图片
      console.log('🎨 生成演示图片，包含用户提示信息');
      const demoImageData = this.generateDemoImage(prompt, request);

      return {
        imageData: demoImageData,
        width: 1024,
        height: 1024,
        fileSize: 2048,
        optimized: true
      };
    }
  }

  /**
   * 获取占位符图片 - 演示用
   */
  private getPlaceholderImage(): string {
    // 创建一个Canvas来生成有效的PNG图片
    try {
      const canvas = document.createElement('canvas');
      canvas.width = 1024;
      canvas.height = 1024;
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        throw new Error('无法创建Canvas上下文');
      }

      // 绘制背景
      ctx.fillStyle = '#f8f9fa';
      ctx.fillRect(0, 0, 1024, 1024);

      // 绘制产品区域
      ctx.fillStyle = '#ffffff';
      ctx.strokeStyle = '#dee2e6';
      ctx.lineWidth = 2;
      ctx.fillRect(102, 102, 820, 820);
      ctx.strokeRect(102, 102, 820, 820);

      // 绘制文字
      ctx.fillStyle = '#6c757d';
      ctx.font = '24px Arial, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('AI生成的产品图片', 512, 460);

      ctx.fillStyle = '#adb5bd';
      ctx.font = '16px Arial, sans-serif';
      ctx.fillText('演示版本 - AI生成', 512, 490);

      // 绘制装饰圆圈
      ctx.fillStyle = 'rgba(0, 123, 255, 0.3)';
      ctx.beginPath();
      ctx.arc(512, 600, 50, 0, 2 * Math.PI);
      ctx.fill();

      // 转换为PNG格式的base64
      return canvas.toDataURL('image/png');
    } catch (error) {
      console.error('生成占位符图片失败:', error);
      // 如果Canvas失败，回退到简单的SVG
      const svg = `
        <svg width="1024" height="1024" xmlns="http://www.w3.org/2000/svg">
          <rect width="100%" height="100%" fill="#f8f9fa"/>
          <rect x="10%" y="10%" width="80%" height="80%" fill="#ffffff" stroke="#dee2e6" stroke-width="2"/>
          <text x="50%" y="45%" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" fill="#6c757d">
            AI生成的产品图片
          </text>
          <text x="50%" y="55%" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#adb5bd">
            演示版本 - AI生成
          </text>
          <circle cx="50%" cy="70%" r="50" fill="#007bff" opacity="0.3"/>
        </svg>
      `;
      const base64 = btoa(unescape(encodeURIComponent(svg)));
      return `data:image/svg+xml;base64,${base64}`;
    }
  }

  /**
   * 验证图片是否符合亚马逊要求
   */
  async validateAmazonCompliance(imageData: string, spec: keyof typeof AMAZON_IMAGE_SPECS): Promise<{
    isCompliant: boolean;
    issues: string[];
    suggestions: string[];
  }> {
    // 使用图片处理工具进行验证
    try {
      const validation = await validateAmazonImage(imageData, spec as any);
      return {
        isCompliant: validation.isValid,
        issues: validation.issues,
        suggestions: validation.suggestions
      };
    } catch (error) {
      console.error('图片验证失败:', error);
      return {
        isCompliant: false,
        issues: ['图片验证失败'],
        suggestions: ['请检查图片格式和内容']
      };
    }
  }

  /**
   * 优化图片质量和格式
   */
  async optimizeImage(imageData: string, config: ImageQualityConfig): Promise<ImageGenerationResult> {
    try {
      const result = await convertImageFormat(imageData, config);

      return {
        imageData: result.dataUrl,
        prompt: '图片优化处理',
        metadata: {
          model: 'image-processing',
          aspectRatio: `${result.width}:${result.height}`,
          processingTime: 0,
          timestamp: new Date().toISOString(),
          width: result.width,
          height: result.height,
          fileSize: result.fileSize,
          optimized: true
        }
      };
    } catch (error) {
      console.error('图片优化失败:', error);
      throw new Error(`图片优化失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }
}

// 创建单例实例
export const productImageGenerator = new ProductImageGenerator();

// 导出便捷函数
export const generateProductImage = (request: ImageGenerationRequest) => 
  productImageGenerator.generateProductImage(request);

export const editProductImage = (request: ImageEditRequest) => 
  productImageGenerator.editProductImage(request);

export const batchGenerateImages = (requests: ImageGenerationRequest[]) => 
  productImageGenerator.batchGenerateImages(requests);
