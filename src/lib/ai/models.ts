// Gemini模型配置管理
// 为不同场景和用途提供智能模型选择

export enum GeminiModel {
  // 文字优化专用模型 - 轻量高效
  GEMINI_2_5_FLASH_LITE = 'gemini-2.5-flash-lite',

  // 主要模型 - Gemini 2.0 Flash  
  GEMINI_2_0_FLASH = 'gemini-2.0-flash',

  // 图片生成专用模型 - 免费层级可用
  GEMINI_2_0_FLASH_IMAGE_GENERATION = 'gemini-2.0-flash-preview-image-generation',

  // 未来扩展模型
  GEMINI_PRO = 'gemini-pro',
  GEMINI_PRO_VISION = 'gemini-pro-vision',
  GEMINI_ULTRA = 'gemini-ultra',
  GEMINI_NANO = 'gemini-nano'
}

export interface ModelConfig {
  model: GeminiModel;
  maxTokens: number;
  temperature: number;
  useCase: string[];
  description: string;
  costLevel: 'low' | 'medium' | 'high';
  capabilities: string[];
}

/**
 * 场景化模型配置
 * 根据不同的AI任务场景选择最适合的模型和参数
 */
export const SCENARIO_MODEL_CONFIGS: Record<string, ModelConfig> = {
  // 标题分析场景
  'title-analysis': {
    model: GeminiModel.GEMINI_2_5_FLASH_LITE,
    maxTokens: 2000,
    temperature: 0.3, // 较低温度确保分析的一致性
    useCase: ['标题分析', 'SEO优化', '关键词分析', '合规检查'],
    description: '专用于亚马逊产品标题的深度分析和优化建议',
    costLevel: 'low',
    capabilities: ['文本分析', 'JSON输出', '结构化数据', '多维度评估']
  },

  // 标题修复场景
  'title-fix': {
    model: GeminiModel.GEMINI_2_5_FLASH_LITE,
    maxTokens: 1500,
    temperature: 0.2, // 最低温度确保修复的准确性
    useCase: ['标题修复', '合规修复', '违规处理', '政策遵循'],
    description: '专用于修复违规标题，确保100%符合Amazon政策',
    costLevel: 'low',
    capabilities: ['精准修复', '同义词替换', '规则遵循', '多版本生成']
  },

  // 通用文本处理
  'general-text': {
    model: GeminiModel.GEMINI_2_5_FLASH_LITE,
    maxTokens: 2000,
    temperature: 0.7, // 标准温度平衡创意和准确性
    useCase: ['通用对话', '文本生成', '内容创作', '问答'],
    description: '通用文本处理和对话场景',
    costLevel: 'low',
    capabilities: ['自然对话', '创意写作', '信息提取', '多语言支持']
  },

  // 图片分析场景（未来扩展）
  'image-analysis': {
    model: GeminiModel.GEMINI_PRO_VISION,
    maxTokens: 3000,
    temperature: 0.4,
    useCase: ['图片分析', '产品识别', '视觉内容理解', '图文结合'],
    description: '专用于图片内容分析和视觉理解',
    costLevel: 'high',
    capabilities: ['图像识别', '视觉分析', '多模态理解', '产品检测']
  },

  // 批量处理场景
  'batch-processing': {
    model: GeminiModel.GEMINI_2_0_FLASH,
    maxTokens: 1000,
    temperature: 0.1, // 极低温度确保批量处理的一致性
    useCase: ['批量处理', '数据清洗', '格式转换', '自动化任务'],
    description: '专用于大批量数据处理和自动化任务',
    costLevel: 'low',
    capabilities: ['高效处理', '一致性输出', '格式标准化', '错误容忍']
  },

  // 创意生成场景
  'creative-generation': {
    model: GeminiModel.GEMINI_2_0_FLASH,
    maxTokens: 2500,
    temperature: 0.8, // 较高温度激发创意
    useCase: ['创意写作', '营销文案', '产品描述', '广告语生成'],
    description: '专用于创意内容生成和营销文案创作',
    costLevel: 'medium',
    capabilities: ['创意思维', '营销洞察', '文案优化', '品牌表达']
  },

  // 图片生成场景 - 免费层级可用
  'image-generation': {
    model: GeminiModel.GEMINI_2_0_FLASH_IMAGE_GENERATION,
    maxTokens: 2000,
    temperature: 0.7, // 平衡创意和准确性
    useCase: ['产品图片生成', '背景替换', '场景化展示', '图片编辑'],
    description: '专用于产品图片生成和编辑，基于Gemini 2.0 Flash图片生成能力',
    costLevel: 'medium',
    capabilities: ['文本转图片', '图片编辑', '背景替换', '多轮对话编辑', '多种宽高比']
  },

  // 图片编辑场景
  'image-editing': {
    model: GeminiModel.GEMINI_2_0_FLASH_IMAGE_GENERATION,
    maxTokens: 1500,
    temperature: 0.5, // 较低温度确保编辑准确性
    useCase: ['图片修改', '元素添加', '背景更换', '风格调整'],
    description: '专用于现有图片的编辑和修改',
    costLevel: 'medium',
    capabilities: ['精准编辑', '元素控制', '风格转换', '质量优化']
  }
};

/**
 * 模型类型映射
 * 将传统的模型类型映射到具体的Gemini模型
 */
export const MODEL_TYPE_MAPPING: Record<string, GeminiModel> = {
  'PREMIUM': GeminiModel.GEMINI_2_0_FLASH,
  'STANDARD': GeminiModel.GEMINI_2_0_FLASH,
  'ECONOMY': GeminiModel.GEMINI_2_0_FLASH
};

/**
 * 根据场景获取模型配置
 */
export function getModelConfig(scenario: string): ModelConfig {
  return SCENARIO_MODEL_CONFIGS[scenario] || SCENARIO_MODEL_CONFIGS['general-text'];
}

/**
 * 根据模型类型获取Gemini模型
 */
export function getGeminiModel(modelType: string): GeminiModel {
  return MODEL_TYPE_MAPPING[modelType] || GeminiModel.GEMINI_2_0_FLASH;
}

/**
 * 智能模型选择器
 * 根据任务特征自动选择最适合的模型配置
 */
export class ModelSelector {
  /**
   * 根据任务特征选择模型
   */
  static selectModel(taskFeatures: {
    type: 'analysis' | 'generation' | 'fix' | 'creative' | 'batch';
    complexity: 'low' | 'medium' | 'high';
    accuracy: 'standard' | 'high' | 'critical';
    creativity: 'none' | 'low' | 'medium' | 'high';
  }): ModelConfig {
    // 根据任务类型确定基础场景
    let scenario = 'general-text';
    
    switch (taskFeatures.type) {
      case 'analysis':
        scenario = 'title-analysis';
        break;
      case 'fix':
        scenario = 'title-fix';
        break;
      case 'creative':
        scenario = 'creative-generation';
        break;
      case 'batch':
        scenario = 'batch-processing';
        break;
      case 'generation':
      default:
        scenario = 'general-text';
        break;
    }

    const baseConfig = getModelConfig(scenario);

    // 根据准确性要求调整温度
    let temperature = baseConfig.temperature;
    if (taskFeatures.accuracy === 'critical') {
      temperature = Math.min(temperature, 0.2);
    } else if (taskFeatures.accuracy === 'high') {
      temperature = Math.min(temperature, 0.4);
    }

    // 根据创意要求调整温度
    if (taskFeatures.creativity === 'high') {
      temperature = Math.max(temperature, 0.8);
    } else if (taskFeatures.creativity === 'medium') {
      temperature = Math.max(temperature, 0.6);
    }

    // 根据复杂度调整token限制
    let maxTokens = baseConfig.maxTokens;
    if (taskFeatures.complexity === 'high') {
      maxTokens = Math.max(maxTokens, 3000);
    } else if (taskFeatures.complexity === 'low') {
      maxTokens = Math.min(maxTokens, 1000);
    }

    return {
      ...baseConfig,
      temperature,
      maxTokens
    };
  }

  /**
   * 为标题分析任务选择模型
   */
  static forTitleAnalysis(): ModelConfig {
    return this.selectModel({
      type: 'analysis',
      complexity: 'medium',
      accuracy: 'high',
      creativity: 'none'
    });
  }

  /**
   * 为标题修复任务选择模型
   */
  static forTitleFix(): ModelConfig {
    return this.selectModel({
      type: 'fix',
      complexity: 'medium',
      accuracy: 'critical',
      creativity: 'low'
    });
  }

  /**
   * 为创意生成任务选择模型
   */
  static forCreativeGeneration(): ModelConfig {
    return this.selectModel({
      type: 'creative',
      complexity: 'high',
      accuracy: 'standard',
      creativity: 'high'
    });
  }

  /**
   * 为批量处理任务选择模型
   */
  static forBatchProcessing(): ModelConfig {
    return this.selectModel({
      type: 'batch',
      complexity: 'low',
      accuracy: 'high',
      creativity: 'none'
    });
  }
}

/**
 * 模型性能监控
 * 跟踪不同模型的使用情况和性能指标
 */
export class ModelPerformanceTracker {
  private static usage: Map<string, {
    calls: number;
    totalTime: number;
    errors: number;
    lastUsed: Date;
  }> = new Map();

  /**
   * 记录模型使用
   */
  static recordUsage(model: string, responseTime: number, success: boolean) {
    const current = this.usage.get(model) || {
      calls: 0,
      totalTime: 0,
      errors: 0,
      lastUsed: new Date()
    };

    current.calls++;
    current.totalTime += responseTime;
    current.lastUsed = new Date();
    
    if (!success) {
      current.errors++;
    }

    this.usage.set(model, current);
  }

  /**
   * 获取模型统计信息
   */
  static getStats(model: string) {
    const stats = this.usage.get(model);
    if (!stats) {
      return null;
    }

    return {
      model,
      totalCalls: stats.calls,
      averageResponseTime: stats.totalTime / stats.calls,
      errorRate: stats.errors / stats.calls,
      lastUsed: stats.lastUsed,
      successRate: (stats.calls - stats.errors) / stats.calls
    };
  }

  /**
   * 获取所有模型的统计信息
   */
  static getAllStats() {
    const allStats = [];
    for (const [model] of this.usage) {
      const stats = this.getStats(model);
      if (stats) {
        allStats.push(stats);
      }
    }
    return allStats.sort((a, b) => b.totalCalls - a.totalCalls);
  }

  /**
   * 重置统计信息
   */
  static reset() {
    this.usage.clear();
  }
}

/**
 * 导出常用配置
 */
export const COMMON_CONFIGS = {
  titleAnalysis: ModelSelector.forTitleAnalysis(),
  titleFix: ModelSelector.forTitleFix(),
  creativeGeneration: ModelSelector.forCreativeGeneration(),
  batchProcessing: ModelSelector.forBatchProcessing()
};

/**
 * 获取推荐的模型配置
 */
export function getRecommendedConfig(scenario: string, options?: {
  prioritizeAccuracy?: boolean;
  prioritizeCreativity?: boolean;
  prioritizeSpeed?: boolean;
}): ModelConfig {
  const baseConfig = getModelConfig(scenario);
  
  if (!options) {
    return baseConfig;
  }

  let adjustedConfig = { ...baseConfig };

  // 优先考虑准确性
  if (options.prioritizeAccuracy) {
    adjustedConfig.temperature = Math.min(adjustedConfig.temperature, 0.3);
  }

  // 优先考虑创意性
  if (options.prioritizeCreativity) {
    adjustedConfig.temperature = Math.max(adjustedConfig.temperature, 0.7);
  }

  // 优先考虑速度
  if (options.prioritizeSpeed) {
    adjustedConfig.maxTokens = Math.min(adjustedConfig.maxTokens, 1500);
  }

  return adjustedConfig;
}