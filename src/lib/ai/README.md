# 🤖 AI服务架构文档

> 基于Cloudflare Pages + Workers + Gemini代理的AI服务架构 - 专为亚马逊卖家工具优化

## 📋 架构概述

本AI服务架构基于统一的Gemini代理服务，通过Cloudflare Workers作为代理层，解决了前端直接调用AI API的跨域限制问题，实现高性能、低延迟、完全可控的AI服务调用。

### 🎯 设计原则

- ✅ **统一服务** - 使用单一的Gemini代理，简化架构
- ✅ **零跨域问题** - 前端通过Workers代理调用AI服务
- ✅ **完全可控** - 使用自建代理，无第三方依赖风险
- ✅ **成本透明** - 完全掌控AI服务成本和使用
- ✅ **向后兼容** - 保持现有API不变，平滑迁移
- ✅ **类型安全** - 完整的TypeScript类型定义

## 🏗️ 架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                    Cloudflare Global Network                    │
├─────────────────────────────────────────────────────────────────┤
│  🌍 CDN + DDoS Protection + SSL/TLS + DNS                     │
└─────────────────────────────────────────────────────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
        ┌───────▼──────┐ ┌──────▼──────┐ ┌─────▼─────┐
        │ Pages (前端)  │ │Workers (API)│ │ AI服务商   │
        │              │ │             │ │           │
        │ React SPA    │ │ AI代理服务   │ │ Gemini AI │
        │ AI客户端     │ │ /api/ai/*   │ │ 代理服务   │
        └──────────────┘ └─────────────┘ └───────────┘
                │                │               │
                │                └───────────────┘
                │                        │
        ┌───────▼──────┐          ┌──────▼──────┐
        │ 本地存储      │          │ 配额管理     │
        │ 用户偏好     │          │ 使用统计     │
        │ 缓存数据     │          │ 限制控制     │
        └──────────────┘          └─────────────┘
```

## 📁 文件结构

```
src/lib/ai/
├── index.ts              # 统一入口，AI服务客户端
├── README.md            # 本文档
└── quotaManager.ts      # Cloudflare配额管理（独立文件）

已删除的复杂架构：
├── manager.ts           # ❌ 过度设计的管理器
├── types.ts            # ❌ 复杂的类型定义
├── providers/          # ❌ 不必要的提供商抽象
└── services/           # ❌ 过度分层的服务
```

## 🔧 核心组件

### 1. AIServiceClient 类

**位置**: `src/lib/ai/index.ts`

**功能**: 统一的AI服务客户端，处理所有AI调用

**特性**:
- 🎯 自动选择最佳AI提供商
- 🔄 智能故障转移机制
- 📊 统一的错误处理
- ⚡ 通过Workers代理调用

```typescript
class AIServiceClient {
  private baseUrl: string;
  private defaultProvider: AIProvider;
  
  // 自动检测最佳提供商
  private determineDefaultProvider(): AIProvider
  
  // 检查服务可用性
  isAvailable(): boolean
  
  // 统一AI调用接口
  async callAI(prompt: string, modelType?: ModelType, preferredProvider?: AIProvider): Promise<string>
}
```

### 2. 配额管理器

**位置**: `src/lib/quotaManager.ts`

**功能**: Cloudflare Workers AI免费额度管理

**特性**:
- 📊 实时额度追踪
- ⚠️ 智能警告机制
- 🚫 自动限制保护
- 💾 本地存储持久化

## 🌐 Gemini代理服务

### 统一的Gemini代理

**优势**:
- 💰 **成本可控**: 使用您自建的代理服务，成本完全透明
- ⚡ **高性能**: 优秀的响应速度和稳定性
- 🔒 **安全性**: 完全可控，无第三方数据泄露风险
- 📈 **可扩展**: 支持未来多模态AI功能扩展
- 🧠 **智能**: 优秀的中文理解和JSON格式输出

**服务配置**:
```typescript
const GEMINI_CONFIG = {
  baseURL: 'https://gemini-balance-c1w2.onrender.com',
  apiKey: 'sk-kdjferKjidjrf9jihdhfnghjxkdefKKGHYI',
  model: 'gemini-2.0-flash',
  timeout: 30000,
  maxRetries: 3
};
```

**场景化配置**:
```typescript
const SCENARIO_CONFIGS = {
  'title-fix': { temperature: 0.2, maxTokens: 1500 },      // 标题修复
  'title-analysis': { temperature: 0.3, maxTokens: 2000 }, // 标题分析
  'general-text': { temperature: 0.7, maxTokens: 2000 },   // 通用文本
  'creative-generation': { temperature: 0.8, maxTokens: 2500 } // 创意生成
};
```

## 🚀 使用指南

### 基础调用

```typescript
import { callAI, isAIAvailable, getAIServiceConfig } from '../lib/ai';

// 检查服务可用性
if (isAIAvailable()) {
  // 基础AI调用
  const response = await callAI('请优化这个标题', 'STANDARD');
  
  // 指定提供商
  const response2 = await callAI('分析合规性', 'ECONOMY', 'cloudflare');
}

// 获取服务配置
const config = getAIServiceConfig();
console.log(`当前提供商: ${config.provider}`);
```

### 标题修复功能

```typescript
import { fixTitleCompliance } from '../lib/ai';

const result = await fixTitleCompliance(
  'Original Title with Issues!',  // 原标题
  'Special characters not allowed', // 错误信息
  'US',                           // 市场
  'general',                      // 类别
  violations,                     // 违规项
  'STANDARD'                      // 模型类型
);

console.log(result.fixedTitles);    // 3个修复版本
console.log(result.changes);       // 修改说明
console.log(result.explanation);   // 详细解释
console.log(result.complianceScore); // 合规评分
```

### 标题优化建议

```typescript
import { generateTitleOptimization } from '../lib/ai';

const optimization = await generateTitleOptimization(
  'Current Product Title',
  'target keyword',
  'US',
  'electronics',
  ['feature1', 'feature2']
);

console.log(optimization.optimizedTitles);     // 优化建议
console.log(optimization.improvements);       // 改进说明
console.log(optimization.keywordSuggestions); // 关键词建议
```

## ⚙️ 环境配置

### 环境变量

```bash
# API配置
VITE_API_BASE_URL=https://api.amzova.com

# Cloudflare Workers AI (推荐)
VITE_CLOUDFLARE_API_KEY=your_cloudflare_api_key
VITE_CLOUDFLARE_ACCOUNT_ID=your_cloudflare_account_id

# SiliconFlow (备用)
VITE_Silicon_API_KEY=your_siliconflow_api_key

# AI提供商选择
VITE_AI_PROVIDER=cloudflare  # 或 siliconflow

# 模型配置 (可选)
VITE_MODEL_PREMIUM=Qwen/Qwen2.5-72B-Instruct
VITE_MODEL_STANDARD=Qwen/Qwen2.5-32B-Instruct
VITE_MODEL_ECONOMY=Qwen/Qwen2.5-7B-Instruct
```

### Workers配置

**文件**: `sellerbox-api/src/index.js`

**AI代理端点**:
- `POST /api/ai/siliconflow` - SiliconFlow代理
- `POST /api/ai/cloudflare` - Cloudflare AI代理

**请求格式**:
```json
{
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant..."
    },
    {
      "role": "user", 
      "content": "用户提示词"
    }
  ],
  "model": "模型名称",
  "temperature": 0.7,
  "max_tokens": 2000
}
```

## 📊 性能优化

### 1. 智能提供商选择

```typescript
// 自动选择逻辑
private determineDefaultProvider(): AIProvider {
  // 1. 环境变量指定
  if (envProvider && isValidProvider(envProvider)) {
    return envProvider;
  }
  
  // 2. Cloudflare优先（免费额度）
  if (hasCloudflareConfig()) {
    return 'cloudflare';
  }
  
  // 3. 默认SiliconFlow
  return 'siliconflow';
}
```

### 2. 配额管理

```typescript
// 实时配额检查
const quotaStatus = getQuotaStatus();
if (quotaStatus.isBlocked) {
  // 自动切换到备用提供商
  switchToBackupProvider();
}
```

### 3. 错误处理与重试

```typescript
// 统一错误处理
try {
  return await primaryProvider.call(request);
} catch (error) {
  console.log('主提供商失败，切换到备用');
  return await backupProvider.call(request);
}
```

## 🔒 安全特性

### 1. API密钥保护
- ✅ 密钥存储在Workers环境变量
- ✅ 前端不暴露任何敏感信息
- ✅ 请求通过HTTPS加密传输

### 2. 访问控制
- ✅ CORS策略由Workers统一管理
- ✅ 请求频率限制
- ✅ 输入验证和清理

### 3. 配额保护
- ✅ 实时使用量监控
- ✅ 自动限制机制
- ✅ 成本控制预警

## 🧪 测试指南

### 本地测试

```bash
# 1. 启动开发服务器
npm run dev

# 2. 启动Workers开发服务器
cd sellerbox-api
wrangler dev

# 3. 测试AI服务
curl -X POST http://localhost:8787/api/ai/cloudflare \
  -H "Content-Type: application/json" \
  -d '{"messages":[{"role":"user","content":"Hello"}]}'
```

### 功能测试

```typescript
// 测试AI服务可用性
console.log('AI服务可用:', isAIAvailable());

// 测试配置
const config = getAIServiceConfig();
console.log('当前配置:', config);

// 测试调用
const response = await callAI('测试提示词', 'ECONOMY');
console.log('AI响应:', response);
```

## 📈 监控与分析

### 1. 使用统计
- 📊 调用次数统计
- ⏱️ 响应时间监控
- 💰 成本使用追踪
- 🔄 提供商切换记录

### 2. 错误监控
- ❌ 失败率统计
- 🔍 错误类型分析
- 📝 详细错误日志
- 🚨 异常告警机制

### 3. 性能指标
- ⚡ 平均响应时间: <2秒
- 📈 成功率: >99%
- 💾 缓存命中率: >80%
- 🌍 全球延迟: <500ms

## 🔄 部署流程

### 1. 前端部署 (Cloudflare Pages)

```bash
# 构建项目
npm run build

# 部署到Pages
wrangler pages deploy dist --project-name=amzova-frontend
```

### 2. API部署 (Cloudflare Workers)

```bash
# 切换到API目录
cd sellerbox-api

# 部署Workers
wrangler deploy

# 验证部署
curl https://api.amzova.com/api/health
```

### 3. 环境变量配置

**Cloudflare Pages环境变量**:
```
VITE_API_BASE_URL=https://api.amzova.com
VITE_CLOUDFLARE_API_KEY=[已配置]
VITE_CLOUDFLARE_ACCOUNT_ID=[已配置]
VITE_AI_PROVIDER=cloudflare
```

**Cloudflare Workers环境变量**:
```
VITE_CLOUDFLARE_API_KEY=[已配置]
VITE_CLOUDFLARE_ACCOUNT_ID=[已配置]
VITE_Silicon_API_KEY=[已配置]
```

## 🚀 未来扩展

### 1. 新AI提供商支持
- 🤖 OpenAI GPT-4
- 🧠 Anthropic Claude
- 🔬 Google Gemini
- 🎯 专业模型集成

### 2. 功能增强
- 📊 实时性能监控
- 🎨 自定义提示词模板
- 💾 智能缓存机制
- 🔄 批量处理支持

### 3. 优化改进
- ⚡ 响应时间优化
- 💰 成本进一步降低
- 🌍 更多地区支持
- 📱 移动端优化

---

## 📞 技术支持

- **文档**: 本README.md
- **示例**: `src/components/tools/TitleFixer.tsx`
- **配置**: `src/lib/ai/index.ts`
- **API**: `sellerbox-api/src/index.js`

**如有问题，请查看相关组件的实现代码或联系技术团队。**
