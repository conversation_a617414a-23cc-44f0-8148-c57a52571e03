// AI服务统一入口 - 基于Gemini代理的新架构
// 完全替换原有的Cloudflare Workers AI和SiliconFlow

import { 
  getModelConfig, 
  getGeminiModel, 
  ModelSelector, 
  ModelPerformanceTracker,
  type ModelConfig 
} from './models';
import { titleFixerService } from './services/titleFixer';

// 类型定义
export type ModelType = 'PREMIUM' | 'STANDARD' | 'ECONOMY';

export interface AIRequest {
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  model?: string;
  temperature?: number;
  maxTokens?: number;
}

export interface AIResponse {
  content: string;
  model: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  metadata?: {
    requestId: string;
    timestamp: string;
    processingTime: number;
  };
}

export interface AIServiceConfig {
  baseUrl: string;
  apiKey: string;
  defaultModel: string;
  timeout: number;
  isAvailable: boolean;
}

// 业务功能接口 - 保持向后兼容
export interface TitleFixResult {
  fixedTitles: string[];
  changes: string[];
  explanation: string;
  complianceScore: number;
}

export interface TitleOptimizationResult {
  optimizedTitles: string[];
  improvements: string[];
  keywordSuggestions: string[];
  complianceNotes: string[];
}

export interface ComplianceAnalysisResult {
  isCompliant: boolean;
  violations: string[];
  suggestions: string[];
  score: number;
}

/**
 * Gemini AI服务客户端
 * 统一管理所有AI调用，通过Cloudflare Workers代理
 */
class GeminiAIClient {
  private config: AIServiceConfig;

  constructor() {
    // 获取环境变量，提供多个备选方案
    const baseUrl = import.meta.env.VITE_API_BASE_URL || 
                   'https://amzova-backend-service.sellerbox.workers.dev' ||
                   'https://api.amzova.com';
    const apiKey = import.meta.env.GEMINI_API_KEY || 'sk-kdjferKjidjrf9jihdhfnghjxkdefKKGHYI';
    
    // 开发环境下输出调试信息
    if (import.meta.env.DEV) {
      console.log('[AI Config] Base URL:', baseUrl);
      console.log('[AI Config] API Key:', apiKey ? `${apiKey.substring(0, 10)}...` : 'Not set');
    }
    
    this.config = {
      baseUrl,
      apiKey,
      defaultModel: 'gemini-2.5-flash-lite', // 文字优化的默认模型
      timeout: 30000,
      isAvailable: !!(baseUrl && apiKey)
    };
  }

  /**
   * 检查AI服务是否可用
   */
  private checkAvailability(): boolean {
    return !!(this.config?.baseUrl && this.config?.apiKey);
  }

  /**
   * 获取AI服务配置
   */
  getConfig(): AIServiceConfig {
    return { ...this.config };
  }

  /**
   * 检查服务可用性
   */
  isAvailable(): boolean {
    return this.config.isAvailable;
  }

  /**
   * 根据场景选择合适的模型配置
   */
  private getModelConfigForScenario(scenario: string, modelType: ModelType = 'STANDARD'): ModelConfig {
    // 优先使用场景化配置
    const scenarioConfig = getModelConfig(scenario);
    if (scenarioConfig) {
      return scenarioConfig;
    }

    // 回退到传统模型类型映射
    const model = getGeminiModel(modelType);
    return {
      model,
      maxTokens: 2000,
      temperature: 0.7,
      useCase: ['通用'],
      description: '通用AI任务',
      costLevel: 'medium',
      capabilities: ['文本处理']
    };
  }

  /**
   * 统一AI调用接口
   */
  async chat(request: AIRequest, scenario: string = 'general'): Promise<AIResponse> {
    if (!this.isAvailable()) {
      throw new Error('AI服务不可用，请检查配置');
    }

    // 获取场景化模型配置
    const modelConfig = this.getModelConfigForScenario(scenario);
    const model = request.model || modelConfig.model;
    const startTime = Date.now();

    // 构建请求体，使用模型配置的默认参数
    const requestBody = {
      messages: request.messages,
      model,
      temperature: request.temperature || modelConfig.temperature,
      max_tokens: request.maxTokens || modelConfig.maxTokens,
      stream: false
    };

    try {
      // 创建超时控制器
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

      // 通过Workers代理调用Gemini服务
      const response = await fetch(`${this.config.baseUrl}/api/ai/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`AI服务调用失败: ${response.status} - ${errorText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'AI服务调用失败');
      }

      // 提取响应内容
      const data = result.data;
      let content = '';

      if (data.choices && data.choices[0] && data.choices[0].message) {
        content = data.choices[0].message.content;
      } else if (data.content) {
        content = data.content;
      } else {
        throw new Error('AI响应格式异常');
      }

      const processingTime = Date.now() - startTime;

      // 记录模型性能
      ModelPerformanceTracker.recordUsage(model, processingTime, true);

      return {
        content,
        model,
        usage: data.usage || {},
        metadata: {
          requestId: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: new Date().toISOString(),
          processingTime
        }
      };

    } catch (error) {
      console.error('AI服务调用失败:', error);

      // 记录错误
      const processingTime = Date.now() - startTime;
      ModelPerformanceTracker.recordUsage(model, processingTime, false);

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('AI请求超时，请稍后重试');
        } else if (error.message.includes('fetch')) {
          throw new Error('网络连接失败，请检查网络后重试');
        }
      }

      throw error;
    }
  }

  /**
   * 简化的AI调用接口 - 向后兼容
   */
  async callAI(
    prompt: string,
    modelType: ModelType = 'STANDARD',
    scenario: string = 'general'
  ): Promise<string> {
    const response = await this.chat({
      messages: [
        {
          role: 'system',
          content: 'You are a helpful assistant designed to output JSON. Always respond with valid JSON format.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      maxTokens: 2000
    }, scenario);

    return response.content;
  }
}

// 创建单例实例
const geminiClient = new GeminiAIClient();

// 导出便捷函数 - 保持向后兼容
export const isAIAvailable = (): boolean => geminiClient.isAvailable();
export const getAIServiceConfig = () => geminiClient.getConfig();
export const callAI = (prompt: string, modelType?: ModelType, scenario?: string) =>
  geminiClient.callAI(prompt, modelType, scenario);

// 导出AI客户端实例
export const aiService = geminiClient;

// 导出模型相关功能
export { 
  getModelConfig, 
  getGeminiModel, 
  ModelSelector, 
  ModelPerformanceTracker,
  SCENARIO_MODEL_CONFIGS,
  COMMON_CONFIGS 
} from './models';
export type { ModelConfig } from './models';

/**
 * 修复标题合规性问题
 * 使用Gemini代理服务，保持原有接口不变
 */
export const fixTitleCompliance = async (
  originalTitle: string,
  errorMessage: string,
  marketplace: string = 'US',
  category: string = 'general',
  violations: any[] = [],
  modelType: ModelType = 'STANDARD',
  language: string = 'zh' // 新增语言参数
): Promise<TitleFixResult> => {
  // 使用titleFixerService处理多语言逻辑
  return await titleFixerService.fixCompliance(
    originalTitle,
    errorMessage,
    marketplace,
    category,
    violations,
    modelType,
    language
  );
};

/**
 * 生成标题优化建议
 * 使用Gemini代理服务，保持原有接口不变
 */
export const generateTitleOptimization = async (
  title: string,
  targetKeyword: string,
  marketplace: string,
  category: string,
  productFeatures?: string[],
  modelType: ModelType = 'STANDARD',
  language: string = 'zh' // 新增语言参数
): Promise<TitleOptimizationResult> => {
  
  // 根据语言选择提示词
  const getPromptByLanguage = (lang: string) => {
    switch (lang) {
      case 'en':
        return `As an Amazon SEO expert, please analyze and optimize the following product title:

Original Title: "${title}"
Target Keyword: "${targetKeyword}"
Market: ${marketplace}
Category: ${category}
Product Features: ${productFeatures?.join(', ') || 'Not provided'}

Based on Amazon's latest 2025 policies and best practices, please provide:

1. 3 optimized title suggestions (each under 200 characters)
2. Specific improvement suggestions
3. Related keyword suggestions
4. Compliance notes

Requirements:
- Follow Amazon title policies (no subjective words, proper capitalization, no special symbol abuse)
- Place keywords at the front to improve search ranking
- Include core product features and selling points
- Match target market language habits
- Optimize conversion rate and click-through rate

Please return results in JSON format:
{
  "optimizedTitles": ["Title 1", "Title 2", "Title 3"],
  "improvements": ["Improvement 1", "Improvement 2", "Improvement 3"],
  "keywordSuggestions": ["Keyword 1", "Keyword 2", "Keyword 3"],
  "complianceNotes": ["Compliance note 1", "Compliance note 2", "Compliance note 3"]
}

⚠️ Important: Return only JSON, no additional explanatory text or code block markers.`;

      case 'ja':
        return `Amazonの SEO専門家として、以下の商品タイトルを分析・最適化してください：

元のタイトル: "${title}"
ターゲットキーワード: "${targetKeyword}"
市場: ${marketplace}
カテゴリ: ${category}
商品特徴: ${productFeatures?.join(', ') || '提供されていません'}

Amazon の2025年最新ポリシーとベストプラクティスに基づいて、以下を提供してください：

1. 3つの最適化されたタイトル提案（各200文字以内）
2. 具体的な改善提案
3. 関連キーワード提案
4. コンプライアンス注意事項

要件：
- Amazonタイトルポリシーに従う（主観的な言葉なし、適切な大文字小文字、特殊記号の乱用なし）
- 検索ランキング向上のためキーワードを前方に配置
- 核心的な商品特徴とセールスポイントを含む
- ターゲット市場の言語習慣に合わせる
- コンバージョン率とクリック率を最適化

JSON形式で結果を返してください：
{
  "optimizedTitles": ["タイトル1", "タイトル2", "タイトル3"],
  "improvements": ["改善提案1", "改善提案2", "改善提案3"],
  "keywordSuggestions": ["キーワード1", "キーワード2", "キーワード3"],
  "complianceNotes": ["コンプライアンス注意1", "コンプライアンス注意2", "コンプライアンス注意3"]
}

⚠️ 重要：JSONのみを返し、追加の説明文やコードブロックマーカーは含めないでください。`;

      default: // 中文和其他语言
        return `作为亚马逊SEO专家，请分析并优化以下产品标题：

原标题: "${title}"
目标关键词: "${targetKeyword}"
市场: ${marketplace}
类别: ${category}
产品特性: ${productFeatures?.join(', ') || '未提供'}

请根据2025年亚马逊最新政策和最佳实践，提供：

1. 3个优化后的标题建议（每个不超过200字符）
2. 具体改进建议
3. 相关关键词建议
4. 合规性注意事项

要求：
- 遵循亚马逊标题政策（无主观性词汇、正确大小写、无特殊符号滥用）
- 关键词前置，提高搜索排名
- 包含核心产品特性和卖点
- 符合目标市场的语言习惯
- 优化转化率和点击率

请以JSON格式返回结果：
{
  "optimizedTitles": ["标题1", "标题2", "标题3"],
  "improvements": ["改进建议1", "改进建议2", "改进建议3"],
  "keywordSuggestions": ["关键词1", "关键词2", "关键词3"],
  "complianceNotes": ["合规注意1", "合规注意2", "合规注意3"]
}

⚠️ 重要：只返回JSON，不要添加任何解释文字或代码块标记。`;
    }
  };

  const prompt = getPromptByLanguage(language);

  const response = await geminiClient.chat({
    messages: [
      {
        role: 'system',
        content: '你是专业的亚马逊SEO专家。你必须严格按照JSON格式返回结果，不要添加任何额外的文字或代码块标记。'
      },
      {
        role: 'user',
        content: prompt
      }
    ],
    temperature: 0.3,
    maxTokens: 2000
  }, 'title-analysis');

  try {
    // 处理可能的markdown格式
    const jsonMatch = response.content.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    
    // 尝试直接解析
    return JSON.parse(response.content);
  } catch (parseError) {
    console.warn('Failed to parse AI response as JSON:', parseError);
    throw new Error('AI响应格式错误，请重试');
  }
};

/**
 * 分析标题合规性
 * 使用Gemini代理服务，保持原有接口不变
 */
export const analyzeTitleCompliance = async (
  title: string,
  marketplace: string,
  category: string,
  modelType: ModelType = 'ECONOMY'
): Promise<ComplianceAnalysisResult> => {
  const prompt = `作为亚马逊合规专家，请分析以下产品标题的合规性：

标题: "${title}"
市场: ${marketplace}
类别: ${category}

请根据亚马逊2025年最新标题政策检查：

1. 禁用词汇（best, amazing, perfect, ultimate, premium等主观性词汇）
2. 大小写规范（避免全大写，使用正确的标题大小写）
3. 特殊符号使用（™®©限制使用，避免过多标点符号）
4. 字符长度限制（不同市场的限制）
5. 品牌名称位置和格式
6. 产品信息准确性和完整性
7. 搜索相关性和关键词使用
8. 类别特定要求

评分标准：
- 90-100分：完全合规，优秀
- 70-89分：基本合规，有改进空间
- 50-69分：部分违规，需要修改
- 0-49分：严重违规，必须重写

请以JSON格式返回：
{
  "isCompliant": true,
  "violations": [],
  "suggestions": ["建议1", "建议2", "建议3"],
  "score": 95
}

⚠️ 重要：只返回JSON，不要添加任何解释文字或代码块标记。`;

  const response = await geminiClient.chat({
    messages: [
      {
        role: 'system',
        content: '你是专业的亚马逊合规专家。你必须严格按照JSON格式返回结果，不要添加任何额外的文字或代码块标记。'
      },
      {
        role: 'user',
        content: prompt
      }
    ],
    temperature: 0.1,
    maxTokens: 1500
  }, 'title-analysis');

  try {
    // 处理可能的markdown格式
    const jsonMatch = response.content.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    
    // 尝试直接解析
    return JSON.parse(response.content);
  } catch (parseError) {
    console.warn('Failed to parse compliance analysis:', parseError);
    throw new Error('AI响应格式错误，请重试');
  }
};

// 向后兼容的别名
export const initializeGemini = isAIAvailable;
export const isGeminiAvailable = isAIAvailable;