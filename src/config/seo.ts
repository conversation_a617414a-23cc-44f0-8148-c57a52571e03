// SEO 配置文件
export const SEO_CONFIG = {
  // 基础信息
  siteName: '爱麦蛙 - 亚马逊卖家专业工具平台',
  siteNameEn: 'AmzOva - Amazon Seller Professional Tools',
  siteUrl: 'https://amzova.com',
  
  // 默认 Meta 信息
  defaultTitle: '爱麦蛙 - 免费亚马逊卖家工具 | 标题优化、利润计算、汇率换算',
  defaultTitleEn: 'AmzOva - Free Amazon Seller Tools | Title Optimizer, Profit Calculator, Currency Converter',
  defaultDescription: '专业的亚马逊卖家工具平台，提供标题分析器、利润计算器、实时汇率换算、装箱计算器等12+免费工具。无需注册，AI驱动，助力跨境电商成功。',
  defaultDescriptionEn: 'Professional Amazon seller tools platform with 12+ free tools including title analyzer, profit calculator, real-time currency converter, and packing calculator. No registration required, AI-powered.',
  
  // 关键词
  keywords: {
    zh: [
      '亚马逊卖家工具',
      '亚马逊标题优化',
      '亚马逊利润计算器',
      '亚马逊FBA计算器',
      '汇率换算器',
      '跨境电商工具',
      '亚马逊运营工具',
      '免费卖家工具',
      'Amazon工具',
      '爱麦蛙'
    ],
    en: [
      'Amazon seller tools',
      'Amazon title optimizer',
      'Amazon profit calculator',
      'Amazon FBA calculator',
      'Currency converter',
      'Cross-border e-commerce tools',
      'Amazon operation tools',
      'Free seller tools',
      'AmzOva',
      'Amazon business tools'
    ]
  },
  
  // Open Graph 配置
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    siteName: '爱麦蛙 AmzOva',
    images: [
      {
        url: 'https://amzova.com/amzova.png',
        width: 512,
        height: 512,
        alt: '爱麦蛙 - 亚马逊卖家工具'
      }
    ]
  },
  
  // Twitter Card 配置
  twitter: {
    card: 'summary_large_image',
    site: '@AmzOva',
    creator: '@AmzOva',
    images: ['https://amzova.com/amzova.png']
  },
  
  // 结构化数据模板
  structuredData: {
    organization: {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: '爱麦蛙 AmzOva',
      url: 'https://amzova.com',
      logo: {
        '@type': 'ImageObject',
        url: 'https://amzova.com/amzova.png',
        width: 512,
        height: 512
      },
      sameAs: [
        'https://github.com/amzova',
        'https://twitter.com/amzova'
      ],
      contactPoint: {
        '@type': 'ContactPoint',
        contactType: 'Customer Service',
        availableLanguage: ['Chinese', 'English', 'Japanese', 'Indonesian', 'Vietnamese']
      }
    },
    
    website: {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      name: '爱麦蛙 - 亚马逊卖家工具',
      alternateName: 'AmzOva - Amazon Seller Tools',
      url: 'https://amzova.com',
      potentialAction: {
        '@type': 'SearchAction',
        target: 'https://amzova.com/tools?q={search_term_string}',
        'query-input': 'required name=search_term_string'
      }
    }
  },
  
  // 搜索引擎验证码（需要替换为真实值）
  verification: {
    google: 'a175af350543cbcc',
    bing: 'your-bing-verification-code',
    yandex: 'your-yandex-verification-code',
    baidu: 'codeva-U1LCnWiNN1',
    sogou: 'your-sogou-verification-code',
    qihoo360: 'your-360-verification-code'
  },
  
  // 分析工具 ID（需要替换为真实值）
  analytics: {
    googleAnalytics: 'G-PP6Q9DF7LK',
    baiduAnalytics: 'your-baidu-analytics-id',
    microsoftClarity: 'your-clarity-id'
  },
  
  // 语言和地区配置
  languages: [
    { code: 'zh', name: '简体中文', hreflang: 'zh-CN', region: 'CN' },
    { code: 'zh-TW', name: '繁體中文', hreflang: 'zh-TW', region: 'TW' },
    { code: 'en', name: 'English', hreflang: 'en-US', region: 'US' },
    { code: 'ja', name: '日本語', hreflang: 'ja-JP', region: 'JP' },
    { code: 'id', name: 'Bahasa Indonesia', hreflang: 'id-ID', region: 'ID' },
    { code: 'vi', name: 'Tiếng Việt', hreflang: 'vi-VN', region: 'VN' }
  ]
};

// 工具页面 SEO 配置
export const TOOL_SEO_CONFIG = {
  'title-analyzer': {
    title: {
      zh: '亚马逊标题分析器 - AI智能优化产品标题 | 爱麦蛙',
      en: 'Amazon Title Analyzer - AI Smart Product Title Optimizer | AmzOva'
    },
    description: {
      zh: '专业的亚马逊标题分析器，AI智能分析产品标题，提供SEO优化建议、关键词密度检查、合规性验证。免费使用，提升listing排名。',
      en: 'Professional Amazon title analyzer with AI-powered analysis, SEO optimization suggestions, keyword density check, and compliance verification. Free to use.'
    },
    keywords: {
      zh: ['亚马逊标题分析', '标题优化', 'Amazon标题', 'SEO优化', '关键词分析'],
      en: ['Amazon title analyzer', 'title optimization', 'Amazon title', 'SEO optimization', 'keyword analysis']
    }
  },
  
  'currency-converter': {
    title: {
      zh: '实时汇率换算器 - 170+货币实时汇率 | 爱麦蛙',
      en: 'Real-time Currency Converter - 170+ Currencies Live Rates | AmzOva'
    },
    description: {
      zh: '支持170+种货币的实时汇率换算器，每10分钟更新汇率数据，提供汇率趋势图表。跨境电商必备工具，准确计算成本利润。',
      en: 'Real-time currency converter supporting 170+ currencies, updated every 10 minutes with rate trend charts. Essential tool for cross-border e-commerce.'
    },
    keywords: {
      zh: ['汇率换算', '实时汇率', '货币转换', '跨境电商', '汇率计算器'],
      en: ['currency converter', 'real-time exchange rate', 'currency conversion', 'cross-border ecommerce', 'exchange rate calculator']
    }
  },
  
  'profit-calculator': {
    title: {
      zh: '亚马逊利润计算器 - FBA费用计算ROI分析 | 爱麦蛙',
      en: 'Amazon Profit Calculator - FBA Fee Calculator ROI Analysis | AmzOva'
    },
    description: {
      zh: '精确的亚马逊利润计算器，自动计算FBA费用、ROI投资回报率，支持实时汇率换算。可视化成本结构，优化定价策略。',
      en: 'Accurate Amazon profit calculator with automatic FBA fee calculation, ROI analysis, and real-time currency conversion. Visualize cost structure and optimize pricing.'
    },
    keywords: {
      zh: ['亚马逊利润计算', 'FBA费用计算', 'ROI计算', '投资回报率', '亚马逊定价'],
      en: ['Amazon profit calculator', 'FBA fee calculator', 'ROI calculation', 'return on investment', 'Amazon pricing']
    }
  }
};
