// 产品图片生成器配置文件
// 包含成本控制、用户管理、功能限制等配置

/**
 * 用户等级枚举
 */
export enum UserTier {
  FREE = 'free',
  PRO = 'pro',
  ENTERPRISE = 'enterprise'
}

/**
 * 功能特性枚举
 */
export enum Feature {
  BASIC_GENERATION = 'basic_generation',
  ADVANCED_EDITING = 'advanced_editing',
  BATCH_PROCESSING = 'batch_processing',
  CUSTOM_TEMPLATES = 'custom_templates',
  PRIORITY_SUPPORT = 'priority_support',
  API_ACCESS = 'api_access',
  WHITE_LABEL = 'white_label'
}

/**
 * 用户等级配置
 */
export interface UserTierConfig {
  name: string;
  displayName: string;
  price: {
    monthly: number;
    yearly: number;
    currency: string;
  };
  limits: {
    dailyGenerations: number;
    hourlyGenerations: number;
    sessionGenerations: number;
    maxImageSize: number; // MB
    maxBatchSize: number;
    cooldownMinutes: number;
  };
  features: Feature[];
  modelAccess: string[];
  priority: number; // 数字越小优先级越高
}

/**
 * 成本控制配置
 */
export interface CostControlConfig {
  // API调用成本（每次调用的成本，美元）
  apiCosts: {
    'gemini-2.0-flash-image-generation': number;
    'imagen-3': number;
    'imagen-4': number;
    'imagen-4-ultra': number;
  };
  
  // 成本预警阈值
  costAlerts: {
    daily: number;   // 每日成本预警阈值（美元）
    monthly: number; // 每月成本预警阈值（美元）
    user: number;    // 单用户成本预警阈值（美元）
  };
  
  // 自动限制配置
  autoLimits: {
    enableCostBasedLimiting: boolean;
    maxDailyCost: number;
    maxMonthlyCost: number;
    emergencyShutoff: number; // 紧急停止阈值
  };
}

/**
 * 用户等级配置
 */
export const USER_TIER_CONFIGS: Record<UserTier, UserTierConfig> = {
  [UserTier.FREE]: {
    name: 'free',
    displayName: '免费版',
    price: {
      monthly: 0,
      yearly: 0,
      currency: 'USD'
    },
    limits: {
      dailyGenerations: 5,
      hourlyGenerations: 5,
      sessionGenerations: 5,
      maxImageSize: 10, // 10MB
      maxBatchSize: 1,
      cooldownMinutes: 0 // 取消冷却时间，与title-fixer保持一致
    },
    features: [
      Feature.BASIC_GENERATION
    ],
    modelAccess: [
      'gemini-2.0-flash-image-generation'
    ],
    priority: 3
  },
  
  [UserTier.PRO]: {
    name: 'pro',
    displayName: '专业版',
    price: {
      monthly: 29,
      yearly: 290, // 2个月免费
      currency: 'USD'
    },
    limits: {
      dailyGenerations: 50,
      hourlyGenerations: 20,
      sessionGenerations: 10,
      maxImageSize: 50, // 50MB
      maxBatchSize: 5,
      cooldownMinutes: 1
    },
    features: [
      Feature.BASIC_GENERATION,
      Feature.ADVANCED_EDITING,
      Feature.BATCH_PROCESSING,
      Feature.CUSTOM_TEMPLATES
    ],
    modelAccess: [
      'gemini-2.0-flash-image-generation',
      'imagen-3',
      'imagen-4'
    ],
    priority: 2
  },
  
  [UserTier.ENTERPRISE]: {
    name: 'enterprise',
    displayName: '企业版',
    price: {
      monthly: 99,
      yearly: 990, // 2个月免费
      currency: 'USD'
    },
    limits: {
      dailyGenerations: -1, // 无限制
      hourlyGenerations: 100,
      sessionGenerations: 50,
      maxImageSize: 100, // 100MB
      maxBatchSize: 20,
      cooldownMinutes: 0
    },
    features: [
      Feature.BASIC_GENERATION,
      Feature.ADVANCED_EDITING,
      Feature.BATCH_PROCESSING,
      Feature.CUSTOM_TEMPLATES,
      Feature.PRIORITY_SUPPORT,
      Feature.API_ACCESS,
      Feature.WHITE_LABEL
    ],
    modelAccess: [
      'gemini-2.0-flash-image-generation',
      'imagen-3',
      'imagen-4',
      'imagen-4-ultra'
    ],
    priority: 1
  }
};

/**
 * 成本控制配置
 */
export const COST_CONTROL_CONFIG: CostControlConfig = {
  apiCosts: {
    'gemini-2.0-flash-image-generation': 0.00, // 免费模型
    'imagen-3': 0.04, // 每次生成$0.04
    'imagen-4': 0.08, // 每次生成$0.08
    'imagen-4-ultra': 0.16 // 每次生成$0.16
  },
  
  costAlerts: {
    daily: 50,   // $50/天
    monthly: 1000, // $1000/月
    user: 10     // $10/用户
  },
  
  autoLimits: {
    enableCostBasedLimiting: true,
    maxDailyCost: 100,   // $100/天
    maxMonthlyCost: 2000, // $2000/月
    emergencyShutoff: 500 // $500紧急停止
  }
};

/**
 * 功能特性配置
 */
export const FEATURE_CONFIGS: Record<Feature, {
  name: string;
  description: string;
  requiredTier: UserTier;
  enabled: boolean;
}> = {
  [Feature.BASIC_GENERATION]: {
    name: '基础图片生成',
    description: '使用AI生成基础的产品图片',
    requiredTier: UserTier.FREE,
    enabled: true
  },
  
  [Feature.ADVANCED_EDITING]: {
    name: '高级编辑功能',
    description: '背景替换、元素添加、风格调整等高级编辑功能',
    requiredTier: UserTier.PRO,
    enabled: false // 暂未实现
  },
  
  [Feature.BATCH_PROCESSING]: {
    name: '批量处理',
    description: '同时处理多张图片，提高工作效率',
    requiredTier: UserTier.PRO,
    enabled: false // 暂未实现
  },
  
  [Feature.CUSTOM_TEMPLATES]: {
    name: '自定义模板',
    description: '保存和复用常用的图片生成设置',
    requiredTier: UserTier.PRO,
    enabled: false // 暂未实现
  },
  
  [Feature.PRIORITY_SUPPORT]: {
    name: '优先技术支持',
    description: '享受优先的技术支持和客户服务',
    requiredTier: UserTier.ENTERPRISE,
    enabled: true
  },
  
  [Feature.API_ACCESS]: {
    name: 'API访问',
    description: '通过API集成图片生成功能到您的系统',
    requiredTier: UserTier.ENTERPRISE,
    enabled: false // 暂未实现
  },
  
  [Feature.WHITE_LABEL]: {
    name: '白标解决方案',
    description: '定制化的白标解决方案',
    requiredTier: UserTier.ENTERPRISE,
    enabled: false // 暂未实现
  }
};

/**
 * 获取用户等级配置
 */
export function getUserTierConfig(tier: UserTier): UserTierConfig {
  return USER_TIER_CONFIGS[tier];
}

/**
 * 检查用户是否有权限使用某个功能
 */
export function hasFeatureAccess(userTier: UserTier, feature: Feature): boolean {
  const userConfig = getUserTierConfig(userTier);
  const featureConfig = FEATURE_CONFIGS[feature];
  
  // 检查功能是否启用
  if (!featureConfig.enabled) {
    return false;
  }
  
  // 检查用户等级是否满足要求
  const userPriority = userConfig.priority;
  const requiredPriority = getUserTierConfig(featureConfig.requiredTier).priority;
  
  return userPriority <= requiredPriority;
}

/**
 * 检查用户是否可以访问某个模型
 */
export function hasModelAccess(userTier: UserTier, model: string): boolean {
  const userConfig = getUserTierConfig(userTier);
  return userConfig.modelAccess.includes(model);
}

/**
 * 计算API调用成本
 */
export function calculateApiCost(model: string, count: number = 1): number {
  const costPerCall = COST_CONTROL_CONFIG.apiCosts[model as keyof typeof COST_CONTROL_CONFIG.apiCosts];
  return (costPerCall || 0) * count;
}

/**
 * 获取用户的使用限制
 */
export function getUserLimits(userTier: UserTier) {
  return getUserTierConfig(userTier).limits;
}

/**
 * 检查是否超出成本预警阈值
 */
export function checkCostAlert(dailyCost: number, monthlyCost: number, userCost: number): {
  hasAlert: boolean;
  alerts: string[];
} {
  const alerts: string[] = [];
  const config = COST_CONTROL_CONFIG.costAlerts;
  
  if (dailyCost >= config.daily) {
    alerts.push(`每日成本已达到预警阈值: $${dailyCost.toFixed(2)}`);
  }
  
  if (monthlyCost >= config.monthly) {
    alerts.push(`每月成本已达到预警阈值: $${monthlyCost.toFixed(2)}`);
  }
  
  if (userCost >= config.user) {
    alerts.push(`单用户成本已达到预警阈值: $${userCost.toFixed(2)}`);
  }
  
  return {
    hasAlert: alerts.length > 0,
    alerts
  };
}

/**
 * 检查是否需要自动限制
 */
export function shouldAutoLimit(dailyCost: number, monthlyCost: number): {
  shouldLimit: boolean;
  reason: string;
  isEmergency: boolean;
} {
  const config = COST_CONTROL_CONFIG.autoLimits;
  
  if (!config.enableCostBasedLimiting) {
    return { shouldLimit: false, reason: '', isEmergency: false };
  }
  
  if (dailyCost >= config.emergencyShutoff || monthlyCost >= config.emergencyShutoff) {
    return {
      shouldLimit: true,
      reason: '触发紧急停止阈值',
      isEmergency: true
    };
  }
  
  if (dailyCost >= config.maxDailyCost) {
    return {
      shouldLimit: true,
      reason: '超出每日成本限制',
      isEmergency: false
    };
  }
  
  if (monthlyCost >= config.maxMonthlyCost) {
    return {
      shouldLimit: true,
      reason: '超出每月成本限制',
      isEmergency: false
    };
  }
  
  return { shouldLimit: false, reason: '', isEmergency: false };
}

/**
 * 默认用户等级（免费用户）
 */
export const DEFAULT_USER_TIER = UserTier.FREE;
