import React, { useEffect } from 'react';
import { Routes, Route, useLocation, useNavigate, useParams } from 'react-router-dom';
import Header from './components/Header';
import Hero from './components/Hero';
import Tools from './components/Tools';
import Footer from './components/Footer';
import AdSpace from './components/AdSpace';
import UserFeedback from './components/UserFeedback';
import Features from './components/Features';
import ProductPhilosophy from './components/ProductPhilosophy';
import CustomerReviews from './components/CustomerReviews';
import WhyChooseUs from './components/WhyChooseUs';
import FeedbackAdmin from './components/admin/FeedbackAdmin';
import AnalyticsDashboard from './components/admin/AnalyticsDashboard';
import AdminDashboard from './components/admin/AdminDashboard';
import AdminLogin from './components/admin/AdminLogin';
import AnnouncementAdmin from './components/admin/AnnouncementAdmin';
import ProtectedRoute from './components/admin/ProtectedRoute';
import { PageWrapper } from './components/PageWrapper';
import { usePageTracking } from './services/analyticsService';
import { usePerformanceMonitor } from './hooks/usePerformanceMonitor';
import SEOOptimization from './components/SEOOptimization';
import AnalyticsScripts from './components/AnalyticsScripts';
import { PWAInstallPrompt, OfflineIndicator, PWAUpdatePrompt } from './components/PWAInstallPrompt';


function App() {
  console.log('🚀 App: 应用程序启动');

  const location = useLocation();
  const navigate = useNavigate();
  const { trackPageView } = usePageTracking();

  // 性能监控
  usePerformanceMonitor();

  // 页面访问追踪
  useEffect(() => {
    trackPageView(location.pathname, document.title);
  }, [location.pathname, trackPageView]);

  // 检查当前是否在工具页面
  const isToolPage = location.pathname.startsWith('/tools/');

  const handleToolSelect = (toolId: string) => {
    // 导航到对应的工具页面
    navigate(`/tools/${toolId}`);
  };

  return (
    <div className="min-h-screen">
      {/* 搜索引擎优化组件 */}
      <SEOOptimization />
      <AnalyticsScripts />

      {/* PWA 相关组件 */}
      <OfflineIndicator />
      <PWAUpdatePrompt />

      <Header />
      <Routes>
        <Route path="/" element={
          <PageWrapper page="home">
            <Hero onToolSelect={handleToolSelect} />
            {/* 工具展示 - 前置到最重要位置 */}
            <Tools showAsHomepage={true} />
            {/* 广告招商位置 - 在工具和其他内容之间 */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
              <AdSpace position="between-sections" size="medium" />
            </div>
            {/* 功能特色部分 */}
            <Features />
            {/* 产品理念部分 */}
            <ProductPhilosophy />
            {/* 客户评价部分 */}
            <CustomerReviews />
            {/* 为什么选择我们 */}
            <WhyChooseUs />
            <Footer />
          </PageWrapper>
        } />
        <Route path="/tools/:toolId" element={
          <PageWrapper page="tool-specific">
            <Tools showAsHomepage={false} />
          </PageWrapper>
        } />
        <Route path="/tools" element={
          <PageWrapper page="tools">
            <Tools showAsHomepage={false} />
          </PageWrapper>
        } />
        <Route path="/feedback" element={
          <PageWrapper page="feedback">
            <UserFeedback />
            <Footer />
          </PageWrapper>
        } />
        {/* 管理员登录页面 */}
        <Route path="/admin/login" element={<AdminLogin />} />

        {/* 受保护的管理员路由 */}
        <Route path="/admin" element={
          <ProtectedRoute requiredRole={['super_admin', 'admin', 'viewer']}>
            <AdminDashboard />
          </ProtectedRoute>
        } />
        <Route path="/admin/feedback" element={
          <ProtectedRoute requiredRole={['super_admin', 'admin']}>
            <FeedbackAdmin />
          </ProtectedRoute>
        } />
        <Route path="/admin/analytics" element={
          <ProtectedRoute requiredRole={['super_admin', 'admin']}>
            <AnalyticsDashboard />
          </ProtectedRoute>
        } />
        <Route path="/admin/announcements" element={
          <ProtectedRoute requiredRole={['super_admin', 'admin']}>
            <AnnouncementAdmin />
          </ProtectedRoute>
        } />
        {/* 404 fallback - redirect to home */}
        <Route path="*" element={
          <>
            <Hero onToolSelect={handleToolSelect} />
            <Tools showAsHomepage={true} />
            <Footer />
          </>
        } />
      </Routes>

      {/* PWA 安装提示 */}
      <PWAInstallPrompt />


    </div>
  );
}

export default App;