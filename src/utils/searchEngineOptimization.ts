// 搜索引擎特定优化配置
export interface SearchEngineConfig {
  name: string;
  userAgent: string;
  features: {
    supportsWebP: boolean;
    supportsStructuredData: boolean;
    preferredImageFormat: string;
    maxTitleLength: number;
    maxDescriptionLength: number;
    supportsBreadcrumbs: boolean;
    supportsAMP: boolean;
  };
  specificOptimizations: string[];
}

// 主要搜索引擎配置
export const searchEngineConfigs: Record<string, SearchEngineConfig> = {
  google: {
    name: 'Google',
    userAgent: 'Googlebot',
    features: {
      supportsWebP: true,
      supportsStructuredData: true,
      preferredImageFormat: 'webp',
      maxTitleLength: 60,
      maxDescriptionLength: 160,
      supportsBreadcrumbs: true,
      supportsAMP: true,
    },
    specificOptimizations: [
      'Core Web Vitals optimization',
      'Mobile-first indexing',
      'Structured data (JSON-LD)',
      'Page Experience signals',
      'E-A-T (Expertise, Authoritativeness, Trustworthiness)',
    ],
  },
  baidu: {
    name: '百度 (Baidu)',
    userAgent: 'Baiduspider',
    features: {
      supportsWebP: false, // 百度对WebP支持有限
      supportsStructuredData: true,
      preferredImageFormat: 'jpg',
      maxTitleLength: 30, // 中文字符，约30个字符
      maxDescriptionLength: 80, // 中文字符，约80个字符
      supportsBreadcrumbs: true,
      supportsAMP: false,
    },
    specificOptimizations: [
      'Chinese content optimization',
      'ICP备案信息',
      'Simplified Chinese preferred',
      'Local server hosting in China',
      'Baidu Analytics integration',
    ],
  },
  bing: {
    name: 'Bing',
    userAgent: 'Bingbot',
    features: {
      supportsWebP: true,
      supportsStructuredData: true,
      preferredImageFormat: 'webp',
      maxTitleLength: 65,
      maxDescriptionLength: 160,
      supportsBreadcrumbs: true,
      supportsAMP: false,
    },
    specificOptimizations: [
      'Bing Webmaster Tools integration',
      'Social signals importance',
      'Exact match domains preference',
      'Image search optimization',
      'Local business optimization',
    ],
  },
  yandex: {
    name: 'Yandex',
    userAgent: 'YandexBot',
    features: {
      supportsWebP: true,
      supportsStructuredData: true,
      preferredImageFormat: 'webp',
      maxTitleLength: 60,
      maxDescriptionLength: 160,
      supportsBreadcrumbs: true,
      supportsAMP: false,
    },
    specificOptimizations: [
      'Yandex.Metrica integration',
      'Russian language optimization',
      'Regional content relevance',
      'Turbo pages support',
      'Behavioral factors importance',
    ],
  },
  duckduckgo: {
    name: 'DuckDuckGo',
    userAgent: 'DuckDuckBot',
    features: {
      supportsWebP: true,
      supportsStructuredData: true,
      preferredImageFormat: 'webp',
      maxTitleLength: 60,
      maxDescriptionLength: 160,
      supportsBreadcrumbs: true,
      supportsAMP: false,
    },
    specificOptimizations: [
      'Privacy-focused content',
      'No tracking scripts',
      'Clean HTML structure',
      'Fast loading times',
      'Minimal JavaScript',
    ],
  },
};

// 检测用户代理对应的搜索引擎
export const detectSearchEngine = (userAgent: string): string => {
  const ua = userAgent.toLowerCase();
  
  if (ua.includes('googlebot')) return 'google';
  if (ua.includes('baiduspider')) return 'baidu';
  if (ua.includes('bingbot')) return 'bing';
  if (ua.includes('yandexbot')) return 'yandex';
  if (ua.includes('duckduckbot')) return 'duckduckgo';
  
  return 'google'; // 默认使用Google配置
};

// 根据搜索引擎优化标题
export const optimizeTitleForSearchEngine = (title: string, searchEngine: string): string => {
  const config = searchEngineConfigs[searchEngine];
  if (!config) return title;
  
  // 截断标题到合适长度
  if (title.length > config.features.maxTitleLength) {
    return title.substring(0, config.features.maxTitleLength - 3) + '...';
  }
  
  return title;
};

// 根据搜索引擎优化描述
export const optimizeDescriptionForSearchEngine = (description: string, searchEngine: string): string => {
  const config = searchEngineConfigs[searchEngine];
  if (!config) return description;
  
  // 截断描述到合适长度
  if (description.length > config.features.maxDescriptionLength) {
    return description.substring(0, config.features.maxDescriptionLength - 3) + '...';
  }
  
  return description;
};

// 生成搜索引擎特定的meta标签
export const generateSearchEngineSpecificMeta = (searchEngine: string) => {
  const config = searchEngineConfigs[searchEngine];
  if (!config) return [];
  
  const metaTags = [];
  
  // 百度特定优化
  if (searchEngine === 'baidu') {
    metaTags.push(
      { name: 'baidu-site-verification', content: 'your-baidu-verification-code' },
      { name: 'applicable-device', content: 'pc,mobile' },
      { name: 'MobileOptimized', content: 'width' },
      { name: 'HandheldFriendly', content: 'true' }
    );
  }
  
  // Yandex特定优化
  if (searchEngine === 'yandex') {
    metaTags.push(
      { name: 'yandex-verification', content: 'your-yandex-verification-code' },
      { name: 'format-detection', content: 'telephone=no' }
    );
  }
  
  // Bing特定优化
  if (searchEngine === 'bing') {
    metaTags.push(
      { name: 'msvalidate.01', content: 'your-bing-verification-code' },
      { name: 'msapplication-TileColor', content: '#232F3E' }
    );
  }
  
  return metaTags;
};

// 生成搜索引擎特定的结构化数据
export const generateSearchEngineStructuredData = (searchEngine: string, data: any) => {
  const config = searchEngineConfigs[searchEngine];
  if (!config || !config.features.supportsStructuredData) return null;
  
  // 基础结构化数据
  let structuredData = { ...data };
  
  // 百度特定结构化数据调整
  if (searchEngine === 'baidu') {
    // 百度更偏好简化的结构化数据
    structuredData = {
      '@context': 'https://schema.org',
      '@type': 'WebApplication',
      name: data.name,
      description: data.description,
      url: data.url,
      applicationCategory: 'BusinessApplication',
    };
  }
  
  return structuredData;
};

// 获取搜索引擎优化建议
export const getSearchEngineOptimizationTips = (searchEngine: string): string[] => {
  const config = searchEngineConfigs[searchEngine];
  if (!config) return [];
  
  return config.specificOptimizations;
};
