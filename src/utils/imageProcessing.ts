// 图片处理和优化工具函数
// 用于产品图片生成器的图片处理功能

/**
 * 图片格式枚举
 */
export enum ImageFormat {
  PNG = 'image/png',
  JPEG = 'image/jpeg',
  WEBP = 'image/webp'
}

/**
 * 图片质量配置
 */
export interface ImageQualityConfig {
  format: ImageFormat;
  quality: number; // 0-1之间，仅对JPEG和WebP有效
  maxWidth?: number;
  maxHeight?: number;
  maintainAspectRatio?: boolean;
}

/**
 * 图片处理结果
 */
export interface ImageProcessingResult {
  dataUrl: string;
  format: ImageFormat;
  width: number;
  height: number;
  fileSize: number; // 估算的文件大小（字节）
}

/**
 * 亚马逊图片规格要求
 */
export const AMAZON_IMAGE_REQUIREMENTS = {
  main: {
    minSize: 1000, // 最小1000x1000像素
    maxSize: 10000, // 最大10000x10000像素
    aspectRatio: 1, // 1:1正方形
    backgroundColor: '#FFFFFF', // 纯白背景
    formats: [ImageFormat.JPEG, ImageFormat.PNG]
  },
  additional: {
    minSize: 500,
    maxSize: 10000,
    aspectRatio: null, // 不限制宽高比
    backgroundColor: null, // 不限制背景色
    formats: [ImageFormat.JPEG, ImageFormat.PNG, ImageFormat.WEBP]
  },
  aplus: {
    minSize: 970, // A+内容最小宽度
    maxSize: 1464, // A+内容最大宽度
    aspectRatio: 16/9, // 16:9宽屏
    backgroundColor: null,
    formats: [ImageFormat.JPEG, ImageFormat.PNG]
  }
};

/**
 * 将文件转换为base64数据URL
 */
export function fileToDataUrl(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      resolve(result);
    };
    reader.onerror = () => {
      reject(new Error('文件读取失败'));
    };
    reader.readAsDataURL(file);
  });
}

/**
 * 从base64数据URL创建Image对象
 */
export function dataUrlToImage(dataUrl: string): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = () => reject(new Error('图片加载失败'));
    img.src = dataUrl;
  });
}

/**
 * 调整图片尺寸
 */
export async function resizeImage(
  dataUrl: string,
  targetWidth: number,
  targetHeight: number,
  maintainAspectRatio: boolean = true
): Promise<string> {
  const img = await dataUrlToImage(dataUrl);
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  if (!ctx) {
    throw new Error('无法创建Canvas上下文');
  }

  let { width, height } = calculateDimensions(
    img.width,
    img.height,
    targetWidth,
    targetHeight,
    maintainAspectRatio
  );

  canvas.width = width;
  canvas.height = height;

  // 使用高质量缩放
  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = 'high';

  // 绘制调整后的图片
  ctx.drawImage(img, 0, 0, width, height);

  return canvas.toDataURL('image/png');
}

/**
 * 计算调整后的尺寸
 */
function calculateDimensions(
  originalWidth: number,
  originalHeight: number,
  targetWidth: number,
  targetHeight: number,
  maintainAspectRatio: boolean
): { width: number; height: number } {
  if (!maintainAspectRatio) {
    return { width: targetWidth, height: targetHeight };
  }

  const aspectRatio = originalWidth / originalHeight;
  const targetAspectRatio = targetWidth / targetHeight;

  let width: number, height: number;

  if (aspectRatio > targetAspectRatio) {
    // 原图更宽，以宽度为准
    width = targetWidth;
    height = targetWidth / aspectRatio;
  } else {
    // 原图更高，以高度为准
    height = targetHeight;
    width = targetHeight * aspectRatio;
  }

  return { width: Math.round(width), height: Math.round(height) };
}

/**
 * 转换图片格式和质量
 */
export async function convertImageFormat(
  dataUrl: string,
  config: ImageQualityConfig
): Promise<ImageProcessingResult> {
  const img = await dataUrlToImage(dataUrl);
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  if (!ctx) {
    throw new Error('无法创建Canvas上下文');
  }

  // 计算目标尺寸
  let width = img.width;
  let height = img.height;

  if (config.maxWidth || config.maxHeight) {
    const dimensions = calculateDimensions(
      img.width,
      img.height,
      config.maxWidth || img.width,
      config.maxHeight || img.height,
      config.maintainAspectRatio !== false
    );
    width = dimensions.width;
    height = dimensions.height;
  }

  canvas.width = width;
  canvas.height = height;

  // 设置高质量渲染
  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = 'high';

  // 如果是JPEG格式，先填充白色背景
  if (config.format === ImageFormat.JPEG) {
    ctx.fillStyle = '#FFFFFF';
    ctx.fillRect(0, 0, width, height);
  }

  // 绘制图片
  ctx.drawImage(img, 0, 0, width, height);

  // 转换格式
  const resultDataUrl = canvas.toDataURL(config.format, config.quality);

  // 估算文件大小（base64编码后的大小约为原始大小的4/3）
  const base64Length = resultDataUrl.split(',')[1].length;
  const fileSize = Math.round((base64Length * 3) / 4);

  return {
    dataUrl: resultDataUrl,
    format: config.format,
    width,
    height,
    fileSize
  };
}

/**
 * 优化图片以符合亚马逊要求
 */
export async function optimizeForAmazon(
  dataUrl: string,
  spec: keyof typeof AMAZON_IMAGE_REQUIREMENTS
): Promise<ImageProcessingResult> {
  const requirements = AMAZON_IMAGE_REQUIREMENTS[spec];
  const img = await dataUrlToImage(dataUrl);

  // 计算目标尺寸
  let targetWidth = img.width;
  let targetHeight = img.height;

  // 确保满足最小尺寸要求
  if (Math.min(img.width, img.height) < requirements.minSize) {
    const scale = requirements.minSize / Math.min(img.width, img.height);
    targetWidth = Math.round(img.width * scale);
    targetHeight = Math.round(img.height * scale);
  }

  // 确保不超过最大尺寸
  if (Math.max(targetWidth, targetHeight) > requirements.maxSize) {
    const scale = requirements.maxSize / Math.max(targetWidth, targetHeight);
    targetWidth = Math.round(targetWidth * scale);
    targetHeight = Math.round(targetHeight * scale);
  }

  // 如果有宽高比要求，调整尺寸
  if (requirements.aspectRatio) {
    if (requirements.aspectRatio === 1) {
      // 正方形
      const size = Math.min(targetWidth, targetHeight);
      targetWidth = targetHeight = size;
    } else {
      // 其他宽高比
      const currentRatio = targetWidth / targetHeight;
      if (currentRatio > requirements.aspectRatio) {
        targetWidth = Math.round(targetHeight * requirements.aspectRatio);
      } else {
        targetHeight = Math.round(targetWidth / requirements.aspectRatio);
      }
    }
  }

  // 选择最佳格式
  const format = requirements.formats.includes(ImageFormat.WEBP) 
    ? ImageFormat.WEBP 
    : requirements.formats[0];

  // 转换图片
  const config: ImageQualityConfig = {
    format,
    quality: 0.9, // 高质量
    maxWidth: targetWidth,
    maxHeight: targetHeight,
    maintainAspectRatio: true
  };

  return await convertImageFormat(dataUrl, config);
}

/**
 * 验证图片是否符合亚马逊要求
 */
export async function validateAmazonImage(
  dataUrl: string,
  spec: keyof typeof AMAZON_IMAGE_REQUIREMENTS
): Promise<{
  isValid: boolean;
  issues: string[];
  suggestions: string[];
}> {
  const requirements = AMAZON_IMAGE_REQUIREMENTS[spec];
  const img = await dataUrlToImage(dataUrl);
  const issues: string[] = [];
  const suggestions: string[] = [];

  // 检查尺寸
  const minDimension = Math.min(img.width, img.height);
  const maxDimension = Math.max(img.width, img.height);

  if (minDimension < requirements.minSize) {
    issues.push(`图片尺寸过小，最小边长应为${requirements.minSize}像素`);
    suggestions.push('请上传更高分辨率的图片');
  }

  if (maxDimension > requirements.maxSize) {
    issues.push(`图片尺寸过大，最大边长不应超过${requirements.maxSize}像素`);
    suggestions.push('建议压缩图片尺寸');
  }

  // 检查宽高比
  if (requirements.aspectRatio) {
    const currentRatio = img.width / img.height;
    const tolerance = 0.1; // 10%的容差
    
    if (Math.abs(currentRatio - requirements.aspectRatio) > tolerance) {
      issues.push(`图片宽高比不符合要求，应为${requirements.aspectRatio.toFixed(2)}`);
      suggestions.push('请调整图片的宽高比');
    }
  }

  // 检查背景色（仅对主图）
  if (spec === 'main' && requirements.backgroundColor) {
    // 这里可以添加背景色检测逻辑
    // 由于复杂性，暂时跳过实际检测
    suggestions.push('确保主图使用纯白背景');
  }

  return {
    isValid: issues.length === 0,
    issues,
    suggestions
  };
}

/**
 * 批量处理图片
 */
export async function batchProcessImages(
  dataUrls: string[],
  config: ImageQualityConfig
): Promise<ImageProcessingResult[]> {
  const results: ImageProcessingResult[] = [];
  
  for (const dataUrl of dataUrls) {
    try {
      const result = await convertImageFormat(dataUrl, config);
      results.push(result);
    } catch (error) {
      console.error('批量处理图片失败:', error);
      // 继续处理其他图片
    }
  }
  
  return results;
}

/**
 * 估算图片文件大小
 */
export function estimateFileSize(dataUrl: string): number {
  const base64Data = dataUrl.split(',')[1];
  return Math.round((base64Data.length * 3) / 4);
}

/**
 * 检查图片格式是否受支持
 */
export function isSupportedFormat(file: File): boolean {
  const supportedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
  return supportedTypes.includes(file.type);
}

/**
 * 获取图片信息
 */
export async function getImageInfo(dataUrl: string): Promise<{
  width: number;
  height: number;
  aspectRatio: number;
  format: string;
  estimatedSize: number;
}> {
  const img = await dataUrlToImage(dataUrl);
  const format = dataUrl.split(';')[0].split(':')[1];
  
  return {
    width: img.width,
    height: img.height,
    aspectRatio: img.width / img.height,
    format,
    estimatedSize: estimateFileSize(dataUrl)
  };
}
