// 文本对比和违规项可视化工具
import { Violation } from './complianceCheck';

export interface TextDiff {
  type: 'added' | 'removed' | 'unchanged' | 'modified';
  text: string;
  originalIndex?: number;
  newIndex?: number;
}

export interface ViolationHighlight {
  start: number;
  end: number;
  type: 'special_chars' | 'repeated_words' | 'length';
  severity: 'error' | 'warning';
  message: string;
  violationData?: any;
}

/**
 * 生成文本差异对比
 */
export const generateTextDiff = (originalText: string, fixedText: string): TextDiff[] => {
  const originalWords = originalText.split(/(\s+)/);
  const fixedWords = fixedText.split(/(\s+)/);
  
  const diffs: TextDiff[] = [];
  const originalSet = new Set(originalWords);
  const fixedSet = new Set(fixedWords);
  
  let originalIndex = 0;
  let fixedIndex = 0;
  
  // 简化的差异算法
  while (originalIndex < originalWords.length || fixedIndex < fixedWords.length) {
    const originalWord = originalWords[originalIndex];
    const fixedWord = fixedWords[fixedIndex];
    
    if (originalIndex >= originalWords.length) {
      // 只剩下新增的词
      diffs.push({
        type: 'added',
        text: fixedWord,
        newIndex: fixedIndex
      });
      fixedIndex++;
    } else if (fixedIndex >= fixedWords.length) {
      // 只剩下删除的词
      diffs.push({
        type: 'removed',
        text: originalWord,
        originalIndex: originalIndex
      });
      originalIndex++;
    } else if (originalWord === fixedWord) {
      // 相同的词
      diffs.push({
        type: 'unchanged',
        text: originalWord,
        originalIndex: originalIndex,
        newIndex: fixedIndex
      });
      originalIndex++;
      fixedIndex++;
    } else {
      // 检查是否是修改
      if (fixedSet.has(originalWord)) {
        // 原词在修复后的文本中存在，可能是位置变化
        diffs.push({
          type: 'removed',
          text: originalWord,
          originalIndex: originalIndex
        });
        originalIndex++;
      } else if (originalSet.has(fixedWord)) {
        // 新词在原文本中存在，可能是位置变化
        diffs.push({
          type: 'added',
          text: fixedWord,
          newIndex: fixedIndex
        });
        fixedIndex++;
      } else {
        // 真正的修改
        diffs.push({
          type: 'modified',
          text: `${originalWord} → ${fixedWord}`,
          originalIndex: originalIndex,
          newIndex: fixedIndex
        });
        originalIndex++;
        fixedIndex++;
      }
    }
  }
  
  return diffs;
};

/**
 * 生成违规项高亮信息
 */
export const generateViolationHighlights = (text: string, violations: Violation[]): ViolationHighlight[] => {
  const highlights: ViolationHighlight[] = [];
  
  violations.forEach(violation => {
    switch (violation.type) {
      case 'special_chars':
        if (violation.position && violation.details?.foundChars) {
          violation.details.foundChars.forEach((charInfo: any) => {
            charInfo.positions.forEach((pos: number) => {
              highlights.push({
                start: pos,
                end: pos + 1,
                type: 'special_chars',
                severity: violation.severity,
                message: `特殊字符 "${charInfo.char}" 违规`,
                violationData: charInfo
              });
            });
          });
        }
        break;
        
      case 'repeated_words':
        if (violation.details?.repeatedWords) {
          violation.details.repeatedWords.forEach((wordInfo: any) => {
            // 查找重复词汇在文本中的位置
            const regex = new RegExp(`\\b${escapeRegExp(wordInfo.word)}\\b`, 'gi');
            let match;
            while ((match = regex.exec(text)) !== null) {
              highlights.push({
                start: match.index,
                end: match.index + match[0].length,
                type: 'repeated_words',
                severity: violation.severity,
                message: `重复词汇 "${wordInfo.word}" (${wordInfo.count}次)`,
                violationData: wordInfo
              });
            }
          });
        }
        break;
        
      case 'length':
        if (violation.severity === 'error') {
          // 对于长度违规，高亮超出部分
          const maxLength = violation.details?.maxLength || 200;
          if (text.length > maxLength) {
            highlights.push({
              start: maxLength,
              end: text.length,
              type: 'length',
              severity: 'error',
              message: `超出长度限制 (${text.length}/${maxLength})`,
              violationData: violation.details
            });
          }
        }
        break;
    }
  });
  
  // 按位置排序
  return highlights.sort((a, b) => a.start - b.start);
};

/**
 * 转义正则表达式特殊字符
 */
const escapeRegExp = (string: string): string => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
};

/**
 * 合并重叠的高亮区域
 */
export const mergeOverlappingHighlights = (highlights: ViolationHighlight[]): ViolationHighlight[] => {
  if (highlights.length <= 1) return highlights;
  
  const sorted = [...highlights].sort((a, b) => a.start - b.start);
  const merged: ViolationHighlight[] = [sorted[0]];
  
  for (let i = 1; i < sorted.length; i++) {
    const current = sorted[i];
    const last = merged[merged.length - 1];
    
    if (current.start <= last.end) {
      // 重叠，合并
      last.end = Math.max(last.end, current.end);
      last.message += ` | ${current.message}`;
      // 保持最高严重级别
      if (current.severity === 'error' && last.severity === 'warning') {
        last.severity = 'error';
      }
    } else {
      merged.push(current);
    }
  }
  
  return merged;
};

/**
 * 将文本分割为带高亮的片段
 */
export const splitTextWithHighlights = (text: string, highlights: ViolationHighlight[]) => {
  const mergedHighlights = mergeOverlappingHighlights(highlights);
  const segments: Array<{
    text: string;
    isHighlighted: boolean;
    highlight?: ViolationHighlight;
  }> = [];
  
  let lastIndex = 0;
  
  mergedHighlights.forEach(highlight => {
    // 添加高亮前的普通文本
    if (highlight.start > lastIndex) {
      segments.push({
        text: text.slice(lastIndex, highlight.start),
        isHighlighted: false
      });
    }
    
    // 添加高亮文本
    segments.push({
      text: text.slice(highlight.start, highlight.end),
      isHighlighted: true,
      highlight
    });
    
    lastIndex = highlight.end;
  });
  
  // 添加最后的普通文本
  if (lastIndex < text.length) {
    segments.push({
      text: text.slice(lastIndex),
      isHighlighted: false
    });
  }
  
  return segments;
};

/**
 * 生成修改统计信息
 */
export const generateChangeStats = (diffs: TextDiff[]) => {
  const stats = {
    added: 0,
    removed: 0,
    modified: 0,
    unchanged: 0,
    totalChanges: 0
  };
  
  diffs.forEach(diff => {
    switch (diff.type) {
      case 'added':
        stats.added++;
        stats.totalChanges++;
        break;
      case 'removed':
        stats.removed++;
        stats.totalChanges++;
        break;
      case 'modified':
        stats.modified++;
        stats.totalChanges++;
        break;
      case 'unchanged':
        stats.unchanged++;
        break;
    }
  });
  
  return stats;
};
