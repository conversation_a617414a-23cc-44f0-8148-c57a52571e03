// 标题修复智能缓存机制
export interface CachedTitleFix {
  key: string;
  originalTitle: string;
  marketplace: string;
  category: string;
  violations: any[];
  result: {
    fixedTitles: string[];
    changes?: string[]; // 可选字段，向后兼容
    explanation?: string; // 可选字段，向后兼容
    complianceScore: number;
  };
  timestamp: number;
  hitCount: number;
}

const CACHE_KEY = 'titleFixCache';
const MAX_CACHE_SIZE = 100; // 最多缓存100个结果
const CACHE_EXPIRY_MS = 7 * 24 * 60 * 60 * 1000; // 7天过期

/**
 * 生成缓存键
 */
const generateCacheKey = (
  originalTitle: string,
  marketplace: string,
  category: string,
  violations: any[]
): string => {
  const violationTypes = violations.map(v => v.type).sort().join(',');
  const content = `${originalTitle}|${marketplace}|${category}|${violationTypes}`;
  
  // 简单哈希函数
  let hash = 0;
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  
  return Math.abs(hash).toString(36);
};

/**
 * 获取缓存
 */
const getCache = (): CachedTitleFix[] => {
  try {
    const stored = localStorage.getItem(CACHE_KEY);
    if (stored) {
      const cache = JSON.parse(stored);
      return Array.isArray(cache) ? cache : [];
    }
  } catch (error) {
    console.warn('Failed to load title fix cache:', error);
  }
  return [];
};

/**
 * 保存缓存
 */
const saveCache = (cache: CachedTitleFix[]): void => {
  try {
    localStorage.setItem(CACHE_KEY, JSON.stringify(cache));
  } catch (error) {
    console.warn('Failed to save title fix cache:', error);
  }
};

/**
 * 清理过期缓存
 */
const cleanExpiredCache = (cache: CachedTitleFix[]): CachedTitleFix[] => {
  const now = Date.now();
  return cache.filter(item => (now - item.timestamp) < CACHE_EXPIRY_MS);
};

/**
 * 检查是否有缓存的修复结果
 */
export const getCachedTitleFix = (
  originalTitle: string,
  marketplace: string,
  category: string,
  violations: any[]
): CachedTitleFix | null => {
  const cache = getCache();
  const cleanCache = cleanExpiredCache(cache);
  
  // 如果缓存被清理了，保存清理后的版本
  if (cleanCache.length !== cache.length) {
    saveCache(cleanCache);
  }
  
  const key = generateCacheKey(originalTitle, marketplace, category, violations);
  const cached = cleanCache.find(item => item.key === key);
  
  if (cached) {
    // 增加命中次数
    cached.hitCount++;
    saveCache(cleanCache);
    
    console.log('Cache hit for title fix:', originalTitle);
    return cached;
  }
  
  return null;
};

/**
 * 缓存修复结果
 */
export const cacheTitleFix = (
  originalTitle: string,
  marketplace: string,
  category: string,
  violations: any[],
  result: {
    fixedTitles: string[];
    changes?: string[]; // 可选字段，向后兼容
    explanation?: string; // 可选字段，向后兼容
    complianceScore: number;
  }
): void => {
  try {
    const cache = getCache();
    const cleanCache = cleanExpiredCache(cache);
    
    const key = generateCacheKey(originalTitle, marketplace, category, violations);
    
    // 检查是否已存在
    const existingIndex = cleanCache.findIndex(item => item.key === key);
    
    const cachedItem: CachedTitleFix = {
      key,
      originalTitle,
      marketplace,
      category,
      violations,
      result,
      timestamp: Date.now(),
      hitCount: existingIndex >= 0 ? cleanCache[existingIndex].hitCount : 0
    };
    
    if (existingIndex >= 0) {
      // 更新现有缓存
      cleanCache[existingIndex] = cachedItem;
    } else {
      // 添加新缓存
      cleanCache.unshift(cachedItem);
    }
    
    // 限制缓存大小，保留最常用的
    if (cleanCache.length > MAX_CACHE_SIZE) {
      cleanCache.sort((a, b) => {
        // 按命中次数和时间排序
        const scoreA = a.hitCount * 0.7 + (Date.now() - a.timestamp) * 0.3;
        const scoreB = b.hitCount * 0.7 + (Date.now() - b.timestamp) * 0.3;
        return scoreB - scoreA;
      });
      
      cleanCache.splice(MAX_CACHE_SIZE);
    }
    
    saveCache(cleanCache);
    console.log('Cached title fix result:', originalTitle);
  } catch (error) {
    console.warn('Failed to cache title fix:', error);
  }
};

/**
 * 获取缓存统计信息
 */
export const getCacheStats = () => {
  const cache = getCache();
  const cleanCache = cleanExpiredCache(cache);
  
  const stats = {
    totalItems: cleanCache.length,
    totalHits: cleanCache.reduce((sum, item) => sum + item.hitCount, 0),
    averageHits: 0,
    oldestItem: null as Date | null,
    newestItem: null as Date | null,
    marketplaceDistribution: {} as Record<string, number>,
    categoryDistribution: {} as Record<string, number>
  };
  
  if (cleanCache.length > 0) {
    stats.averageHits = Math.round(stats.totalHits / cleanCache.length);
    
    const timestamps = cleanCache.map(item => item.timestamp);
    stats.oldestItem = new Date(Math.min(...timestamps));
    stats.newestItem = new Date(Math.max(...timestamps));
    
    // 统计分布
    cleanCache.forEach(item => {
      stats.marketplaceDistribution[item.marketplace] = 
        (stats.marketplaceDistribution[item.marketplace] || 0) + 1;
      stats.categoryDistribution[item.category] = 
        (stats.categoryDistribution[item.category] || 0) + 1;
    });
  }
  
  return stats;
};

/**
 * 清空缓存
 */
export const clearTitleFixCache = (): void => {
  try {
    localStorage.removeItem(CACHE_KEY);
    console.log('Title fix cache cleared');
  } catch (error) {
    console.warn('Failed to clear title fix cache:', error);
  }
};

/**
 * 预热缓存 - 基于历史记录
 */
export const preloadCacheFromHistory = (historyItems: any[]): void => {
  try {
    historyItems.forEach(item => {
      if (item.fixedTitles && item.originalTitle) {
        cacheTitleFix(
          item.originalTitle,
          item.marketplace,
          item.category,
          item.violations || [],
          {
            fixedTitles: item.fixedTitles,
            changes: item.changes || [],
            explanation: item.explanation || '',
            complianceScore: item.complianceScore || 90
          }
        );
      }
    });
    
    console.log('Cache preloaded from history');
  } catch (error) {
    console.warn('Failed to preload cache from history:', error);
  }
};

/**
 * 智能缓存建议
 */
export const getCacheSuggestions = (originalTitle: string): string[] => {
  const cache = getCache();
  const suggestions: string[] = [];
  
  // 查找相似的标题
  const words = originalTitle.toLowerCase().split(/\s+/);
  
  cache.forEach(item => {
    const itemWords = item.originalTitle.toLowerCase().split(/\s+/);
    const commonWords = words.filter(word => itemWords.includes(word));
    const similarity = commonWords.length / Math.max(words.length, itemWords.length);
    
    if (similarity > 0.5 && item.result.fixedTitles.length > 0) {
      suggestions.push(item.result.fixedTitles[0]);
    }
  });
  
  return suggestions.slice(0, 3); // 返回最多3个建议
};

/**
 * 导出缓存数据
 */
export const exportTitleFixCache = (): string => {
  const cache = getCache();
  return JSON.stringify(cache, null, 2);
};

/**
 * 导入缓存数据
 */
export const importTitleFixCache = (jsonData: string): boolean => {
  try {
    const imported = JSON.parse(jsonData);
    if (Array.isArray(imported)) {
      const validItems = imported.filter(item => 
        item.key && 
        item.originalTitle && 
        item.result && 
        Array.isArray(item.result.fixedTitles)
      );
      
      if (validItems.length > 0) {
        saveCache(validItems.slice(0, MAX_CACHE_SIZE));
        return true;
      }
    }
  } catch (error) {
    console.warn('Failed to import title fix cache:', error);
  }
  return false;
};
