// 标题修复历史记录管理
export interface TitleFixHistoryItem {
  id: string;
  timestamp: number;
  originalTitle: string;
  fixedTitles: string[];
  selectedVersion: number;
  marketplace: string;
  category: string;
  complianceScore: number;
  violations: any[];
  changes?: string[]; // 可选字段，向后兼容
  explanation?: string; // 可选字段，向后兼容
}

const STORAGE_KEY = 'titleFixHistory';
const MAX_HISTORY_ITEMS = 50; // 最多保存50条记录

/**
 * 获取修复历史记录
 */
export const getTitleFixHistory = (): TitleFixHistoryItem[] => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const history = JSON.parse(stored);
      return Array.isArray(history) ? history : [];
    }
  } catch (error) {
    console.warn('Failed to load title fix history:', error);
  }
  return [];
};

/**
 * 保存修复记录
 */
export const saveTitleFixHistory = (item: Omit<TitleFixHistoryItem, 'id' | 'timestamp'>): void => {
  try {
    const history = getTitleFixHistory();
    
    const newItem: TitleFixHistoryItem = {
      ...item,
      id: generateId(),
      timestamp: Date.now()
    };

    // 添加到历史记录开头
    history.unshift(newItem);

    // 限制历史记录数量
    if (history.length > MAX_HISTORY_ITEMS) {
      history.splice(MAX_HISTORY_ITEMS);
    }

    localStorage.setItem(STORAGE_KEY, JSON.stringify(history));
  } catch (error) {
    console.warn('Failed to save title fix history:', error);
  }
};

/**
 * 删除历史记录项
 */
export const deleteTitleFixHistoryItem = (id: string): void => {
  try {
    const history = getTitleFixHistory();
    const filtered = history.filter(item => item.id !== id);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(filtered));
  } catch (error) {
    console.warn('Failed to delete title fix history item:', error);
  }
};

/**
 * 清空所有历史记录
 */
export const clearTitleFixHistory = (): void => {
  try {
    localStorage.removeItem(STORAGE_KEY);
  } catch (error) {
    console.warn('Failed to clear title fix history:', error);
  }
};

/**
 * 搜索历史记录
 */
export const searchTitleFixHistory = (query: string): TitleFixHistoryItem[] => {
  const history = getTitleFixHistory();
  const lowerQuery = query.toLowerCase();
  
  return history.filter(item => 
    item.originalTitle.toLowerCase().includes(lowerQuery) ||
    item.fixedTitles.some(title => title.toLowerCase().includes(lowerQuery)) ||
    item.marketplace.toLowerCase().includes(lowerQuery) ||
    item.category.toLowerCase().includes(lowerQuery)
  );
};

/**
 * 获取统计信息
 */
export const getTitleFixStats = () => {
  const history = getTitleFixHistory();
  
  const stats = {
    totalFixes: history.length,
    averageScore: 0,
    marketplaceStats: {} as Record<string, number>,
    categoryStats: {} as Record<string, number>,
    recentActivity: history.slice(0, 10)
  };

  if (history.length > 0) {
    // 计算平均分
    stats.averageScore = Math.round(
      history.reduce((sum, item) => sum + item.complianceScore, 0) / history.length
    );

    // 统计市场分布
    history.forEach(item => {
      stats.marketplaceStats[item.marketplace] = (stats.marketplaceStats[item.marketplace] || 0) + 1;
      stats.categoryStats[item.category] = (stats.categoryStats[item.category] || 0) + 1;
    });
  }

  return stats;
};

/**
 * 导出历史记录为JSON
 */
export const exportTitleFixHistory = (): string => {
  const history = getTitleFixHistory();
  return JSON.stringify(history, null, 2);
};

/**
 * 从JSON导入历史记录
 */
export const importTitleFixHistory = (jsonData: string): boolean => {
  try {
    const imported = JSON.parse(jsonData);
    if (Array.isArray(imported)) {
      // 验证数据格式
      const validItems = imported.filter(item => 
        item.originalTitle && 
        Array.isArray(item.fixedTitles) && 
        item.marketplace && 
        item.category
      );

      if (validItems.length > 0) {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(validItems.slice(0, MAX_HISTORY_ITEMS)));
        return true;
      }
    }
  } catch (error) {
    console.warn('Failed to import title fix history:', error);
  }
  return false;
};

/**
 * 生成唯一ID
 */
const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

/**
 * 格式化时间戳
 */
export const formatTimestamp = (timestamp: number): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffMins < 1) return '刚刚';
  if (diffMins < 60) return `${diffMins}分钟前`;
  if (diffHours < 24) return `${diffHours}小时前`;
  if (diffDays < 7) return `${diffDays}天前`;
  
  return date.toLocaleDateString();
};

/**
 * 检查是否有重复的修复记录
 */
export const isDuplicateTitle = (originalTitle: string): boolean => {
  const history = getTitleFixHistory();
  return history.some(item => item.originalTitle === originalTitle);
};

/**
 * 获取相似的历史记录
 */
export const getSimilarTitleFixes = (originalTitle: string, limit: number = 5): TitleFixHistoryItem[] => {
  const history = getTitleFixHistory();
  const words = originalTitle.toLowerCase().split(/\s+/);
  
  const scored = history.map(item => {
    const itemWords = item.originalTitle.toLowerCase().split(/\s+/);
    const commonWords = words.filter(word => itemWords.includes(word));
    const similarity = commonWords.length / Math.max(words.length, itemWords.length);
    
    return { item, similarity };
  });

  return scored
    .filter(({ similarity }) => similarity > 0.3) // 至少30%相似度
    .sort((a, b) => b.similarity - a.similarity)
    .slice(0, limit)
    .map(({ item }) => item);
};
