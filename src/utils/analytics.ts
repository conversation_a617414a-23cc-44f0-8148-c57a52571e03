// 工具点击量统计和分析
// 用于热门工具排序和数据分析
import { analyticsService } from '../services/analyticsService';

export interface ToolClickData {
  toolId: string;
  clickCount: number;
  lastClicked: Date;
  weeklyClicks: number;
  monthlyClicks: number;
}

export interface WeeklyStats {
  week: string;
  toolStats: { [toolId: string]: number };
}

// 本地存储键名
const CLICK_DATA_KEY = 'amztools_click_data';
const WEEKLY_STATS_KEY = 'amztools_weekly_stats';

// 工具名称映射
const TOOL_NAMES: { [key: string]: string } = {
  'title-fixer': '标题修改器',
  'currency-converter': '货币换算器',
  'unit-converter': '单位换算器',
  'fba-calculator': 'FBA计算器',
  'title-analyzer': '标题分析器',
  'profit-calculator': '利润计算器',
  'keyword-density': '关键词密度检查器',
  'listing-optimizer': 'Listing优化器',
  'keyword-researcher': '关键词研究器',
  'competitor-analyzer': '竞品分析器',
  'review-analyzer': '评论分析器',
  'packing-calculator': '装箱计算器',
  'world-clock': '世界时钟',
  'reference-table': '常用信息对照表',
  'case-converter': '大小写转换器',
  'word-frequency-analyzer': '词频统计器',
  'qr-code-generator': '二维码生成器',
  'product-image-generator': 'AI产品图片生成器'
};

/**
 * 记录工具点击
 */
export function recordToolClick(toolId: string, toolName?: string): void {
  const finalToolName = toolName || TOOL_NAMES[toolId] || toolId;
  try {
    const clickData = getClickData();
    const now = new Date();

    if (!clickData[toolId]) {
      clickData[toolId] = {
        toolId,
        clickCount: 0,
        lastClicked: now,
        weeklyClicks: 0,
        monthlyClicks: 0
      };
    }

    // 更新点击数据
    clickData[toolId].clickCount += 1;
    clickData[toolId].lastClicked = now;
    clickData[toolId].weeklyClicks += 1;
    clickData[toolId].monthlyClicks += 1;

    // 保存到本地存储
    localStorage.setItem(CLICK_DATA_KEY, JSON.stringify(clickData));

    // 更新周统计
    updateWeeklyStats(toolId);

    // 同时记录到数据库
    analyticsService.trackToolUsage({
      tool_id: toolId,
      tool_name: finalToolName,
      usage_type: 'view'
    });

    console.log(`工具点击记录: ${toolId}, 总点击: ${clickData[toolId].clickCount}`);
  } catch (error) {
    console.error('记录工具点击失败:', error);
  }
}

/**
 * 记录工具使用（实际使用，如计算、转换等）
 */
export function recordToolUsage(toolId: string, usageData?: any): void {
  const toolName = TOOL_NAMES[toolId] || toolId;

  analyticsService.trackToolUsage({
    tool_id: toolId,
    tool_name: toolName,
    usage_type: 'use',
    usage_data: usageData
  });

  console.log(`工具使用记录: ${toolId}`, usageData);
}

/**
 * 记录工具计算（特定的计算操作）
 */
export function recordToolCalculation(toolId: string, calculationData?: any): void {
  const toolName = TOOL_NAMES[toolId] || toolId;

  analyticsService.trackToolUsage({
    tool_id: toolId,
    tool_name: toolName,
    usage_type: 'calculate',
    usage_data: calculationData
  });

  console.log(`工具计算记录: ${toolId}`, calculationData);
}

/**
 * 获取点击数据
 */
export function getClickData(): { [toolId: string]: ToolClickData } {
  try {
    const stored = localStorage.getItem(CLICK_DATA_KEY);
    if (!stored) return {};
    
    const data = JSON.parse(stored);
    
    // 转换日期字符串为Date对象
    Object.keys(data).forEach(toolId => {
      data[toolId].lastClicked = new Date(data[toolId].lastClicked);
    });
    
    return data;
  } catch (error) {
    console.error('获取点击数据失败:', error);
    return {};
  }
}

/**
 * 获取热门工具（基于周点击量）
 */
export function getHotToolsByWeeklyClicks(limit: number = 4): string[] {
  try {
    const clickData = getClickData();
    
    // 清理过期的周数据
    cleanupWeeklyData(clickData);
    
    // 按周点击量排序
    const sortedTools = Object.values(clickData)
      .filter(tool => tool.weeklyClicks > 0)
      .sort((a, b) => b.weeklyClicks - a.weeklyClicks)
      .slice(0, limit)
      .map(tool => tool.toolId);
    
    return sortedTools;
  } catch (error) {
    console.error('获取热门工具失败:', error);
    return [];
  }
}

/**
 * 更新周统计
 */
function updateWeeklyStats(toolId: string): void {
  try {
    const weekKey = getWeekKey(new Date());
    const weeklyStats = getWeeklyStats();
    
    if (!weeklyStats[weekKey]) {
      weeklyStats[weekKey] = {
        week: weekKey,
        toolStats: {}
      };
    }
    
    if (!weeklyStats[weekKey].toolStats[toolId]) {
      weeklyStats[weekKey].toolStats[toolId] = 0;
    }
    
    weeklyStats[weekKey].toolStats[toolId] += 1;
    
    // 保存周统计
    localStorage.setItem(WEEKLY_STATS_KEY, JSON.stringify(weeklyStats));
    
    // 清理旧的周数据（保留最近8周）
    cleanupOldWeeklyStats(weeklyStats);
  } catch (error) {
    console.error('更新周统计失败:', error);
  }
}

/**
 * 获取周统计数据
 */
function getWeeklyStats(): { [weekKey: string]: WeeklyStats } {
  try {
    const stored = localStorage.getItem(WEEKLY_STATS_KEY);
    return stored ? JSON.parse(stored) : {};
  } catch (error) {
    console.error('获取周统计失败:', error);
    return {};
  }
}

/**
 * 生成周键名 (YYYY-WW格式)
 */
function getWeekKey(date: Date): string {
  const year = date.getFullYear();
  const startOfYear = new Date(year, 0, 1);
  const days = Math.floor((date.getTime() - startOfYear.getTime()) / (24 * 60 * 60 * 1000));
  const weekNumber = Math.ceil((days + startOfYear.getDay() + 1) / 7);
  return `${year}-${weekNumber.toString().padStart(2, '0')}`;
}

/**
 * 清理过期的周数据
 */
function cleanupWeeklyData(clickData: { [toolId: string]: ToolClickData }): void {
  const currentWeek = getWeekKey(new Date());
  const lastWeek = getWeekKey(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000));
  
  Object.keys(clickData).forEach(toolId => {
    const tool = clickData[toolId];
    const lastClickedWeek = getWeekKey(tool.lastClicked);
    
    // 如果上次点击不是本周或上周，重置周点击量
    if (lastClickedWeek !== currentWeek && lastClickedWeek !== lastWeek) {
      tool.weeklyClicks = 0;
    }
  });
  
  // 保存更新后的数据
  localStorage.setItem(CLICK_DATA_KEY, JSON.stringify(clickData));
}

/**
 * 清理旧的周统计数据
 */
function cleanupOldWeeklyStats(weeklyStats: { [weekKey: string]: WeeklyStats }): void {
  const currentDate = new Date();
  const eightWeeksAgo = new Date(currentDate.getTime() - 8 * 7 * 24 * 60 * 60 * 1000);
  const cutoffWeek = getWeekKey(eightWeeksAgo);
  
  Object.keys(weeklyStats).forEach(weekKey => {
    if (weekKey < cutoffWeek) {
      delete weeklyStats[weekKey];
    }
  });
  
  localStorage.setItem(WEEKLY_STATS_KEY, JSON.stringify(weeklyStats));
}

/**
 * 获取工具使用统计报告
 */
export function getToolUsageReport(): {
  totalClicks: number;
  topTools: Array<{ toolId: string; clicks: number; weeklyClicks: number }>;
  weeklyTrend: Array<{ week: string; totalClicks: number }>;
} {
  try {
    const clickData = getClickData();
    const weeklyStats = getWeeklyStats();
    
    // 计算总点击量
    const totalClicks = Object.values(clickData).reduce((sum, tool) => sum + tool.clickCount, 0);
    
    // 获取热门工具
    const topTools = Object.values(clickData)
      .sort((a, b) => b.clickCount - a.clickCount)
      .slice(0, 10)
      .map(tool => ({
        toolId: tool.toolId,
        clicks: tool.clickCount,
        weeklyClicks: tool.weeklyClicks
      }));
    
    // 计算周趋势
    const weeklyTrend = Object.values(weeklyStats)
      .sort((a, b) => a.week.localeCompare(b.week))
      .map(week => ({
        week: week.week,
        totalClicks: Object.values(week.toolStats).reduce((sum, clicks) => sum + clicks, 0)
      }));
    
    return {
      totalClicks,
      topTools,
      weeklyTrend
    };
  } catch (error) {
    console.error('获取使用统计报告失败:', error);
    return {
      totalClicks: 0,
      topTools: [],
      weeklyTrend: []
    };
  }
}

/**
 * 重置所有统计数据（仅用于开发/测试）
 */
export function resetAllStats(): void {
  try {
    localStorage.removeItem(CLICK_DATA_KEY);
    localStorage.removeItem(WEEKLY_STATS_KEY);
    console.log('所有统计数据已重置');
  } catch (error) {
    console.error('重置统计数据失败:', error);
  }
}
