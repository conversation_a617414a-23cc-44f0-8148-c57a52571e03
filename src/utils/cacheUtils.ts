// 缓存清除工具函数

/**
 * 清除公告相关的缓存
 */
export const clearAnnouncementCache = async (): Promise<void> => {
  try {
    // 清除Service Worker缓存
    if ('caches' in window) {
      const cacheNames = await caches.keys();
      const announcementCachePromises = cacheNames.map(async (cacheName) => {
        const cache = await caches.open(cacheName);
        const requests = await cache.keys();
        
        // 删除公告相关的缓存条目
        const deletePromises = requests
          .filter(request => 
            request.url.includes('/api/v1/announcements') ||
            request.url.includes('/announcements')
          )
          .map(request => cache.delete(request));
        
        return Promise.all(deletePromises);
      });
      
      await Promise.all(announcementCachePromises);
      console.log('公告缓存已清除');
    }
    
    // 清除浏览器缓存（通过添加时间戳强制刷新）
    const timestamp = Date.now();
    localStorage.setItem('announcement_cache_bust', timestamp.toString());
    
  } catch (error) {
    console.error('清除公告缓存失败:', error);
  }
};

/**
 * 清除所有应用缓存
 */
export const clearAllCache = async (): Promise<void> => {
  try {
    if ('caches' in window) {
      const cacheNames = await caches.keys();
      await Promise.all(cacheNames.map(cacheName => caches.delete(cacheName)));
      console.log('所有缓存已清除');
    }
    
    // 清除localStorage中的缓存标记
    localStorage.removeItem('announcement_cache_bust');
    
  } catch (error) {
    console.error('清除缓存失败:', error);
  }
};

/**
 * 获取缓存破坏参数
 */
export const getCacheBustParam = (): string => {
  const timestamp = localStorage.getItem('announcement_cache_bust') || Date.now().toString();
  return `t=${timestamp}`;
};

/**
 * 通知Service Worker更新缓存
 */
export const notifyServiceWorkerUpdate = (): void => {
  if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
    navigator.serviceWorker.controller.postMessage({
      type: 'CLEAR_ANNOUNCEMENT_CACHE'
    });
  }
};

/**
 * 强制刷新公告数据
 */
export const forceRefreshAnnouncements = async (): Promise<void> => {
  try {
    // 清除公告缓存
    await clearAnnouncementCache();
    
    // 通知Service Worker
    notifyServiceWorkerUpdate();
    
    // 触发自定义事件，通知组件刷新
    window.dispatchEvent(new CustomEvent('announcementCacheCleared'));
    
  } catch (error) {
    console.error('强制刷新公告失败:', error);
  }
};
