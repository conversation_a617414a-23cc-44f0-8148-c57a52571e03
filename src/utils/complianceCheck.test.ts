// 合规性检查算法测试用例
import { checkTitleCompliance, checkTitleLength, checkSpecialCharacters, checkRepeatedWords } from './complianceCheck';

// 测试用例
const testCases = [
  {
    title: "Women's Cotton T-Shirt Casual Short Sleeve Top",
    category: 'apparel',
    expected: {
      shouldPass: true,
      description: '正常的服装标题，应该通过所有检查'
    }
  },
  {
    title: "Amazing! Best Quality Women's Cotton T-Shirt with Special Design! Perfect for Summer! Buy Now!",
    category: 'apparel',
    expected: {
      shouldPass: false,
      description: '包含特殊字符和超长标题，应该失败'
    }
  },
  {
    title: "Women Women Women Cotton Cotton Cotton T-Shirt T-Shirt T-Shirt",
    category: 'apparel',
    expected: {
      shouldPass: false,
      description: '重复词汇过多，应该失败'
    }
  },
  {
    title: "Wireless Bluetooth Headphones with Noise Cancelling Technology",
    category: 'electronics',
    expected: {
      shouldPass: true,
      description: '正常的电子产品标题，应该通过'
    }
  }
];

// 运行测试
console.log('🧪 开始合规性检查算法测试...\n');

testCases.forEach((testCase, index) => {
  console.log(`测试 ${index + 1}: ${testCase.expected.description}`);
  console.log(`标题: "${testCase.title}"`);
  console.log(`类别: ${testCase.category}`);
  
  const result = checkTitleCompliance(testCase.title, testCase.category);
  
  console.log(`结果: ${result.isCompliant ? '✅ 通过' : '❌ 失败'}`);
  console.log(`评分: ${result.score}/100`);
  
  if (result.violations.length > 0) {
    console.log('违规项:');
    result.violations.forEach(violation => {
      console.log(`  - ${violation.type}: ${violation.message}`);
    });
  }
  
  if (result.suggestions.length > 0) {
    console.log('建议:');
    result.suggestions.slice(0, 3).forEach(suggestion => {
      console.log(`  - ${suggestion}`);
    });
  }
  
  console.log('---\n');
});

console.log('✅ 测试完成！');

// 导出测试函数供其他地方使用
export const runComplianceTests = () => {
  return testCases.map(testCase => {
    const result = checkTitleCompliance(testCase.title, testCase.category);
    return {
      ...testCase,
      result,
      passed: result.isCompliant === testCase.expected.shouldPass
    };
  });
};
