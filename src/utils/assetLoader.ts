/**
 * 资源加载工具 - 支持从 R2 存储或本地加载资源
 * 
 * 使用方法:
 * import { getAssetUrl } from '../utils/assetLoader';
 * 
 * // 在组件中使用
 * <img src={getAssetUrl('logo.png')} alt="Logo" />
 */

// 环境变量中的 R2 资源基础 URL
const R2_ASSETS_URL = import.meta.env.VITE_R2_ASSETS_URL || '';

// 是否使用 R2 存储
const USE_R2_STORAGE = !!R2_ASSETS_URL;

// 资源映射表 - 将本地路径映射到 R2 路径
const ASSET_MAP: Record<string, string> = {
  // Logo 图片
  '/amzova.png': 'logos/amzova.png',
  '/amzova_cn.png': 'logos/amzova_cn.png',
  
  // 图标
  '/favicon.ico': 'icons/favicon.ico',
  '/icon-192.png': 'icons/icon-192.png',
  '/icon-512.png': 'icons/icon-512.png',
  '/apple-touch-icon.png': 'icons/apple-touch-icon.png',
  
  // 截图
  '/screenshot-wide.png': 'screenshots/screenshot-wide.png',
  '/screenshot-narrow.png': 'screenshots/screenshot-narrow.png',
};

/**
 * 获取资源 URL
 * @param path 资源路径，以 / 开头
 * @returns 完整的资源 URL
 */
export function getAssetUrl(path: string): string {
  // 如果未启用 R2 存储，直接返回本地路径
  if (!USE_R2_STORAGE) {
    return path;
  }
  
  // 如果资源在映射表中，使用 R2 路径
  if (path in ASSET_MAP) {
    return `${R2_ASSETS_URL}/${ASSET_MAP[path]}`;
  }
  
  // 默认返回本地路径
  return path;
}

/**
 * 预加载关键资源
 * 在应用启动时调用，预加载关键图片
 */
export function preloadCriticalAssets(): void {
  const criticalAssets = [
    '/amzova.png',
    '/amzova_cn.png',
    '/favicon.ico',
    '/icon-192.png',
  ];
  
  criticalAssets.forEach(path => {
    const img = new Image();
    img.src = getAssetUrl(path);
  });
}

/**
 * 获取资源加载状态
 * @returns 当前资源加载配置信息
 */
export function getAssetLoadingStatus(): { 
  useR2: boolean; 
  baseUrl: string;
  assetCount: number;
} {
  return {
    useR2: USE_R2_STORAGE,
    baseUrl: R2_ASSETS_URL,
    assetCount: Object.keys(ASSET_MAP).length,
  };
}
