/**
 * API 配置工具
 * 统一管理所有 API 请求的基础 URL
 */

// 获取 API 基础 URL
export const getApiBaseUrl = (): string => {
  // 优先使用环境变量中的 API URL
  const envApiUrl = import.meta.env.VITE_API_BASE_URL;
  
  if (envApiUrl) {
    return envApiUrl;
  }
  
  // 如果没有设置环境变量，根据当前域名判断
  const currentHost = window.location.hostname;
  
  if (currentHost === 'localhost' || currentHost === '127.0.0.1') {
    // 本地开发环境
    return 'http://localhost:8787';
  } else if (currentHost === 'amzova.com' || currentHost.endsWith('.amzova.com')) {
    // 生产环境使用 API 子域名
    return 'https://api.amzova.com';
  } else {
    // 其他环境（如 Cloudflare Pages 预览）也使用自定义域名
    return 'https://api.amzova.com';
  }
};

// 构建完整的 API URL
export const buildApiUrl = (endpoint: string): string => {
  const baseUrl = getApiBaseUrl();
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  return `${baseUrl}${cleanEndpoint}`;
};

// 获取认证头
export const getAuthHeaders = (): HeadersInit => {
  const token = localStorage.getItem('adminToken');
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
  };
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  return headers;
};

// 统一的 API 请求方法
export const apiRequest = async <T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> => {
  const url = buildApiUrl(endpoint);
  
  const config: RequestInit = {
    ...options,
    headers: {
      ...getAuthHeaders(),
      ...options.headers,
    },
  };
  
  const response = await fetch(url, config);
  
  if (!response.ok) {
    throw new Error(`API request failed: ${response.status} ${response.statusText}`);
  }
  
  return response.json();
};

// 导出配置信息（用于调试）
export const getApiConfig = () => {
  return {
    baseUrl: getApiBaseUrl(),
    envApiUrl: import.meta.env.VITE_API_BASE_URL,
    currentHost: window.location.hostname,
  };
};
