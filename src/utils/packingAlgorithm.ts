// 装箱算法核心逻辑
// 亚马逊FBA入库大纸箱装箱优化

import {
  ProductPackage,
  ShippingCarton,
  PackingResult,
  PackagePlacement,
  ShippingCost,
  PackingOptimization,
  STANDARD_SHIPPING_CARTONS,
  AMAZON_FBA_CONSTANTS
} from '../data/packingData';

// 3D空间点
interface Point3D {
  x: number;
  y: number;
  z: number;
}

// 已占用空间
interface OccupiedSpace {
  x1: number;
  y1: number;
  z1: number;
  x2: number;
  y2: number;
  z2: number;
}

/**
 * 主要装箱函数：推荐最优大纸箱
 */
export function recommendShippingCarton(packages: ProductPackage[]): PackingResult {
  // 1. 计算总体积和重量
  const totalStats = calculateTotalStats(packages);

  // 2. 筛选合适的大纸箱
  const suitableCartons = filterSuitableCartons(totalStats);

  // 3. 为每个大纸箱尝试装箱
  const packingOptions: PackingResult[] = [];

  for (const carton of suitableCartons) {
    const result = tryPackInCarton(packages, carton);
    if (result.placements.length === getTotalPackageCount(packages)) {
      packingOptions.push(result);
    }
  }

  // 4. 选择最优方案（优先考虑物流成本）
  return selectBestOption(packingOptions);
}

/**
 * 指定大纸箱的装箱优化
 */
export function optimizePackingInCarton(packages: ProductPackage[], carton: ShippingCarton): PackingResult {
  return tryPackInCarton(packages, carton);
}

/**
 * 获取包装优化建议
 */
export function getPackingOptimization(packages: ProductPackage[]): PackingOptimization {
  const totalStats = calculateTotalStats(packages);
  const recommendedCartons = filterSuitableCartons(totalStats).slice(0, 3);
  const costComparison: ShippingCost[] = [];

  for (const carton of recommendedCartons) {
    const cost = calculateShippingCost(carton, totalStats.totalWeight);
    costComparison.push(cost);
  }

  return {
    totalPackages: getTotalPackageCount(packages),
    totalWeight: totalStats.totalWeight,
    totalVolume: totalStats.totalVolume,
    recommendedCartons,
    costComparison
  };
}

/**
 * 计算商品包装盒总体积和重量
 */
function calculateTotalStats(packages: ProductPackage[]) {
  let totalVolume = 0;
  let totalWeight = 0;
  let hasFragile = false;
  let totalPackageCount = 0;

  for (const pkg of packages) {
    const packageVolume = pkg.length * pkg.width * pkg.height;
    totalVolume += packageVolume * pkg.quantity;
    totalWeight += pkg.weight * pkg.quantity;
    totalPackageCount += pkg.quantity;
    if (pkg.isFragile) hasFragile = true;
  }

  return { totalVolume, totalWeight, hasFragile, totalPackageCount };
}

/**
 * 获取总包装盒数量
 */
function getTotalPackageCount(packages: ProductPackage[]): number {
  return packages.reduce((sum, pkg) => sum + pkg.quantity, 0);
}

/**
 * 筛选合适的大纸箱
 */
function filterSuitableCartons(stats: { totalVolume: number; totalWeight: number; hasFragile: boolean }) {
  return STANDARD_SHIPPING_CARTONS.filter(carton => {
    // 重量检查
    if (stats.totalWeight > carton.maxWeight) return false;

    // 亚马逊尺寸限制检查
    if (carton.outerLength > AMAZON_FBA_CONSTANTS.CARTON_LIMITS.MAX_SIDE_LENGTH ||
        carton.outerWidth > AMAZON_FBA_CONSTANTS.CARTON_LIMITS.MAX_SIDE_LENGTH ||
        carton.outerHeight > AMAZON_FBA_CONSTANTS.CARTON_LIMITS.MAX_SIDE_LENGTH) {
      return false;
    }

    // 体积检查（考虑缓冲空间）
    const bufferSpace = stats.hasFragile ?
      AMAZON_FBA_CONSTANTS.BUFFER_SPACE.FRAGILE :
      AMAZON_FBA_CONSTANTS.BUFFER_SPACE.NORMAL;

    const availableVolume = (carton.innerLength - bufferSpace * 2) *
                           (carton.innerWidth - bufferSpace * 2) *
                           (carton.innerHeight - bufferSpace * 2);

    return stats.totalVolume <= availableVolume;
  }).sort((a, b) => {
    // 按物流成本排序
    const costA = calculateShippingCost(a, stats.totalWeight);
    const costB = calculateShippingCost(b, stats.totalWeight);
    return costA.shippingCost - costB.shippingCost;
  });
}

/**
 * 尝试在指定大纸箱中装箱
 */
function tryPackInCarton(packages: ProductPackage[], carton: ShippingCarton): PackingResult {
  // 展开所有包装盒（考虑数量）
  const allPackages: ProductPackage[] = [];
  for (const pkg of packages) {
    for (let i = 0; i < pkg.quantity; i++) {
      allPackages.push({
        ...pkg,
        id: `${pkg.id}-${i + 1}`,
        quantity: 1
      });
    }
  }

  // 按体积从大到小排序
  const sortedPackages = allPackages.sort((a, b) => {
    const volumeA = a.length * a.width * a.height;
    const volumeB = b.length * b.width * b.height;
    return volumeB - volumeA;
  });

  const placements: PackagePlacement[] = [];
  const occupiedSpaces: OccupiedSpace[] = [];
  const warnings: string[] = [];
  const suggestions: string[] = [];

  // 缓冲空间
  const hasFragile = packages.some(p => p.isFragile);
  const bufferSpace = hasFragile ?
    AMAZON_FBA_CONSTANTS.BUFFER_SPACE.FRAGILE :
    AMAZON_FBA_CONSTANTS.BUFFER_SPACE.NORMAL;

  // 可用空间
  const availableSpace = {
    length: carton.innerLength - bufferSpace * 2,
    width: carton.innerWidth - bufferSpace * 2,
    height: carton.innerHeight - bufferSpace * 2
  };

  // 逐个放置包装盒
  for (const pkg of sortedPackages) {
    const placement = findBestPlacement(pkg, availableSpace, occupiedSpaces, bufferSpace);

    if (placement) {
      placements.push(placement);
      occupiedSpaces.push({
        x1: placement.x,
        y1: placement.y,
        z1: placement.z,
        x2: placement.x + (placement.rotated ? pkg.width : pkg.length),
        y2: placement.y + (placement.rotated ? pkg.length : pkg.width),
        z2: placement.z + pkg.height
      });
    } else {
      warnings.push(`商品包装盒 "${pkg.name}" 无法放入大纸箱`);
    }
  }

  // 计算空间利用率
  const totalPackageVolume = allPackages.reduce((sum, p) =>
    sum + p.length * p.width * p.height, 0);
  const cartonInnerVolume = carton.innerLength * carton.innerWidth * carton.innerHeight;
  const utilization = totalPackageVolume / cartonInnerVolume;

  // 计算总重量
  const totalWeight = packages.reduce((sum, p) => sum + p.weight * p.quantity, 0);

  // 计算物流费用
  const shippingCost = calculateShippingCost(carton, totalWeight);

  // 生成建议
  if (utilization < 0.6) {
    suggestions.push('空间利用率较低，考虑选择更小的大纸箱');
  }
  if (shippingCost.chargeableWeight > shippingCost.actualWeight) {
    suggestions.push('体积重量大于实际重量，考虑优化包装减少空间浪费');
  }

  return {
    recommendedCarton: carton,
    productPackages: packages,
    placements,
    utilization,
    shippingCost,
    warnings,
    suggestions
  };
}

/**
 * 计算物流成本（体积重量vs实际重量）
 */
function calculateShippingCost(carton: ShippingCarton, actualWeight: number): ShippingCost {
  // 计算大纸箱的体积重量
  const cartonVolume = carton.outerLength * carton.outerWidth * carton.outerHeight; // cm³
  const volumetricWeight = cartonVolume / AMAZON_FBA_CONSTANTS.VOLUMETRIC_CALCULATION.DIVISOR; // kg

  // 计费重量取较大值
  const chargeableWeight = Math.max(actualWeight, volumetricWeight);

  // 估算物流费用（以海运为例）
  const costPerKg = AMAZON_FBA_CONSTANTS.SHIPPING_RATES.SEA_FREIGHT;
  const shippingCost = chargeableWeight * costPerKg;

  return {
    actualWeight,
    volumetricWeight,
    chargeableWeight,
    shippingCost,
    costPerKg
  };
}

/**
 * 寻找包装盒的最佳放置位置
 */
function findBestPlacement(
  pkg: ProductPackage,
  availableSpace: { length: number; width: number; height: number },
  occupiedSpaces: OccupiedSpace[],
  bufferSpace: number
): PackagePlacement | null {

  // 尝试不同的放置方向
  const orientations = [
    { length: pkg.length, width: pkg.width, height: pkg.height, rotated: false },
    { length: pkg.width, width: pkg.length, height: pkg.height, rotated: true }
  ];

  for (const orientation of orientations) {
    // 检查是否超出可用空间
    if (orientation.length > availableSpace.length ||
        orientation.width > availableSpace.width ||
        orientation.height > availableSpace.height) {
      continue;
    }

    // 使用Bottom-Left-Fill算法寻找位置
    const position = findBottomLeftPosition(orientation, occupiedSpaces, availableSpace, bufferSpace);

    if (position) {
      return {
        package: pkg,
        x: position.x,
        y: position.y,
        z: position.z,
        rotated: orientation.rotated,
        layer: Math.floor(position.z / pkg.height) + 1
      };
    }
  }

  return null;
}

/**
 * Bottom-Left-Fill算法寻找最佳位置
 */
function findBottomLeftPosition(
  dimensions: { length: number; width: number; height: number },
  occupiedSpaces: OccupiedSpace[],
  availableSpace: { length: number; width: number; height: number },
  bufferSpace: number
): Point3D | null {
  
  // 生成候选位置点
  const candidatePoints: Point3D[] = [
    { x: bufferSpace, y: bufferSpace, z: bufferSpace } // 起始点
  ];
  
  // 从已占用空间生成新的候选点
  for (const space of occupiedSpaces) {
    candidatePoints.push(
      { x: space.x2, y: space.y1, z: space.z1 }, // 右侧
      { x: space.x1, y: space.y2, z: space.z1 }, // 后侧
      { x: space.x1, y: space.y1, z: space.z2 }  // 上方
    );
  }
  
  // 按Bottom-Left-Fill原则排序（Z优先，然后Y，最后X）
  candidatePoints.sort((a, b) => {
    if (a.z !== b.z) return a.z - b.z;
    if (a.y !== b.y) return a.y - b.y;
    return a.x - b.x;
  });
  
  // 检查每个候选位置
  for (const point of candidatePoints) {
    if (canPlaceAt(point, dimensions, occupiedSpaces, availableSpace, bufferSpace)) {
      return point;
    }
  }
  
  return null;
}

/**
 * 检查是否可以在指定位置放置商品
 */
function canPlaceAt(
  position: Point3D,
  dimensions: { length: number; width: number; height: number },
  occupiedSpaces: OccupiedSpace[],
  availableSpace: { length: number; width: number; height: number },
  bufferSpace: number
): boolean {
  
  // 检查是否超出边界
  if (position.x + dimensions.length > availableSpace.length + bufferSpace ||
      position.y + dimensions.width > availableSpace.width + bufferSpace ||
      position.z + dimensions.height > availableSpace.height + bufferSpace) {
    return false;
  }
  
  // 检查是否与已占用空间重叠
  const newSpace = {
    x1: position.x,
    y1: position.y,
    z1: position.z,
    x2: position.x + dimensions.length,
    y2: position.y + dimensions.width,
    z2: position.z + dimensions.height
  };
  
  for (const occupied of occupiedSpaces) {
    if (spacesOverlap(newSpace, occupied)) {
      return false;
    }
  }
  
  return true;
}

/**
 * 检查两个空间是否重叠
 */
function spacesOverlap(space1: OccupiedSpace, space2: OccupiedSpace): boolean {
  return !(space1.x2 <= space2.x1 || space1.x1 >= space2.x2 ||
           space1.y2 <= space2.y1 || space1.y1 >= space2.y2 ||
           space1.z2 <= space2.z1 || space1.z1 >= space2.z2);
}

/**
 * 生成装箱建议
 */
function generatePackingSuggestions(
  packages: ProductPackage[],
  carton: ShippingCarton,
  utilization: number,
  shippingCost: ShippingCost
): string[] {
  const suggestions: string[] = [];

  // 空间利用率建议
  if (utilization < 0.5) {
    suggestions.push('空间利用率较低，建议选择更小的大纸箱或增加商品数量');
  } else if (utilization > 0.9) {
    suggestions.push('空间利用率很高，但要确保有足够的缓冲空间保护商品');
  }

  // 体积重量建议
  const volumetricRatio = shippingCost.volumetricWeight / shippingCost.actualWeight;
  if (volumetricRatio > 1.2) {
    suggestions.push('体积重量明显大于实际重量，考虑优化包装减少空间浪费');
  }

  // 亚马逊限制建议
  if (carton.outerLength > 60 || carton.outerWidth > 60 || carton.outerHeight > 60) {
    suggestions.push('大纸箱尺寸接近亚马逊限制，确保不超过63.5cm');
  }

  // 易碎品建议
  const hasFragile = packages.some(p => p.isFragile);
  if (hasFragile) {
    suggestions.push('包含易碎商品，确保四周预留5cm缓冲空间并使用适当填充材料');
  }

  return suggestions;
}

/**
 * 选择最优装箱方案
 */
function selectBestOption(options: PackingResult[]): PackingResult {
  if (options.length === 0) {
    // 返回默认失败结果
    return {
      recommendedCarton: STANDARD_SHIPPING_CARTONS[0],
      productPackages: [],
      placements: [],
      utilization: 0,
      shippingCost: {
        actualWeight: 0,
        volumetricWeight: 0,
        chargeableWeight: 0,
        shippingCost: 0,
        costPerKg: 0
      },
      warnings: ['无法找到合适的大纸箱'],
      suggestions: []
    };
  }

  // 按物流成本排序，选择最经济的方案
  return options.sort((a, b) => {
    // 优先考虑成功装入所有包装盒的方案
    if (a.placements.length !== b.placements.length) {
      return b.placements.length - a.placements.length;
    }

    // 然后考虑物流成本
    if (a.shippingCost.shippingCost !== b.shippingCost.shippingCost) {
      return a.shippingCost.shippingCost - b.shippingCost.shippingCost;
    }

    // 最后考虑空间利用率
    return b.utilization - a.utilization;
  })[0];
}
