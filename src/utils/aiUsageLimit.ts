// AI使用频率限制管理
export interface UsageRecord {
  timestamp: number;
  feature: string;
  success: boolean;
}

export interface UsageStats {
  totalUsage: number;
  successfulUsage: number;
  remainingQuota: number;
  nextResetTime: number;
  isLimited: boolean;
  limitReason?: string;
  isSuspiciousActivity?: boolean; // 用于Turnstile触发判断
}

export interface UsageLimits {
  hourlyLimit: number;
  dailyLimit: number;
  sessionLimit: number;
  cooldownMinutes: number;
}

// 产品图片生成器限制配置
const IMAGE_GENERATOR_LIMITS: UsageLimits = {
  hourlyLimit: 5,       // 每小时5次（相对宽松）
  dailyLimit: 5,        // 每天5次（北京时间24点重置）
  sessionLimit: 0,      // 取消会话限制
  cooldownMinutes: 0    // 取消冷却时间
};

// 标题工具限制配置 - title-fixer和title-analyzer
const TITLE_TOOLS_LIMITS: UsageLimits = {
  hourlyLimit: 3,       // 每小时3次
  dailyLimit: 10,       // 每天10次（北京时间24点重置）
  sessionLimit: 0,      // 取消会话限制
  cooldownMinutes: 0    // 取消冷却时间
};

/**
 * 获取特定功能的使用限制配置
 */
export const getUsageLimits = (feature: string): UsageLimits => {
  switch (feature) {
    case 'title-fixer':
    case 'title-analyzer':
      return TITLE_TOOLS_LIMITS;
    case 'product-image-generator':
      return IMAGE_GENERATOR_LIMITS;
    default:
      return TITLE_TOOLS_LIMITS; // 默认使用标题工具的限制
  }
};

const STORAGE_KEY = 'aiUsageRecords';
const SESSION_KEY = 'aiSessionUsage';
const FINGERPRINT_KEY = 'userFingerprint';

/**
 * 获取北京时间的今日开始时间戳
 */
const getBeijingDayStart = (): number => {
  const now = new Date();
  // 转换为北京时间 (UTC+8)
  const beijingTime = new Date(now.getTime() + (8 * 60 * 60 * 1000));
  beijingTime.setUTCHours(0, 0, 0, 0);
  // 转换回本地时间戳
  return beijingTime.getTime() - (8 * 60 * 60 * 1000);
};

/**
 * 获取北京时间的下一日开始时间戳
 */
const getBeijingNextDayStart = (): number => {
  return getBeijingDayStart() + (24 * 60 * 60 * 1000);
};

/**
 * 获取北京时间的当前时间信息（用于调试和验证）
 */
export const getBeijingTimeInfo = () => {
  const now = new Date();
  const beijingTime = new Date(now.getTime() + (8 * 60 * 60 * 1000));
  const beijingDayStart = getBeijingDayStart();
  const beijingNextDayStart = getBeijingNextDayStart();
  
  return {
    localTime: now.toISOString(),
    beijingTime: beijingTime.toISOString(),
    beijingHour: beijingTime.getUTCHours(),
    beijingDayStartTimestamp: beijingDayStart,
    beijingNextDayStartTimestamp: beijingNextDayStart,
    beijingDayStart: new Date(beijingDayStart + (8 * 60 * 60 * 1000)).toISOString(),
    beijingNextDayStart: new Date(beijingNextDayStart + (8 * 60 * 60 * 1000)).toISOString(),
    hoursUntilReset: Math.round((beijingNextDayStart - now.getTime()) / (60 * 60 * 1000)),
    minutesUntilReset: Math.round((beijingNextDayStart - now.getTime()) / (60 * 1000))
  };
};

/**
 * 生成用户指纹，用于防恶意消耗
 */
const generateUserFingerprint = (): string => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  if (ctx) {
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillText('User fingerprint', 2, 2);
  }

  const fingerprint = [
    navigator.userAgent,
    navigator.language,
    screen.width + 'x' + screen.height,
    new Date().getTimezoneOffset(),
    canvas.toDataURL()
  ].join('|');

  // 简单哈希
  let hash = 0;
  for (let i = 0; i < fingerprint.length; i++) {
    const char = fingerprint.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  return Math.abs(hash).toString(36);
};

/**
 * 获取或生成用户指纹
 */
const getUserFingerprint = (): string => {
  try {
    let fingerprint = localStorage.getItem(FINGERPRINT_KEY);
    if (!fingerprint) {
      fingerprint = generateUserFingerprint();
      localStorage.setItem(FINGERPRINT_KEY, fingerprint);
    }
    return fingerprint;
  } catch (error) {
    return 'anonymous';
  }
};

/**
 * 检查是否需要Turnstile验证
 */
export const shouldRequireTurnstileVerification = (
  feature: string,
  usageStats: UsageStats
): boolean => {
  // Turnstile触发逻辑：每分钟超过2次请求或恶意机器人刷时启用
  const conditions = [
    // 检测到可疑活动（1分钟内超过2次请求）
    usageStats.isSuspiciousActivity === true,

    // 已经达到限制时要求验证（防止绕过限制）
    usageStats.isLimited === true
  ];

  return conditions.some(condition => condition);
};

/**
 * 验证Turnstile token
 */
export const verifyTurnstileToken = async (token: string): Promise<boolean> => {
  try {
    const response = await fetch('/api/verify-turnstile', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ token }),
    });

    const result = await response.json();
    return result.success === true;
  } catch (error) {
    console.error('Turnstile verification error:', error);
    return false;
  }
};

/**
 * 获取存储的使用记录
 */
const getUsageRecords = (): UsageRecord[] => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const records = JSON.parse(stored);
      return Array.isArray(records) ? records : [];
    }
  } catch (error) {
    console.warn('Failed to load AI usage records:', error);
  }
  return [];
};

/**
 * 保存使用记录
 */
const saveUsageRecords = (records: UsageRecord[]): void => {
  try {
    // 只保留最近7天的记录
    const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
    const filteredRecords = records.filter(record => record.timestamp > sevenDaysAgo);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(filteredRecords));
  } catch (error) {
    console.warn('Failed to save AI usage records:', error);
  }
};

/**
 * 获取会话使用次数（按功能分别计算）
 */
const getSessionUsage = (feature: string = 'title-fixer'): number => {
  try {
    const stored = sessionStorage.getItem(`${SESSION_KEY}_${feature}`);
    return stored ? parseInt(stored, 10) : 0;
  } catch (error) {
    return 0;
  }
};

/**
 * 设置会话使用次数（按功能分别计算）
 */
const setSessionUsage = (feature: string, count: number): void => {
  try {
    sessionStorage.setItem(`${SESSION_KEY}_${feature}`, count.toString());
  } catch (error) {
    console.warn('Failed to save session usage:', error);
  }
};

/**
 * 检查是否可以使用AI功能
 */
export const checkAIUsageLimit = (
  feature: string = 'title-fixer',
  limits?: UsageLimits
): UsageStats => {
  // 如果没有提供limits，则根据功能自动获取
  const usageLimits = limits || getUsageLimits(feature);
  const now = Date.now();
  const records = getUsageRecords();

  // 过滤相关功能的记录
  const featureRecords = records.filter(record => record.feature === feature);

  // 计算时间范围 - 使用北京时间计算每日重置
  const oneHourAgo = now - (60 * 60 * 1000);
  const beijingDayStart = getBeijingDayStart();

  // 统计使用次数 - 使用北京时间计算每日使用量
  const hourlyUsage = featureRecords.filter(record => record.timestamp > oneHourAgo).length;
  const dailyUsage = featureRecords.filter(record => record.timestamp > beijingDayStart).length;

  // 检查是否有可疑的快速连续请求（用于Turnstile触发判断）
  const recentRequests = featureRecords.filter(record => record.timestamp > (now - 60000)); // 1分钟内
  const isSuspiciousActivity = recentRequests.length >= 2; // 1分钟内超过2次请求（包含第2次）

  // 检查各种限制
  let isLimited = false;
  let limitReason = '';

  // 简化的限制检查
  if (hourlyUsage >= usageLimits.hourlyLimit) {
    isLimited = true;
    limitReason = 'hourly_limit';
  } else if (dailyUsage >= usageLimits.dailyLimit) {
    isLimited = true;
    limitReason = 'daily_limit';
  }

  // 可疑活动检测（用于触发Turnstile验证，而不是直接阻止）
  // 这个信息会传递给前端，让前端决定是否需要Turnstile验证
  // 冷却时间检查已禁用，提升用户体验
  // 其他限制机制（每小时、每日、每会话）已足够防止滥用

  // 计算剩余配额（简化版）
  const remainingHourly = Math.max(0, usageLimits.hourlyLimit - hourlyUsage);
  const remainingDaily = Math.max(0, usageLimits.dailyLimit - dailyUsage);
  const remainingQuota = Math.min(remainingHourly, remainingDaily);

  // 计算下次重置时间 - 使用北京时间
  const nextHourReset = Math.ceil(now / (60 * 60 * 1000)) * (60 * 60 * 1000);
  const nextBeijingDayReset = getBeijingNextDayStart(); // 北京时间下一天0点
  const nextResetTime = limitReason === 'daily_limit' ? nextBeijingDayReset : nextHourReset;

  return {
    totalUsage: dailyUsage,
    successfulUsage: featureRecords.filter(r => r.success && r.timestamp > beijingDayStart).length,
    remainingQuota,
    nextResetTime,
    isLimited,
    limitReason,
    isSuspiciousActivity // 传递给前端用于Turnstile判断
  };
};

/**
 * 记录AI使用
 */
export const recordAIUsage = (
  feature: string = 'title-fixer',
  success: boolean = true
): void => {
  const records = getUsageRecords();

  // 添加新记录
  records.push({
    timestamp: Date.now(),
    feature,
    success
  });

  // 保存记录（简化版，不再维护会话计数）
  saveUsageRecords(records);
};

/**
 * 获取使用统计信息
 */
export const getAIUsageStats = (feature: string = 'title-fixer'): {
  today: number;
  thisHour: number;
  thisSession: number;
  successRate: number;
  averageDaily: number;
} => {
  const now = Date.now();
  const records = getUsageRecords().filter(record => record.feature === feature);
  const sessionUsage = getSessionUsage(feature);

  const oneHourAgo = now - (60 * 60 * 1000);
  const oneDayAgo = now - (24 * 60 * 60 * 1000);
  const sevenDaysAgo = now - (7 * 24 * 60 * 60 * 1000);

  const todayRecords = records.filter(record => record.timestamp > oneDayAgo);
  const hourRecords = records.filter(record => record.timestamp > oneHourAgo);
  const weekRecords = records.filter(record => record.timestamp > sevenDaysAgo);

  const successfulToday = todayRecords.filter(record => record.success).length;
  const successRate = todayRecords.length > 0 ? (successfulToday / todayRecords.length) * 100 : 100;
  const averageDaily = weekRecords.length / 7;

  return {
    today: todayRecords.length,
    thisHour: hourRecords.length,
    thisSession: sessionUsage,
    successRate: Math.round(successRate),
    averageDaily: Math.round(averageDaily * 10) / 10
  };
};

/**
 * 重置会话使用计数
 */
export const resetSessionUsage = (feature: string = 'title-fixer'): void => {
  setSessionUsage(feature, 0);
};

/**
 * 清除所有使用记录
 */
export const clearAIUsageRecords = (): void => {
  try {
    localStorage.removeItem(STORAGE_KEY);
    sessionStorage.removeItem(SESSION_KEY);
  } catch (error) {
    console.warn('Failed to clear AI usage records:', error);
  }
};

/**
 * 获取限制说明文本
 */
export const getLimitExplanation = (limitReason: string, language: string = 'zh'): string => {
  const explanations: Record<string, Record<string, string>> = {
    zh: {
      hourly_limit: '您已达到每小时使用限制（3次），请稍后再试',
      daily_limit: '您已达到每日使用限制（10次），每天北京时间0点自动重置',
      session_limit: '您已达到本次会话使用限制，请刷新页面后继续',
      cooldown: '请等待后再次使用AI功能',
      suspicious_activity: '检测到异常活动，请稍后再试'
    },
    en: {
      hourly_limit: 'You have reached the hourly usage limit (3 times), please try again later',
      daily_limit: 'You have reached the daily usage limit (10 times), resets daily at Beijing midnight',
      session_limit: 'You have reached the session usage limit, please refresh the page to continue',
      cooldown: 'Please wait before using AI features again',
      suspicious_activity: 'Suspicious activity detected, please try again later'
    }
  };

  return explanations[language]?.[limitReason] || explanations.zh[limitReason] || '使用受限，请稍后再试';
};

/**
 * 格式化剩余时间
 */
export const formatTimeUntilReset = (resetTime: number, language: string = 'zh'): string => {
  const now = Date.now();
  const diff = resetTime - now;
  
  if (diff <= 0) return language === 'zh' ? '现在可用' : 'Available now';
  
  const hours = Math.floor(diff / (60 * 60 * 1000));
  const minutes = Math.floor((diff % (60 * 60 * 1000)) / (60 * 1000));
  
  if (language === 'zh') {
    if (hours > 0) {
      return `${hours}小时${minutes}分钟后重置`;
    }
    return `${minutes}分钟后重置`;
  } else {
    if (hours > 0) {
      return `Resets in ${hours}h ${minutes}m`;
    }
    return `Resets in ${minutes}m`;
  }
};
