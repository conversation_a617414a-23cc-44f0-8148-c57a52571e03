// 亚马逊标题合规性检查算法
// 基于2025年1月21日生效的最新政策

import { getTranslation, type Language } from '../locales';

export interface Violation {
  type: 'length' | 'special_chars' | 'repeated_words';
  severity: 'error' | 'warning';
  message: string;
  position?: number[];
  suggestions?: string[];
  details?: any;
}

export interface RepeatedWord {
  word: string;
  count: number;
  positions: number[];
  isException: boolean; // 介词、连词、冠词
}

export interface ComplianceResult {
  isCompliant: boolean;
  score: number; // 0-100
  violations: Violation[];
  suggestions: string[];
  stats: {
    length: number;
    maxAllowed: number;
    specialCharsCount: number;
    repeatedWordsCount: number;
  };
}

// 2025年亚马逊禁用的特殊字符
const FORBIDDEN_SPECIAL_CHARS = ['!', '$', '?', '_', '{', '}', '^', '¬', '¦'];

// 不算重复的例外词汇（介词、连词、冠词等）
const EXCEPTION_WORDS = new Set([
  // 英文介词、连词、冠词
  'a', 'an', 'the', 'and', 'or', 'but', 'for', 'with', 'by', 'in', 'on', 'at', 'to', 'from', 'of', 'is', 'are', 'was', 'were',
  // 常见连接词
  'that', 'this', 'these', 'those', 'it', 'its', 'his', 'her', 'their', 'our', 'your', 'my',
  // 数字
  '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine', 'ten'
]);

/**
 * 检查标题字符长度
 */
export const checkTitleLength = (title: string, category: string, language: Language = 'zh'): Violation[] => {
  const violations: Violation[] = [];
  const maxLength = category === 'apparel' ? 125 : 200;
  const currentLength = title.length;

  if (currentLength > maxLength) {
    violations.push({
      type: 'length',
      severity: 'error',
      message: getTranslation(language, 'tfLengthExceeded')
        .replace('{current}', currentLength.toString())
        .replace('{max}', maxLength.toString()),
      suggestions: [
        getTranslation(language, 'tfSugRemoveRepeated'),
        getTranslation(language, 'tfSugSimplifyDesc'),
        getTranslation(language, 'tfSugRemoveUnnecessary')
      ],
      details: { currentLength, maxLength, excess: currentLength - maxLength }
    });
  } else if (currentLength > maxLength * 0.9) {
    violations.push({
      type: 'length',
      severity: 'warning',
      message: getTranslation(language, 'tfLengthNearLimit')
        .replace('{current}', currentLength.toString())
        .replace('{max}', maxLength.toString()),
      suggestions: [
        getTranslation(language, 'tfSugConsiderConcise'),
        getTranslation(language, 'tfSugAvoidMoreContent')
      ],
      details: { currentLength, maxLength }
    });
  }

  return violations;
};

/**
 * 检查特殊字符
 */
export const checkSpecialCharacters = (title: string, language: Language = 'zh'): Violation[] => {
  const violations: Violation[] = [];
  const foundChars: { char: string; positions: number[] }[] = [];

  // 检查每个禁用字符
  FORBIDDEN_SPECIAL_CHARS.forEach(char => {
    const positions: number[] = [];
    let index = title.indexOf(char);

    while (index !== -1) {
      positions.push(index);
      index = title.indexOf(char, index + 1);
    }

    if (positions.length > 0) {
      foundChars.push({ char, positions });
    }
  });

  if (foundChars.length > 0) {
    violations.push({
      type: 'special_chars',
      severity: 'error',
      message: getTranslation(language, 'tfSpecialCharsFound')
        .replace('{chars}', foundChars.map(f => f.char).join(', ')),
      position: foundChars.flatMap(f => f.positions),
      suggestions: [
        getTranslation(language, 'tfSugRemoveSpecialChars'),
        getTranslation(language, 'tfSugReplaceWithText'),
        getTranslation(language, 'tfSugCheckBrandName')
      ],
      details: { foundChars }
    });
  }

  return violations;
};

/**
 * 检查重复词汇
 */
export const checkRepeatedWords = (title: string, language: Language = 'zh'): Violation[] => {
  const violations: Violation[] = [];
  const words = title.toLowerCase()
    .replace(/[^\w\s]/g, ' ') // 移除标点符号
    .split(/\s+/)
    .filter(word => word.length > 0);

  const wordCount = new Map<string, RepeatedWord>();

  // 统计词汇出现次数和位置
  words.forEach((word, index) => {
    if (wordCount.has(word)) {
      const existing = wordCount.get(word)!;
      existing.count++;
      existing.positions.push(index);
    } else {
      wordCount.set(word, {
        word,
        count: 1,
        positions: [index],
        isException: EXCEPTION_WORDS.has(word)
      });
    }
  });

  // 检查重复词汇（超过2次且不是例外词汇）
  const repeatedWords: RepeatedWord[] = [];
  wordCount.forEach(wordInfo => {
    if (wordInfo.count > 2 && !wordInfo.isException) {
      repeatedWords.push(wordInfo);
    }
  });

  if (repeatedWords.length > 0) {
    violations.push({
      type: 'repeated_words',
      severity: 'error',
      message: getTranslation(language, 'tfRepeatedWordsFound')
        .replace('{words}', repeatedWords.map(w => `"${w.word}"(${w.count}${language === 'zh' || language === 'zh-TW' ? '次' : ' times'})`).join(', ')),
      suggestions: [
        getTranslation(language, 'tfSugRemoveExtraRepeated'),
        getTranslation(language, 'tfSugUseSynonyms'),
        getTranslation(language, 'tfSugReorganizeStructure')
      ],
      details: { repeatedWords }
    });
  }

  // 检查可能的复数形式重复
  const pluralViolations = checkPluralDuplicates(wordCount, language);
  violations.push(...pluralViolations);

  return violations;
};

/**
 * 检查复数形式重复
 */
const checkPluralDuplicates = (wordCount: Map<string, RepeatedWord>, language: Language = 'zh'): Violation[] => {
  const violations: Violation[] = [];
  const checkedPairs = new Set<string>();

  wordCount.forEach((wordInfo, word) => {
    if (wordInfo.isException) return;

    // 检查单复数形式
    const singular = word.endsWith('s') ? word.slice(0, -1) : word;
    const plural = word.endsWith('s') ? word : word + 's';

    const pairKey = [singular, plural].sort().join('-');
    if (checkedPairs.has(pairKey)) return;
    checkedPairs.add(pairKey);

    const singularInfo = wordCount.get(singular);
    const pluralInfo = wordCount.get(plural);

    if (singularInfo && pluralInfo) {
      violations.push({
        type: 'repeated_words',
        severity: 'warning',
        message: getTranslation(language, 'tfPluralDuplicateFound')
          .replace('{singular}', singular)
          .replace('{plural}', plural),
        suggestions: [
          getTranslation(language, 'tfSugKeepOneForm'),
          getTranslation(language, 'tfSugUsePreciseDesc')
        ],
        details: {
          words: [singularInfo, pluralInfo],
          totalCount: singularInfo.count + pluralInfo.count
        }
      });
    }
  });

  return violations;
};

/**
 * 计算合规性评分
 */
export const calculateComplianceScore = (violations: Violation[]): number => {
  let score = 100;

  violations.forEach(violation => {
    switch (violation.type) {
      case 'length':
        score -= violation.severity === 'error' ? 30 : 10;
        break;
      case 'special_chars':
        score -= violation.severity === 'error' ? 25 : 5;
        break;
      case 'repeated_words':
        score -= violation.severity === 'error' ? 20 : 8;
        break;
    }
  });

  return Math.max(0, score);
};

/**
 * 生成优化建议
 */
export const generateSuggestions = (violations: Violation[], language: Language = 'zh'): string[] => {
  const suggestions = new Set<string>();

  violations.forEach(violation => {
    violation.suggestions?.forEach(suggestion => {
      suggestions.add(suggestion);
    });
  });

  // 添加通用建议
  if (violations.length > 0) {
    suggestions.add(getTranslation(language, 'tfSugFollowPolicy'));
    suggestions.add(getTranslation(language, 'tfSugKeepConcise'));
  }

  return Array.from(suggestions);
};

/**
 * 主要的合规性检查函数
 */
export const checkTitleCompliance = (title: string, category: string = 'general', language: Language = 'zh'): ComplianceResult => {
  const violations: Violation[] = [];

  // 执行各项检查
  violations.push(...checkTitleLength(title, category, language));
  violations.push(...checkSpecialCharacters(title, language));
  violations.push(...checkRepeatedWords(title, language));

  // 计算评分和建议
  const score = calculateComplianceScore(violations);
  const suggestions = generateSuggestions(violations, language);
  const isCompliant = violations.filter(v => v.severity === 'error').length === 0;

  // 统计信息
  const maxLength = category === 'apparel' ? 125 : 200;
  const specialCharsCount = FORBIDDEN_SPECIAL_CHARS.reduce((count, char) => {
    return count + (title.split(char).length - 1);
  }, 0);

  const words = title.toLowerCase().replace(/[^\w\s]/g, ' ').split(/\s+/).filter(w => w.length > 0);
  const wordCount = new Map<string, number>();
  words.forEach(word => {
    if (!EXCEPTION_WORDS.has(word)) {
      wordCount.set(word, (wordCount.get(word) || 0) + 1);
    }
  });
  const repeatedWordsCount = Array.from(wordCount.values()).filter(count => count > 2).length;

  return {
    isCompliant,
    score,
    violations,
    suggestions,
    stats: {
      length: title.length,
      maxAllowed: maxLength,
      specialCharsCount,
      repeatedWordsCount
    }
  };
};
