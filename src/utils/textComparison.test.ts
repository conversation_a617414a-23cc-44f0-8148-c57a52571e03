// 文本对比和违规项可视化测试
import { generateTextDiff, generateViolationHighlights, generateChangeStats } from './textComparison';
import { type Violation } from './complianceCheck';

// 测试文本差异生成
console.log('🧪 测试文本差异生成...\n');

const originalText = "Women's Cotton T-Shirt! Amazing Quality! Best Price!";
const fixedText = "Women's Cotton T-Shirt Amazing Quality Best Price";

const diffs = generateTextDiff(originalText, fixedText);
const stats = generateChangeStats(diffs);

console.log('原文本:', originalText);
console.log('修复后:', fixedText);
console.log('差异分析:', diffs);
console.log('统计信息:', stats);
console.log('---\n');

// 测试违规项高亮
console.log('🎯 测试违规项高亮...\n');

const testViolations: Violation[] = [
  {
    type: 'special_chars',
    severity: 'error',
    message: '包含禁用特殊字符：!',
    position: [20, 41],
    details: {
      foundChars: [
        { char: '!', positions: [20, 36, 51] }
      ]
    }
  },
  {
    type: 'repeated_words',
    severity: 'warning',
    message: '重复词汇：Quality',
    details: {
      repeatedWords: [
        { word: 'quality', count: 2, positions: [0, 1] }
      ]
    }
  }
];

const highlights = generateViolationHighlights(originalText, testViolations);
console.log('违规高亮:', highlights);
console.log('---\n');

// 测试边界情况
console.log('🔍 测试边界情况...\n');

// 空文本
const emptyDiffs = generateTextDiff('', '');
console.log('空文本差异:', emptyDiffs);

// 相同文本
const sameDiffs = generateTextDiff('Same text', 'Same text');
console.log('相同文本差异:', sameDiffs);

// 完全不同的文本
const differentDiffs = generateTextDiff('Apple', 'Orange');
console.log('完全不同文本差异:', differentDiffs);

console.log('✅ 所有测试完成！');
