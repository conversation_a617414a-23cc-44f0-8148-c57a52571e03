// 装箱计算器数据定义
// 亚马逊FBA入库大纸箱装箱优化

export interface ProductPackage {
  id: string;
  name: string;
  length: number;  // 商品包装盒长度 (cm)
  width: number;   // 商品包装盒宽度 (cm)
  height: number;  // 商品包装盒高度 (cm)
  weight: number;  // 单件重量 (kg)
  quantity: number; // 数量
  isFragile?: boolean;  // 是否易碎
  category?: string;    // 商品类别
}

export interface ShippingCarton {
  id: string;
  name: string;
  outerLength: number;  // 外部长度 (cm)
  outerWidth: number;   // 外部宽度 (cm)
  outerHeight: number;  // 外部高度 (cm)
  innerLength: number;  // 内部长度 (cm)
  innerWidth: number;   // 内部宽度 (cm)
  innerHeight: number;  // 内部高度 (cm)
  maxWeight: number;    // 最大承重 (kg)
  wallThickness: number; // 纸箱壁厚 (cm)
  type: 'standard' | 'custom';
}

export interface ShippingCost {
  actualWeight: number;     // 实际重量 (kg)
  volumetricWeight: number; // 体积重量 (kg)
  chargeableWeight: number; // 计费重量 (kg)
  shippingCost: number;     // 物流费用 (USD)
  costPerKg: number;        // 每公斤费用 (USD)
}

export interface PackingResult {
  recommendedCarton: ShippingCarton;
  productPackages: ProductPackage[];
  placements: PackagePlacement[];
  utilization: number;  // 空间利用率 (0-1)
  shippingCost: ShippingCost;
  warnings: string[];
  suggestions: string[];
}

export interface PackagePlacement {
  package: ProductPackage;
  x: number;  // X坐标
  y: number;  // Y坐标
  z: number;  // Z坐标
  rotated: boolean;  // 是否旋转
  layer: number;      // 层数
}

export interface PackingOptimization {
  totalPackages: number;     // 总包装盒数量
  totalWeight: number;       // 总重量
  totalVolume: number;       // 总体积
  recommendedCartons: ShippingCarton[];  // 推荐的大纸箱
  costComparison: ShippingCost[];        // 成本对比
}

// Amazon FBA 入库大纸箱规格 (2025年)
export const STANDARD_SHIPPING_CARTONS: ShippingCarton[] = [
  // 小型大纸箱
  {
    id: 'carton-small-1',
    name: '小型瓦楞纸箱 A',
    outerLength: 30,
    outerWidth: 20,
    outerHeight: 15,
    innerLength: 28,
    innerWidth: 18,
    innerHeight: 13,
    maxWeight: 22.68,
    wallThickness: 1,
    type: 'standard'
  },
  {
    id: 'carton-small-2',
    name: '小型瓦楞纸箱 B',
    outerLength: 35,
    outerWidth: 25,
    outerHeight: 18,
    innerLength: 33,
    innerWidth: 23,
    innerHeight: 16,
    maxWeight: 22.68,
    wallThickness: 1,
    type: 'standard'
  },
  {
    id: 'carton-medium-1',
    name: '中型瓦楞纸箱 A',
    outerLength: 45,
    outerWidth: 35,
    outerHeight: 25,
    innerLength: 43,
    innerWidth: 33,
    innerHeight: 23,
    maxWeight: 22.68,
    wallThickness: 1,
    type: 'standard'
  },

  // 大型瓦楞纸箱（接近亚马逊限制）
  {
    id: 'carton-large-1',
    name: '大型瓦楞纸箱 A',
    outerLength: 60,
    outerWidth: 40,
    outerHeight: 30,
    innerLength: 58,
    innerWidth: 38,
    innerHeight: 28,
    maxWeight: 22.68,
    wallThickness: 1,
    type: 'standard'
  },
  {
    id: 'carton-large-2',
    name: '大型瓦楞纸箱 B',
    outerLength: 63,  // 接近亚马逊25英寸(63.5cm)限制
    outerWidth: 45,
    outerHeight: 35,
    innerLength: 61,
    innerWidth: 43,
    innerHeight: 33,
    maxWeight: 22.68,
    wallThickness: 1,
    type: 'standard'
  },
  {
    id: 'carton-max-1',
    name: '最大瓦楞纸箱 A',
    outerLength: 63,  // 亚马逊最大限制
    outerWidth: 50,
    outerHeight: 40,
    innerLength: 61,
    innerWidth: 48,
    innerHeight: 38,
    maxWeight: 22.68,
    wallThickness: 1,
    type: 'standard'
  },
  {
    id: 'carton-max-2',
    name: '最大瓦楞纸箱 B',
    outerLength: 63,  // 亚马逊最大限制
    outerWidth: 63,   // 正方形设计
    outerHeight: 50,
    innerLength: 61,
    innerWidth: 61,
    innerHeight: 48,
    maxWeight: 22.68,
    wallThickness: 1,
    type: 'standard'
  }
];

// 亚马逊FBA入库要求常量
export const AMAZON_FBA_CONSTANTS = {
  // 大纸箱尺寸限制 (cm) - 亚马逊2025年规定
  CARTON_LIMITS: {
    MAX_SIDE_LENGTH: 63.5,  // 25英寸 = 63.5cm
    MAX_WEIGHT: 22.68,      // 50磅 = 22.68kg
    MIN_DIMENSIONS: {       // 最小尺寸避免延迟
      length: 15.24,        // 6英寸
      width: 10.16,         // 4英寸
      height: 2.54          // 1英寸
    },
    MIN_WEIGHT: 0.45        // 1磅
  },

  // 缓冲空间要求 (cm)
  BUFFER_SPACE: {
    FRAGILE: 5.08,          // 易碎物品缓冲空间 (2英寸)
    NORMAL: 2.54,           // 普通物品缓冲空间 (1英寸)
    MINIMUM: 1.27           // 最小缓冲空间 (0.5英寸)
  },

  // 体积重量计算
  VOLUMETRIC_CALCULATION: {
    DIVISOR: 6000,          // 体积重量除数 (cm³/kg)
    FORMULA: 'volume_cm3 / 6000 = volumetric_weight_kg'
  },

  // 物流费用估算 (USD/kg) - 示例费率
  SHIPPING_RATES: {
    SEA_FREIGHT: 2.5,       // 海运费率
    AIR_FREIGHT: 8.0,       // 空运费率
    EXPRESS: 15.0           // 快递费率
  }
};

// 装箱优化建议文案
export const PACKING_TIPS = {
  COST_OPTIMIZATION: [
    '优化体积重量比，降低物流成本',
    '选择合适的大纸箱尺寸，避免浪费空间',
    '合理安排装箱数量，平衡重量和体积',
    '考虑海运vs空运的成本差异'
  ],

  SPACE_OPTIMIZATION: [
    '重物放底部，轻物放顶部，保持重心稳定',
    '长条形商品沿大纸箱长边摆放',
    '填充空隙，防止运输中商品移动碰撞',
    '易碎商品四周预留5cm缓冲空间'
  ],

  AMAZON_COMPLIANCE: [
    '大纸箱任一边不得超过63.5cm（25英寸）',
    '大纸箱总重量不得超过22.68kg（50磅）',
    '使用坚固的瓦楞纸箱，确保六面完整',
    '移除或遮盖旧的条形码和标签'
  ],

  SHIPPING_EFFICIENCY: [
    '体积重量 = 长×宽×高(cm) ÷ 6000',
    '计费重量 = max(实际重量, 体积重量)',
    '优化包装减少不必要的空间浪费',
    '考虑商品包装盒的标准化尺寸'
  ]
};
