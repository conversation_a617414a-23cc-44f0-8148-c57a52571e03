// 常用信息对照表数据
export interface ReferenceTableItem {
  id: string;
  [key: string]: string | number;
}

export interface ReferenceTable {
  id: string;
  category: string;
  nameKey: string;
  descriptionKey: string;
  columns: {
    key: string;
    labelKey: string;
    width?: string;
    searchable?: boolean;
  }[];
  data: ReferenceTableItem[];
}

// 美国州名缩写对照表
export const usStatesTable: ReferenceTable = {
  id: 'us-states',
  category: 'geography',
  nameKey: 'usStatesTable',
  descriptionKey: 'usStatesTableDesc',
  columns: [
    { key: 'stateName', labelKey: 'stateName', width: '25%', searchable: true },
    { key: 'localName', labelKey: 'localName', width: '25%', searchable: true },
    { key: 'abbreviation2', labelKey: 'abbreviation2Letter', width: '15%', searchable: true },
    { key: 'abbreviation3', labelKey: 'abbreviation3Letter', width: '15%', searchable: true },
    { key: 'capital', labelKey: 'capital', width: '20%', searchable: true }
  ],
  data: [
    { id: '1', stateName: 'Alabama', abbreviation2: 'AL', abbreviation3: 'ALA', capital: 'Montgomery' },
    { id: '2', stateName: 'Alaska', abbreviation2: 'AK', abbreviation3: 'AK', capital: 'Juneau' },
    { id: '3', stateName: 'Arizona', abbreviation2: 'AZ', abbreviation3: 'ARIZ', capital: 'Phoenix' },
    { id: '4', stateName: 'Arkansas', abbreviation2: 'AR', abbreviation3: 'ARK', capital: 'Little Rock' },
    { id: '5', stateName: 'California', abbreviation2: 'CA', abbreviation3: 'CALIF', capital: 'Sacramento' },
    { id: '6', stateName: 'Colorado', abbreviation2: 'CO', abbreviation3: 'COLO', capital: 'Denver' },
    { id: '7', stateName: 'Connecticut', abbreviation2: 'CT', abbreviation3: 'CONN', capital: 'Hartford' },
    { id: '8', stateName: 'Delaware', abbreviation2: 'DE', abbreviation3: 'DEL', capital: 'Dover' },
    { id: '9', stateName: 'Florida', abbreviation2: 'FL', abbreviation3: 'FLA', capital: 'Tallahassee' },
    { id: '10', stateName: 'Georgia', abbreviation2: 'GA', abbreviation3: 'GA', capital: 'Atlanta' },
    { id: '11', stateName: 'Hawaii', abbreviation2: 'HI', abbreviation3: 'HAWAII', capital: 'Honolulu' },
    { id: '12', stateName: 'Idaho', abbreviation2: 'ID', abbreviation3: 'IDAHO', capital: 'Boise' },
    { id: '13', stateName: 'Illinois', abbreviation2: 'IL', abbreviation3: 'ILL', capital: 'Springfield' },
    { id: '14', stateName: 'Indiana', abbreviation2: 'IN', abbreviation3: 'IND', capital: 'Indianapolis' },
    { id: '15', stateName: 'Iowa', abbreviation2: 'IA', abbreviation3: 'IOWA', capital: 'Des Moines' },
    { id: '16', stateName: 'Kansas', abbreviation2: 'KS', abbreviation3: 'KANS', capital: 'Topeka' },
    { id: '17', stateName: 'Kentucky', abbreviation2: 'KY', abbreviation3: 'KY', capital: 'Frankfort' },
    { id: '18', stateName: 'Louisiana', abbreviation2: 'LA', abbreviation3: 'LA', capital: 'Baton Rouge' },
    { id: '19', stateName: 'Maine', abbreviation2: 'ME', abbreviation3: 'MAINE', capital: 'Augusta' },
    { id: '20', stateName: 'Maryland', abbreviation2: 'MD', abbreviation3: 'MD', capital: 'Annapolis' },
    { id: '21', stateName: 'Massachusetts', abbreviation2: 'MA', abbreviation3: 'MASS', capital: 'Boston' },
    { id: '22', stateName: 'Michigan', abbreviation2: 'MI', abbreviation3: 'MICH', capital: 'Lansing' },
    { id: '23', stateName: 'Minnesota', abbreviation2: 'MN', abbreviation3: 'MINN', capital: 'Saint Paul' },
    { id: '24', stateName: 'Mississippi', abbreviation2: 'MS', abbreviation3: 'MISS', capital: 'Jackson' },
    { id: '25', stateName: 'Missouri', abbreviation2: 'MO', abbreviation3: 'MO', capital: 'Jefferson City' },
    { id: '26', stateName: 'Montana', abbreviation2: 'MT', abbreviation3: 'MONT', capital: 'Helena' },
    { id: '27', stateName: 'Nebraska', abbreviation2: 'NE', abbreviation3: 'NEBR', capital: 'Lincoln' },
    { id: '28', stateName: 'Nevada', abbreviation2: 'NV', abbreviation3: 'NEV', capital: 'Carson City' },
    { id: '29', stateName: 'New Hampshire', abbreviation2: 'NH', abbreviation3: 'NH', capital: 'Concord' },
    { id: '30', stateName: 'New Jersey', abbreviation2: 'NJ', abbreviation3: 'NJ', capital: 'Trenton' },
    { id: '31', stateName: 'New Mexico', abbreviation2: 'NM', abbreviation3: 'NM', capital: 'Santa Fe' },
    { id: '32', stateName: 'New York', abbreviation2: 'NY', abbreviation3: 'NY', capital: 'Albany' },
    { id: '33', stateName: 'North Carolina', abbreviation2: 'NC', abbreviation3: 'NC', capital: 'Raleigh' },
    { id: '34', stateName: 'North Dakota', abbreviation2: 'ND', abbreviation3: 'ND', capital: 'Bismarck' },
    { id: '35', stateName: 'Ohio', abbreviation2: 'OH', abbreviation3: 'OHIO', capital: 'Columbus' },
    { id: '36', stateName: 'Oklahoma', abbreviation2: 'OK', abbreviation3: 'OKLA', capital: 'Oklahoma City' },
    { id: '37', stateName: 'Oregon', abbreviation2: 'OR', abbreviation3: 'ORE', capital: 'Salem' },
    { id: '38', stateName: 'Pennsylvania', abbreviation2: 'PA', abbreviation3: 'PA', capital: 'Harrisburg' },
    { id: '39', stateName: 'Rhode Island', abbreviation2: 'RI', abbreviation3: 'RI', capital: 'Providence' },
    { id: '40', stateName: 'South Carolina', abbreviation2: 'SC', abbreviation3: 'SC', capital: 'Columbia' },
    { id: '41', stateName: 'South Dakota', abbreviation2: 'SD', abbreviation3: 'SD', capital: 'Pierre' },
    { id: '42', stateName: 'Tennessee', abbreviation2: 'TN', abbreviation3: 'TENN', capital: 'Nashville' },
    { id: '43', stateName: 'Texas', abbreviation2: 'TX', abbreviation3: 'TEX', capital: 'Austin' },
    { id: '44', stateName: 'Utah', abbreviation2: 'UT', abbreviation3: 'UTAH', capital: 'Salt Lake City' },
    { id: '45', stateName: 'Vermont', abbreviation2: 'VT', abbreviation3: 'VT', capital: 'Montpelier' },
    { id: '46', stateName: 'Virginia', abbreviation2: 'VA', abbreviation3: 'VA', capital: 'Richmond' },
    { id: '47', stateName: 'Washington', abbreviation2: 'WA', abbreviation3: 'WASH', capital: 'Olympia' },
    { id: '48', stateName: 'West Virginia', abbreviation2: 'WV', abbreviation3: 'WV', capital: 'Charleston' },
    { id: '49', stateName: 'Wisconsin', abbreviation2: 'WI', abbreviation3: 'WIS', capital: 'Madison' },
    { id: '50', stateName: 'Wyoming', abbreviation2: 'WY', abbreviation3: 'WYO', capital: 'Cheyenne' }
  ]
};

// 全球鞋码对照表
export const shoeSizesTable: ReferenceTable = {
  id: 'shoe-sizes',
  category: 'sizing',
  nameKey: 'shoeSizesTable',
  descriptionKey: 'shoeSizesTableDesc',
  columns: [
    { key: 'us', labelKey: 'usShoeSizes', width: '20%', searchable: true },
    { key: 'eu', labelKey: 'euShoeSizes', width: '20%', searchable: true },
    { key: 'uk', labelKey: 'ukShoeSizes', width: '20%', searchable: true },
    { key: 'cn', labelKey: 'cnShoeSizes', width: '20%', searchable: true },
    { key: 'jp', labelKey: 'jpShoeSizes', width: '20%', searchable: true }
  ],
  data: [
    { id: '1', us: '4', eu: '35', uk: '2', cn: '220', jp: '22' },
    { id: '2', us: '4.5', eu: '35.5', uk: '2.5', cn: '225', jp: '22.5' },
    { id: '3', us: '5', eu: '36', uk: '3', cn: '230', jp: '23' },
    { id: '4', us: '5.5', eu: '37', uk: '3.5', cn: '235', jp: '23.5' },
    { id: '5', us: '6', eu: '37.5', uk: '4', cn: '240', jp: '24' },
    { id: '6', us: '6.5', eu: '38', uk: '4.5', cn: '245', jp: '24.5' },
    { id: '7', us: '7', eu: '38.5', uk: '5', cn: '250', jp: '25' },
    { id: '8', us: '7.5', eu: '39', uk: '5.5', cn: '255', jp: '25.5' },
    { id: '9', us: '8', eu: '40', uk: '6', cn: '260', jp: '26' },
    { id: '10', us: '8.5', eu: '41', uk: '6.5', cn: '265', jp: '26.5' },
    { id: '11', us: '9', eu: '42', uk: '7', cn: '270', jp: '27' },
    { id: '12', us: '9.5', eu: '42.5', uk: '7.5', cn: '275', jp: '27.5' },
    { id: '13', us: '10', eu: '43', uk: '8', cn: '280', jp: '28' },
    { id: '14', us: '10.5', eu: '44', uk: '8.5', cn: '285', jp: '28.5' },
    { id: '15', us: '11', eu: '44.5', uk: '9', cn: '290', jp: '29' },
    { id: '16', us: '11.5', eu: '45', uk: '9.5', cn: '295', jp: '29.5' },
    { id: '17', us: '12', eu: '46', uk: '10', cn: '300', jp: '30' },
    { id: '18', us: '12.5', eu: '46.5', uk: '10.5', cn: '305', jp: '30.5' },
    { id: '19', us: '13', eu: '47', uk: '11', cn: '310', jp: '31' },
    { id: '20', us: '14', eu: '48', uk: '12', cn: '320', jp: '32' }
  ]
};

// 国际贸易术语对照表
export const incotermsTable: ReferenceTable = {
  id: 'incoterms',
  category: 'logistics',
  nameKey: 'incotermsTable',
  descriptionKey: 'incotermsTableDesc',
  columns: [
    { key: 'term', labelKey: 'incotermCode', width: '15%', searchable: true },
    { key: 'fullName', labelKey: 'incotermFullName', width: '35%', searchable: true },
    { key: 'responsibility', labelKey: 'sellerResponsibility', width: '25%', searchable: true },
    { key: 'riskTransfer', labelKey: 'riskTransferPoint', width: '25%', searchable: true }
  ],
  data: [
    {
      id: '1',
      term: 'EXW',
      fullName: 'Ex Works',
      responsibility: 'Minimum seller responsibility',
      riskTransfer: 'Seller premises'
    },
    {
      id: '2',
      term: 'FCA',
      fullName: 'Free Carrier',
      responsibility: 'Delivery to carrier',
      riskTransfer: 'Named place'
    },
    {
      id: '3',
      term: 'CPT',
      fullName: 'Carriage Paid To',
      responsibility: 'Transport to destination',
      riskTransfer: 'First carrier'
    },
    {
      id: '4',
      term: 'CIP',
      fullName: 'Carriage and Insurance Paid To',
      responsibility: 'Transport + insurance',
      riskTransfer: 'First carrier'
    },
    {
      id: '5',
      term: 'DAP',
      fullName: 'Delivered at Place',
      responsibility: 'Delivery ready for unloading',
      riskTransfer: 'Named destination'
    },
    {
      id: '6',
      term: 'DPU',
      fullName: 'Delivered at Place Unloaded',
      responsibility: 'Delivery and unloading',
      riskTransfer: 'After unloading'
    },
    {
      id: '7',
      term: 'DDP',
      fullName: 'Delivered Duty Paid',
      responsibility: 'Maximum seller responsibility',
      riskTransfer: 'Final destination'
    },
    {
      id: '8',
      term: 'FAS',
      fullName: 'Free Alongside Ship',
      responsibility: 'Delivery alongside vessel',
      riskTransfer: 'Port of shipment'
    },
    {
      id: '9',
      term: 'FOB',
      fullName: 'Free on Board',
      responsibility: 'Loading on vessel',
      riskTransfer: 'On board vessel'
    },
    {
      id: '10',
      term: 'CFR',
      fullName: 'Cost and Freight',
      responsibility: 'Sea freight to destination',
      riskTransfer: 'On board vessel'
    },
    {
      id: '11',
      term: 'CIF',
      fullName: 'Cost, Insurance and Freight',
      responsibility: 'Sea freight + insurance',
      riskTransfer: 'On board vessel'
    }
  ]
};

// 货币代码对照表
export const currencyCodesTable: ReferenceTable = {
  id: 'currency-codes',
  category: 'financial',
  nameKey: 'currencyCodesTable',
  descriptionKey: 'currencyCodesTableDesc',
  columns: [
    { key: 'currency', labelKey: 'currencyName', width: '40%', searchable: true },
    { key: 'code', labelKey: 'currencyCode', width: '20%', searchable: true },
    { key: 'symbol', labelKey: 'currencySymbol', width: '20%', searchable: true },
    { key: 'country', labelKey: 'primaryCountry', width: '20%', searchable: true }
  ],
  data: [
    { id: '1', currency: 'US Dollar', code: 'USD', symbol: '$', country: 'United States' },
    { id: '2', currency: 'Euro', code: 'EUR', symbol: '€', country: 'European Union' },
    { id: '3', currency: 'British Pound', code: 'GBP', symbol: '£', country: 'United Kingdom' },
    { id: '4', currency: 'Japanese Yen', code: 'JPY', symbol: '¥', country: 'Japan' },
    { id: '5', currency: 'Chinese Yuan', code: 'CNY', symbol: '¥', country: 'China' },
    { id: '6', currency: 'Canadian Dollar', code: 'CAD', symbol: 'C$', country: 'Canada' },
    { id: '7', currency: 'Australian Dollar', code: 'AUD', symbol: 'A$', country: 'Australia' },
    { id: '8', currency: 'Swiss Franc', code: 'CHF', symbol: 'CHF', country: 'Switzerland' },
    { id: '9', currency: 'Hong Kong Dollar', code: 'HKD', symbol: 'HK$', country: 'Hong Kong' },
    { id: '10', currency: 'Singapore Dollar', code: 'SGD', symbol: 'S$', country: 'Singapore' },
    { id: '11', currency: 'South Korean Won', code: 'KRW', symbol: '₩', country: 'South Korea' },
    { id: '12', currency: 'Indian Rupee', code: 'INR', symbol: '₹', country: 'India' },
    { id: '13', currency: 'Mexican Peso', code: 'MXN', symbol: '$', country: 'Mexico' },
    { id: '14', currency: 'Brazilian Real', code: 'BRL', symbol: 'R$', country: 'Brazil' },
    { id: '15', currency: 'Russian Ruble', code: 'RUB', symbol: '₽', country: 'Russia' }
  ]
};

// 纸张尺寸对照表
export const paperSizesTable: ReferenceTable = {
  id: 'paper-sizes',
  category: 'technical',
  nameKey: 'paperSizesTable',
  descriptionKey: 'paperSizesTableDesc',
  columns: [
    { key: 'name', labelKey: 'paperSizeName', width: '25%', searchable: true },
    { key: 'widthMm', labelKey: 'widthMm', width: '20%', searchable: true },
    { key: 'heightMm', labelKey: 'heightMm', width: '20%', searchable: true },
    { key: 'widthIn', labelKey: 'widthInches', width: '17.5%', searchable: true },
    { key: 'heightIn', labelKey: 'heightInches', width: '17.5%', searchable: true }
  ],
  data: [
    { id: '1', name: 'A0', widthMm: '841', heightMm: '1189', widthIn: '33.1', heightIn: '46.8' },
    { id: '2', name: 'A1', widthMm: '594', heightMm: '841', widthIn: '23.4', heightIn: '33.1' },
    { id: '3', name: 'A2', widthMm: '420', heightMm: '594', widthIn: '16.5', heightIn: '23.4' },
    { id: '4', name: 'A3', widthMm: '297', heightMm: '420', widthIn: '11.7', heightIn: '16.5' },
    { id: '5', name: 'A4', widthMm: '210', heightMm: '297', widthIn: '8.3', heightIn: '11.7' },
    { id: '6', name: 'A5', widthMm: '148', heightMm: '210', widthIn: '5.8', heightIn: '8.3' },
    { id: '7', name: 'Letter', widthMm: '216', heightMm: '279', widthIn: '8.5', heightIn: '11.0' },
    { id: '8', name: 'Legal', widthMm: '216', heightMm: '356', widthIn: '8.5', heightIn: '14.0' },
    { id: '9', name: 'Tabloid', widthMm: '279', heightMm: '432', widthIn: '11.0', heightIn: '17.0' },
    { id: '10', name: 'Executive', widthMm: '184', heightMm: '267', widthIn: '7.25', heightIn: '10.5' }
  ]
};

// 所有对照表的集合
export const allReferenceTables: ReferenceTable[] = [
  usStatesTable,
  shoeSizesTable,
  incotermsTable,
  currencyCodesTable,
  paperSizesTable
];

// 对照表分类
export const referenceCategories = [
  { id: 'all', nameKey: 'allCategories' },
  { id: 'geography', nameKey: 'geographyCategory' },
  { id: 'sizing', nameKey: 'sizingCategory' },
  { id: 'logistics', nameKey: 'logisticsCategory' },
  { id: 'financial', nameKey: 'financialCategory' },
  { id: 'technical', nameKey: 'technicalCategory' }
];
