// 货币数据配置 - 包含国旗、符号、名称等信息
export interface CurrencyInfo {
  code: string;
  name: string;
  symbol: string;
  flag: string;
  country: string;
}

// 多语言货币名称和国家名称 - 仅包含freecurrencyapi.com支持的货币
const CURRENCY_NAMES: { [key: string]: { [lang: string]: { name: string; country: string } } } = {
  USD: {
    zh: { name: '美元', country: '美国' },
    'zh-TW': { name: '美元', country: '美國' },
    en: { name: 'US Dollar', country: 'United States' },
    ja: { name: '米ドル', country: 'アメリカ' },
    id: { name: 'Dolar AS', country: 'Amerika Serikat' },
    vi: { name: 'Đô la M<PERSON>', country: 'Hoa Kỳ' }
  },
  EUR: {
    zh: { name: '欧元', country: '欧盟' },
    'zh-TW': { name: '歐元', country: '歐盟' },
    en: { name: 'Euro', country: 'European Union' },
    ja: { name: 'ユーロ', country: 'ヨーロッパ連合' },
    id: { name: 'Euro', country: 'Uni Eropa' },
    vi: { name: 'Euro', country: 'Liên minh châu Âu' }
  },
  GBP: {
    zh: { name: '英镑', country: '英国' },
    'zh-TW': { name: '英鎊', country: '英國' },
    en: { name: 'British Pound', country: 'United Kingdom' },
    ja: { name: '英ポンド', country: 'イギリス' },
    id: { name: 'Pound Sterling', country: 'Inggris' },
    vi: { name: 'Bảng Anh', country: 'Vương quốc Anh' }
  },
  JPY: {
    zh: { name: '日元', country: '日本' },
    'zh-TW': { name: '日圓', country: '日本' },
    en: { name: 'Japanese Yen', country: 'Japan' },
    ja: { name: '日本円', country: '日本' },
    id: { name: 'Yen Jepang', country: 'Jepang' },
    vi: { name: 'Yên Nhật', country: 'Nhật Bản' }
  },
  CNY: {
    zh: { name: '人民币', country: '中国' },
    'zh-TW': { name: '人民幣', country: '中國' },
    en: { name: 'Chinese Yuan', country: 'China' },
    ja: { name: '中国元', country: '中国' },
    id: { name: 'Yuan Tiongkok', country: 'Tiongkok' },
    vi: { name: 'Nhân dân tệ', country: 'Trung Quốc' }
  },
  BGN: {
    zh: { name: '保加利亚列弗', country: '保加利亚' },
    'zh-TW': { name: '保加利亞列弗', country: '保加利亞' },
    en: { name: 'Bulgarian Lev', country: 'Bulgaria' },
    ja: { name: 'ブルガリア・レフ', country: 'ブルガリア' },
    id: { name: 'Lev Bulgaria', country: 'Bulgaria' },
    vi: { name: 'Lev Bulgaria', country: 'Bulgaria' }
  },
  CZK: {
    zh: { name: '捷克克朗', country: '捷克' },
    'zh-TW': { name: '捷克克朗', country: '捷克' },
    en: { name: 'Czech Koruna', country: 'Czech Republic' },
    ja: { name: 'チェコ・コルナ', country: 'チェコ' },
    id: { name: 'Koruna Ceko', country: 'Republik Ceko' },
    vi: { name: 'Koruna Séc', country: 'Cộng hòa Séc' }
  },
  DKK: {
    zh: { name: '丹麦克朗', country: '丹麦' },
    'zh-TW': { name: '丹麥克朗', country: '丹麥' },
    en: { name: 'Danish Krone', country: 'Denmark' },
    ja: { name: 'デンマーク・クローネ', country: 'デンマーク' },
    id: { name: 'Krone Denmark', country: 'Denmark' },
    vi: { name: 'Krone Đan Mạch', country: 'Đan Mạch' }
  },
  HUF: {
    zh: { name: '匈牙利福林', country: '匈牙利' },
    'zh-TW': { name: '匈牙利福林', country: '匈牙利' },
    en: { name: 'Hungarian Forint', country: 'Hungary' },
    ja: { name: 'ハンガリー・フォリント', country: 'ハンガリー' },
    id: { name: 'Forint Hungaria', country: 'Hungaria' },
    vi: { name: 'Forint Hungary', country: 'Hungary' }
  },
  PLN: {
    zh: { name: '波兰兹罗提', country: '波兰' },
    'zh-TW': { name: '波蘭茲羅提', country: '波蘭' },
    en: { name: 'Polish Zloty', country: 'Poland' },
    ja: { name: 'ポーランド・ズウォティ', country: 'ポーランド' },
    id: { name: 'Zloty Polandia', country: 'Polandia' },
    vi: { name: 'Zloty Ba Lan', country: 'Ba Lan' }
  },
  RON: {
    zh: { name: '罗马尼亚列伊', country: '罗马尼亚' },
    'zh-TW': { name: '羅馬尼亞列伊', country: '羅馬尼亞' },
    en: { name: 'Romanian Leu', country: 'Romania' },
    ja: { name: 'ルーマニア・レウ', country: 'ルーマニア' },
    id: { name: 'Leu Rumania', country: 'Rumania' },
    vi: { name: 'Leu Romania', country: 'Romania' }
  },
  SEK: {
    zh: { name: '瑞典克朗', country: '瑞典' },
    'zh-TW': { name: '瑞典克朗', country: '瑞典' },
    en: { name: 'Swedish Krona', country: 'Sweden' },
    ja: { name: 'スウェーデン・クローナ', country: 'スウェーデン' },
    id: { name: 'Krona Swedia', country: 'Swedia' },
    vi: { name: 'Krona Thụy Điển', country: 'Thụy Điển' }
  },
  CHF: {
    zh: { name: '瑞士法郎', country: '瑞士' },
    'zh-TW': { name: '瑞士法郎', country: '瑞士' },
    en: { name: 'Swiss Franc', country: 'Switzerland' },
    ja: { name: 'スイス・フラン', country: 'スイス' },
    id: { name: 'Franc Swiss', country: 'Swiss' },
    vi: { name: 'Franc Thụy Sĩ', country: 'Thụy Sĩ' }
  },
  NOK: {
    zh: { name: '挪威克朗', country: '挪威' },
    'zh-TW': { name: '挪威克朗', country: '挪威' },
    en: { name: 'Norwegian Krone', country: 'Norway' },
    ja: { name: 'ノルウェー・クローネ', country: 'ノルウェー' },
    id: { name: 'Krone Norwegia', country: 'Norwegia' },
    vi: { name: 'Krone Na Uy', country: 'Na Uy' }
  },
  RUB: {
    zh: { name: '俄罗斯卢布', country: '俄罗斯' },
    'zh-TW': { name: '俄羅斯盧布', country: '俄羅斯' },
    en: { name: 'Russian Ruble', country: 'Russia' },
    ja: { name: 'ロシア・ルーブル', country: 'ロシア' },
    id: { name: 'Rubel Rusia', country: 'Rusia' },
    vi: { name: 'Ruble Nga', country: 'Nga' }
  },
  TRY: {
    zh: { name: '土耳其里拉', country: '土耳其' },
    'zh-TW': { name: '土耳其里拉', country: '土耳其' },
    en: { name: 'Turkish Lira', country: 'Turkey' },
    ja: { name: 'トルコ・リラ', country: 'トルコ' },
    id: { name: 'Lira Turki', country: 'Turki' },
    vi: { name: 'Lira Thổ Nhĩ Kỳ', country: 'Thổ Nhĩ Kỳ' }
  }
};

// 货币数据定义 - 仅包含freecurrencyapi.com支持的货币
export const CURRENCY_DATA: { [key: string]: CurrencyInfo } = {
  USD: {
    code: 'USD',
    name: '美元',
    symbol: '$',
    flag: '🇺🇸',
    country: '美国'
  },
  EUR: {
    code: 'EUR',
    name: '欧元',
    symbol: '€',
    flag: '🇪🇺',
    country: '欧盟'
  },
  GBP: {
    code: 'GBP',
    name: '英镑',
    symbol: '£',
    flag: '🇬🇧',
    country: '英国'
  },
  JPY: {
    code: 'JPY',
    name: '日元',
    symbol: '¥',
    flag: '🇯🇵',
    country: '日本'
  },
  CNY: {
    code: 'CNY',
    name: '人民币',
    symbol: '¥',
    flag: '🇨🇳',
    country: '中国'
  },
  BGN: {
    code: 'BGN',
    name: '保加利亚列弗',
    symbol: 'лв',
    flag: '🇧🇬',
    country: '保加利亚'
  },
  CZK: {
    code: 'CZK',
    name: '捷克克朗',
    symbol: 'Kč',
    flag: '🇨🇿',
    country: '捷克'
  },
  DKK: {
    code: 'DKK',
    name: '丹麦克朗',
    symbol: 'kr',
    flag: '🇩🇰',
    country: '丹麦'
  },
  HUF: {
    code: 'HUF',
    name: '匈牙利福林',
    symbol: 'Ft',
    flag: '🇭🇺',
    country: '匈牙利'
  },
  PLN: {
    code: 'PLN',
    name: '波兰兹罗提',
    symbol: 'zł',
    flag: '🇵🇱',
    country: '波兰'
  },
  RON: {
    code: 'RON',
    name: '罗马尼亚列伊',
    symbol: 'lei',
    flag: '🇷🇴',
    country: '罗马尼亚'
  },
  SEK: {
    code: 'SEK',
    name: '瑞典克朗',
    symbol: 'kr',
    flag: '🇸🇪',
    country: '瑞典'
  },
  CHF: {
    code: 'CHF',
    name: '瑞士法郎',
    symbol: 'CHF',
    flag: '🇨🇭',
    country: '瑞士'
  },
  ISK: {
    code: 'ISK',
    name: '冰岛克朗',
    symbol: 'kr',
    flag: '🇮🇸',
    country: '冰岛'
  },
  NOK: {
    code: 'NOK',
    name: '挪威克朗',
    symbol: 'kr',
    flag: '🇳🇴',
    country: '挪威'
  },
  HRK: {
    code: 'HRK',
    name: '克罗地亚库纳',
    symbol: 'kn',
    flag: '🇭🇷',
    country: '克罗地亚'
  },
  RUB: {
    code: 'RUB',
    name: '俄罗斯卢布',
    symbol: '₽',
    flag: '🇷🇺',
    country: '俄罗斯'
  },
  TRY: {
    code: 'TRY',
    name: '土耳其里拉',
    symbol: '₺',
    flag: '🇹🇷',
    country: '土耳其'
  },
  AUD: {
    code: 'AUD',
    name: '澳元',
    symbol: 'A$',
    flag: '🇦🇺',
    country: '澳大利亚'
  },
  BRL: {
    code: 'BRL',
    name: '巴西雷亚尔',
    symbol: 'R$',
    flag: '🇧🇷',
    country: '巴西'
  },
  CAD: {
    code: 'CAD',
    name: '加拿大元',
    symbol: 'C$',
    flag: '🇨🇦',
    country: '加拿大'
  },
  HKD: {
    code: 'HKD',
    name: '港币',
    symbol: 'HK$',
    flag: '🇭🇰',
    country: '香港'
  },
  IDR: {
    code: 'IDR',
    name: '印尼盾',
    symbol: 'Rp',
    flag: '🇮🇩',
    country: '印度尼西亚'
  },
  ILS: {
    code: 'ILS',
    name: '以色列新谢克尔',
    symbol: '₪',
    flag: '🇮🇱',
    country: '以色列'
  },
  INR: {
    code: 'INR',
    name: '印度卢比',
    symbol: '₹',
    flag: '🇮🇳',
    country: '印度'
  },
  KRW: {
    code: 'KRW',
    name: '韩元',
    symbol: '₩',
    flag: '🇰🇷',
    country: '韩国'
  },
  MXN: {
    code: 'MXN',
    name: '墨西哥比索',
    symbol: '$',
    flag: '🇲🇽',
    country: '墨西哥'
  },
  MYR: {
    code: 'MYR',
    name: '马来西亚林吉特',
    symbol: 'RM',
    flag: '🇲🇾',
    country: '马来西亚'
  },
  NZD: {
    code: 'NZD',
    name: '新西兰元',
    symbol: 'NZ$',
    flag: '🇳🇿',
    country: '新西兰'
  },
  PHP: {
    code: 'PHP',
    name: '菲律宾比索',
    symbol: '₱',
    flag: '🇵🇭',
    country: '菲律宾'
  },
  SGD: {
    code: 'SGD',
    name: '新加坡元',
    symbol: 'S$',
    flag: '🇸🇬',
    country: '新加坡'
  },
  THB: {
    code: 'THB',
    name: '泰铢',
    symbol: '฿',
    flag: '🇹🇭',
    country: '泰国'
  },
  ZAR: {
    code: 'ZAR',
    name: '南非兰特',
    symbol: 'R',
    flag: '🇿🇦',
    country: '南非'
  }
};

// 获取货币信息（支持多语言）
export const getCurrencyInfo = (code: string, language: string = 'zh'): CurrencyInfo => {
  const baseCurrency = CURRENCY_DATA[code];
  if (!baseCurrency) {
    return {
      code,
      name: code,
      symbol: code,
      flag: '🌍',
      country: language === 'zh' ? '未知' : 'Unknown'
    };
  }

  const localizedNames = CURRENCY_NAMES[code];
  if (localizedNames && localizedNames[language]) {
    return {
      ...baseCurrency,
      name: localizedNames[language].name,
      country: localizedNames[language].country
    };
  }

  // 如果没有找到对应语言，返回默认的中文版本
  return baseCurrency;
};

// 格式化金额显示
export const formatCurrencyAmount = (amount: number, currencyCode: string): string => {
  const info = getCurrencyInfo(currencyCode);
  
  if (amount >= 1000000) {
    return `${info.symbol}${(amount / 1000000).toFixed(2)}M`;
  } else if (amount >= 1000) {
    return `${info.symbol}${(amount / 1000).toFixed(2)}K`;
  } else if (amount >= 1) {
    return `${info.symbol}${amount.toFixed(2)}`;
  } else {
    return `${info.symbol}${amount.toFixed(4)}`;
  }
};

// 热门货币列表（用于快速选择） - 仅包含freecurrencyapi.com支持的货币
export const POPULAR_CURRENCIES = ['USD', 'EUR', 'GBP', 'JPY', 'CNY', 'CAD', 'AUD', 'CHF', 'HKD', 'SGD'];

// 所有支持的货币列表 - 基于freecurrencyapi.com的支持列表
export const SUPPORTED_CURRENCIES = [
  'USD', 'EUR', 'JPY', 'BGN', 'CZK', 'DKK', 'GBP', 'HUF', 'PLN', 'RON', 'SEK', 'CHF',
  'ISK', 'NOK', 'HRK', 'RUB', 'TRY', 'AUD', 'BRL', 'CAD', 'CNY', 'HKD', 'IDR', 'ILS',
  'INR', 'KRW', 'MXN', 'MYR', 'NZD', 'PHP', 'SGD', 'THB', 'ZAR'
];
