// 多语言对照表数据 - 2025年7月最新数据
import { Language } from '../locales';

export interface MultilingualReferenceTableItem {
  id: string;
  [key: string]: string | number | Record<Language, string>;
}

export interface MultilingualReferenceTable {
  id: string;
  category: string;
  nameKey: string;
  descriptionKey: string;
  columns: {
    key: string;
    labelKey: string;
    width?: string;
    searchable?: boolean;
    isMultilingual?: boolean;
  }[];
  data: MultilingualReferenceTableItem[];
}

// 美国州名缩写对照表 - 2025年最新数据
export const usStatesMultilingualTable: MultilingualReferenceTable = {
  id: 'us-states',
  category: 'geography',
  nameKey: 'usStatesTable',
  descriptionKey: 'usStatesTableDesc',
  columns: [
    { key: 'stateName', labelKey: 'stateName', width: '25%', searchable: true },
    { key: 'localName', labelKey: 'localName', width: '25%', searchable: true, isMultilingual: true },
    { key: 'abbreviation2', labelKey: 'abbreviation2Letter', width: '15%', searchable: true },
    { key: 'abbreviation3', labelKey: 'abbreviation3Letter', width: '15%', searchable: true },
    { key: 'capital', labelKey: 'capital', width: '20%', searchable: true }
  ],
  data: [
    { 
      id: '1', 
      stateName: 'Alabama', 
      localName: {
        zh: '阿拉巴马州',
        'zh-TW': '阿拉巴馬州',
        en: 'Alabama',
        ja: 'アラバマ州',
        id: 'Alabama',
        vi: 'Alabama'
      },
      abbreviation2: 'AL', 
      abbreviation3: 'ALA', 
      capital: 'Montgomery' 
    },
    { 
      id: '2', 
      stateName: 'Alaska', 
      localName: {
        zh: '阿拉斯加州',
        'zh-TW': '阿拉斯加州',
        en: 'Alaska',
        ja: 'アラスカ州',
        id: 'Alaska',
        vi: 'Alaska'
      },
      abbreviation2: 'AK', 
      abbreviation3: 'AK', 
      capital: 'Juneau' 
    },
    { 
      id: '3', 
      stateName: 'Arizona', 
      localName: {
        zh: '亚利桑那州',
        'zh-TW': '亞利桑那州',
        en: 'Arizona',
        ja: 'アリゾナ州',
        id: 'Arizona',
        vi: 'Arizona'
      },
      abbreviation2: 'AZ', 
      abbreviation3: 'ARIZ', 
      capital: 'Phoenix' 
    },
    { 
      id: '4', 
      stateName: 'Arkansas', 
      localName: {
        zh: '阿肯色州',
        'zh-TW': '阿肯色州',
        en: 'Arkansas',
        ja: 'アーカンソー州',
        id: 'Arkansas',
        vi: 'Arkansas'
      },
      abbreviation2: 'AR', 
      abbreviation3: 'ARK', 
      capital: 'Little Rock' 
    },
    { 
      id: '5', 
      stateName: 'California', 
      localName: {
        zh: '加利福尼亚州',
        'zh-TW': '加利福尼亞州',
        en: 'California',
        ja: 'カリフォルニア州',
        id: 'California',
        vi: 'California'
      },
      abbreviation2: 'CA', 
      abbreviation3: 'CALIF', 
      capital: 'Sacramento' 
    },
    { 
      id: '6', 
      stateName: 'Colorado', 
      localName: {
        zh: '科罗拉多州',
        'zh-TW': '科羅拉多州',
        en: 'Colorado',
        ja: 'コロラド州',
        id: 'Colorado',
        vi: 'Colorado'
      },
      abbreviation2: 'CO', 
      abbreviation3: 'COLO', 
      capital: 'Denver' 
    },
    { 
      id: '7', 
      stateName: 'Connecticut', 
      localName: {
        zh: '康涅狄格州',
        'zh-TW': '康涅狄格州',
        en: 'Connecticut',
        ja: 'コネチカット州',
        id: 'Connecticut',
        vi: 'Connecticut'
      },
      abbreviation2: 'CT', 
      abbreviation3: 'CONN', 
      capital: 'Hartford' 
    },
    { 
      id: '8', 
      stateName: 'Delaware', 
      localName: {
        zh: '特拉华州',
        'zh-TW': '德拉瓦州',
        en: 'Delaware',
        ja: 'デラウェア州',
        id: 'Delaware',
        vi: 'Delaware'
      },
      abbreviation2: 'DE', 
      abbreviation3: 'DEL', 
      capital: 'Dover' 
    },
    { 
      id: '9', 
      stateName: 'Florida', 
      localName: {
        zh: '佛罗里达州',
        'zh-TW': '佛羅里達州',
        en: 'Florida',
        ja: 'フロリダ州',
        id: 'Florida',
        vi: 'Florida'
      },
      abbreviation2: 'FL', 
      abbreviation3: 'FLA', 
      capital: 'Tallahassee' 
    },
    { 
      id: '10', 
      stateName: 'Georgia', 
      localName: {
        zh: '佐治亚州',
        'zh-TW': '喬治亞州',
        en: 'Georgia',
        ja: 'ジョージア州',
        id: 'Georgia',
        vi: 'Georgia'
      },
      abbreviation2: 'GA', 
      abbreviation3: 'GA', 
      capital: 'Atlanta'
    },
    {
      id: '11',
      stateName: 'Hawaii',
      localName: {
        zh: '夏威夷州',
        'zh-TW': '夏威夷州',
        en: 'Hawaii',
        ja: 'ハワイ州',
        id: 'Hawaii',
        vi: 'Hawaii'
      },
      abbreviation2: 'HI',
      abbreviation3: 'HAWAII',
      capital: 'Honolulu'
    },
    {
      id: '12',
      stateName: 'Idaho',
      localName: {
        zh: '爱达荷州',
        'zh-TW': '愛達荷州',
        en: 'Idaho',
        ja: 'アイダホ州',
        id: 'Idaho',
        vi: 'Idaho'
      },
      abbreviation2: 'ID',
      abbreviation3: 'IDAHO',
      capital: 'Boise'
    },
    {
      id: '13',
      stateName: 'Illinois',
      localName: {
        zh: '伊利诺伊州',
        'zh-TW': '伊利諾州',
        en: 'Illinois',
        ja: 'イリノイ州',
        id: 'Illinois',
        vi: 'Illinois'
      },
      abbreviation2: 'IL',
      abbreviation3: 'ILL',
      capital: 'Springfield'
    },
    {
      id: '14',
      stateName: 'Indiana',
      localName: {
        zh: '印第安纳州',
        'zh-TW': '印第安納州',
        en: 'Indiana',
        ja: 'インディアナ州',
        id: 'Indiana',
        vi: 'Indiana'
      },
      abbreviation2: 'IN',
      abbreviation3: 'IND',
      capital: 'Indianapolis'
    },
    {
      id: '15',
      stateName: 'Iowa',
      localName: {
        zh: '爱荷华州',
        'zh-TW': '愛荷華州',
        en: 'Iowa',
        ja: 'アイオワ州',
        id: 'Iowa',
        vi: 'Iowa'
      },
      abbreviation2: 'IA',
      abbreviation3: 'IOWA',
      capital: 'Des Moines'
    },
    {
      id: '16',
      stateName: 'Kansas',
      localName: {
        zh: '堪萨斯州',
        'zh-TW': '堪薩斯州',
        en: 'Kansas',
        ja: 'カンザス州',
        id: 'Kansas',
        vi: 'Kansas'
      },
      abbreviation2: 'KS',
      abbreviation3: 'KANS',
      capital: 'Topeka'
    },
    {
      id: '17',
      stateName: 'Kentucky',
      localName: {
        zh: '肯塔基州',
        'zh-TW': '肯塔基州',
        en: 'Kentucky',
        ja: 'ケンタッキー州',
        id: 'Kentucky',
        vi: 'Kentucky'
      },
      abbreviation2: 'KY',
      abbreviation3: 'KY',
      capital: 'Frankfort'
    },
    {
      id: '18',
      stateName: 'Louisiana',
      localName: {
        zh: '路易斯安那州',
        'zh-TW': '路易斯安那州',
        en: 'Louisiana',
        ja: 'ルイジアナ州',
        id: 'Louisiana',
        vi: 'Louisiana'
      },
      abbreviation2: 'LA',
      abbreviation3: 'LA',
      capital: 'Baton Rouge'
    },
    {
      id: '19',
      stateName: 'Maine',
      localName: {
        zh: '缅因州',
        'zh-TW': '緬因州',
        en: 'Maine',
        ja: 'メイン州',
        id: 'Maine',
        vi: 'Maine'
      },
      abbreviation2: 'ME',
      abbreviation3: 'MAINE',
      capital: 'Augusta'
    },
    {
      id: '20',
      stateName: 'Maryland',
      localName: {
        zh: '马里兰州',
        'zh-TW': '馬里蘭州',
        en: 'Maryland',
        ja: 'メリーランド州',
        id: 'Maryland',
        vi: 'Maryland'
      },
      abbreviation2: 'MD',
      abbreviation3: 'MD',
      capital: 'Annapolis'
    },
    {
      id: '21',
      stateName: 'Massachusetts',
      localName: {
        zh: '马萨诸塞州',
        'zh-TW': '麻薩諸塞州',
        en: 'Massachusetts',
        ja: 'マサチューセッツ州',
        id: 'Massachusetts',
        vi: 'Massachusetts'
      },
      abbreviation2: 'MA',
      abbreviation3: 'MASS',
      capital: 'Boston'
    },
    {
      id: '22',
      stateName: 'Michigan',
      localName: {
        zh: '密歇根州',
        'zh-TW': '密西根州',
        en: 'Michigan',
        ja: 'ミシガン州',
        id: 'Michigan',
        vi: 'Michigan'
      },
      abbreviation2: 'MI',
      abbreviation3: 'MICH',
      capital: 'Lansing'
    },
    {
      id: '23',
      stateName: 'Minnesota',
      localName: {
        zh: '明尼苏达州',
        'zh-TW': '明尼蘇達州',
        en: 'Minnesota',
        ja: 'ミネソタ州',
        id: 'Minnesota',
        vi: 'Minnesota'
      },
      abbreviation2: 'MN',
      abbreviation3: 'MINN',
      capital: 'Saint Paul'
    },
    {
      id: '24',
      stateName: 'Nevada',
      localName: {
        zh: '内华达州',
        'zh-TW': '內華達州',
        en: 'Nevada',
        ja: 'ネバダ州',
        id: 'Nevada',
        vi: 'Nevada'
      },
      abbreviation2: 'NV',
      abbreviation3: 'NEV',
      capital: 'Carson City'
    },
    {
      id: '25',
      stateName: 'New York',
      localName: {
        zh: '纽约州',
        'zh-TW': '紐約州',
        en: 'New York',
        ja: 'ニューヨーク州',
        id: 'New York',
        vi: 'New York'
      },
      abbreviation2: 'NY',
      abbreviation3: 'NY',
      capital: 'Albany'
    },
    {
      id: '26',
      stateName: 'Texas',
      localName: {
        zh: '得克萨斯州',
        'zh-TW': '德克薩斯州',
        en: 'Texas',
        ja: 'テキサス州',
        id: 'Texas',
        vi: 'Texas'
      },
      abbreviation2: 'TX',
      abbreviation3: 'TEX',
      capital: 'Austin'
    },
    {
      id: '27',
      stateName: 'Washington',
      localName: {
        zh: '华盛顿州',
        'zh-TW': '華盛頓州',
        en: 'Washington',
        ja: 'ワシントン州',
        id: 'Washington',
        vi: 'Washington'
      },
      abbreviation2: 'WA',
      abbreviation3: 'WASH',
      capital: 'Olympia'
    }
  ]
};

// 全球鞋码对照表 - 2025年最新标准
export const shoeSizesMultilingualTable: MultilingualReferenceTable = {
  id: 'shoe-sizes',
  category: 'sizing',
  nameKey: 'shoeSizesTable',
  descriptionKey: 'shoeSizesTableDesc',
  columns: [
    { key: 'us', labelKey: 'usShoeSizes', width: '16%', searchable: true },
    { key: 'eu', labelKey: 'euShoeSizes', width: '16%', searchable: true },
    { key: 'uk', labelKey: 'ukShoeSizes', width: '16%', searchable: true },
    { key: 'cn', labelKey: 'cnShoeSizes', width: '16%', searchable: true },
    { key: 'jp', labelKey: 'jpShoeSizes', width: '16%', searchable: true },
    { key: 'footLength', labelKey: 'footLength', width: '20%', searchable: true }
  ],
  data: [
    { id: '1', us: '4', eu: '35', uk: '2', cn: '220', jp: '22', footLength: '22.0 cm' },
    { id: '2', us: '4.5', eu: '35.5', uk: '2.5', cn: '225', jp: '22.5', footLength: '22.5 cm' },
    { id: '3', us: '5', eu: '36', uk: '3', cn: '230', jp: '23', footLength: '23.0 cm' },
    { id: '4', us: '5.5', eu: '37', uk: '3.5', cn: '235', jp: '23.5', footLength: '23.5 cm' },
    { id: '5', us: '6', eu: '37.5', uk: '4', cn: '240', jp: '24', footLength: '24.0 cm' },
    { id: '6', us: '6.5', eu: '38', uk: '4.5', cn: '245', jp: '24.5', footLength: '24.5 cm' },
    { id: '7', us: '7', eu: '38.5', uk: '5', cn: '250', jp: '25', footLength: '25.0 cm' },
    { id: '8', us: '7.5', eu: '39', uk: '5.5', cn: '255', jp: '25.5', footLength: '25.5 cm' },
    { id: '9', us: '8', eu: '40', uk: '6', cn: '260', jp: '26', footLength: '26.0 cm' },
    { id: '10', us: '8.5', eu: '41', uk: '6.5', cn: '265', jp: '26.5', footLength: '26.5 cm' },
    { id: '11', us: '9', eu: '42', uk: '7', cn: '270', jp: '27', footLength: '27.0 cm' },
    { id: '12', us: '9.5', eu: '42.5', uk: '7.5', cn: '275', jp: '27.5', footLength: '27.5 cm' },
    { id: '13', us: '10', eu: '43', uk: '8', cn: '280', jp: '28', footLength: '28.0 cm' },
    { id: '14', us: '10.5', eu: '44', uk: '8.5', cn: '285', jp: '28.5', footLength: '28.5 cm' },
    { id: '15', us: '11', eu: '44.5', uk: '9', cn: '290', jp: '29', footLength: '29.0 cm' },
    { id: '16', us: '11.5', eu: '45', uk: '9.5', cn: '295', jp: '29.5', footLength: '29.5 cm' },
    { id: '17', us: '12', eu: '46', uk: '10', cn: '300', jp: '30', footLength: '30.0 cm' },
    { id: '18', us: '12.5', eu: '46.5', uk: '10.5', cn: '305', jp: '30.5', footLength: '30.5 cm' },
    { id: '19', us: '13', eu: '47', uk: '11', cn: '310', jp: '31', footLength: '31.0 cm' },
    { id: '20', us: '14', eu: '48', uk: '12', cn: '320', jp: '32', footLength: '32.0 cm' }
  ]
};

// 2025年最新货币代码对照表
export const currencyCodesMultilingualTable: MultilingualReferenceTable = {
  id: 'currency-codes',
  category: 'financial',
  nameKey: 'currencyCodesTable',
  descriptionKey: 'currencyCodesTableDesc',
  columns: [
    { key: 'currency', labelKey: 'currencyName', width: '30%', searchable: true },
    { key: 'localName', labelKey: 'localName', width: '30%', searchable: true, isMultilingual: true },
    { key: 'code', labelKey: 'currencyCode', width: '15%', searchable: true },
    { key: 'symbol', labelKey: 'currencySymbol', width: '15%', searchable: true },
    { key: 'country', labelKey: 'primaryCountry', width: '10%', searchable: true }
  ],
  data: [
    {
      id: '1',
      currency: 'US Dollar',
      localName: {
        zh: '美元',
        'zh-TW': '美元',
        en: 'US Dollar',
        ja: '米ドル',
        id: 'Dolar AS',
        vi: 'Đô la Mỹ'
      },
      code: 'USD',
      symbol: '$',
      country: 'US'
    },
    {
      id: '2',
      currency: 'Euro',
      localName: {
        zh: '欧元',
        'zh-TW': '歐元',
        en: 'Euro',
        ja: 'ユーロ',
        id: 'Euro',
        vi: 'Euro'
      },
      code: 'EUR',
      symbol: '€',
      country: 'EU'
    },
    {
      id: '3',
      currency: 'British Pound',
      localName: {
        zh: '英镑',
        'zh-TW': '英鎊',
        en: 'British Pound',
        ja: '英ポンド',
        id: 'Pound Sterling',
        vi: 'Bảng Anh'
      },
      code: 'GBP',
      symbol: '£',
      country: 'UK'
    },
    {
      id: '4',
      currency: 'Japanese Yen',
      localName: {
        zh: '日元',
        'zh-TW': '日圓',
        en: 'Japanese Yen',
        ja: '日本円',
        id: 'Yen Jepang',
        vi: 'Yên Nhật'
      },
      code: 'JPY',
      symbol: '¥',
      country: 'JP'
    },
    {
      id: '5',
      currency: 'Chinese Yuan',
      localName: {
        zh: '人民币',
        'zh-TW': '人民幣',
        en: 'Chinese Yuan',
        ja: '中国元',
        id: 'Yuan Tiongkok',
        vi: 'Nhân dân tệ'
      },
      code: 'CNY',
      symbol: '¥',
      country: 'CN'
    }
  ]
};

// 国际贸易术语对照表 - Incoterms 2025
export const incotermsMultilingualTable: MultilingualReferenceTable = {
  id: 'incoterms',
  category: 'logistics',
  nameKey: 'incotermsTable',
  descriptionKey: 'incotermsTableDesc',
  columns: [
    { key: 'term', labelKey: 'incotermCode', width: '15%', searchable: true },
    { key: 'fullName', labelKey: 'incotermFullName', width: '25%', searchable: true },
    { key: 'localName', labelKey: 'localName', width: '25%', searchable: true, isMultilingual: true },
    { key: 'responsibility', labelKey: 'sellerResponsibility', width: '20%', searchable: true },
    { key: 'riskTransfer', labelKey: 'riskTransferPoint', width: '15%', searchable: true }
  ],
  data: [
    {
      id: '1',
      term: 'EXW',
      fullName: 'Ex Works',
      localName: {
        zh: '工厂交货',
        'zh-TW': '工廠交貨',
        en: 'Ex Works',
        ja: '工場渡し',
        id: 'Franco Pabrik',
        vi: 'Giao tại xưởng'
      },
      responsibility: 'Minimum seller responsibility',
      riskTransfer: 'Seller premises'
    },
    {
      id: '2',
      term: 'FCA',
      fullName: 'Free Carrier',
      localName: {
        zh: '货交承运人',
        'zh-TW': '貨交承運人',
        en: 'Free Carrier',
        ja: '運送人渡し',
        id: 'Franco Pengangkut',
        vi: 'Giao cho người vận chuyển'
      },
      responsibility: 'Delivery to carrier',
      riskTransfer: 'Named place'
    },
    {
      id: '3',
      term: 'CPT',
      fullName: 'Carriage Paid To',
      localName: {
        zh: '运费付至',
        'zh-TW': '運費付至',
        en: 'Carriage Paid To',
        ja: '輸送費込み',
        id: 'Angkutan Dibayar Sampai',
        vi: 'Trả cước vận chuyển đến'
      },
      responsibility: 'Transport to destination',
      riskTransfer: 'First carrier'
    },
    {
      id: '4',
      term: 'CIP',
      fullName: 'Carriage and Insurance Paid To',
      localName: {
        zh: '运费保险费付至',
        'zh-TW': '運費保險費付至',
        en: 'Carriage and Insurance Paid To',
        ja: '輸送費保険料込み',
        id: 'Angkutan dan Asuransi Dibayar',
        vi: 'Trả cước và bảo hiểm đến'
      },
      responsibility: 'Transport + insurance',
      riskTransfer: 'First carrier'
    },
    {
      id: '5',
      term: 'DAP',
      fullName: 'Delivered at Place',
      localName: {
        zh: '目的地交货',
        'zh-TW': '目的地交貨',
        en: 'Delivered at Place',
        ja: '仕向地持込渡し',
        id: 'Diserahkan di Tempat',
        vi: 'Giao tại địa điểm'
      },
      responsibility: 'Delivery ready for unloading',
      riskTransfer: 'Named destination'
    },
    {
      id: '6',
      term: 'DPU',
      fullName: 'Delivered at Place Unloaded',
      localName: {
        zh: '卸货地交货',
        'zh-TW': '卸貨地交貨',
        en: 'Delivered at Place Unloaded',
        ja: '荷卸込持込渡し',
        id: 'Diserahkan di Tempat Dibongkar',
        vi: 'Giao tại nơi đã dỡ hàng'
      },
      responsibility: 'Delivery and unloading',
      riskTransfer: 'After unloading'
    },
    {
      id: '7',
      term: 'DDP',
      fullName: 'Delivered Duty Paid',
      localName: {
        zh: '完税后交货',
        'zh-TW': '完稅後交貨',
        en: 'Delivered Duty Paid',
        ja: '関税込持込渡し',
        id: 'Diserahkan Bea Dibayar',
        vi: 'Giao đã trả thuế'
      },
      responsibility: 'Maximum seller responsibility',
      riskTransfer: 'Final destination'
    },
    {
      id: '8',
      term: 'FAS',
      fullName: 'Free Alongside Ship',
      localName: {
        zh: '船边交货',
        'zh-TW': '船邊交貨',
        en: 'Free Alongside Ship',
        ja: '船側渡し',
        id: 'Franco di Samping Kapal',
        vi: 'Giao bên cạnh tàu'
      },
      responsibility: 'Delivery alongside vessel',
      riskTransfer: 'Port of shipment'
    },
    {
      id: '9',
      term: 'FOB',
      fullName: 'Free on Board',
      localName: {
        zh: '船上交货',
        'zh-TW': '船上交貨',
        en: 'Free on Board',
        ja: '本船渡し',
        id: 'Franco di Atas Kapal',
        vi: 'Giao trên tàu'
      },
      responsibility: 'Loading on vessel',
      riskTransfer: 'On board vessel'
    },
    {
      id: '10',
      term: 'CFR',
      fullName: 'Cost and Freight',
      localName: {
        zh: '成本加运费',
        'zh-TW': '成本加運費',
        en: 'Cost and Freight',
        ja: '運賃込み',
        id: 'Biaya dan Angkutan',
        vi: 'Chi phí và cước phí'
      },
      responsibility: 'Sea freight to destination',
      riskTransfer: 'On board vessel'
    },
    {
      id: '11',
      term: 'CIF',
      fullName: 'Cost, Insurance and Freight',
      localName: {
        zh: '成本保险费加运费',
        'zh-TW': '成本保險費加運費',
        en: 'Cost, Insurance and Freight',
        ja: '運賃保険料込み',
        id: 'Biaya, Asuransi dan Angkutan',
        vi: 'Chi phí, bảo hiểm và cước phí'
      },
      responsibility: 'Sea freight + insurance',
      riskTransfer: 'On board vessel'
    }
  ]
};

// 纸张尺寸对照表 - 2025年国际标准
export const paperSizesMultilingualTable: MultilingualReferenceTable = {
  id: 'paper-sizes',
  category: 'technical',
  nameKey: 'paperSizesTable',
  descriptionKey: 'paperSizesTableDesc',
  columns: [
    { key: 'name', labelKey: 'paperSizeName', width: '20%', searchable: true },
    { key: 'localName', labelKey: 'localName', width: '20%', searchable: true, isMultilingual: true },
    { key: 'widthMm', labelKey: 'widthMm', width: '15%', searchable: true },
    { key: 'heightMm', labelKey: 'heightMm', width: '15%', searchable: true },
    { key: 'widthIn', labelKey: 'widthInches', width: '15%', searchable: true },
    { key: 'heightIn', labelKey: 'heightInches', width: '15%', searchable: true }
  ],
  data: [
    {
      id: '1',
      name: 'A0',
      localName: {
        zh: 'A0纸',
        'zh-TW': 'A0紙',
        en: 'A0',
        ja: 'A0サイズ',
        id: 'A0',
        vi: 'Khổ A0'
      },
      widthMm: '841',
      heightMm: '1189',
      widthIn: '33.1',
      heightIn: '46.8'
    },
    {
      id: '2',
      name: 'A1',
      localName: {
        zh: 'A1纸',
        'zh-TW': 'A1紙',
        en: 'A1',
        ja: 'A1サイズ',
        id: 'A1',
        vi: 'Khổ A1'
      },
      widthMm: '594',
      heightMm: '841',
      widthIn: '23.4',
      heightIn: '33.1'
    },
    {
      id: '3',
      name: 'A2',
      localName: {
        zh: 'A2纸',
        'zh-TW': 'A2紙',
        en: 'A2',
        ja: 'A2サイズ',
        id: 'A2',
        vi: 'Khổ A2'
      },
      widthMm: '420',
      heightMm: '594',
      widthIn: '16.5',
      heightIn: '23.4'
    },
    {
      id: '4',
      name: 'A3',
      localName: {
        zh: 'A3纸',
        'zh-TW': 'A3紙',
        en: 'A3',
        ja: 'A3サイズ',
        id: 'A3',
        vi: 'Khổ A3'
      },
      widthMm: '297',
      heightMm: '420',
      widthIn: '11.7',
      heightIn: '16.5'
    },
    {
      id: '5',
      name: 'A4',
      localName: {
        zh: 'A4纸',
        'zh-TW': 'A4紙',
        en: 'A4',
        ja: 'A4サイズ',
        id: 'A4',
        vi: 'Khổ A4'
      },
      widthMm: '210',
      heightMm: '297',
      widthIn: '8.3',
      heightIn: '11.7'
    },
    {
      id: '6',
      name: 'A5',
      localName: {
        zh: 'A5纸',
        'zh-TW': 'A5紙',
        en: 'A5',
        ja: 'A5サイズ',
        id: 'A5',
        vi: 'Khổ A5'
      },
      widthMm: '148',
      heightMm: '210',
      widthIn: '5.8',
      heightIn: '8.3'
    },
    {
      id: '7',
      name: 'Letter',
      localName: {
        zh: '信纸',
        'zh-TW': '信紙',
        en: 'Letter',
        ja: 'レターサイズ',
        id: 'Letter',
        vi: 'Khổ Letter'
      },
      widthMm: '216',
      heightMm: '279',
      widthIn: '8.5',
      heightIn: '11.0'
    },
    {
      id: '8',
      name: 'Legal',
      localName: {
        zh: '法律文件纸',
        'zh-TW': '法律文件紙',
        en: 'Legal',
        ja: 'リーガルサイズ',
        id: 'Legal',
        vi: 'Khổ Legal'
      },
      widthMm: '216',
      heightMm: '356',
      widthIn: '8.5',
      heightIn: '14.0'
    },
    {
      id: '9',
      name: 'Tabloid',
      localName: {
        zh: '小报纸',
        'zh-TW': '小報紙',
        en: 'Tabloid',
        ja: 'タブロイドサイズ',
        id: 'Tabloid',
        vi: 'Khổ Tabloid'
      },
      widthMm: '279',
      heightMm: '432',
      widthIn: '11.0',
      heightIn: '17.0'
    },
    {
      id: '10',
      name: 'Executive',
      localName: {
        zh: '行政纸',
        'zh-TW': '行政紙',
        en: 'Executive',
        ja: 'エグゼクティブサイズ',
        id: 'Executive',
        vi: 'Khổ Executive'
      },
      widthMm: '184',
      heightMm: '267',
      widthIn: '7.25',
      heightIn: '10.5'
    }
  ]
};

// 国家代码对照表 - 2025年ISO标准
export const countryCodesMultilingualTable: MultilingualReferenceTable = {
  id: 'country-codes',
  category: 'geography',
  nameKey: 'countryCodesTable',
  descriptionKey: 'countryCodesTableDesc',
  columns: [
    { key: 'countryName', labelKey: 'countryName', width: '30%', searchable: true },
    { key: 'localName', labelKey: 'localName', width: '30%', searchable: true, isMultilingual: true },
    { key: 'iso2', labelKey: 'iso2Code', width: '15%', searchable: true },
    { key: 'iso3', labelKey: 'iso3Code', width: '15%', searchable: true },
    { key: 'numericCode', labelKey: 'numericCode', width: '10%', searchable: true }
  ],
  data: [
    {
      id: '1',
      countryName: 'United States',
      localName: {
        zh: '美国',
        'zh-TW': '美國',
        en: 'United States',
        ja: 'アメリカ合衆国',
        id: 'Amerika Serikat',
        vi: 'Hoa Kỳ'
      },
      iso2: 'US',
      iso3: 'USA',
      numericCode: '840'
    },
    {
      id: '2',
      countryName: 'China',
      localName: {
        zh: '中国',
        'zh-TW': '中國',
        en: 'China',
        ja: '中国',
        id: 'Tiongkok',
        vi: 'Trung Quốc'
      },
      iso2: 'CN',
      iso3: 'CHN',
      numericCode: '156'
    },
    {
      id: '3',
      countryName: 'Japan',
      localName: {
        zh: '日本',
        'zh-TW': '日本',
        en: 'Japan',
        ja: '日本',
        id: 'Jepang',
        vi: 'Nhật Bản'
      },
      iso2: 'JP',
      iso3: 'JPN',
      numericCode: '392'
    },
    {
      id: '4',
      countryName: 'Germany',
      localName: {
        zh: '德国',
        'zh-TW': '德國',
        en: 'Germany',
        ja: 'ドイツ',
        id: 'Jerman',
        vi: 'Đức'
      },
      iso2: 'DE',
      iso3: 'DEU',
      numericCode: '276'
    },
    {
      id: '5',
      countryName: 'United Kingdom',
      localName: {
        zh: '英国',
        'zh-TW': '英國',
        en: 'United Kingdom',
        ja: 'イギリス',
        id: 'Inggris',
        vi: 'Vương quốc Anh'
      },
      iso2: 'GB',
      iso3: 'GBR',
      numericCode: '826'
    },
    {
      id: '6',
      countryName: 'France',
      localName: {
        zh: '法国',
        'zh-TW': '法國',
        en: 'France',
        ja: 'フランス',
        id: 'Prancis',
        vi: 'Pháp'
      },
      iso2: 'FR',
      iso3: 'FRA',
      numericCode: '250'
    },
    {
      id: '7',
      countryName: 'Canada',
      localName: {
        zh: '加拿大',
        'zh-TW': '加拿大',
        en: 'Canada',
        ja: 'カナダ',
        id: 'Kanada',
        vi: 'Canada'
      },
      iso2: 'CA',
      iso3: 'CAN',
      numericCode: '124'
    },
    {
      id: '8',
      countryName: 'Australia',
      localName: {
        zh: '澳大利亚',
        'zh-TW': '澳大利亞',
        en: 'Australia',
        ja: 'オーストラリア',
        id: 'Australia',
        vi: 'Úc'
      },
      iso2: 'AU',
      iso3: 'AUS',
      numericCode: '036'
    },
    {
      id: '9',
      countryName: 'South Korea',
      localName: {
        zh: '韩国',
        'zh-TW': '韓國',
        en: 'South Korea',
        ja: '韓国',
        id: 'Korea Selatan',
        vi: 'Hàn Quốc'
      },
      iso2: 'KR',
      iso3: 'KOR',
      numericCode: '410'
    },
    {
      id: '10',
      countryName: 'Singapore',
      localName: {
        zh: '新加坡',
        'zh-TW': '新加坡',
        en: 'Singapore',
        ja: 'シンガポール',
        id: 'Singapura',
        vi: 'Singapore'
      },
      iso2: 'SG',
      iso3: 'SGP',
      numericCode: '702'
    }
  ]
};

// 服装尺码对照表 - 2025年国际标准
export const clothingSizesMultilingualTable: MultilingualReferenceTable = {
  id: 'clothing-sizes',
  category: 'sizing',
  nameKey: 'clothingSizesTable',
  descriptionKey: 'clothingSizesTableDesc',
  columns: [
    { key: 'sizeLabel', labelKey: 'sizeLabel', width: '15%', searchable: true },
    { key: 'localName', labelKey: 'localName', width: '15%', searchable: true, isMultilingual: true },
    { key: 'us', labelKey: 'usClothingSize', width: '14%', searchable: true },
    { key: 'eu', labelKey: 'euClothingSize', width: '14%', searchable: true },
    { key: 'uk', labelKey: 'ukClothingSize', width: '14%', searchable: true },
    { key: 'chest', labelKey: 'chestSize', width: '14%', searchable: true },
    { key: 'waist', labelKey: 'waistSize', width: '14%', searchable: true }
  ],
  data: [
    {
      id: '1',
      sizeLabel: 'XS',
      localName: {
        zh: '特小号',
        'zh-TW': '特小號',
        en: 'Extra Small',
        ja: 'エクストラスモール',
        id: 'Ekstra Kecil',
        vi: 'Cực nhỏ'
      },
      us: 'XS',
      eu: '32-34',
      uk: '6',
      chest: '81-86 cm',
      waist: '66-71 cm'
    },
    {
      id: '2',
      sizeLabel: 'S',
      localName: {
        zh: '小号',
        'zh-TW': '小號',
        en: 'Small',
        ja: 'スモール',
        id: 'Kecil',
        vi: 'Nhỏ'
      },
      us: 'S',
      eu: '36-38',
      uk: '8-10',
      chest: '86-91 cm',
      waist: '71-76 cm'
    },
    {
      id: '3',
      sizeLabel: 'M',
      localName: {
        zh: '中号',
        'zh-TW': '中號',
        en: 'Medium',
        ja: 'ミディアム',
        id: 'Sedang',
        vi: 'Vừa'
      },
      us: 'M',
      eu: '40-42',
      uk: '12-14',
      chest: '91-96 cm',
      waist: '76-81 cm'
    },
    {
      id: '4',
      sizeLabel: 'L',
      localName: {
        zh: '大号',
        'zh-TW': '大號',
        en: 'Large',
        ja: 'ラージ',
        id: 'Besar',
        vi: 'Lớn'
      },
      us: 'L',
      eu: '44-46',
      uk: '16-18',
      chest: '96-101 cm',
      waist: '81-86 cm'
    },
    {
      id: '5',
      sizeLabel: 'XL',
      localName: {
        zh: '特大号',
        'zh-TW': '特大號',
        en: 'Extra Large',
        ja: 'エクストララージ',
        id: 'Ekstra Besar',
        vi: 'Cực lớn'
      },
      us: 'XL',
      eu: '48-50',
      uk: '20-22',
      chest: '101-106 cm',
      waist: '86-91 cm'
    },
    {
      id: '6',
      sizeLabel: 'XXL',
      localName: {
        zh: '加大号',
        'zh-TW': '加大號',
        en: 'Double Extra Large',
        ja: 'ダブルエクストララージ',
        id: 'Ekstra Besar Ganda',
        vi: 'Cực lớn đôi'
      },
      us: 'XXL',
      eu: '52-54',
      uk: '24-26',
      chest: '106-111 cm',
      waist: '91-96 cm'
    },
    {
      id: '7',
      sizeLabel: 'XXXL',
      localName: {
        zh: '超大号',
        'zh-TW': '超大號',
        en: 'Triple Extra Large',
        ja: 'トリプルエクストララージ',
        id: 'Ekstra Besar Tiga',
        vi: 'Cực lớn ba'
      },
      us: 'XXXL',
      eu: '56-58',
      uk: '28-30',
      chest: '111-116 cm',
      waist: '96-101 cm'
    }
  ]
};

// 电池型号对照表 - 2025年常用标准
export const batteryTypesMultilingualTable: MultilingualReferenceTable = {
  id: 'battery-types',
  category: 'technical',
  nameKey: 'batteryTypesTable',
  descriptionKey: 'batteryTypesTableDesc',
  columns: [
    { key: 'commonName', labelKey: 'commonName', width: '20%', searchable: true },
    { key: 'localName', labelKey: 'localName', width: '20%', searchable: true, isMultilingual: true },
    { key: 'iecName', labelKey: 'iecName', width: '15%', searchable: true },
    { key: 'ansiName', labelKey: 'ansiName', width: '15%', searchable: true },
    { key: 'voltage', labelKey: 'voltage', width: '15%', searchable: true },
    { key: 'dimensions', labelKey: 'dimensions', width: '15%', searchable: true }
  ],
  data: [
    {
      id: '1',
      commonName: 'AA',
      localName: {
        zh: '5号电池',
        'zh-TW': '5號電池',
        en: 'AA Battery',
        ja: '単3電池',
        id: 'Baterai AA',
        vi: 'Pin AA'
      },
      iecName: 'LR6',
      ansiName: '15A',
      voltage: '1.5V',
      dimensions: '14.5×50.5mm'
    },
    {
      id: '2',
      commonName: 'AAA',
      localName: {
        zh: '7号电池',
        'zh-TW': '7號電池',
        en: 'AAA Battery',
        ja: '単4電池',
        id: 'Baterai AAA',
        vi: 'Pin AAA'
      },
      iecName: 'LR03',
      ansiName: '24A',
      voltage: '1.5V',
      dimensions: '10.5×44.5mm'
    },
    {
      id: '3',
      commonName: 'C',
      localName: {
        zh: '2号电池',
        'zh-TW': '2號電池',
        en: 'C Battery',
        ja: '単2電池',
        id: 'Baterai C',
        vi: 'Pin C'
      },
      iecName: 'LR14',
      ansiName: '14A',
      voltage: '1.5V',
      dimensions: '26.2×50mm'
    },
    {
      id: '4',
      commonName: 'D',
      localName: {
        zh: '1号电池',
        'zh-TW': '1號電池',
        en: 'D Battery',
        ja: '単1電池',
        id: 'Baterai D',
        vi: 'Pin D'
      },
      iecName: 'LR20',
      ansiName: '13A',
      voltage: '1.5V',
      dimensions: '34.2×61.5mm'
    },
    {
      id: '5',
      commonName: '9V',
      localName: {
        zh: '9伏电池',
        'zh-TW': '9伏電池',
        en: '9V Battery',
        ja: '9V電池',
        id: 'Baterai 9V',
        vi: 'Pin 9V'
      },
      iecName: '6LR61',
      ansiName: '1604A',
      voltage: '9V',
      dimensions: '26.5×17.5×48.5mm'
    },
    {
      id: '6',
      commonName: 'CR2032',
      localName: {
        zh: '纽扣电池',
        'zh-TW': '鈕扣電池',
        en: 'Coin Cell',
        ja: 'コイン電池',
        id: 'Baterai Koin',
        vi: 'Pin cúc áo'
      },
      iecName: 'CR2032',
      ansiName: 'DL2032',
      voltage: '3V',
      dimensions: '20×3.2mm'
    },
    {
      id: '7',
      commonName: 'CR2025',
      localName: {
        zh: '薄型纽扣电池',
        'zh-TW': '薄型鈕扣電池',
        en: 'Thin Coin Cell',
        ja: '薄型コイン電池',
        id: 'Baterai Koin Tipis',
        vi: 'Pin cúc áo mỏng'
      },
      iecName: 'CR2025',
      ansiName: 'DL2025',
      voltage: '3V',
      dimensions: '20×2.5mm'
    },
    {
      id: '8',
      commonName: '18650',
      localName: {
        zh: '锂离子电池',
        'zh-TW': '鋰離子電池',
        en: 'Li-ion Battery',
        ja: 'リチウムイオン電池',
        id: 'Baterai Li-ion',
        vi: 'Pin Li-ion'
      },
      iecName: '18650',
      ansiName: '18650',
      voltage: '3.7V',
      dimensions: '18×65mm'
    }
  ]
};

// 螺丝规格对照表 - 2025年国际标准
export const screwSpecsMultilingualTable: MultilingualReferenceTable = {
  id: 'screw-specs',
  category: 'technical',
  nameKey: 'screwSpecsTable',
  descriptionKey: 'screwSpecsTableDesc',
  columns: [
    { key: 'screwType', labelKey: 'screwType', width: '20%', searchable: true },
    { key: 'localName', labelKey: 'localName', width: '20%', searchable: true, isMultilingual: true },
    { key: 'metricSize', labelKey: 'metricSize', width: '15%', searchable: true },
    { key: 'imperialSize', labelKey: 'imperialSize', width: '15%', searchable: true },
    { key: 'threadPitch', labelKey: 'threadPitch', width: '15%', searchable: true },
    { key: 'headType', labelKey: 'headType', width: '15%', searchable: true }
  ],
  data: [
    {
      id: '1',
      screwType: 'M3',
      localName: {
        zh: '3毫米螺丝',
        'zh-TW': '3毫米螺絲',
        en: '3mm Screw',
        ja: '3mmネジ',
        id: 'Sekrup 3mm',
        vi: 'Vít 3mm'
      },
      metricSize: 'M3',
      imperialSize: '#4-40',
      threadPitch: '0.5mm',
      headType: 'Phillips/Flat'
    },
    {
      id: '2',
      screwType: 'M4',
      localName: {
        zh: '4毫米螺丝',
        'zh-TW': '4毫米螺絲',
        en: '4mm Screw',
        ja: '4mmネジ',
        id: 'Sekrup 4mm',
        vi: 'Vít 4mm'
      },
      metricSize: 'M4',
      imperialSize: '#8-32',
      threadPitch: '0.7mm',
      headType: 'Phillips/Hex'
    },
    {
      id: '3',
      screwType: 'M5',
      localName: {
        zh: '5毫米螺丝',
        'zh-TW': '5毫米螺絲',
        en: '5mm Screw',
        ja: '5mmネジ',
        id: 'Sekrup 5mm',
        vi: 'Vít 5mm'
      },
      metricSize: 'M5',
      imperialSize: '#10-24',
      threadPitch: '0.8mm',
      headType: 'Phillips/Hex'
    },
    {
      id: '4',
      screwType: 'M6',
      localName: {
        zh: '6毫米螺丝',
        'zh-TW': '6毫米螺絲',
        en: '6mm Screw',
        ja: '6mmネジ',
        id: 'Sekrup 6mm',
        vi: 'Vít 6mm'
      },
      metricSize: 'M6',
      imperialSize: '1/4"-20',
      threadPitch: '1.0mm',
      headType: 'Phillips/Hex/Allen'
    },
    {
      id: '5',
      screwType: 'M8',
      localName: {
        zh: '8毫米螺丝',
        'zh-TW': '8毫米螺絲',
        en: '8mm Screw',
        ja: '8mmネジ',
        id: 'Sekrup 8mm',
        vi: 'Vít 8mm'
      },
      metricSize: 'M8',
      imperialSize: '5/16"-18',
      threadPitch: '1.25mm',
      headType: 'Hex/Allen'
    },
    {
      id: '6',
      screwType: 'M10',
      localName: {
        zh: '10毫米螺丝',
        'zh-TW': '10毫米螺絲',
        en: '10mm Screw',
        ja: '10mmネジ',
        id: 'Sekrup 10mm',
        vi: 'Vít 10mm'
      },
      metricSize: 'M10',
      imperialSize: '3/8"-16',
      threadPitch: '1.5mm',
      headType: 'Hex/Allen'
    },
    {
      id: '7',
      screwType: 'M12',
      localName: {
        zh: '12毫米螺丝',
        'zh-TW': '12毫米螺絲',
        en: '12mm Screw',
        ja: '12mmネジ',
        id: 'Sekrup 12mm',
        vi: 'Vít 12mm'
      },
      metricSize: 'M12',
      imperialSize: '1/2"-13',
      threadPitch: '1.75mm',
      headType: 'Hex/Allen'
    },
    {
      id: '8',
      screwType: '#6',
      localName: {
        zh: '6号木螺丝',
        'zh-TW': '6號木螺絲',
        en: '#6 Wood Screw',
        ja: '#6木ネジ',
        id: 'Sekrup Kayu #6',
        vi: 'Vít gỗ #6'
      },
      metricSize: '3.5mm',
      imperialSize: '#6',
      threadPitch: 'Variable',
      headType: 'Phillips/Flat'
    },
    {
      id: '9',
      screwType: '#8',
      localName: {
        zh: '8号木螺丝',
        'zh-TW': '8號木螺絲',
        en: '#8 Wood Screw',
        ja: '#8木ネジ',
        id: 'Sekrup Kayu #8',
        vi: 'Vít gỗ #8'
      },
      metricSize: '4.2mm',
      imperialSize: '#8',
      threadPitch: 'Variable',
      headType: 'Phillips/Flat'
    },
    {
      id: '10',
      screwType: '#10',
      localName: {
        zh: '10号木螺丝',
        'zh-TW': '10號木螺絲',
        en: '#10 Wood Screw',
        ja: '#10木ネジ',
        id: 'Sekrup Kayu #10',
        vi: 'Vít gỗ #10'
      },
      metricSize: '4.8mm',
      imperialSize: '#10',
      threadPitch: 'Variable',
      headType: 'Phillips/Flat/Hex'
    }
  ]
};

// 所有多语言对照表的集合
export const allMultilingualReferenceTables: MultilingualReferenceTable[] = [
  usStatesMultilingualTable,
  shoeSizesMultilingualTable,
  currencyCodesMultilingualTable,
  incotermsMultilingualTable,
  paperSizesMultilingualTable,
  countryCodesMultilingualTable,
  clothingSizesMultilingualTable,
  batteryTypesMultilingualTable,
  screwSpecsMultilingualTable
];

// 对照表分类
export const referenceCategories = [
  { id: 'all', nameKey: 'allCategories' },
  { id: 'geography', nameKey: 'geographyCategory' },
  { id: 'sizing', nameKey: 'sizingCategory' },
  { id: 'logistics', nameKey: 'logisticsCategory' },
  { id: 'financial', nameKey: 'financialCategory' },
  { id: 'technical', nameKey: 'technicalCategory' }
];
