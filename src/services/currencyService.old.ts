// 货币汇率服务 - 纯数据库模式
interface ExchangeRates {
  [key: string]: number;
}

interface CurrencyData {
  rates: ExchangeRates;
  lastUpdated: number;
  baseCurrency: string;
}

class CurrencyService {
  private static instance: CurrencyService;
  private currencyData: CurrencyData | null = null;
  private updateInterval: NodeJS.Timeout | null = null;
  private readonly UPDATE_INTERVAL = 5 * 60 * 1000; // 5分钟检查一次数据库
  private readonly CACHE_KEY = 'currency_rates_cache';
  private isUpdating = false;
  private listeners: (() => void)[] = [];

  // 支持的主要货币列表
  private readonly SUPPORTED_CURRENCIES = [
    'USD', 'EUR', 'GBP', 'JPY', 'CNY', 'CAD', 'AUD', 'CHF', 'HKD', 'SGD',
    'KRW', 'INR', 'THB', 'MYR', 'PHP', 'IDR', 'NZD', 'ZAR', 'BRL', 'MXN',
    'RUB', 'TRY', 'SEK', 'NOK', 'DKK', 'PLN', 'CZK', 'HUF', 'RON', 'BGN'
  ];

  private constructor() {
    console.log('🔄 CurrencyService v5.0 - 纯数据库模式，简化架构');

    // 立即设置默认数据，确保convert方法有数据可用
    this.currencyData = this.getDefaultRates();
    console.log('📝 设置默认汇率数据，CNY汇率:', this.currencyData.rates.CNY);

    // 立即从数据库获取真实数据
    this.loadFromDatabase();

    // 启动定期数据库检查
    this.startDatabasePolling();
  }

  public static getInstance(): CurrencyService {
    if (!CurrencyService.instance) {
      CurrencyService.instance = new CurrencyService();
    }
    return CurrencyService.instance;
  }

  // 从数据库加载汇率数据
  private async loadFromDatabase(): Promise<void> {
    console.log('🔄 从数据库加载汇率数据...');

    try {
      const dbRates = await this.fetchRatesFromDatabase();
      if (dbRates && dbRates.rates.CNY) {
        this.setCurrencyData(dbRates);
        console.log('✅ 成功从数据库加载汇率数据，CNY汇率:', dbRates.rates.CNY);

        // 保存到缓存
        this.saveToCache();
        return;
      }
    } catch (error) {
      console.warn('⚠️ 从数据库获取数据失败:', error);
    }

    // 如果数据库获取失败，尝试使用缓存
    try {
      const cached = localStorage.getItem(this.CACHE_KEY);
      if (cached) {
        const data = JSON.parse(cached);
        // 只使用合理的缓存数据
        if (data.rates && data.rates.CNY && data.rates.CNY > 6) {
          this.setCurrencyData(data);
          console.log('✅ 使用缓存中的汇率数据，CNY汇率:', data.rates.CNY);
          return;
        }
      }
    } catch (error) {
      console.warn('⚠️ 检查缓存失败:', error);
    }

    console.log('⚠️ 数据库和缓存都不可用，使用默认数据');
  }

  // 添加数据更新监听器
  public addUpdateListener(callback: () => void): void {
    this.listeners.push(callback);
  }

  // 移除数据更新监听器
  public removeUpdateListener(callback: () => void): void {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  // 检查是否已初始化完成
  public isReady(): boolean {
    return this.isInitialized && this.currencyData !== null;
  }

  // 通知所有监听器数据已更新
  private notifyListeners(): void {
    this.listeners.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.warn('监听器回调执行失败:', error);
      }
    });
  }

  // 设置汇率数据并通知监听器
  private setCurrencyData(data: CurrencyData): void {
    this.currencyData = data;
    this.notifyListeners();
  }

  // 保存到本地缓存
  private saveToCache(): void {
    if (this.currencyData) {
      try {
        localStorage.setItem(this.CACHE_KEY, JSON.stringify(this.currencyData));
        console.log('💾 汇率数据已保存到缓存');
      } catch (error) {
        console.warn('⚠️ 保存缓存失败:', error);
      }
    }
  }

  // 从本地存储加载缓存数据
  private async loadFromCache(): Promise<void> {
    try {
      const cached = localStorage.getItem(this.CACHE_KEY);
      if (cached) {
        const data = JSON.parse(cached);
        // 检查缓存是否过期（超过15分钟）
        if (Date.now() - data.lastUpdated < 15 * 60 * 1000) {
          this.setCurrencyData(data);
          console.log('汇率数据从缓存加载');
          return;
        } else {
          console.log('缓存已过期，将获取最新数据');
        }
      }
    } catch (error) {
      console.warn('加载汇率缓存失败:', error);
    }

    // 如果没有有效缓存，优先尝试从数据库获取最新数据
    if (!this.currencyData) {
      console.log('正在从数据库获取最新汇率数据...');
      try {
        const dbRates = await this.fetchRatesFromDatabase();
        if (dbRates && this.isDataFresh(dbRates.lastUpdated)) {
          this.setCurrencyData(dbRates);
          console.log('使用数据库中的新鲜汇率数据');
          return;
        } else if (dbRates) {
          this.setCurrencyData(dbRates);
          console.log('使用数据库中的汇率数据（稍旧但可用）');
          // 在后台获取最新数据
          setTimeout(() => {
            this.fetchRates();
          }, 1000);
          return;
        }
      } catch (error) {
        console.warn('从数据库获取汇率失败:', error);
      }

      // 如果数据库也没有数据，才使用默认数据
      this.setCurrencyData(this.getDefaultRates());
      console.log('使用默认汇率数据，立即获取最新数据');

      // 立即获取最新数据，不延迟
      this.fetchRates();
    }
  }

  // 保存到本地存储
  private saveToCache(): void {
    if (this.currencyData) {
      try {
        localStorage.setItem(this.CACHE_KEY, JSON.stringify(this.currencyData));
      } catch (error) {
        console.warn('保存汇率缓存失败:', error);
      }
    }
  }

  // 获取汇率数据
  private async fetchRates(): Promise<void> {
    if (this.isUpdating) {
      console.log('汇率更新中，跳过重复请求');
      return;
    }

    this.isUpdating = true;

    try {
      // 首先尝试从数据库获取最新汇率
      const dbRates = await this.fetchRatesFromDatabase();

      if (dbRates && this.isDataFresh(dbRates.lastUpdated)) {
        this.setCurrencyData(dbRates);
        console.log('使用数据库中的新鲜汇率数据');
        return;
      }

      // 如果有数据库数据，使用数据库数据（即使稍旧）
      if (dbRates) {
        this.setCurrencyData(dbRates);
        console.log('使用数据库汇率数据');
        return;
      }

      // 如果数据库也没有数据，使用默认数据
      if (!this.currencyData) {
        this.setCurrencyData(this.getDefaultRates());
        console.log('数据库无数据，使用默认汇率数据');
      }

    } catch (error) {
      console.error('获取汇率数据失败:', error);

      // 如果没有当前数据，使用默认数据
      if (!this.currencyData) {
        this.setCurrencyData(this.getDefaultRates());
        console.log('使用默认汇率数据');
      }
    } finally {
      this.isUpdating = false;
    }
  }

  // 默认汇率数据（作为备用）
  private getDefaultRates(): CurrencyData {
    const defaultRates = {
      rates: {
        USD: 1,
        EUR: 0.85,
        GBP: 0.73,
        JPY: 110,
        CNY: 7.16,  // 更新为当前真实汇率
        CAD: 1.25,
        AUD: 1.35,
        CHF: 0.92,
        HKD: 7.8,
        SGD: 1.35,
        KRW: 1180,
        INR: 74,
        THB: 33,
        MYR: 4.2,
        PHP: 50,
        IDR: 14300,
        NZD: 1.4,
        ZAR: 14.5,
        BRL: 5.2,
        MXN: 20
      },
      lastUpdated: Date.now(),
      baseCurrency: 'USD'
    };

    // 强制检查：如果CNY汇率是6.45，立即替换为7.16
    if (Math.abs(defaultRates.rates.CNY - 6.45) < 0.01) {
      console.warn('🚨 检测到默认汇率中的CNY为6.45，强制更新为7.16');
      defaultRates.rates.CNY = 7.16;
    }

    return defaultRates;
  }

  // 从数据库获取最新汇率
  private async fetchRatesFromDatabase(): Promise<CurrencyData | null> {
    try {
      const response = await fetch('/api/exchange-rates/latest');

      if (!response.ok) {
        console.warn('从数据库获取汇率失败:', response.statusText);
        return null;
      }

      const result = await response.json();

      if (result.success && result.data) {
        return {
          rates: result.data.rates,
          lastUpdated: new Date(result.data.lastUpdated).getTime(),
          baseCurrency: result.data.baseCurrency
        };
      }

      return null;
    } catch (error) {
      console.warn('从数据库获取汇率异常:', error);
      return null;
    }
  }

  // saveRatesToDatabase方法已移除，前端不再直接保存数据到数据库

  // 检查数据是否新鲜（15分钟内）
  private isDataFresh(lastUpdated: number): boolean {
    const now = Date.now();
    const maxAge = 15 * 60 * 1000; // 15分钟
    return (now - lastUpdated) < maxAge;
  }

  // 启动自动更新
  private startAutoUpdate(): void {
    this.updateInterval = setInterval(() => {
      this.fetchRates();
    }, this.UPDATE_INTERVAL);
  }

  // 停止自动更新
  public stopAutoUpdate(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }

  // 获取当前汇率数据
  public getRates(): ExchangeRates | null {
    return this.currencyData?.rates || null;
  }

  // 获取支持的货币列表
  public getSupportedCurrencies(): string[] {
    return [...this.SUPPORTED_CURRENCIES];
  }

  // 货币换算
  public convert(amount: number, fromCurrency: string, toCurrency: string): number | null {
    console.log('🔄 convert() 被调用:', { amount, fromCurrency, toCurrency });
    console.log('📊 当前currencyData:', this.currencyData);

    if (!this.currencyData || !this.currencyData.rates) {
      console.log('❌ convert() 失败: 没有汇率数据');
      return null;
    }

    const rates = this.currencyData.rates;
    console.log('💱 当前汇率数据:', rates);
    console.log('🎯 CNY汇率:', rates.CNY);

    // 检查CNY汇率是否为过时的6.45
    if (rates.CNY && Math.abs(rates.CNY - 6.45) < 0.01) {
      console.warn('🚨 在convert()中检测到过时的CNY汇率6.45，立即触发刷新');
      this.forceRefreshFromAPI();
    }

    // 如果源货币是USD
    if (fromCurrency === 'USD') {
      const rate = rates[toCurrency] || 1;
      const result = amount * rate;
      console.log(`💰 USD to ${toCurrency}: ${amount} × ${rate} = ${result}`);
      return result;
    }

    // 如果目标货币是USD
    if (toCurrency === 'USD') {
      const rate = rates[fromCurrency] || 1;
      const result = amount / rate;
      console.log(`💰 ${fromCurrency} to USD: ${amount} ÷ ${rate} = ${result}`);
      return result;
    }
    
    // 其他货币之间的换算（通过USD中转）
    const fromRate = rates[fromCurrency] || 1;
    const toRate = rates[toCurrency] || 1;
    
    return (amount / fromRate) * toRate;
  }

  // 获取最后更新时间
  public getLastUpdated(): Date | null {
    return this.currencyData ? new Date(this.currencyData.lastUpdated) : null;
  }

  // 获取历史汇率数据（用于趋势图）
  public async getHistoricalRates(fromCurrency: string, toCurrency: string, timeRange: string = '1d'): Promise<any> {
    try {
      const url = `/api/exchange-rates/historical?base=${fromCurrency}&target=${toCurrency}&range=${timeRange}`;
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`获取历史汇率失败: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.message || '获取历史汇率失败');
      }
    } catch (error) {
      console.error('获取历史汇率异常:', error);
      throw error;
    }
  }

  // 获取汇率统计信息
  public async getRateStats(): Promise<any> {
    try {
      const response = await fetch('/api/exchange-rates/stats');

      if (!response.ok) {
        throw new Error(`获取汇率统计失败: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.message || '获取汇率统计失败');
      }
    } catch (error) {
      console.error('获取汇率统计异常:', error);
      throw error;
    }
  }



  // 获取货币显示名称
  public getCurrencyName(code: string): string {
    const names: { [key: string]: string } = {
      USD: '美元',
      EUR: '欧元',
      GBP: '英镑',
      JPY: '日元',
      CNY: '人民币',
      CAD: '加拿大元',
      AUD: '澳元',
      CHF: '瑞士法郎',
      HKD: '港币',
      SGD: '新加坡元',
      KRW: '韩元',
      INR: '印度卢比',
      THB: '泰铢',
      MYR: '马来西亚林吉特',
      PHP: '菲律宾比索',
      IDR: '印尼盾',
      NZD: '新西兰元',
      ZAR: '南非兰特',
      BRL: '巴西雷亚尔',
      MXN: '墨西哥比索',
      RUB: '俄罗斯卢布',
      TRY: '土耳其里拉',
      SEK: '瑞典克朗',
      NOK: '挪威克朗',
      DKK: '丹麦克朗',
      PLN: '波兰兹罗提',
      CZK: '捷克克朗',
      HUF: '匈牙利福林',
      RON: '罗马尼亚列伊',
      BGN: '保加利亚列弗'
    };
    return names[code] || code;
  }
}

export default CurrencyService;
export type { ExchangeRates, CurrencyData };
