// 反馈服务 - 处理与后端API的通信

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '';

export interface FeedbackData {
  feedback_type: 'general' | 'bug' | 'feature' | 'improvement';
  rating?: number;
  message: string;
  email?: string;
}

export interface FeedbackResponse {
  success: boolean;
  id?: number;
  message: string;
  errors?: Array<{
    field: string;
    message: string;
    value: any;
  }>;
}

export interface FeedbackListResponse {
  success: boolean;
  data: Array<{
    id: number;
    feedback_type: string;
    rating: number;
    message: string;
    email?: string;
    created_at: string;
    status: string;
  }>;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface StatsResponse {
  success: boolean;
  data: {
    overall: {
      total_feedback: number;
      overall_avg_rating: number;
      weekly_count: number;
      monthly_count: number;
    };
    byType: Array<{
      feedback_type: string;
      total_count: number;
      avg_rating: number;
      new_count: number;
      in_progress_count: number;
      resolved_count: number;
      closed_count: number;
    }>;
  };
}

class FeedbackService {
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}/api/v1/feedback${endpoint}`;
    
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, { ...defaultOptions, ...options });
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API请求失败:', error);
      throw error;
    }
  }

  // 提交反馈
  async submitFeedback(feedbackData: FeedbackData): Promise<FeedbackResponse> {
    return this.makeRequest<FeedbackResponse>('', {
      method: 'POST',
      body: JSON.stringify(feedbackData),
    });
  }

  // 获取反馈列表
  async getFeedbackList(
    page: number = 1,
    limit: number = 10,
    filters: {
      feedback_type?: string;
      status?: string;
      rating?: number;
    } = {}
  ): Promise<FeedbackListResponse> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...Object.fromEntries(
        Object.entries(filters).filter(([_, value]) => value !== undefined)
      ),
    });

    return this.makeRequest<FeedbackListResponse>(`?${params}`);
  }

  // 获取统计数据
  async getStats(): Promise<StatsResponse> {
    return this.makeRequest<StatsResponse>('/stats/overview');
  }

  // 获取最近反馈
  async getRecentFeedback(limit: number = 5): Promise<FeedbackListResponse> {
    return this.makeRequest<FeedbackListResponse>(`/recent/list?limit=${limit}`);
  }

  // 更新反馈状态（管理员功能）
  async updateFeedbackStatus(
    id: number,
    status: string,
    adminNotes?: string
  ): Promise<FeedbackResponse> {
    return this.makeRequest<FeedbackResponse>(`/${id}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status, admin_notes: adminNotes }),
    });
  }

  // 删除反馈（管理员功能）
  async deleteFeedback(id: number): Promise<FeedbackResponse> {
    return this.makeRequest<FeedbackResponse>(`/${id}`, {
      method: 'DELETE',
    });
  }

  // 检查API健康状态
  async checkHealth(): Promise<{ success: boolean; message: string }> {
    try {
      const response = await fetch(`${API_BASE_URL}/health`);
      return await response.json();
    } catch (error) {
      return {
        success: false,
        message: '无法连接到服务器'
      };
    }
  }
}

// 创建单例实例
export const feedbackService = new FeedbackService();

// 错误处理工具函数
export const handleApiError = (error: any): string => {
  if (error.message) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  return '网络错误，请稍后重试';
};
