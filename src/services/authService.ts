interface LoginRequest {
  username: string;
  password: string;
}

interface LoginResponse {
  success: boolean;
  message: string;
  data?: {
    user: {
      id: number;
      username: string;
      email: string;
      full_name: string;
      role: string;
    };
    access_token: string;
    refresh_token: string;
    expires_in: number;
    token_type: string;
  };
}

interface RefreshTokenRequest {
  refresh_token: string;
}

interface User {
  id: number;
  username: string;
  email: string;
  full_name: string;
  role: string;
}

interface UserResponse {
  success: boolean;
  data?: {
    user: User;
    session: {
      expires_at: string;
    };
  };
  message?: string;
}

class AuthService {
  private static instance: AuthService;
  private baseUrl: string;
  private accessToken: string | null = null;
  private refreshToken: string | null = null;
  private user: User | null = null;

  private constructor() {
    this.baseUrl = import.meta.env.VITE_API_BASE_URL || 'https://api.amzova.com';
    this.loadTokensFromStorage();
  }

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  // 从localStorage加载tokens
  private loadTokensFromStorage(): void {
    try {
      this.accessToken = localStorage.getItem('access_token') || localStorage.getItem('adminToken');
      this.refreshToken = localStorage.getItem('refresh_token');
      const userStr = localStorage.getItem('user');
      if (userStr) {
        this.user = JSON.parse(userStr);
      }
    } catch (error) {
      console.error('加载认证信息失败:', error);
      this.clearTokens();
    }
  }

  // 保存tokens到localStorage
  private saveTokensToStorage(accessToken: string, refreshToken: string, user: User): void {
    try {
      localStorage.setItem('access_token', accessToken);
      localStorage.setItem('refresh_token', refreshToken);
      localStorage.setItem('user', JSON.stringify(user));
      // 为了兼容现有组件，同时保存adminToken
      localStorage.setItem('adminToken', accessToken);
      this.accessToken = accessToken;
      this.refreshToken = refreshToken;
      this.user = user;
    } catch (error) {
      console.error('保存认证信息失败:', error);
    }
  }

  // 清除tokens
  private clearTokens(): void {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user');
    localStorage.removeItem('adminToken'); // 同时清除adminToken
    this.accessToken = null;
    this.refreshToken = null;
    this.user = null;
  }

  // 获取认证头
  private getAuthHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (this.accessToken) {
      headers['Authorization'] = `Bearer ${this.accessToken}`;
    }

    return headers;
  }

  // 发送HTTP请求
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}/api/v1${endpoint}`;
    
    const config: RequestInit = {
      ...options,
      headers: {
        ...this.getAuthHeaders(),
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      // 如果token过期，尝试刷新
      if (response.status === 401 && this.refreshToken && endpoint !== '/auth/refresh') {
        const refreshed = await this.refreshAccessToken();
        if (refreshed) {
          // 重新发送原请求
          config.headers = {
            ...this.getAuthHeaders(),
            ...options.headers,
          };
          const retryResponse = await fetch(url, config);
          return await retryResponse.json();
        }
      }

      return data;
    } catch (error) {
      console.error('请求失败:', error);
      throw new Error('网络请求失败');
    }
  }

  // 用户登录
  public async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await this.request<LoginResponse>('/auth/login', {
        method: 'POST',
        body: JSON.stringify(credentials),
      });

      if (response.success && response.data) {
        this.saveTokensToStorage(
          response.data.access_token,
          response.data.refresh_token,
          response.data.user
        );
      }

      return response;
    } catch (error) {
      console.error('登录失败:', error);
      return {
        success: false,
        message: '登录失败，请检查网络连接'
      };
    }
  }

  // 用户登出
  public async logout(): Promise<void> {
    try {
      if (this.accessToken) {
        await this.request('/auth/logout', {
          method: 'POST',
        });
      }
    } catch (error) {
      console.error('登出请求失败:', error);
    } finally {
      this.clearTokens();
    }
  }

  // 刷新访问token
  public async refreshAccessToken(): Promise<boolean> {
    if (!this.refreshToken) {
      return false;
    }

    try {
      const response = await this.request<LoginResponse>('/auth/refresh', {
        method: 'POST',
        body: JSON.stringify({ refresh_token: this.refreshToken }),
      });

      if (response.success && response.data) {
        this.saveTokensToStorage(
          response.data.access_token,
          response.data.refresh_token,
          response.data.user
        );
        return true;
      }
    } catch (error) {
      console.error('刷新token失败:', error);
    }

    this.clearTokens();
    return false;
  }

  // 获取当前用户信息
  public async getCurrentUser(): Promise<UserResponse> {
    try {
      return await this.request<UserResponse>('/auth/me');
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return {
        success: false,
        message: '获取用户信息失败'
      };
    }
  }

  // 验证token有效性
  public async verifyToken(): Promise<boolean> {
    if (!this.accessToken) {
      return false;
    }

    try {
      const response = await this.request<UserResponse>('/auth/verify');
      return response.success;
    } catch (error) {
      console.error('验证token失败:', error);
      return false;
    }
  }

  // 检查是否已登录
  public isAuthenticated(): boolean {
    return !!this.accessToken && !!this.user;
  }

  // 获取当前用户
  public getUser(): User | null {
    return this.user;
  }

  // 检查用户权限
  public hasRole(role: string): boolean {
    return this.user?.role === role;
  }

  // 检查是否为管理员
  public isAdmin(): boolean {
    return this.user?.role === 'admin' || this.user?.role === 'super_admin';
  }

  // 检查是否为超级管理员
  public isSuperAdmin(): boolean {
    return this.user?.role === 'super_admin';
  }

  // 获取访问token
  public getAccessToken(): string | null {
    return this.accessToken;
  }

  // 修改密码
  public async changePassword(passwordData: {
    current_password: string;
    new_password: string;
    confirm_password: string;
  }): Promise<{ success: boolean; message: string }> {
    try {
      const response = await this.request<{ success: boolean; message: string }>('/auth/change-password', {
        method: 'POST',
        body: JSON.stringify(passwordData),
      });

      // 如果密码修改成功，清除本地token（需要重新登录）
      if (response.success) {
        this.clearTokens();
      }

      return response;
    } catch (error) {
      console.error('修改密码失败:', error);
      return {
        success: false,
        message: '修改密码失败，请检查网络连接'
      };
    }
  }
}

export default AuthService;
