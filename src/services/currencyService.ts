// 货币汇率服务 - 纯数据库模式
interface ExchangeRates {
  [key: string]: number;
}

interface CurrencyData {
  rates: ExchangeRates;
  lastUpdated: number;
  baseCurrency: string;
}

interface ServiceError {
  hasError: boolean;
  errorType: 'network' | 'api' | 'service' | null;
  errorMessage: string;
  canRetry: boolean;
}

class CurrencyService {
  private static instance: CurrencyService;
  private currencyData: CurrencyData | null = null;
  private serviceError: ServiceError = {
    hasError: false,
    errorType: null,
    errorMessage: '',
    canRetry: true
  };
  private updateInterval: NodeJS.Timeout | null = null;
  private readonly UPDATE_INTERVAL = 5 * 60 * 1000; // 5分钟检查一次数据库
  private readonly CACHE_KEY = 'currency_rates_cache';
  private isUpdating = false;
  private listeners: (() => void)[] = [];
  private isInitialized = false;
  private initPromise: Promise<void> | null = null;

  // 单例模式
  public static getInstance(): CurrencyService {
    if (!CurrencyService.instance) {
      CurrencyService.instance = new CurrencyService();
    }
    return CurrencyService.instance;
  }

  private constructor() {
    console.log('🔄 CurrencyService v7.0 - 移除默认汇率，改为错误状态管理');

    // 检查并清除可能的旧缓存
    this.clearOldCache();

    // 创建初始化Promise
    this.initPromise = this.initializeFromDatabase();

    // 启动定期数据库检查
    this.startDatabasePolling();
  }

  // 检查并清除旧缓存
  private clearOldCache(): void {
    try {
      const cached = localStorage.getItem(this.CACHE_KEY);
      if (cached) {
        const data = JSON.parse(cached);
        // 如果缓存中的CNY汇率小于7.1（旧汇率），清除缓存
        if (data.rates && data.rates.CNY && data.rates.CNY < 7.1) {
          console.warn('🗑️ 检测到旧缓存数据，CNY汇率:', data.rates.CNY, '，清除缓存');
          localStorage.removeItem(this.CACHE_KEY);
        } else {
          console.log('✅ 缓存数据正常，CNY汇率:', data.rates?.CNY);
        }
      }

      // 额外检查：强制清除任何可能导致645结果的缓存
      const allKeys = Object.keys(localStorage);
      for (const key of allKeys) {
        if (key.includes('currency') || key.includes('rate') || key.includes('exchange')) {
          try {
            const value = localStorage.getItem(key);
            if (value && (value.includes('6.45') || value.includes('645'))) {
              console.warn('🗑️ 发现包含旧汇率的缓存项:', key, '，清除');
              localStorage.removeItem(key);
            }
          } catch (e) {
            // 忽略解析错误
          }
        }
      }
    } catch (error) {
      console.warn('⚠️ 检查缓存失败，清除所有相关缓存:', error);
      localStorage.removeItem(this.CACHE_KEY);
    }
  }

  // 初始化时从数据库加载数据并通知监听器
  private async initializeFromDatabase(): Promise<void> {
    console.log('🚀 开始初始化数据库数据...');

    try {
      const dbRates = await this.fetchRatesFromDatabase();
      if (dbRates && dbRates.rates.CNY) {
        console.log('✅ 初始化获取到数据库数据，CNY汇率:', dbRates.rates.CNY);
        this.setCurrencyData(dbRates);
        this.saveToCache();
        this.isInitialized = true;
        console.log('🔔 初始化完成，通知所有监听器');
        return;
      }
    } catch (error) {
      console.warn('⚠️ 初始化时从数据库获取数据失败:', error);
    }

    // 如果数据库获取失败，尝试使用缓存
    try {
      const cached = localStorage.getItem(this.CACHE_KEY);
      if (cached) {
        const data = JSON.parse(cached);
        if (data.rates && data.rates.CNY && data.rates.CNY > 6) {
          this.setCurrencyData(data);
          console.log('✅ 初始化使用缓存中的汇率数据，CNY汇率:', data.rates.CNY);
          this.isInitialized = true;
          return;
        }
      }
    } catch (error) {
      console.warn('⚠️ 初始化时检查缓存失败:', error);
    }

    console.log('⚠️ 初始化时数据库和缓存都不可用，使用默认数据');
    this.isInitialized = true;
  }

  // 从数据库加载汇率数据
  private async loadFromDatabase(): Promise<void> {
    console.log('🔄 从数据库加载汇率数据...');

    try {
      const dbRates = await this.fetchRatesFromDatabase();
      if (dbRates && dbRates.rates.CNY) {
        this.setCurrencyData(dbRates);
        this.clearError(); // 清除错误状态
        console.log('✅ 成功从数据库加载汇率数据，CNY汇率:', dbRates.rates.CNY);
        this.saveToCache();
        return;
      }
    } catch (error) {
      console.warn('⚠️ 从数据库获取数据失败:', error);
      this.setError('api', '无法连接到汇率服务器');
    }

    // 如果数据库获取失败，尝试使用缓存
    try {
      const cached = localStorage.getItem(this.CACHE_KEY);
      if (cached) {
        const data = JSON.parse(cached);
        if (data.rates && data.rates.CNY && data.rates.CNY > 6) {
          this.setCurrencyData(data);
          this.setError('api', '正在使用缓存数据，数据可能不是最新的');
          console.log('✅ 使用缓存中的汇率数据，CNY汇率:', data.rates.CNY);
          return;
        }
      }
    } catch (error) {
      console.warn('⚠️ 检查缓存失败:', error);
    }

    // 如果数据库和缓存都不可用，设置错误状态
    this.setError('service', '汇率服务暂时不可用，请稍后重试');
    console.error('❌ 数据库和缓存都不可用，无法提供汇率数据');
  }

  // 设置汇率数据并通知监听器
  private setCurrencyData(data: CurrencyData): void {
    this.currencyData = data;
    this.notifyListeners();
  }

  // 设置错误状态
  private setError(type: 'network' | 'api' | 'service', message: string): void {
    this.serviceError = {
      hasError: true,
      errorType: type,
      errorMessage: message,
      canRetry: type !== 'service'
    };
    this.notifyListeners();
  }

  // 清除错误状态
  private clearError(): void {
    this.serviceError = {
      hasError: false,
      errorType: null,
      errorMessage: '',
      canRetry: true
    };
  }

  // 保存到本地缓存
  private saveToCache(): void {
    if (this.currencyData) {
      try {
        localStorage.setItem(this.CACHE_KEY, JSON.stringify(this.currencyData));
        console.log('💾 汇率数据已保存到缓存');
      } catch (error) {
        console.warn('⚠️ 保存缓存失败:', error);
      }
    }
  }

  // 启动数据库轮询
  private startDatabasePolling(): void {
    // 每5分钟检查一次数据库更新
    this.updateInterval = setInterval(() => {
      this.checkDatabaseForUpdates();
    }, this.UPDATE_INTERVAL);
    
    console.log('🔄 已启动数据库轮询，每5分钟检查一次更新');
  }

  // 检查数据库是否有更新
  private async checkDatabaseForUpdates(): Promise<void> {
    if (this.isUpdating) {
      console.log('⏳ 正在更新中，跳过本次检查');
      return;
    }

    this.isUpdating = true;
    
    try {
      const dbRates = await this.fetchRatesFromDatabase();
      
      if (dbRates && dbRates.rates.CNY) {
        // 检查是否有更新
        const currentLastUpdated = this.currencyData?.lastUpdated || 0;
        const dbLastUpdated = dbRates.lastUpdated;
        
        if (dbLastUpdated > currentLastUpdated) {
          console.log('🔄 检测到数据库有更新，更新本地数据');
          this.setCurrencyData(dbRates);
          this.saveToCache();
        } else {
          console.log('✅ 数据库数据无更新');
        }
      }
    } catch (error) {
      console.warn('⚠️ 检查数据库更新失败:', error);
    } finally {
      this.isUpdating = false;
    }
  }

  // 从数据库获取汇率数据
  private async fetchRatesFromDatabase(): Promise<CurrencyData | null> {
    try {
      console.log('📡 正在从数据库获取汇率数据...');
      // 使用统一的API配置
      const apiUrl = `${import.meta.env.VITE_API_BASE_URL || 'https://api.amzova.com'}/api/v1/exchange-rates/latest`;
      console.log('🔗 API URL:', apiUrl);
      const response = await fetch(apiUrl);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (result.success && result.data && result.data.rates) {
        console.log('✅ 成功从数据库获取汇率数据');
        return {
          rates: result.data.rates,
          lastUpdated: new Date(result.data.lastUpdated).getTime(),
          baseCurrency: result.data.baseCurrency || 'USD'
        };
      } else {
        console.warn('⚠️ 数据库返回的数据格式不正确:', result);
        return null;
      }
    } catch (error) {
      console.error('❌ 从数据库获取汇率数据失败:', error);
      return null;
    }
  }



  // 通知所有监听器
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener();
      } catch (error) {
        console.warn('监听器执行失败:', error);
      }
    });
  }

  // 公共方法：货币换算
  public convert(amount: number, fromCurrency: string, toCurrency: string): number | null {
    console.log('🔄 convert() 被调用:', { amount, fromCurrency, toCurrency });

    // 如果有错误状态且没有数据，返回null
    if (this.serviceError.hasError && (!this.currencyData || !this.currencyData.rates)) {
      console.log('❌ convert() 失败: 服务错误且没有汇率数据');
      return null;
    }

    if (!this.currencyData || !this.currencyData.rates) {
      console.log('❌ convert() 失败: 没有汇率数据');
      return null;
    }

    const rates = this.currencyData.rates;
    console.log('💱 当前汇率数据CNY:', rates.CNY);
    console.log('📊 数据来源时间:', new Date(this.currencyData.lastUpdated).toLocaleString());

    // 如果源货币是USD
    if (fromCurrency === 'USD') {
      const rate = rates[toCurrency];
      if (!rate) {
        console.warn(`⚠️ 未找到 ${toCurrency} 的汇率数据`);
        return null;
      }
      const result = amount * rate;
      console.log(`💰 USD to ${toCurrency}: ${amount} × ${rate} = ${result}`);
      return result;
    }

    // 如果目标货币是USD
    if (toCurrency === 'USD') {
      const rate = rates[fromCurrency];
      if (!rate) {
        console.warn(`⚠️ 未找到 ${fromCurrency} 的汇率数据`);
        return null;
      }
      const result = amount / rate;
      console.log(`💰 ${fromCurrency} to USD: ${amount} ÷ ${rate} = ${result}`);
      return result;
    }

    // 其他货币转换：先转为USD，再转为目标货币
    const fromRate = rates[fromCurrency];
    const toRate = rates[toCurrency];
    if (!fromRate || !toRate) {
      console.warn(`⚠️ 未找到汇率数据: ${fromCurrency}=${fromRate}, ${toCurrency}=${toRate}`);
      return null;
    }
    const result = (amount / fromRate) * toRate;
    console.log(`💰 ${fromCurrency} to ${toCurrency}: ${amount} ÷ ${fromRate} × ${toRate} = ${result}`);
    return result;
  }

  // 公共方法：添加监听器
  public addListener(listener: () => void): void {
    this.listeners.push(listener);
  }

  // 公共方法：移除监听器
  public removeListener(listener: () => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  // 公共方法：获取支持的货币列表
  public getSupportedCurrencies(): string[] {
    if (!this.currencyData) return [];
    return Object.keys(this.currencyData.rates);
  }

  // 公共方法：获取最后更新时间
  public getLastUpdated(): Date | null {
    if (!this.currencyData) return null;
    return new Date(this.currencyData.lastUpdated);
  }

  // 公共方法：等待初始化完成
  public async waitForInitialization(): Promise<void> {
    if (this.isInitialized) {
      return;
    }
    if (this.initPromise) {
      await this.initPromise;
    }
  }

  // 公共方法：检查是否已初始化
  public isReady(): boolean {
    return this.isInitialized;
  }

  // 公共方法：获取错误状态
  public getError(): ServiceError {
    return { ...this.serviceError };
  }

  // 公共方法：手动刷新数据
  public async refreshData(): Promise<void> {
    console.log('🔄 手动刷新汇率数据...');
    await this.loadFromDatabase();
  }

  // 公共方法：手动刷新数据
  public async refresh(): Promise<void> {
    console.log('🔄 手动刷新汇率数据...');
    await this.loadFromDatabase();
  }

  // 公共方法：获取历史汇率数据（用于趋势图）
  public async getHistoricalRates(fromCurrency: string, toCurrency: string, timeRange: string = '1d'): Promise<any> {
    try {
      console.log('📈 获取历史汇率数据:', { fromCurrency, toCurrency, timeRange });

      // 使用统一的API配置
      const apiUrl = `${import.meta.env.VITE_API_BASE_URL || 'https://api.amzova.com'}/api/v1/exchange-rates/historical`;

      const url = `${apiUrl}?base=${fromCurrency}&target=${toCurrency}&range=${timeRange}`;
      console.log('🔗 历史汇率API URL:', url);

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`获取历史汇率失败: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        console.log('✅ 成功获取历史汇率数据');
        return result.data;
      } else {
        throw new Error(result.message || '获取历史汇率失败');
      }
    } catch (error) {
      console.error('❌ 获取历史汇率异常:', error);
      throw error;
    }
  }

  // 公共方法：获取货币显示名称
  public getCurrencyName(code: string): string {
    const names: { [key: string]: string } = {
      USD: '美元',
      EUR: '欧元',
      GBP: '英镑',
      JPY: '日元',
      CNY: '人民币',
      CAD: '加拿大元',
      AUD: '澳元',
      CHF: '瑞士法郎',
      HKD: '港币',
      SGD: '新加坡元',
      KRW: '韩元',
      INR: '印度卢比',
      THB: '泰铢',
      MYR: '马来西亚林吉特',
      PHP: '菲律宾比索',
      IDR: '印尼盾',
      NZD: '新西兰元',
      ZAR: '南非兰特',
      BRL: '巴西雷亚尔',
      MXN: '墨西哥比索',
      RUB: '俄罗斯卢布',
      TRY: '土耳其里拉',
      SEK: '瑞典克朗',
      NOK: '挪威克朗',
      DKK: '丹麦克朗',
      PLN: '波兰兹罗提',
      CZK: '捷克克朗',
      HUF: '匈牙利福林',
      RON: '罗马尼亚列伊',
      BGN: '保加利亚列弗'
    };
    return names[code] || code;
  }

  // 清理资源
  public destroy(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    this.listeners = [];
  }
}

// 导出单例实例
export default CurrencyService;
