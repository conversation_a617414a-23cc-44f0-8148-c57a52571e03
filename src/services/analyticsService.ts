// 数据分析服务 - 处理用户行为追踪和数据上报
import { getApiBaseUrl } from '../utils/apiConfig';

const API_BASE_URL = getApiBaseUrl();

// 生成会话ID
const generateSessionId = (): string => {
  return 'sess_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
};

// 获取或创建会话ID
const getSessionId = (): string => {
  let sessionId = sessionStorage.getItem('sellerbox_session_id');
  if (!sessionId) {
    sessionId = generateSessionId();
    sessionStorage.setItem('sellerbox_session_id', sessionId);
  }
  return sessionId;
};

export interface ToolUsageData {
  tool_id: string;
  tool_name: string;
  usage_type?: 'view' | 'use' | 'calculate';
  usage_data?: any;
}

export interface PageViewData {
  page_path: string;
  page_title?: string;
}

export interface SessionData {
  session_id: string;
  page_views?: number;
  tool_uses?: number;
}

class AnalyticsService {
  private sessionId: string;
  private eventQueue: any[] = [];
  private isOnline: boolean = navigator.onLine;

  constructor() {
    this.sessionId = getSessionId();
    this.setupEventListeners();
    this.startBatchUpload();
  }

  private setupEventListeners() {
    // 监听网络状态
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.flushEventQueue();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });

    // 页面卸载时上传剩余数据
    window.addEventListener('beforeunload', () => {
      this.flushEventQueue(true);
    });
  }

  private async makeRequest(endpoint: string, data: any, sync: boolean = false): Promise<any> {
    const url = `${API_BASE_URL}/api/v1/analytics${endpoint}`;
    
    const options: RequestInit = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    };

    if (sync && 'sendBeacon' in navigator) {
      // 使用 sendBeacon 进行同步发送（页面卸载时）
      const blob = new Blob([JSON.stringify(data)], { type: 'application/json' });
      return navigator.sendBeacon(url, blob);
    }

    try {
      const response = await fetch(url, options);
      return await response.json();
    } catch (error) {
      console.warn('Analytics request failed:', error);
      return { success: false, error: error.message };
    }
  }

  private addToQueue(event: any) {
    this.eventQueue.push({
      ...event,
      timestamp: new Date().toISOString(),
      session_id: this.sessionId
    });

    // 如果队列太大，立即上传
    if (this.eventQueue.length >= 10) {
      this.flushEventQueue();
    }
  }

  private async flushEventQueue(sync: boolean = false) {
    if (this.eventQueue.length === 0 || (!this.isOnline && !sync)) {
      return;
    }

    const events = [...this.eventQueue];
    this.eventQueue = [];

    try {
      await this.makeRequest('/batch', { events }, sync);
    } catch (error) {
      // 如果上传失败，将事件重新加入队列
      if (!sync) {
        this.eventQueue.unshift(...events);
      }
    }
  }

  private startBatchUpload() {
    // 每30秒上传一次数据
    setInterval(() => {
      this.flushEventQueue();
    }, 30000);
  }

  // 记录工具使用
  trackToolUsage(data: ToolUsageData) {
    this.addToQueue({
      type: 'tool_usage',
      ...data
    });
  }

  // 记录页面访问
  trackPageView(data: PageViewData) {
    this.addToQueue({
      type: 'page_view',
      ...data
    });
  }

  // 更新会话信息
  updateSession(data: Partial<SessionData> = {}) {
    this.addToQueue({
      type: 'session',
      session_id: this.sessionId,
      page_views: 1,
      tool_uses: 0,
      ...data
    });
  }

  // 获取工具使用统计
  async getToolUsageStats() {
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/analytics/tool-usage-stats`);
      return await response.json();
    } catch (error) {
      console.error('Failed to get tool usage stats:', error);
      return { success: false, data: [] };
    }
  }

  // 获取页面访问统计
  async getPageViewStats() {
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/analytics/page-view-stats`);
      return await response.json();
    } catch (error) {
      console.error('Failed to get page view stats:', error);
      return { success: false, data: [] };
    }
  }

  // 获取总体运营统计
  async getOverallAnalytics() {
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/analytics/overall`);
      return await response.json();
    } catch (error) {
      console.error('Failed to get overall analytics:', error);
      return { success: false, data: {} };
    }
  }

  // 获取热门工具排行
  async getTopTools(limit: number = 10) {
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/analytics/top-tools?limit=${limit}`);
      return await response.json();
    } catch (error) {
      console.error('Failed to get top tools:', error);
      return { success: false, data: [] };
    }
  }

  // 获取使用趋势
  async getUsageTrend(days: number = 7) {
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/analytics/usage-trend?days=${days}`);
      return await response.json();
    } catch (error) {
      console.error('Failed to get usage trend:', error);
      return { success: false, data: [] };
    }
  }

  // 获取实时统计
  async getRealTimeStats() {
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/analytics/realtime`);
      return await response.json();
    } catch (error) {
      console.error('Failed to get realtime stats:', error);
      return { success: false, data: {} };
    }
  }

  // 手动上传队列中的数据
  flush() {
    this.flushEventQueue();
  }

  // 获取当前会话ID
  getSessionId(): string {
    return this.sessionId;
  }
}

// 创建单例实例
export const analyticsService = new AnalyticsService();

// 页面访问追踪Hook
export const usePageTracking = () => {
  const trackPageView = (path: string, title?: string) => {
    analyticsService.trackPageView({
      page_path: path,
      page_title: title || document.title
    });
    analyticsService.updateSession({ page_views: 1 });
  };

  return { trackPageView };
};

// 工具使用追踪Hook
export const useToolTracking = () => {
  const trackToolUsage = (toolId: string, toolName: string, usageType: 'view' | 'use' | 'calculate' = 'view', usageData?: any) => {
    analyticsService.trackToolUsage({
      tool_id: toolId,
      tool_name: toolName,
      usage_type: usageType,
      usage_data: usageData
    });
    
    if (usageType !== 'view') {
      analyticsService.updateSession({ tool_uses: 1 });
    }
  };

  return { trackToolUsage };
};
