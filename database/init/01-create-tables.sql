-- SellerBox 用户反馈数据库初始化脚本
-- 创建时间: 2025-01-21

USE sellerbox_db;

-- 创建用户反馈表
CREATE TABLE IF NOT EXISTS user_feedback (
    id INT AUTO_INCREMENT PRIMARY KEY,
    feedback_type ENUM('general', 'bug', 'feature', 'improvement') NOT NULL DEFAULT 'general',
    rating INT CHECK (rating >= 1 AND rating <= 5),
    message TEXT NOT NULL,
    email VARCHAR(255) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    ip_address VARCHAR(45) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('new', 'in_progress', 'resolved', 'closed') DEFAULT 'new',
    admin_notes TEXT DEFAULT NULL,
    INDEX idx_feedback_type (feedback_type),
    INDEX idx_rating (rating),
    INDEX idx_created_at (created_at),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建反馈统计视图
CREATE VIEW feedback_stats AS
SELECT 
    feedback_type,
    COUNT(*) as total_count,
    AVG(rating) as avg_rating,
    COUNT(CASE WHEN status = 'new' THEN 1 END) as new_count,
    COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_count,
    COUNT(CASE WHEN status = 'resolved' THEN 1 END) as resolved_count,
    COUNT(CASE WHEN status = 'closed' THEN 1 END) as closed_count
FROM user_feedback 
GROUP BY feedback_type;

-- 创建总体统计视图
CREATE VIEW overall_stats AS
SELECT 
    COUNT(*) as total_feedback,
    AVG(rating) as overall_avg_rating,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as weekly_count,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as monthly_count
FROM user_feedback;

-- 插入一些示例数据（可选）
INSERT INTO user_feedback (feedback_type, rating, message, email, status) VALUES
('general', 5, '网站设计很棒，工具很实用！', '<EMAIL>', 'resolved'),
('feature', 4, '希望能添加更多货币换算选项', '<EMAIL>', 'in_progress'),
('bug', 3, '在移动端使用时偶尔会卡顿', '<EMAIL>', 'new'),
('improvement', 5, '标题修改器功能非常好用，节省了很多时间', '<EMAIL>', 'resolved'),
('general', 4, '免费使用真的很棒，感谢开发者！', NULL, 'new');

-- 创建管理员用户表
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(255) NOT NULL UNIQUE COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    full_name VARCHAR(100) DEFAULT NULL COMMENT '全名',
    role ENUM('super_admin', 'admin', 'viewer') DEFAULT 'admin' COMMENT '角色',
    status ENUM('active', 'inactive', 'locked') DEFAULT 'active' COMMENT '状态',
    last_login TIMESTAMP NULL DEFAULT NULL COMMENT '最后登录时间',
    login_attempts INT DEFAULT 0 COMMENT '登录尝试次数',
    locked_until TIMESTAMP NULL DEFAULT NULL COMMENT '锁定到期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT DEFAULT NULL COMMENT '创建者ID',
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_role (role),
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建用户会话表（用于JWT token管理）
CREATE TABLE IF NOT EXISTS admin_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token_hash VARCHAR(255) NOT NULL COMMENT 'JWT token哈希',
    refresh_token_hash VARCHAR(255) DEFAULT NULL COMMENT '刷新token哈希',
    ip_address VARCHAR(45) DEFAULT NULL COMMENT '登录IP',
    user_agent TEXT DEFAULT NULL COMMENT '用户代理',
    expires_at TIMESTAMP NOT NULL COMMENT 'token过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_token_hash (token_hash),
    INDEX idx_expires_at (expires_at),
    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建操作日志表
CREATE TABLE IF NOT EXISTS admin_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT DEFAULT NULL,
    action VARCHAR(100) NOT NULL COMMENT '操作类型',
    resource VARCHAR(100) DEFAULT NULL COMMENT '操作资源',
    resource_id INT DEFAULT NULL COMMENT '资源ID',
    details JSON DEFAULT NULL COMMENT '操作详情',
    ip_address VARCHAR(45) DEFAULT NULL COMMENT '操作IP',
    user_agent TEXT DEFAULT NULL COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认管理员账户（密码：admin123）
-- 注意：实际部署时应该修改默认密码
INSERT INTO admin_users (username, email, password_hash, full_name, role, status) VALUES
('admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', 'super_admin', 'active');

-- 创建系统公告表
CREATE TABLE IF NOT EXISTS system_announcements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL COMMENT '公告标题',
    content TEXT NOT NULL COMMENT '公告内容',
    type ENUM('info', 'warning', 'success', 'error', 'maintenance') DEFAULT 'info' COMMENT '公告类型',
    priority INT DEFAULT 0 COMMENT '优先级，数字越大优先级越高',
    status ENUM('draft', 'active', 'inactive', 'expired') DEFAULT 'draft' COMMENT '公告状态',
    start_time TIMESTAMP NULL DEFAULT NULL COMMENT '开始显示时间',
    end_time TIMESTAMP NULL DEFAULT NULL COMMENT '结束显示时间',
    target_audience ENUM('all', 'admin', 'user') DEFAULT 'all' COMMENT '目标受众',
    display_position ENUM('header', 'banner', 'popup') DEFAULT 'header' COMMENT '显示位置',
    is_closable BOOLEAN DEFAULT TRUE COMMENT '是否可关闭',
    click_action VARCHAR(500) DEFAULT NULL COMMENT '点击动作（URL或其他）',
    view_count INT DEFAULT 0 COMMENT '查看次数',
    created_by INT DEFAULT NULL COMMENT '创建者ID',
    updated_by INT DEFAULT NULL COMMENT '更新者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_type (type),
    INDEX idx_start_time (start_time),
    INDEX idx_end_time (end_time),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES admin_users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建公告多语言支持表
CREATE TABLE IF NOT EXISTS announcement_translations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    announcement_id INT NOT NULL,
    language_code VARCHAR(10) NOT NULL COMMENT '语言代码 (zh-CN, en, ja等)',
    title VARCHAR(255) NOT NULL COMMENT '翻译后的标题',
    content TEXT NOT NULL COMMENT '翻译后的内容',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_announcement_language (announcement_id, language_code),
    INDEX idx_language_code (language_code),
    FOREIGN KEY (announcement_id) REFERENCES system_announcements(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建用户公告查看记录表（用于统计和个性化显示）
CREATE TABLE IF NOT EXISTS user_announcement_views (
    id INT AUTO_INCREMENT PRIMARY KEY,
    announcement_id INT NOT NULL,
    user_session VARCHAR(255) NOT NULL COMMENT '用户会话标识',
    ip_address VARCHAR(45) DEFAULT NULL COMMENT '用户IP',
    user_agent TEXT DEFAULT NULL COMMENT '用户代理',
    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_closed BOOLEAN DEFAULT FALSE COMMENT '是否已关闭',
    closed_at TIMESTAMP NULL DEFAULT NULL COMMENT '关闭时间',
    INDEX idx_announcement_id (announcement_id),
    INDEX idx_user_session (user_session),
    INDEX idx_viewed_at (viewed_at),
    FOREIGN KEY (announcement_id) REFERENCES system_announcements(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建公告统计视图
CREATE VIEW announcement_stats AS
SELECT
    a.id,
    a.title,
    a.type,
    a.status,
    a.priority,
    a.start_time,
    a.end_time,
    a.view_count,
    COUNT(DISTINCT uav.user_session) as unique_viewers,
    COUNT(uav.id) as total_views,
    COUNT(CASE WHEN uav.is_closed = TRUE THEN 1 END) as closed_count,
    (COUNT(CASE WHEN uav.is_closed = TRUE THEN 1 END) * 100.0 / NULLIF(COUNT(uav.id), 0)) as close_rate
FROM system_announcements a
LEFT JOIN user_announcement_views uav ON a.id = uav.announcement_id
GROUP BY a.id, a.title, a.type, a.status, a.priority, a.start_time, a.end_time, a.view_count;

-- 插入示例公告数据
INSERT INTO system_announcements (title, content, type, priority, status, start_time, end_time, target_audience, display_position, is_closable, created_by) VALUES
('系统维护通知', '系统将于今晚22:00-24:00进行维护升级，期间可能影响部分功能使用，请提前保存工作内容。', 'maintenance', 10, 'active', NOW(), DATE_ADD(NOW(), INTERVAL 1 DAY), 'all', 'header', TRUE, 1),
('新功能上线', '我们新增了装箱计算器功能，帮助您更好地规划货物装箱方案！', 'success', 5, 'active', NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY), 'all', 'header', TRUE, 1),
('使用提示', '为了获得更好的体验，建议使用Chrome或Firefox浏览器访问本站。', 'info', 1, 'active', NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 'all', 'header', TRUE, 1);

-- 插入公告的多语言翻译
INSERT INTO announcement_translations (announcement_id, language_code, title, content) VALUES
-- 系统维护通知的翻译
(1, 'en', 'System Maintenance Notice', 'The system will undergo maintenance and upgrades from 22:00-24:00 tonight, which may affect some functions. Please save your work in advance.'),
(1, 'ja', 'システムメンテナンス通知', 'システムは今夜22:00-24:00にメンテナンスとアップグレードを行います。一部の機能に影響する可能性がありますので、事前に作業内容を保存してください。'),
(1, 'id', 'Pemberitahuan Pemeliharaan Sistem', 'Sistem akan menjalani pemeliharaan dan peningkatan dari pukul 22:00-24:00 malam ini, yang mungkin mempengaruhi beberapa fungsi. Harap simpan pekerjaan Anda terlebih dahulu.'),
(1, 'vi', 'Thông báo bảo trì hệ thống', 'Hệ thống sẽ được bảo trì và nâng cấp từ 22:00-24:00 tối nay, có thể ảnh hưởng đến một số chức năng. Vui lòng lưu công việc của bạn trước.'),
-- 新功能上线的翻译
(2, 'en', 'New Feature Launch', 'We have added a new Container Loading Calculator to help you better plan your cargo loading solutions!'),
(2, 'ja', '新機能リリース', 'コンテナ積載計算機能を追加しました。貨物の積載プランをより良く計画するのに役立ちます！'),
(2, 'id', 'Peluncuran Fitur Baru', 'Kami telah menambahkan Kalkulator Pemuatan Kontainer baru untuk membantu Anda merencanakan solusi pemuatan kargo dengan lebih baik!'),
(2, 'vi', 'Ra mắt tính năng mới', 'Chúng tôi đã thêm Máy tính Tải Container mới để giúp bạn lập kế hoạch giải pháp tải hàng tốt hơn!'),
-- 使用提示的翻译
(3, 'en', 'Usage Tips', 'For a better experience, we recommend using Chrome or Firefox browser to access this site.'),
(3, 'ja', '使用のヒント', 'より良い体験のために、ChromeまたはFirefoxブラウザでこのサイトにアクセスすることをお勧めします。'),
(3, 'id', 'Tips Penggunaan', 'Untuk pengalaman yang lebih baik, kami merekomendasikan menggunakan browser Chrome atau Firefox untuk mengakses situs ini.'),
(3, 'vi', 'Mẹo sử dụng', 'Để có trải nghiệm tốt hơn, chúng tôi khuyên bạn nên sử dụng trình duyệt Chrome hoặc Firefox để truy cập trang web này.');

-- 创建用于API的用户（如果需要）
-- CREATE USER IF NOT EXISTS 'api_user'@'%' IDENTIFIED BY 'api_pass_2025';
-- GRANT SELECT, INSERT, UPDATE ON sellerbox_db.user_feedback TO 'api_user'@'%';
-- GRANT SELECT ON sellerbox_db.feedback_stats TO 'api_user'@'%';
-- GRANT SELECT ON sellerbox_db.overall_stats TO 'api_user'@'%';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON sellerbox_db.admin_users TO 'api_user'@'%';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON sellerbox_db.admin_sessions TO 'api_user'@'%';
-- GRANT SELECT, INSERT ON sellerbox_db.admin_logs TO 'api_user'@'%';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON sellerbox_db.system_announcements TO 'api_user'@'%';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON sellerbox_db.announcement_translations TO 'api_user'@'%';
-- GRANT SELECT, INSERT ON sellerbox_db.user_announcement_views TO 'api_user'@'%';
-- GRANT SELECT ON sellerbox_db.announcement_stats TO 'api_user'@'%';
-- FLUSH PRIVILEGES;
