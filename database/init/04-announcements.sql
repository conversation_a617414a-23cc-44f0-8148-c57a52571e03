-- SellerBox 公告系统数据库初始化脚本
-- 创建时间: 2025-01-16

USE sellerbox_db;

-- 创建系统公告表
CREATE TABLE IF NOT EXISTS system_announcements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL COMMENT '公告标题',
    content TEXT NOT NULL COMMENT '公告内容',
    type ENUM('info', 'warning', 'success', 'error', 'maintenance') DEFAULT 'info' COMMENT '公告类型',
    priority INT DEFAULT 0 COMMENT '优先级，数字越大优先级越高',
    status ENUM('draft', 'active', 'inactive', 'expired') DEFAULT 'draft' COMMENT '公告状态',
    start_time TIMESTAMP NULL DEFAULT NULL COMMENT '开始显示时间',
    end_time TIMESTAMP NULL DEFAULT NULL COMMENT '结束显示时间',
    target_audience ENUM('all', 'admin', 'user') DEFAULT 'all' COMMENT '目标受众',
    display_position ENUM('header', 'banner', 'popup') DEFAULT 'header' COMMENT '显示位置',
    is_closable BOOLEAN DEFAULT TRUE COMMENT '是否可关闭',
    click_action VARCHAR(500) DEFAULT NULL COMMENT '点击动作（URL或其他）',
    view_count INT DEFAULT 0 COMMENT '查看次数',
    created_by INT DEFAULT NULL COMMENT '创建者ID',
    updated_by INT DEFAULT NULL COMMENT '更新者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_type (type),
    INDEX idx_start_time (start_time),
    INDEX idx_end_time (end_time),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES admin_users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建公告多语言支持表
CREATE TABLE IF NOT EXISTS announcement_translations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    announcement_id INT NOT NULL,
    language_code VARCHAR(10) NOT NULL COMMENT '语言代码 (zh-CN, en, ja等)',
    title VARCHAR(255) NOT NULL COMMENT '翻译后的标题',
    content TEXT NOT NULL COMMENT '翻译后的内容',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_announcement_language (announcement_id, language_code),
    INDEX idx_language_code (language_code),
    FOREIGN KEY (announcement_id) REFERENCES system_announcements(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建用户公告查看记录表（用于统计和个性化显示）
CREATE TABLE IF NOT EXISTS user_announcement_views (
    id INT AUTO_INCREMENT PRIMARY KEY,
    announcement_id INT NOT NULL,
    user_session VARCHAR(255) NOT NULL COMMENT '用户会话标识',
    ip_address VARCHAR(45) DEFAULT NULL COMMENT '用户IP',
    user_agent TEXT DEFAULT NULL COMMENT '用户代理',
    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_closed BOOLEAN DEFAULT FALSE COMMENT '是否已关闭',
    closed_at TIMESTAMP NULL DEFAULT NULL COMMENT '关闭时间',
    INDEX idx_announcement_id (announcement_id),
    INDEX idx_user_session (user_session),
    INDEX idx_viewed_at (viewed_at),
    FOREIGN KEY (announcement_id) REFERENCES system_announcements(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建公告统计视图
CREATE OR REPLACE VIEW announcement_stats AS
SELECT 
    a.id,
    a.title,
    a.type,
    a.status,
    a.priority,
    a.start_time,
    a.end_time,
    a.view_count,
    COUNT(DISTINCT uav.user_session) as unique_viewers,
    COUNT(uav.id) as total_views,
    COUNT(CASE WHEN uav.is_closed = TRUE THEN 1 END) as closed_count,
    (COUNT(CASE WHEN uav.is_closed = TRUE THEN 1 END) * 100.0 / NULLIF(COUNT(uav.id), 0)) as close_rate
FROM system_announcements a
LEFT JOIN user_announcement_views uav ON a.id = uav.announcement_id
GROUP BY a.id, a.title, a.type, a.status, a.priority, a.start_time, a.end_time, a.view_count;

-- 插入示例公告数据
INSERT IGNORE INTO system_announcements (title, content, type, priority, status, start_time, end_time, target_audience, display_position, is_closable, created_by) VALUES
('系统维护通知', '系统将于今晚22:00-24:00进行维护升级，期间可能影响部分功能使用，请提前保存工作内容。', 'maintenance', 10, 'active', NOW(), DATE_ADD(NOW(), INTERVAL 1 DAY), 'all', 'header', TRUE, 1),
('新功能上线', '我们新增了装箱计算器功能，帮助您更好地规划货物装箱方案！', 'success', 5, 'active', NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY), 'all', 'header', TRUE, 1),
('使用提示', '为了获得更好的体验，建议使用Chrome或Firefox浏览器访问本站。', 'info', 1, 'active', NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 'all', 'header', TRUE, 1);

-- 插入公告的多语言翻译
INSERT IGNORE INTO announcement_translations (announcement_id, language_code, title, content) VALUES
-- 系统维护通知的翻译
(1, 'en', 'System Maintenance Notice', 'The system will undergo maintenance and upgrades from 22:00-24:00 tonight, which may affect some functions. Please save your work in advance.'),
(1, 'ja', 'システムメンテナンス通知', 'システムは今夜22:00-24:00にメンテナンスとアップグレードを行います。一部の機能に影響する可能性がありますので、事前に作業内容を保存してください。'),
(1, 'id', 'Pemberitahuan Pemeliharaan Sistem', 'Sistem akan menjalani pemeliharaan dan peningkatan dari pukul 22:00-24:00 malam ini, yang mungkin mempengaruhi beberapa fungsi. Harap simpan pekerjaan Anda terlebih dahulu.'),
(1, 'vi', 'Thông báo bảo trì hệ thống', 'Hệ thống sẽ được bảo trì và nâng cấp từ 22:00-24:00 tối nay, có thể ảnh hưởng đến một số chức năng. Vui lòng lưu công việc của bạn trước.'),
-- 新功能上线的翻译
(2, 'en', 'New Feature Launch', 'We have added a new Container Loading Calculator to help you better plan your cargo loading solutions!'),
(2, 'ja', '新機能リリース', 'コンテナ積載計算機能を追加しました。貨物の積載プランをより良く計画するのに役立ちます！'),
(2, 'id', 'Peluncuran Fitur Baru', 'Kami telah menambahkan Kalkulator Pemuatan Kontainer baru untuk membantu Anda merencanakan solusi pemuatan kargo dengan lebih baik!'),
(2, 'vi', 'Ra mắt tính năng mới', 'Chúng tôi đã thêm Máy tính Tải Container mới để giúp bạn lập kế hoạch giải pháp tải hàng tốt hơn!'),
-- 使用提示的翻译
(3, 'en', 'Usage Tips', 'For a better experience, we recommend using Chrome or Firefox browser to access this site.'),
(3, 'ja', '使用のヒント', 'より良い体験のために、ChromeまたはFirefoxブラウザでこのサイトにアクセスすることをお勧めします。'),
(3, 'id', 'Tips Penggunaan', 'Untuk pengalaman yang lebih baik, kami merekomendasikan menggunakan browser Chrome atau Firefox untuk mengakses situs ini.'),
(3, 'vi', 'Mẹo sử dụng', 'Để có trải nghiệm tốt hơn, chúng tôi khuyên bạn nên sử dụng trình duyệt Chrome hoặc Firefox để truy cập trang web này.');
