-- SellerBox 数据分析表结构
-- 创建时间: 2025-01-21

USE sellerbox_db;

-- 创建工具使用统计表
CREATE TABLE IF NOT EXISTS tool_usage (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tool_id VARCHAR(50) NOT NULL COMMENT '工具ID',
    tool_name VARCHAR(100) NOT NULL COMMENT '工具名称',
    user_ip VARCHAR(45) DEFAULT NULL COMMENT '用户IP',
    user_agent TEXT DEFAULT NULL COMMENT '用户代理',
    session_id VARCHAR(100) DEFAULT NULL COMMENT '会话ID',
    usage_type ENUM('view', 'use', 'calculate') DEFAULT 'view' COMMENT '使用类型',
    usage_data JSON DEFAULT NULL COMMENT '使用数据（JSON格式）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_tool_id (tool_id),
    INDEX idx_created_at (created_at),
    INDEX idx_usage_type (usage_type),
    INDEX idx_user_ip (user_ip)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建页面访问统计表
CREATE TABLE IF NOT EXISTS page_views (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_path VARCHAR(255) NOT NULL COMMENT '页面路径',
    page_title VARCHAR(255) DEFAULT NULL COMMENT '页面标题',
    user_ip VARCHAR(45) DEFAULT NULL COMMENT '用户IP',
    user_agent TEXT DEFAULT NULL COMMENT '用户代理',
    session_id VARCHAR(100) DEFAULT NULL COMMENT '会话ID',
    referrer VARCHAR(500) DEFAULT NULL COMMENT '来源页面',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_page_path (page_path),
    INDEX idx_created_at (created_at),
    INDEX idx_user_ip (user_ip)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建用户会话表
CREATE TABLE IF NOT EXISTS user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(100) NOT NULL UNIQUE COMMENT '会话ID',
    user_ip VARCHAR(45) DEFAULT NULL COMMENT '用户IP',
    user_agent TEXT DEFAULT NULL COMMENT '用户代理',
    first_visit TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '首次访问时间',
    last_visit TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后访问时间',
    page_views INT DEFAULT 0 COMMENT '页面浏览数',
    tool_uses INT DEFAULT 0 COMMENT '工具使用数',
    country VARCHAR(50) DEFAULT NULL COMMENT '国家',
    city VARCHAR(100) DEFAULT NULL COMMENT '城市',
    INDEX idx_session_id (session_id),
    INDEX idx_user_ip (user_ip),
    INDEX idx_first_visit (first_visit)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建工具使用统计视图
CREATE VIEW tool_usage_stats AS
SELECT 
    tool_id,
    tool_name,
    COUNT(*) as total_uses,
    COUNT(DISTINCT user_ip) as unique_users,
    COUNT(DISTINCT session_id) as unique_sessions,
    COUNT(CASE WHEN usage_type = 'view' THEN 1 END) as views,
    COUNT(CASE WHEN usage_type = 'use' THEN 1 END) as uses,
    COUNT(CASE WHEN usage_type = 'calculate' THEN 1 END) as calculations,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 1 END) as daily_uses,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as weekly_uses,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as monthly_uses
FROM tool_usage 
GROUP BY tool_id, tool_name;

-- 创建页面访问统计视图
CREATE VIEW page_view_stats AS
SELECT 
    page_path,
    page_title,
    COUNT(*) as total_views,
    COUNT(DISTINCT user_ip) as unique_visitors,
    COUNT(DISTINCT session_id) as unique_sessions,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 1 END) as daily_views,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as weekly_views,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as monthly_views
FROM page_views 
GROUP BY page_path, page_title;

-- 创建总体运营统计视图
CREATE VIEW overall_analytics AS
SELECT 
    (SELECT COUNT(*) FROM user_sessions) as total_sessions,
    (SELECT COUNT(DISTINCT user_ip) FROM user_sessions) as unique_visitors,
    (SELECT COUNT(*) FROM page_views) as total_page_views,
    (SELECT COUNT(*) FROM tool_usage) as total_tool_uses,
    (SELECT COUNT(DISTINCT tool_id) FROM tool_usage) as active_tools,
    (SELECT COUNT(*) FROM user_feedback) as total_feedback,
    (SELECT COUNT(*) FROM page_views WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)) as daily_page_views,
    (SELECT COUNT(*) FROM tool_usage WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)) as daily_tool_uses,
    (SELECT COUNT(DISTINCT session_id) FROM page_views WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)) as daily_active_sessions,
    (SELECT COUNT(*) FROM page_views WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)) as weekly_page_views,
    (SELECT COUNT(*) FROM tool_usage WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)) as weekly_tool_uses,
    (SELECT COUNT(DISTINCT session_id) FROM page_views WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)) as weekly_active_sessions;

-- 插入一些示例数据
INSERT INTO tool_usage (tool_id, tool_name, user_ip, usage_type, usage_data) VALUES
('title-fixer', '标题修改器', '*************', 'use', '{"input_length": 125, "output_count": 3}'),
('currency-converter', '货币换算器', '*************', 'calculate', '{"from": "USD", "to": "CNY", "amount": 100}'),
('unit-converter', '单位换算器', '*************', 'calculate', '{"from": "kg", "to": "lb", "value": 10}'),
('fba-calculator', 'FBA计算器', '*************', 'calculate', '{"product_price": 29.99, "category": "electronics"}'),
('title-analyzer', '标题分析器', '*************', 'use', '{"title_length": 80, "keyword_count": 5}'),
('profit-calculator', '利润计算器', '*************', 'calculate', '{"revenue": 1000, "costs": 600}');

INSERT INTO page_views (page_path, page_title, user_ip, referrer) VALUES
('/', 'SellerBox - 跨境卖家工具箱', '*************', 'https://google.com'),
('/tools/title-fixer', '标题修改器 - SellerBox', '*************', '/'),
('/tools/currency-converter', '货币换算器 - SellerBox', '*************', '/'),
('/tools/unit-converter', '单位换算器 - SellerBox', '*************', '/'),
('/feedback', '意见反馈 - SellerBox', '*************', '/'),
('/tools/fba-calculator', 'FBA计算器 - SellerBox', '*************', '/');

INSERT INTO user_sessions (session_id, user_ip, page_views, tool_uses) VALUES
('sess_001', '*************', 3, 2),
('sess_002', '*************', 2, 1),
('sess_003', '*************', 1, 1),
('sess_004', '*************', 4, 0),
('sess_005', '*************', 2, 2);

-- 创建汇率数据存储表
CREATE TABLE IF NOT EXISTS exchange_rates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    base_currency VARCHAR(3) NOT NULL DEFAULT 'USD' COMMENT '基础货币（通常为USD）',
    target_currency VARCHAR(3) NOT NULL COMMENT '目标货币',
    rate DECIMAL(15,8) NOT NULL COMMENT '汇率值（保留8位小数）',
    api_source VARCHAR(50) DEFAULT 'freecurrencyapi' COMMENT 'API数据源',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_currency_pair (base_currency, target_currency),
    INDEX idx_created_at (created_at),
    INDEX idx_target_currency (target_currency),
    UNIQUE KEY unique_rate_time (base_currency, target_currency, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='汇率历史数据表';

-- 创建汇率数据清理事件（每天清理120天前的数据）
SET GLOBAL event_scheduler = ON;

DELIMITER $$
CREATE EVENT IF NOT EXISTS cleanup_old_exchange_rates
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
BEGIN
    DELETE FROM exchange_rates
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 120 DAY);

    -- 记录清理日志
    INSERT INTO tool_usage (tool_id, tool_name, usage_type, usage_data)
    VALUES ('system', '汇率数据清理', 'system', JSON_OBJECT('action', 'cleanup_old_rates', 'retention_days', 120));
END$$
DELIMITER ;

-- 创建汇率统计视图
CREATE VIEW exchange_rate_stats AS
SELECT
    target_currency,
    COUNT(*) as total_records,
    MIN(rate) as min_rate,
    MAX(rate) as max_rate,
    AVG(rate) as avg_rate,
    MIN(created_at) as earliest_record,
    MAX(created_at) as latest_record,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 1 END) as daily_records,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as weekly_records,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as monthly_records
FROM exchange_rates
WHERE base_currency = 'USD'
GROUP BY target_currency;

-- 插入一些示例汇率数据
INSERT INTO exchange_rates (base_currency, target_currency, rate, created_at) VALUES
('USD', 'CNY', 7.2345, DATE_SUB(NOW(), INTERVAL 2 HOUR)),
('USD', 'EUR', 0.8567, DATE_SUB(NOW(), INTERVAL 2 HOUR)),
('USD', 'GBP', 0.7834, DATE_SUB(NOW(), INTERVAL 2 HOUR)),
('USD', 'JPY', 149.56, DATE_SUB(NOW(), INTERVAL 2 HOUR)),
('USD', 'CNY', 7.2389, DATE_SUB(NOW(), INTERVAL 1 HOUR)),
('USD', 'EUR', 0.8571, DATE_SUB(NOW(), INTERVAL 1 HOUR)),
('USD', 'GBP', 0.7841, DATE_SUB(NOW(), INTERVAL 1 HOUR)),
('USD', 'JPY', 149.78, DATE_SUB(NOW(), INTERVAL 1 HOUR)),
('USD', 'CNY', 7.2412, NOW()),
('USD', 'EUR', 0.8574, NOW()),
('USD', 'GBP', 0.7845, NOW()),
('USD', 'JPY', 149.82, NOW());
