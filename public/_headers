# Cloudflare Pages 头部规则

# Google 验证文件
/googlea175af350543cbcc.html
  Content-Type: text/plain
  Cache-Control: public, max-age=3600

# 百度验证文件
/baidu_verify_codeva-U1LCnWiNN1.html
  Content-Type: text/html
  Cache-Control: public, max-age=3600

# 旧的百度验证文件（保留兼容性）
/baidu_verify_codeva-G83azNsIXv.html
  Content-Type: text/html
  Cache-Control: public, max-age=3600

# Sitemap
/sitemap.xml
  Content-Type: application/xml
  Cache-Control: public, max-age=3600

# Robots.txt
/robots.txt
  Content-Type: text/plain
  Cache-Control: public, max-age=3600

# Manifest
/manifest.json
  Content-Type: application/json
  Cache-Control: public, max-age=86400

# 静态资源
/*.css
  Cache-Control: public, max-age=31536000, immutable

/*.js
  Cache-Control: public, max-age=31536000, immutable

/*.png
  Cache-Control: public, max-age=31536000, immutable

/*.jpg
  Cache-Control: public, max-age=31536000, immutable

/*.svg
  Cache-Control: public, max-age=31536000, immutable

# 安全头部
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()
