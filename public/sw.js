// SellerBox Service Worker
const CACHE_NAME = 'sellerbox-v1.0.2';
const STATIC_CACHE_NAME = 'sellerbox-static-v1.0.2';
const DYNAMIC_CACHE_NAME = 'sellerbox-dynamic-v1.0.2';

// 需要缓存的静态资源
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/icon.png',
  '/robots.txt',
  '/sitemap.xml'
];

// 需要缓存的外部 API 路径（使用网络优先策略）
const EXTERNAL_API_CACHE_PATTERNS = [
  /^https:\/\/api\.freecurrencyapi\.com/,
  /^https:\/\/api\.siliconflow\.cn/
];

// 本地 API 路径（使用网络优先策略，短时间缓存）
const LOCAL_API_CACHE_PATTERNS = [
  /\/api\/v1\/announcements/,
  /\/api\/v1\/feedback/,
  /\/api\/v1\/analytics/
];

// 不缓存的 API 路径（总是从网络获取）
const NO_CACHE_API_PATTERNS = [
  /\/api\/v1\/announcements\/active/,  // 公告API不缓存，确保实时性
  /\/api\/v1\/admin/                   // 管理员API不缓存
];

// 安装事件 - 缓存静态资源
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');

  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then((cache) => {
        console.log('Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('Service Worker: Installation complete');
        // 只有在有现有控制器时才跳过等待（表示这是一个更新）
        if (self.clients && self.clients.claim) {
          // 检查是否有现有的控制器
          return self.clients.matchAll().then(clients => {
            const hasControllingClient = clients.some(client => client.frameType === 'top-level');
            if (hasControllingClient) {
              console.log('Service Worker: Skipping waiting for update');
              return self.skipWaiting();
            } else {
              console.log('Service Worker: First install, not skipping waiting');
            }
          });
        }
      })
      .catch((error) => {
        console.error('Service Worker: Installation failed', error);
      })
  );
});

// 激活事件 - 清理旧缓存
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE_NAME && 
                cacheName !== DYNAMIC_CACHE_NAME &&
                cacheName !== CACHE_NAME) {
              console.log('Service Worker: Deleting old cache', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Activation complete');
        return self.clients.claim();
      })
  );
});

// 拦截网络请求
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // 跳过非 GET 请求
  if (request.method !== 'GET') {
    return;
  }

  // 跳过 Chrome 扩展请求
  if (url.protocol === 'chrome-extension:') {
    return;
  }

  // 处理静态资源
  if (STATIC_ASSETS.some(asset => url.pathname === asset)) {
    event.respondWith(cacheFirst(request, STATIC_CACHE_NAME));
    return;
  }

  // 处理不缓存的 API 请求（总是从网络获取）
  if (NO_CACHE_API_PATTERNS.some(pattern => pattern.test(request.url))) {
    event.respondWith(networkOnly(request));
    return;
  }

  // 处理外部 API 请求（网络优先，长时间缓存）
  if (EXTERNAL_API_CACHE_PATTERNS.some(pattern => pattern.test(request.url))) {
    event.respondWith(networkFirst(request, DYNAMIC_CACHE_NAME, 3600000)); // 1小时缓存
    return;
  }

  // 处理本地 API 请求（网络优先，短时间缓存）
  if (LOCAL_API_CACHE_PATTERNS.some(pattern => pattern.test(request.url))) {
    event.respondWith(networkFirst(request, DYNAMIC_CACHE_NAME, 300000)); // 5分钟缓存
    return;
  }

  // 处理页面请求 - 使用更宽松的策略
  if (request.headers.get('accept')?.includes('text/html')) {
    event.respondWith(networkFirstWithFallback(request, DYNAMIC_CACHE_NAME));
    return;
  }

  // 处理其他资源（CSS, JS, 图片等）- 使用更宽松的策略
  event.respondWith(cacheFirstWithFallback(request, DYNAMIC_CACHE_NAME));
});

// 缓存优先策略
async function cacheFirst(request, cacheName) {
  try {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      // 后台更新缓存
      fetch(request).then(response => {
        if (response.ok) {
          cache.put(request, response.clone());
        }
      }).catch(() => {
        // 网络错误时忽略
      });
      
      return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.error('Cache first strategy failed:', error);
    
    // 如果是页面请求，返回离线页面
    if (request.headers.get('accept')?.includes('text/html')) {
      const cache = await caches.open(STATIC_CACHE_NAME);
      return cache.match('/') || new Response('Offline', { status: 503 });
    }
    
    throw error;
  }
}

// 网络优先策略（带缓存时间控制）
async function networkFirst(request, cacheName, maxAge = 300000) {
  try {
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      // 添加时间戳到响应头
      const responseWithTimestamp = new Response(networkResponse.body, {
        status: networkResponse.status,
        statusText: networkResponse.statusText,
        headers: {
          ...Object.fromEntries(networkResponse.headers.entries()),
          'sw-cache-timestamp': Date.now().toString()
        }
      });
      cache.put(request, responseWithTimestamp.clone());
      return responseWithTimestamp;
    }

    return networkResponse;
  } catch (error) {
    console.log('Network failed, trying cache:', error);

    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);

    if (cachedResponse) {
      // 检查缓存是否过期
      const cacheTimestamp = cachedResponse.headers.get('sw-cache-timestamp');
      if (cacheTimestamp) {
        const age = Date.now() - parseInt(cacheTimestamp);
        if (age > maxAge) {
          console.log('Cache expired, removing:', request.url);
          cache.delete(request);
          throw new Error('Cache expired');
        }
      }
      return cachedResponse;
    }

    // 如果是页面请求，返回离线页面
    if (request.headers.get('accept')?.includes('text/html')) {
      const staticCache = await caches.open(STATIC_CACHE_NAME);
      return staticCache.match('/') || new Response('Offline', { status: 503 });
    }

    throw error;
  }
}

// 仅网络策略（不缓存）
async function networkOnly(request) {
  try {
    return await fetch(request);
  } catch (error) {
    console.log('Network only request failed:', error);
    throw error;
  }
}

// 更宽松的网络优先策略（带降级处理）
async function networkFirstWithFallback(request, cacheName) {
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      cache.put(request, networkResponse.clone()).catch(() => {
        // 缓存失败时忽略，不影响响应
      });
    }
    return networkResponse;
  } catch (error) {
    console.log('Network failed, trying cache:', error);

    try {
      const cache = await caches.open(cacheName);
      const cachedResponse = await cache.match(request);
      if (cachedResponse) {
        return cachedResponse;
      }
    } catch (cacheError) {
      console.log('Cache access failed:', cacheError);
    }

    // 如果是页面请求，返回基本的离线页面
    if (request.headers.get('accept')?.includes('text/html')) {
      return new Response(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>AmzOva - 离线模式</title>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .offline { color: #666; }
          </style>
        </head>
        <body>
          <h1>AmzOva 爱麦蛙</h1>
          <p class="offline">您当前处于离线状态，请检查网络连接后重试。</p>
          <button onclick="location.reload()">重新加载</button>
        </body>
        </html>
      `, {
        status: 200,
        headers: { 'Content-Type': 'text/html; charset=utf-8' }
      });
    }

    throw error;
  }
}

// 更宽松的缓存优先策略（带降级处理）
async function cacheFirstWithFallback(request, cacheName) {
  try {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);

    if (cachedResponse) {
      // 后台更新缓存，但不等待结果
      fetch(request).then(response => {
        if (response.ok) {
          cache.put(request, response.clone()).catch(() => {
            // 缓存更新失败时忽略
          });
        }
      }).catch(() => {
        // 网络错误时忽略
      });

      return cachedResponse;
    }

    // 缓存中没有，尝试网络请求
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone()).catch(() => {
        // 缓存失败时忽略，不影响响应
      });
    }

    return networkResponse;
  } catch (error) {
    console.log('Cache first with fallback failed:', error);

    // 如果是图片或其他资源，返回一个简单的占位符
    if (request.url.includes('.png') || request.url.includes('.jpg') || request.url.includes('.svg')) {
      return new Response('', { status: 204 }); // No Content
    }

    throw error;
  }
}

// 处理消息
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  } else if (event.data && event.data.type === 'CLEAR_ANNOUNCEMENT_CACHE') {
    clearAnnouncementCache();
  }
});

// 清除公告缓存的函数
async function clearAnnouncementCache() {
  try {
    const cacheNames = await caches.keys();
    const clearPromises = cacheNames.map(async (cacheName) => {
      const cache = await caches.open(cacheName);
      const requests = await cache.keys();

      // 删除公告相关的缓存条目
      const deletePromises = requests
        .filter(request =>
          request.url.includes('/api/v1/announcements') ||
          request.url.includes('/announcements')
        )
        .map(request => cache.delete(request));

      return Promise.all(deletePromises);
    });

    await Promise.all(clearPromises);
    console.log('Service Worker: 公告缓存已清除');
  } catch (error) {
    console.error('Service Worker: 清除公告缓存失败', error);
  }
}

// 后台同步（如果支持）
if ('sync' in self.registration) {
  self.addEventListener('sync', (event) => {
    if (event.tag === 'background-sync') {
      event.waitUntil(doBackgroundSync());
    }
  });
}

async function doBackgroundSync() {
  // 这里可以添加后台同步逻辑
  console.log('Background sync triggered');
}
