name = "amzova-backend-service"
main = "src/index.js"
compatibility_date = "2024-09-23"

# 自定义域名路由配置
[[routes]]
pattern = "api.amzova.com/*"
zone_name = "amzova.com"

[vars]
ENVIRONMENT = "production"
GEMINI_BASE_URL = "https://gemini-balance-c1w2.onrender.com"
GEMINI_API_KEY = "sk-kdjferKjidjrf9jihdhfnghjxkdefKKGHYI"
AI_TIMEOUT = "30000"
AI_MAX_RETRIES = "3"
AI_DEFAULT_MODEL = "gemini-2.5-flash-lite"
ENABLE_AI_MONITORING = "true"
ENABLE_ERROR_TRACKING = "true"
TURNSTILE_SECRET_KEY = "0x4AAAAAABnZT9BRzlZOBtxrgvvdo5Q-mZ8"

# 定时任务配置 - 每10分钟更新汇率数据
[triggers]
crons = ["*/10 * * * *"]

[[d1_databases]]
binding = "DB"
database_name = "amzova-production"
database_id = "d040d902-b8c7-41a5-8d65-5a44035f58ad"

[[r2_buckets]]
binding = "ASSETS"
bucket_name = "amzova-assets-0718"

# KV存储配置 - 用于汇率数据缓存
[[kv_namespaces]]
binding = "EXCHANGE_RATES_KV"
id = "c4c917541e5a4985a4f26ad12b6afb03"
preview_id = "38bd46d4f3a34cc29742f539178a5d8a"
