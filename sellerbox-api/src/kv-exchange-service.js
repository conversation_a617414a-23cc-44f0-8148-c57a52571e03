// KV优化的汇率服务 - 提升性能和用户体验
// 使用Cloudflare KV作为全球边缘缓存层

class KVExchangeRateService {
  constructor(env) {
    this.env = env;
    this.KV = env.EXCHANGE_RATES_KV;
    this.DB = env.DB;
    this.CACHE_TTL = 600; // 10分钟缓存
    this.HISTORICAL_CACHE_TTL = 3600; // 1小时缓存历史数据
  }

  // 获取最新汇率 - KV优先，DB备用
  async getLatestRates(baseCurrency = 'USD') {
    const cacheKey = `latest_rates_${baseCurrency}`;
    
    try {
      // 1. 首先尝试从KV获取缓存数据
      const cachedData = await this.KV.get(cacheKey, 'json');
      if (cachedData && this.isCacheValid(cachedData.timestamp)) {
        console.log('✅ 从KV缓存返回汇率数据');
        return {
          success: true,
          data: cachedData,
          source: 'KV_Cache'
        };
      }

      // 2. KV缓存失效，从数据库获取
      console.log('🔄 KV缓存失效，从数据库获取数据');
      const dbResult = await this.getLatestRatesFromDB(baseCurrency);
      
      if (dbResult.success) {
        // 3. 将数据存储到KV缓存
        await this.cacheLatestRates(cacheKey, dbResult.data);
        return dbResult;
      }

      throw new Error('数据库中无可用汇率数据');

    } catch (error) {
      console.error('❌ 获取汇率数据失败:', error);
      return {
        success: false,
        error: 'Failed to fetch exchange rates',
        message: '汇率服务暂时不可用，请稍后重试'
      };
    }
  }

  // 从数据库获取最新汇率
  async getLatestRatesFromDB(baseCurrency) {
    const result = await this.DB.prepare(`
      SELECT base_currency, target_currency, rate, created_at
      FROM exchange_rates
      WHERE created_at >= datetime('now', '-2 hours')
      ORDER BY created_at DESC
      LIMIT 50
    `).all();

    if (!result.results || result.results.length === 0) {
      return {
        success: false,
        error: 'No exchange rate data available'
      };
    }

    const rates = {};
    let lastUpdated = null;
    
    result.results.forEach(row => {
      if (!rates[row.target_currency]) {
        rates[row.target_currency] = row.rate;
        if (!lastUpdated || new Date(row.created_at) > new Date(lastUpdated)) {
          lastUpdated = row.created_at;
        }
      }
    });

    rates.USD = 1; // 基础货币

    return {
      success: true,
      data: {
        rates: rates,
        baseCurrency: baseCurrency,
        lastUpdated: lastUpdated || new Date().toISOString(),
        source: 'FreeCurrencyAPI',
        dataCount: Object.keys(rates).length,
        timestamp: Date.now()
      }
    };
  }

  // 获取历史汇率数据 - KV缓存优化
  async getHistoricalRates(base, target, range) {
    const cacheKey = `historical_${base}_${target}_${range}`;
    
    try {
      // 1. 尝试从KV获取缓存的历史数据
      const cachedData = await this.KV.get(cacheKey, 'json');
      if (cachedData && this.isHistoricalCacheValid(cachedData.timestamp, range)) {
        console.log('✅ 从KV缓存返回历史汇率数据');
        return {
          success: true,
          data: cachedData,
          source: 'KV_Cache'
        };
      }

      // 2. 从数据库获取历史数据
      console.log('🔄 从数据库获取历史汇率数据');
      const dbResult = await this.getHistoricalRatesFromDB(base, target, range);
      
      if (dbResult.success) {
        // 3. 缓存到KV
        await this.cacheHistoricalRates(cacheKey, dbResult.data);
        return dbResult;
      }

      throw new Error('数据库中无历史汇率数据');

    } catch (error) {
      console.error('❌ 获取历史汇率失败:', error);
      return {
        success: false,
        error: 'Failed to fetch historical rates',
        message: '历史汇率服务暂时不可用，请稍后重试'
      };
    }
  }

  // 从数据库获取历史汇率
  async getHistoricalRatesFromDB(base, target, range) {
    let timeCondition, orderBy;
    switch (range) {
      case '1d':
        timeCondition = "datetime('now', '-1 day')";
        orderBy = 'created_at ASC';
        break;
      case '1m':
        timeCondition = "datetime('now', '-30 days')";
        orderBy = 'created_at ASC';
        break;
      case '3m':
        timeCondition = "datetime('now', '-90 days')";
        orderBy = 'created_at ASC';
        break;
      default:
        timeCondition = "datetime('now', '-1 day')";
        orderBy = 'created_at ASC';
    }

    const result = await this.DB.prepare(`
      SELECT base_currency, target_currency, rate, created_at
      FROM exchange_rates
      WHERE base_currency = ? 
        AND target_currency = ?
        AND created_at >= ${timeCondition}
      ORDER BY ${orderBy}
      LIMIT 200
    `).bind(base, target).all();

    if (!result.results || result.results.length === 0) {
      return {
        success: false,
        error: 'No historical data available'
      };
    }

    const historicalRates = result.results.map(row => ({
      timestamp: row.created_at,
      rate: row.rate
    }));

    return {
      success: true,
      data: {
        base: base,
        target: target,
        range: range,
        rates: historicalRates,
        dataPoints: historicalRates.length,
        source: 'FreeCurrencyAPI',
        timestamp: Date.now()
      }
    };
  }

  // 缓存最新汇率到KV
  async cacheLatestRates(key, data) {
    try {
      await this.KV.put(key, JSON.stringify(data), {
        expirationTtl: this.CACHE_TTL
      });
      console.log('💾 汇率数据已缓存到KV');
    } catch (error) {
      console.warn('⚠️ KV缓存失败:', error);
    }
  }

  // 缓存历史汇率到KV
  async cacheHistoricalRates(key, data) {
    try {
      await this.KV.put(key, JSON.stringify(data), {
        expirationTtl: this.HISTORICAL_CACHE_TTL
      });
      console.log('💾 历史汇率数据已缓存到KV');
    } catch (error) {
      console.warn('⚠️ KV历史数据缓存失败:', error);
    }
  }

  // 检查缓存是否有效
  isCacheValid(timestamp) {
    const now = Date.now();
    const cacheAge = now - timestamp;
    return cacheAge < (this.CACHE_TTL * 1000);
  }

  // 检查历史数据缓存是否有效
  isHistoricalCacheValid(timestamp, range) {
    const now = Date.now();
    const cacheAge = now - timestamp;
    
    // 根据时间范围调整缓存有效期
    let maxAge;
    switch (range) {
      case '1d':
        maxAge = 300000; // 5分钟
        break;
      case '1m':
        maxAge = 1800000; // 30分钟
        break;
      case '3m':
        maxAge = 3600000; // 1小时
        break;
      default:
        maxAge = 300000;
    }
    
    return cacheAge < maxAge;
  }

  // 更新汇率数据时清除相关缓存
  async invalidateCache() {
    try {
      // 清除最新汇率缓存
      await this.KV.delete('latest_rates_USD');
      
      // 清除常用货币对的历史数据缓存
      const commonPairs = [
        'historical_USD_CNY_1d',
        'historical_USD_EUR_1d',
        'historical_USD_GBP_1d',
        'historical_USD_JPY_1d'
      ];
      
      for (const key of commonPairs) {
        await this.KV.delete(key);
      }
      
      console.log('🗑️ KV缓存已清除');
    } catch (error) {
      console.warn('⚠️ 清除KV缓存失败:', error);
    }
  }
}

module.exports = { KVExchangeRateService };
