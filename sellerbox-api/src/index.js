import GeminiAIProxy from './ai-proxy.js';

export default {
  async fetch(request, env, ctx) {
    try {
      const url = new URL(request.url);

      // 获取请求的 Origin
      const origin = request.headers.get('Origin');
      const allowedOrigins = [
        'https://amzova.com',
        'https://www.amzova.com',
        'https://amzova-frontend.pages.dev',
        'http://localhost:5173',
        'http://localhost:3000'
      ];

      // 动态添加Cloudflare Pages部署域名
      if (origin && origin.includes('.amzova-frontend.pages.dev')) {
        allowedOrigins.push(origin);
      }

      const corsHeaders = {
        'Access-Control-Allow-Origin': allowedOrigins.includes(origin) ? origin : 'https://amzova.com',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, Cache-Control, Pragma',
        'Access-Control-Allow-Credentials': 'false'
      };

      if (request.method === 'OPTIONS') {
        return new Response(null, { status: 204, headers: corsHeaders });
      }

      // Turnstile verification endpoint
      if (url.pathname === '/api/verify-turnstile' || url.pathname === '/api/v1/verify-turnstile') {
        if (request.method !== 'POST') {
          return new Response(JSON.stringify({ error: 'Method not allowed' }), {
            status: 405,
            headers: { 'Content-Type': 'application/json', ...corsHeaders }
          });
        }

        try {
          const { token } = await request.json();

          if (!token) {
            return new Response(JSON.stringify({
              success: false,
              error: 'Missing token'
            }), {
              status: 400,
              headers: { 'Content-Type': 'application/json', ...corsHeaders }
            });
          }

          // 验证Turnstile token
          const verifyResponse = await fetch('https://challenges.cloudflare.com/turnstile/v0/siteverify', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
              secret: env.TURNSTILE_SECRET_KEY,
              response: token,
              remoteip: request.headers.get('CF-Connecting-IP') || request.headers.get('X-Forwarded-For') || 'unknown'
            })
          });

          const verifyResult = await verifyResponse.json();

          return new Response(JSON.stringify({
            success: verifyResult.success,
            timestamp: new Date().toISOString()
          }), {
            headers: { 'Content-Type': 'application/json', ...corsHeaders }
          });

        } catch (error) {
          console.error('Turnstile verification error:', error);
          return new Response(JSON.stringify({
            success: false,
            error: 'Verification failed'
          }), {
            status: 500,
            headers: { 'Content-Type': 'application/json', ...corsHeaders }
          });
        }
      }

      // Health check endpoints (support both /api/ and /api/v1/)
      if (url.pathname === '/api/health' || url.pathname === '/api/v1/health') {
        return new Response(JSON.stringify({
          status: 'ok',
          timestamp: new Date().toISOString(),
          environment: env.ENVIRONMENT || 'production',
          version: '1.0.0'
        }), {
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        });
      }

      // Database test endpoint
      if (url.pathname === '/api/db-test' || url.pathname === '/api/v1/db-test') {
        try {
          const result = await env.DB.prepare('SELECT 1 as test').first();
          return new Response(JSON.stringify({
            status: 'ok',
            database: 'connected',
            test_result: result
          }), {
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        } catch (error) {
          return new Response(JSON.stringify({
            status: 'error',
            database: 'disconnected',
            error: error.message
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }
      }

      // Manual exchange rates update endpoint (for testing)
      if (url.pathname === '/api/exchange-rates/update' || url.pathname === '/api/v1/exchange-rates/update') {
        if (request.method !== 'POST') {
          return new Response(JSON.stringify({
            success: false,
            error: 'Method not allowed'
          }), {
            status: 405,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }

        try {
          await updateExchangeRates(env);
          return new Response(JSON.stringify({
            success: true,
            message: 'Exchange rates updated successfully',
            timestamp: new Date().toISOString()
          }), {
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }
      }

      // Change password endpoint
      if ((url.pathname === '/api/auth/change-password' || url.pathname === '/api/v1/auth/change-password') && request.method === 'POST') {
        try {
          const authHeader = request.headers.get('Authorization');

          if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Authorization required'
            }), {
              status: 401,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          const token = authHeader.substring(7);

          if (!token.startsWith('mock_access_token_')) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Invalid token'
            }), {
              status: 401,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          const data = await request.json();

          if (!data.current_password || !data.new_password) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Current password and new password are required'
            }), {
              status: 400,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          // Get current admin user
          const adminUser = await env.DB.prepare(`
            SELECT id, username, password_hash FROM admin_users WHERE username = 'admin'
          `).first();

          if (!adminUser) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Admin user not found'
            }), {
              status: 404,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          // Verify current password
          let isCurrentPasswordValid = false;

          // Check if password has been changed (has custom hash)
          if (adminUser.password_hash && adminUser.password_hash.startsWith('$2b$10$')) {
            // Password has been changed, check against new hash
            const encoder = new TextEncoder();
            const passwordBytes = encoder.encode(data.current_password);
            const base64Password = btoa(String.fromCharCode(...passwordBytes));
            isCurrentPasswordValid = adminUser.password_hash.includes(base64Password);
          }
          // Check if it's still the default password
          else if (data.current_password === 'TempAdmin2025!') {
            isCurrentPasswordValid = true;
          }

          if (!isCurrentPasswordValid) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Current password is incorrect'
            }), {
              status: 400,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          // Generate new password hash (simplified for demo)
          // In production, you should use proper bcrypt hashing
          const encoder = new TextEncoder();
          const passwordBytes = encoder.encode(data.new_password);
          const base64Password = btoa(String.fromCharCode(...passwordBytes));
          const newPasswordHash = `$2b$10$${base64Password}`;

          // Update password in database
          const result = await env.DB.prepare(`
            UPDATE admin_users
            SET password_hash = ?, updated_at = datetime('now')
            WHERE username = 'admin'
          `).bind(newPasswordHash).run();

          if (result.changes === 0) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Failed to update password'
            }), {
              status: 500,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          return new Response(JSON.stringify({
            success: true,
            message: 'Password changed successfully'
          }), {
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Password change failed',
            error: error.message
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }
      }

      // Analytics endpoints

      // Get overall analytics
      if ((url.pathname === '/api/analytics/overall' || url.pathname === '/api/v1/analytics/overall') && request.method === 'GET') {
        try {
          // Get basic stats from database
          const totalSessions = await env.DB.prepare(`
            SELECT COUNT(DISTINCT session_id) as count FROM user_sessions
          `).first();

          const totalPageViews = await env.DB.prepare(`
            SELECT COUNT(*) as count FROM page_views
          `).first();

          const totalToolUses = await env.DB.prepare(`
            SELECT COUNT(*) as count FROM tool_usage
          `).first();

          const totalFeedback = await env.DB.prepare(`
            SELECT COUNT(*) as count FROM user_feedback
          `).first();

          // Get today's stats
          const todayPageViews = await env.DB.prepare(`
            SELECT COUNT(*) as count FROM page_views
            WHERE DATE(created_at) = DATE('now')
          `).first();

          const todayToolUses = await env.DB.prepare(`
            SELECT COUNT(*) as count FROM tool_usage
            WHERE DATE(created_at) = DATE('now')
          `).first();

          const todaySessions = await env.DB.prepare(`
            SELECT COUNT(DISTINCT session_id) as count FROM user_sessions
            WHERE DATE(first_visit) = DATE('now')
          `).first();

          return new Response(JSON.stringify({
            success: true,
            data: {
              total_sessions: totalSessions?.count || 0,
              unique_visitors: totalSessions?.count || 0,
              total_page_views: totalPageViews?.count || 0,
              total_tool_uses: totalToolUses?.count || 0,
              active_tools: 12, // Static count of available tools
              total_feedback: totalFeedback?.count || 0,
              daily_page_views: todayPageViews?.count || 0,
              daily_tool_uses: todayToolUses?.count || 0,
              daily_active_sessions: todaySessions?.count || 0,
              weekly_page_views: todayPageViews?.count || 0, // Simplified for demo
              weekly_tool_uses: todayToolUses?.count || 0,
              weekly_active_sessions: todaySessions?.count || 0
            }
          }), {
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Failed to get overall analytics',
            error: error.message
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }
      }

      // Get top tools
      if ((url.pathname === '/api/analytics/top-tools' || url.pathname === '/api/v1/analytics/top-tools') && request.method === 'GET') {
        try {
          const urlParams = new URL(request.url).searchParams;
          const limit = parseInt(urlParams.get('limit')) || 10;

          const topTools = await env.DB.prepare(`
            SELECT
              tool_id,
              tool_name,
              COUNT(*) as total_uses,
              COUNT(DISTINCT session_id) as unique_users,
              COUNT(CASE WHEN DATE(created_at) = DATE('now') THEN 1 END) as daily_uses,
              COUNT(CASE WHEN created_at >= DATE('now', '-7 days') THEN 1 END) as weekly_uses
            FROM tool_usage
            GROUP BY tool_id, tool_name
            ORDER BY total_uses DESC
            LIMIT ?
          `).bind(limit).all();

          return new Response(JSON.stringify({
            success: true,
            data: topTools.results || []
          }), {
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Failed to get top tools',
            error: error.message
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }
      }

      // Get usage trend
      if ((url.pathname === '/api/analytics/usage-trend' || url.pathname === '/api/v1/analytics/usage-trend') && request.method === 'GET') {
        try {
          const urlParams = new URL(request.url).searchParams;
          const days = parseInt(urlParams.get('days')) || 7;

          const trend = await env.DB.prepare(`
            SELECT
              DATE(created_at) as date,
              COUNT(*) as total_uses,
              COUNT(DISTINCT session_id) as unique_users
            FROM tool_usage
            WHERE created_at >= DATE('now', '-' || ? || ' days')
            GROUP BY DATE(created_at)
            ORDER BY date DESC
          `).bind(days).all();

          return new Response(JSON.stringify({
            success: true,
            data: trend.results || []
          }), {
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Failed to get usage trend',
            error: error.message
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }
      }

      // Get realtime stats
      if ((url.pathname === '/api/analytics/realtime' || url.pathname === '/api/v1/analytics/realtime') && request.method === 'GET') {
        try {
          // Get stats from last hour
          const realtimeStats = await env.DB.prepare(`
            SELECT
              COUNT(DISTINCT session_id) as active_users,
              COUNT(*) as page_views,
              (SELECT COUNT(*) FROM tool_usage WHERE created_at >= datetime('now', '-1 hour')) as tool_uses
            FROM page_views
            WHERE created_at >= datetime('now', '-1 hour')
          `).first();

          return new Response(JSON.stringify({
            success: true,
            data: {
              active_users: realtimeStats?.active_users || 0,
              page_views: realtimeStats?.page_views || 0,
              tool_uses: realtimeStats?.tool_uses || 0,
              timestamp: new Date().toISOString()
            }
          }), {
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Failed to get realtime stats',
            error: error.message
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }
      }

      // Record analytics data (batch endpoint)
      if ((url.pathname === '/api/analytics/batch' || url.pathname === '/api/v1/analytics/batch') && request.method === 'POST') {
        try {
          const data = await request.json();
          const { events } = data;

          if (!events || !Array.isArray(events)) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Invalid events data'
            }), {
              status: 400,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          const results = [];

          for (const event of events) {
            try {
              if (event.type === 'tool_usage') {
                await env.DB.prepare(`
                  INSERT INTO tool_usage (tool_id, tool_name, session_id, usage_type, created_at)
                  VALUES (?, ?, ?, ?, ?)
                `).bind(
                  event.tool_id || 'unknown',
                  event.tool_name || 'Unknown Tool',
                  event.session_id || 'anonymous',
                  event.usage_type || 'use',
                  event.timestamp || new Date().toISOString()
                ).run();
                results.push({ success: true, type: 'tool_usage' });
              } else if (event.type === 'page_view') {
                await env.DB.prepare(`
                  INSERT INTO page_views (page_path, page_title, session_id, created_at)
                  VALUES (?, ?, ?, ?)
                `).bind(
                  event.page_path || '/',
                  event.page_title || 'Unknown Page',
                  event.session_id || 'anonymous',
                  event.timestamp || new Date().toISOString()
                ).run();
                results.push({ success: true, type: 'page_view' });
              } else if (event.type === 'session') {
                // Update or insert session
                await env.DB.prepare(`
                  INSERT OR REPLACE INTO user_sessions (session_id, page_views, tool_uses, first_visit, last_visit)
                  VALUES (?, ?, ?, ?, ?)
                `).bind(
                  event.session_id || 'anonymous',
                  event.page_views || 1,
                  event.tool_uses || 0,
                  event.timestamp || new Date().toISOString(),
                  new Date().toISOString()
                ).run();
                results.push({ success: true, type: 'session' });
              }
            } catch (eventError) {
              results.push({ success: false, type: event.type, error: eventError.message });
            }
          }

          return new Response(JSON.stringify({
            success: true,
            results,
            processed: results.length
          }), {
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Failed to process analytics batch',
            error: error.message
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }
      }

      // Exchange rates endpoints - 只返回真实数据，严禁模拟数据
      if (url.pathname === '/api/exchange-rates/latest' || url.pathname === '/api/v1/exchange-rates/latest') {
        try {
          const cacheKey = 'latest_rates_USD';
          const CACHE_TTL = 300; // 5分钟缓存

          // 1. 尝试从KV缓存获取数据
          console.log('🔍 检查KV绑定状态:', !!env.EXCHANGE_RATES_KV);
          let cachedData = null;
          if (env.EXCHANGE_RATES_KV) {
            try {
              console.log('📖 尝试从KV读取缓存:', cacheKey);
              const cached = await env.EXCHANGE_RATES_KV.get(cacheKey, 'json');
              console.log('📊 KV缓存结果:', cached ? '有数据' : '无数据');
              if (cached && cached.timestamp && (Date.now() - cached.timestamp) < CACHE_TTL * 1000) {
                console.log('✅ 从KV缓存返回汇率数据');
                return new Response(JSON.stringify({
                  success: true,
                  data: cached.data,
                  source: 'KV_Cache'
                }), {
                  headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'public, max-age=300',
                    ...corsHeaders
                  }
                });
              } else if (cached) {
                console.log('⏰ KV缓存已过期，时间差:', Date.now() - cached.timestamp, 'ms');
              }
            } catch (kvError) {
              console.warn('⚠️ KV缓存读取失败:', kvError);
            }
          } else {
            console.warn('❌ KV绑定不存在');
          }

          // 2. 从数据库获取最新的真实汇率数据
          console.log('🔄 从数据库获取汇率数据');
          const result = await env.DB.prepare(`
            SELECT base_currency, target_currency, rate, created_at
            FROM exchange_rates
            WHERE created_at >= datetime('now', '-2 hours')
            ORDER BY created_at DESC
            LIMIT 50
          `).all();

          if (!result.results || result.results.length === 0) {
            return new Response(JSON.stringify({
              success: false,
              error: 'No exchange rate data available',
              message: '暂无汇率数据，请稍后重试'
            }), {
              status: 503,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          const rates = {};
          let lastUpdated = null;

          // 处理数据库中的汇率数据
          result.results.forEach(row => {
            if (!rates[row.target_currency]) {
              rates[row.target_currency] = row.rate;
              if (!lastUpdated || new Date(row.created_at) > new Date(lastUpdated)) {
                lastUpdated = row.created_at;
              }
            }
          });

          // USD作为基础货币
          rates.USD = 1;

          const responseData = {
            rates: rates,
            baseCurrency: 'USD',
            lastUpdated: lastUpdated || new Date().toISOString(),
            source: 'FreeCurrencyAPI',
            dataCount: Object.keys(rates).length
          };

          // 3. 缓存到KV存储
          if (env.EXCHANGE_RATES_KV) {
            try {
              console.log('💾 准备写入KV缓存:', cacheKey);
              await env.EXCHANGE_RATES_KV.put(cacheKey, JSON.stringify({
                data: responseData,
                timestamp: Date.now()
              }), {
                expirationTtl: CACHE_TTL
              });
              console.log('✅ 汇率数据已成功缓存到KV');
            } catch (kvError) {
              console.warn('⚠️ KV缓存写入失败:', kvError);
            }
          } else {
            console.warn('❌ KV绑定不存在，无法写入缓存');
          }

          return new Response(JSON.stringify({
            success: true,
            data: responseData
          }), {
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        } catch (error) {
          console.error('获取汇率数据失败:', error);
          return new Response(JSON.stringify({
            success: false,
            error: 'Failed to fetch exchange rates',
            message: '汇率服务暂时不可用，请稍后重试'
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }
      }

      // Historical exchange rates endpoint - 只返回真实历史数据
      if ((url.pathname === '/api/exchange-rates/historical' || url.pathname === '/api/v1/exchange-rates/historical') && request.method === 'GET') {
        try {
          const urlParams = new URLSearchParams(url.search);
          const base = urlParams.get('base') || 'USD';
          const target = urlParams.get('target') || 'CNY';
          const range = urlParams.get('range') || '1d';

          const cacheKey = `historical_${base}_${target}_${range}`;
          // 根据时间范围设置缓存TTL：1小时数据缓存5分钟，12小时缓存10分钟，24小时缓存15分钟
          const HISTORICAL_CACHE_TTL = range === '1h' ? 300 : (range === '12h' ? 600 : 900);

          // 1. 尝试从KV缓存获取历史数据
          if (env.EXCHANGE_RATES_KV) {
            try {
              const cached = await env.EXCHANGE_RATES_KV.get(cacheKey, 'json');
              if (cached && cached.timestamp && (Date.now() - cached.timestamp) < HISTORICAL_CACHE_TTL * 1000) {
                console.log('✅ 从KV缓存返回历史汇率数据');
                return new Response(JSON.stringify({
                  success: true,
                  data: cached.data,
                  source: 'KV_Cache'
                }), {
                  headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': `public, max-age=${HISTORICAL_CACHE_TTL}`,
                    ...corsHeaders
                  }
                });
              }
            } catch (kvError) {
              console.warn('⚠️ KV历史数据缓存读取失败:', kvError);
            }
          }

          // 根据时间范围确定查询参数（仅支持24小时内的数据）
          let timeCondition, orderBy;
          switch (range) {
            case '1h':
              timeCondition = "datetime('now', '-1 hour')";
              orderBy = 'created_at ASC';
              break;
            case '12h':
              timeCondition = "datetime('now', '-12 hours')";
              orderBy = 'created_at ASC';
              break;
            case '24h':
            case '1d': // 兼容旧的1d参数
              timeCondition = "datetime('now', '-24 hours')";
              orderBy = 'created_at ASC';
              break;
            default:
              timeCondition = "datetime('now', '-1 hour')";
              orderBy = 'created_at ASC';
          }

          // 智能获取历史汇率数据 - 支持所有货币对
          let historicalRates = [];

          if (base === 'USD') {
            // 直接查询USD为基础货币的数据
            const result = await env.DB.prepare(`
              SELECT base_currency, target_currency, rate, created_at
              FROM exchange_rates
              WHERE base_currency = ?
                AND target_currency = ?
                AND created_at >= ${timeCondition}
              ORDER BY ${orderBy}
              LIMIT 200
            `).bind(base, target).all();

            if (result.results && result.results.length > 0) {
              historicalRates = result.results.map(row => ({
                timestamp: row.created_at,
                rate: row.rate
              }));
            }
          } else if (target === 'USD') {
            // 查询目标货币为USD的数据（需要计算倒数）
            const result = await env.DB.prepare(`
              SELECT base_currency, target_currency, rate, created_at
              FROM exchange_rates
              WHERE base_currency = 'USD'
                AND target_currency = ?
                AND created_at >= ${timeCondition}
              ORDER BY ${orderBy}
              LIMIT 200
            `).bind(base).all();

            if (result.results && result.results.length > 0) {
              historicalRates = result.results.map(row => ({
                timestamp: row.created_at,
                rate: 1 / row.rate // 计算倒数
              }));
            }
          } else {
            // 计算非USD货币对的汇率（通过USD中转）
            const result = await env.DB.prepare(`
              SELECT
                r1.created_at,
                r1.rate as base_rate,
                r2.rate as target_rate
              FROM exchange_rates r1
              INNER JOIN exchange_rates r2
                ON r1.created_at = r2.created_at
              WHERE r1.base_currency = 'USD'
                AND r1.target_currency = ?
                AND r2.base_currency = 'USD'
                AND r2.target_currency = ?
                AND r1.created_at >= ${timeCondition}
              ORDER BY r1.created_at ASC
              LIMIT 200
            `).bind(base, target).all();

            if (result.results && result.results.length > 0) {
              historicalRates = result.results.map(row => ({
                timestamp: row.created_at,
                rate: row.target_rate / row.base_rate // 计算交叉汇率
              }));
            }
          }

          if (historicalRates.length === 0) {
            return new Response(JSON.stringify({
              success: false,
              error: 'No historical data available',
              message: `暂无 ${base}/${target} 的历史汇率数据`
            }), {
              status: 404,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          const responseData = {
            base: base,
            target: target,
            range: range,
            rates: historicalRates,
            dataPoints: historicalRates.length,
            source: 'FreeCurrencyAPI'
          };

          // 2. 缓存历史数据到KV存储
          if (env.EXCHANGE_RATES_KV) {
            try {
              await env.EXCHANGE_RATES_KV.put(cacheKey, JSON.stringify({
                data: responseData,
                timestamp: Date.now()
              }), {
                expirationTtl: HISTORICAL_CACHE_TTL
              });
              console.log(`💾 历史汇率数据已缓存到KV: ${base}/${target} ${range}`);
            } catch (kvError) {
              console.warn('⚠️ KV历史数据缓存写入失败:', kvError);
            }
          }

          return new Response(JSON.stringify({
            success: true,
            data: responseData
          }), {
            headers: {
              'Content-Type': 'application/json',
              'Cache-Control': `public, max-age=${HISTORICAL_CACHE_TTL}`,
              ...corsHeaders
            }
          });
        } catch (error) {
          console.error('获取历史汇率数据失败:', error);
          return new Response(JSON.stringify({
            success: false,
            error: 'Failed to fetch historical rates',
            message: '历史汇率服务暂时不可用，请稍后重试'
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }
      }

      // Feedback endpoints
      if ((url.pathname === '/api/feedback' || url.pathname === '/api/v1/feedback') && request.method === 'POST') {
        try {
          const data = await request.json();

          // Validate required fields
          if (!data.message || !data.feedback_type) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Missing required fields'
            }), {
              status: 400,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          // Insert feedback into database
          const result = await env.DB.prepare(`
            INSERT INTO user_feedback (feedback_type, rating, message, email, created_at)
            VALUES (?, ?, ?, ?, datetime('now'))
          `).bind(
            data.feedback_type,
            data.rating || null,
            data.message,
            data.email || null
          ).run();

          return new Response(JSON.stringify({
            success: true,
            message: 'Feedback submitted successfully',
            id: result.meta?.last_row_id
          }), {
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Failed to submit feedback',
            error: error.message
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }
      }

      // Get feedback list
      if ((url.pathname === '/api/feedback' || url.pathname === '/api/v1/feedback') && request.method === 'GET') {
        try {
          const urlParams = new URLSearchParams(url.search);
          const page = parseInt(urlParams.get('page') || '1');
          const limit = parseInt(urlParams.get('limit') || '10');
          const offset = (page - 1) * limit;

          // Get total count
          const countResult = await env.DB.prepare('SELECT COUNT(*) as total FROM user_feedback').first();
          const total = countResult.total;

          // Get feedback items
          const result = await env.DB.prepare(`
            SELECT id, feedback_type, rating, message, email, created_at
            FROM user_feedback
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
          `).bind(limit, offset).all();

          return new Response(JSON.stringify({
            success: true,
            data: result.results,
            pagination: {
              page,
              limit,
              total,
              totalPages: Math.ceil(total / limit)
            }
          }), {
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Failed to fetch feedback',
            error: error.message
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }
      }

      // Auth login endpoint
      if ((url.pathname === '/api/auth/login' || url.pathname === '/api/v1/auth/login') && request.method === 'POST') {
        try {
          const data = await request.json();

          if (!data.username || !data.password) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Username and password are required'
            }), {
              status: 400,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          // Get user from database to verify credentials
          const user = await env.DB.prepare(`
            SELECT id, username, email, role, password_hash
            FROM admin_users
            WHERE username = ? AND status = 'active'
          `).bind(data.username).first();

          if (!user) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Invalid credentials'
            }), {
              status: 401,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          // Verify password
          let isValidPassword = false;

          // Check if password has been changed (has custom hash)
          if (user.password_hash && user.password_hash.startsWith('$2b$10$')) {
            // Password has been changed, check against new hash
            const encoder = new TextEncoder();
            const passwordBytes = encoder.encode(data.password);
            const base64Password = btoa(String.fromCharCode(...passwordBytes));
            isValidPassword = user.password_hash.includes(base64Password);
          }
          // Check if it's still the default password (no custom hash set)
          else if (data.password === 'TempAdmin2025!') {
            isValidPassword = true;
          }

          if (!isValidPassword) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Invalid credentials'
            }), {
              status: 401,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          // Generate mock tokens
          const accessToken = 'mock_access_token_' + Date.now();
          const refreshToken = 'mock_refresh_token_' + Date.now();

          return new Response(JSON.stringify({
            success: true,
            message: 'Login successful',
            data: {
              user: {
                id: user.id,
                username: user.username,
                email: user.email,
                full_name: user.username,
                role: user.role
              },
              access_token: accessToken,
              refresh_token: refreshToken,
              expires_in: 3600,
              token_type: 'Bearer'
            }
          }), {
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Login failed',
            error: error.message
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }
      }

      // Auth verify endpoint
      if ((url.pathname === '/api/auth/verify' || url.pathname === '/api/v1/auth/verify') && request.method === 'GET') {
        try {
          const authHeader = request.headers.get('Authorization');

          if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Authorization header required'
            }), {
              status: 401,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          const token = authHeader.substring(7); // Remove 'Bearer ' prefix

          // For demo purposes, accept any mock token that starts with 'mock_access_token_'
          if (token.startsWith('mock_access_token_')) {
            // Extract timestamp from token to check if it's still valid (1 hour)
            const timestamp = parseInt(token.replace('mock_access_token_', ''));
            const now = Date.now();
            const tokenAge = now - timestamp;
            const oneHour = 60 * 60 * 1000; // 1 hour in milliseconds

            if (tokenAge > oneHour) {
              return new Response(JSON.stringify({
                success: false,
                message: 'Token expired'
              }), {
                status: 401,
                headers: {
                  'Content-Type': 'application/json',
                  ...corsHeaders
                }
              });
            }

            // Return mock user data for valid token
            return new Response(JSON.stringify({
              success: true,
              message: 'Token valid',
              data: {
                user: {
                  id: 1,
                  username: 'admin',
                  email: '<EMAIL>',
                  full_name: 'System Administrator',
                  role: 'super_admin'
                },
                expires_at: new Date(timestamp + oneHour).toISOString()
              }
            }), {
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          return new Response(JSON.stringify({
            success: false,
            message: 'Invalid token'
          }), {
            status: 401,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Token verification failed',
            error: error.message
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }
      }

      // Auth me endpoint
      if ((url.pathname === '/api/auth/me' || url.pathname === '/api/v1/auth/me') && request.method === 'GET') {
        try {
          const authHeader = request.headers.get('Authorization');

          if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Authorization header required'
            }), {
              status: 401,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          const token = authHeader.substring(7);

          // For demo purposes, accept any mock token that starts with 'mock_access_token_'
          if (token.startsWith('mock_access_token_')) {
            const timestamp = parseInt(token.replace('mock_access_token_', ''));
            const now = Date.now();
            const tokenAge = now - timestamp;
            const oneHour = 60 * 60 * 1000;

            if (tokenAge > oneHour) {
              return new Response(JSON.stringify({
                success: false,
                message: 'Token expired'
              }), {
                status: 401,
                headers: {
                  'Content-Type': 'application/json',
                  ...corsHeaders
                }
              });
            }

            return new Response(JSON.stringify({
              success: true,
              data: {
                user: {
                  id: 1,
                  username: 'admin',
                  email: '<EMAIL>',
                  full_name: 'System Administrator',
                  role: 'super_admin'
                },
                session: {
                  expires_at: new Date(timestamp + oneHour).toISOString()
                }
              }
            }), {
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          return new Response(JSON.stringify({
            success: false,
            message: 'Invalid token'
          }), {
            status: 401,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Failed to get user info',
            error: error.message
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }
      }

      // Announcements endpoints

      // Get active announcements (public endpoint)
      if ((url.pathname === '/api/announcements/active' || url.pathname === '/api/v1/announcements/active') && request.method === 'GET') {
        try {
          const urlParams = new URL(request.url).searchParams;
          const language = urlParams.get('language') || 'zh-CN';
          const userSession = urlParams.get('user_session');

          // Get active announcements from database
          const announcements = await env.DB.prepare(`
            SELECT
              id, title, content, type, priority, display_position,
              is_closable, click_action, start_time, end_time
            FROM system_announcements
            WHERE status = 'active'
              AND target_audience IN ('all', 'user')
              AND (start_time IS NULL OR start_time <= datetime('now'))
              AND (end_time IS NULL OR end_time >= datetime('now'))
            ORDER BY priority DESC, created_at DESC
          `).all();

          let responseData = announcements.results || [];

          // Apply translations if language is not Chinese
          if (language !== 'zh-CN' && responseData.length > 0) {
            for (let announcement of responseData) {
              const translation = await env.DB.prepare(`
                SELECT title, content
                FROM announcement_translations
                WHERE announcement_id = ? AND language_code = ?
              `).bind(announcement.id, language).first();

              if (translation) {
                announcement.title = translation.title;
                announcement.content = translation.content;
              }
            }
          }

          // For demo purposes, return mock data if no announcements exist

          if (responseData.length === 0) {
            responseData = [
              {
                id: 1,
                title: '欢迎使用 SellerBox',
                content: '我们为您提供最专业的亚马逊卖家工具',
                type: 'info',
                priority: 10,
                display_position: 'header',
                is_closable: 1,
                click_action: null,
                start_time: null,
                end_time: null
              }
            ];
          }

          return new Response(JSON.stringify({
            success: true,
            data: responseData
          }), {
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Failed to get announcements',
            error: error.message
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }
      }

      // Get all announcements (admin endpoint)
      if ((url.pathname === '/api/announcements' || url.pathname === '/api/v1/announcements' ||
           url.pathname === '/api/admin/announcements' || url.pathname === '/api/v1/admin/announcements') && request.method === 'GET') {
        try {
          const authHeader = request.headers.get('Authorization');

          if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Authorization required'
            }), {
              status: 401,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          const token = authHeader.substring(7);

          // Verify token (simplified)
          if (!token.startsWith('mock_access_token_')) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Invalid token'
            }), {
              status: 401,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          // Get all announcements from database
          const announcements = await env.DB.prepare(`
            SELECT
              id, title, content, type, priority, status, display_position,
              is_closable, click_action, start_time, end_time, target_audience,
              created_at, updated_at
            FROM system_announcements
            ORDER BY priority DESC, created_at DESC
          `).all();

          return new Response(JSON.stringify({
            success: true,
            data: announcements.results || [],
            pagination: {
              page: 1,
              limit: 20
            }
          }), {
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Failed to get announcements',
            error: error.message
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }
      }

      // Create announcement (admin endpoint)
      if ((url.pathname === '/api/announcements' || url.pathname === '/api/v1/announcements' ||
           url.pathname === '/api/admin/announcements' || url.pathname === '/api/v1/admin/announcements') && request.method === 'POST') {
        try {
          const authHeader = request.headers.get('Authorization');

          if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Authorization required'
            }), {
              status: 401,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          const token = authHeader.substring(7);

          // Verify token (simplified)
          if (!token.startsWith('mock_access_token_')) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Invalid token'
            }), {
              status: 401,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          const data = await request.json();

          // Validate required fields
          if (!data.title || !data.content) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Title and content are required'
            }), {
              status: 400,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          // Insert announcement into database
          const result = await env.DB.prepare(`
            INSERT INTO system_announcements
            (title, content, type, priority, status, start_time, end_time,
             target_audience, display_position, is_closable, click_action, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `).bind(
            data.title,
            data.content,
            data.type || 'info',
            data.priority || 0,
            data.status || 'draft',
            data.start_time || null,
            data.end_time || null,
            data.target_audience || 'all',
            data.display_position || 'header',
            data.is_closable ? 1 : 0,
            data.click_action || null,
            1 // created_by (admin user id)
          ).run();

          const announcementId = result.meta.last_row_id;

          // Handle translations if provided
          if (data.translations && data.translations.length > 0) {
            for (const translation of data.translations) {
              if (translation.title.trim() && translation.content.trim()) {
                await env.DB.prepare(`
                  INSERT INTO announcement_translations (announcement_id, language_code, title, content)
                  VALUES (?, ?, ?, ?)
                `).bind(
                  announcementId,
                  translation.language_code,
                  translation.title,
                  translation.content
                ).run();
              }
            }
          }

          return new Response(JSON.stringify({
            success: true,
            message: 'Announcement created successfully',
            data: {
              id: announcementId
            }
          }), {
            status: 201,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Failed to create announcement',
            error: error.message
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }
      }

      // Update announcement (admin endpoint)
      if (url.pathname.match(/^\/api\/(v1\/)?admin\/announcements\/\d+$/) && request.method === 'PUT') {
        try {
          const authHeader = request.headers.get('Authorization');

          if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Authorization required'
            }), {
              status: 401,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          const token = authHeader.substring(7);

          if (!token.startsWith('mock_access_token_')) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Invalid token'
            }), {
              status: 401,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          // Extract announcement ID from URL
          const pathParts = url.pathname.split('/');
          const announcementId = parseInt(pathParts[pathParts.length - 1]);

          if (!announcementId) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Invalid announcement ID'
            }), {
              status: 400,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          const data = await request.json();

          // Update announcement in database
          const result = await env.DB.prepare(`
            UPDATE system_announcements
            SET title = ?, content = ?, type = ?, priority = ?, status = ?,
                start_time = ?, end_time = ?, target_audience = ?, display_position = ?,
                is_closable = ?, click_action = ?, updated_at = datetime('now')
            WHERE id = ?
          `).bind(
            data.title,
            data.content,
            data.type || 'info',
            data.priority || 0,
            data.status || 'draft',
            data.start_time || null,
            data.end_time || null,
            data.target_audience || 'all',
            data.display_position || 'header',
            data.is_closable ? 1 : 0,
            data.click_action || null,
            announcementId
          ).run();

          // Handle translations if provided
          if (data.translations && data.translations.length > 0) {
            // Delete existing translations
            await env.DB.prepare(`
              DELETE FROM announcement_translations WHERE announcement_id = ?
            `).bind(announcementId).run();

            // Insert new translations
            for (const translation of data.translations) {
              if (translation.title.trim() && translation.content.trim()) {
                await env.DB.prepare(`
                  INSERT INTO announcement_translations (announcement_id, language_code, title, content)
                  VALUES (?, ?, ?, ?)
                `).bind(
                  announcementId,
                  translation.language_code,
                  translation.title,
                  translation.content
                ).run();
              }
            }
          }

          if (result.changes === 0) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Announcement not found'
            }), {
              status: 404,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          return new Response(JSON.stringify({
            success: true,
            message: 'Announcement updated successfully',
            data: { id: announcementId }
          }), {
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Failed to update announcement',
            error: error.message
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }
      }

      // Delete announcement (admin endpoint)
      if (url.pathname.match(/^\/api\/(v1\/)?admin\/announcements\/\d+$/) && request.method === 'DELETE') {
        try {
          const authHeader = request.headers.get('Authorization');

          if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Authorization required'
            }), {
              status: 401,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          const token = authHeader.substring(7);

          if (!token.startsWith('mock_access_token_')) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Invalid token'
            }), {
              status: 401,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          // Extract announcement ID from URL
          const pathParts = url.pathname.split('/');
          const announcementId = parseInt(pathParts[pathParts.length - 1]);

          if (!announcementId) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Invalid announcement ID'
            }), {
              status: 400,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          // Delete announcement from database
          const result = await env.DB.prepare(`
            DELETE FROM system_announcements WHERE id = ?
          `).bind(announcementId).run();

          if (result.changes === 0) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Announcement not found'
            }), {
              status: 404,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }

          return new Response(JSON.stringify({
            success: true,
            message: 'Announcement deleted successfully'
          }), {
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Failed to delete announcement',
            error: error.message
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }
      }

      // Admin stats endpoint
      if ((url.pathname === '/api/admin/stats' || url.pathname === '/api/v1/admin/stats') && request.method === 'GET') {
        const authHeader = request.headers.get('Authorization');

        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Authorization required'
          }), {
            status: 401,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }

        const token = authHeader.substring(7);

        if (!token.startsWith('mock_access_token_')) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Invalid token'
          }), {
            status: 401,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }

        // Mock admin statistics
        return new Response(JSON.stringify({
          success: true,
          data: {
            users: {
              total: 1250,
              active_today: 89,
              new_this_week: 23
            },
            tools: {
              total_usage: 5420,
              popular_tools: [
                { name: 'Title Analyzer', usage: 1200 },
                { name: 'Profit Calculator', usage: 980 },
                { name: 'Keyword Density', usage: 750 }
              ]
            },
            announcements: {
              total: 5,
              active: 2,
              views_today: 156
            },
            system: {
              uptime: '99.9%',
              response_time: '120ms',
              last_updated: new Date().toISOString()
            }
          }
        }), {
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        });
      }

      // Admin analytics - users endpoint
      if ((url.pathname === '/api/admin/analytics/users' || url.pathname === '/api/v1/admin/analytics/users') && request.method === 'GET') {
        const authHeader = request.headers.get('Authorization');

        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Authorization required'
          }), {
            status: 401,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }

        const token = authHeader.substring(7);

        if (!token.startsWith('mock_access_token_')) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Invalid token'
          }), {
            status: 401,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }

        // Mock user analytics data
        return new Response(JSON.stringify({
          success: true,
          data: {
            daily_active_users: [
              { date: '2025-01-15', count: 45 },
              { date: '2025-01-16', count: 52 },
              { date: '2025-01-17', count: 48 },
              { date: '2025-01-18', count: 61 },
              { date: '2025-01-19', count: 58 },
              { date: '2025-01-20', count: 73 },
              { date: '2025-01-21', count: 89 }
            ],
            user_locations: [
              { country: 'China', count: 450 },
              { country: 'United States', count: 320 },
              { country: 'Germany', count: 180 },
              { country: 'Japan', count: 150 },
              { country: 'United Kingdom', count: 120 }
            ],
            device_types: [
              { type: 'Desktop', count: 780 },
              { type: 'Mobile', count: 350 },
              { type: 'Tablet', count: 120 }
            ]
          }
        }), {
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        });
      }

      // Admin analytics - tools endpoint
      if ((url.pathname === '/api/admin/analytics/tools' || url.pathname === '/api/v1/admin/analytics/tools') && request.method === 'GET') {
        const authHeader = request.headers.get('Authorization');

        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Authorization required'
          }), {
            status: 401,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }

        const token = authHeader.substring(7);

        if (!token.startsWith('mock_access_token_')) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Invalid token'
          }), {
            status: 401,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }

        // Mock tools analytics data
        return new Response(JSON.stringify({
          success: true,
          data: {
            tool_usage: [
              { tool: 'Title Analyzer', usage: 1200, growth: '+15%' },
              { tool: 'Profit Calculator', usage: 980, growth: '+8%' },
              { tool: 'Keyword Density', usage: 750, growth: '+22%' },
              { tool: 'Title Fixer', usage: 650, growth: '+12%' },
              { tool: 'Unit Converter', usage: 420, growth: '+5%' }
            ],
            daily_usage: [
              { date: '2025-01-15', count: 180 },
              { date: '2025-01-16', count: 195 },
              { date: '2025-01-17', count: 210 },
              { date: '2025-01-18', count: 225 },
              { date: '2025-01-19', count: 240 },
              { date: '2025-01-20', count: 260 },
              { date: '2025-01-21', count: 285 }
            ],
            popular_times: [
              { hour: 9, usage: 45 },
              { hour: 10, usage: 62 },
              { hour: 11, usage: 78 },
              { hour: 14, usage: 85 },
              { hour: 15, usage: 92 },
              { hour: 16, usage: 88 },
              { hour: 20, usage: 65 }
            ]
          }
        }), {
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        });
      }

      // 初始化Gemini AI代理
      const geminiProxy = new GeminiAIProxy(env);

      // 统一AI聊天端点 - 替换原有的多提供商架构
      if ((url.pathname === '/api/ai/chat' || url.pathname === '/api/v1/ai/chat') && request.method === 'POST') {
        const response = await geminiProxy.handleChatRequest(request);
        
        // 添加CORS头
        const corsResponse = new Response(response.body, {
          status: response.status,
          headers: {
            ...Object.fromEntries(response.headers),
            ...corsHeaders
          }
        });
        
        return corsResponse;
      }

      // AI服务健康检查
      if ((url.pathname === '/api/ai/health' || url.pathname === '/api/v1/ai/health') && request.method === 'GET') {
        const response = await geminiProxy.healthCheck();
        
        return new Response(response.body, {
          status: response.status,
          headers: {
            ...Object.fromEntries(response.headers),
            ...corsHeaders
          }
        });
      }

      // AI模型列表
      if ((url.pathname === '/api/ai/models' || url.pathname === '/api/v1/ai/models') && request.method === 'GET') {
        const response = await geminiProxy.getModels();
        
        return new Response(response.body, {
          status: response.status,
          headers: {
            ...Object.fromEntries(response.headers),
            ...corsHeaders
          }
        });
      }

      // AI使用统计
      if ((url.pathname === '/api/ai/stats' || url.pathname === '/api/v1/ai/stats') && request.method === 'GET') {
        const response = await geminiProxy.getUsageStats();

        return new Response(response.body, {
          status: response.status,
          headers: {
            ...Object.fromEntries(response.headers),
            ...corsHeaders
          }
        });
      }

      // AI图片生成端点
      if ((url.pathname === '/api/ai/image-generation' || url.pathname === '/api/v1/ai/image-generation') && request.method === 'POST') {
        const response = await geminiProxy.handleImageGeneration(request);

        return new Response(response.body, {
          status: response.status,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        });
      }

      // Default response
      return new Response(JSON.stringify({
        message: 'SellerBox API Service',
        path: url.pathname,
        method: request.method,
        timestamp: new Date().toISOString()
      }), {
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });

    } catch (error) {
      return new Response(JSON.stringify({
        error: 'Internal Server Error',
        message: error.message,
        timestamp: new Date().toISOString()
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }
  },

  // 定时任务处理 - 每10分钟更新汇率数据
  async scheduled(event, env, ctx) {
    console.log('🕒 定时任务开始: 更新汇率数据');

    try {
      await updateExchangeRates(env);
      console.log('✅ 汇率数据更新成功');
    } catch (error) {
      console.error('❌ 汇率数据更新失败:', error);
    }
  }
};

// 汇率更新函数
async function updateExchangeRates(env) {
  const API_KEY = env.FREECURRENCY_API_KEY || 'fca_live_your_api_key_here';
  const API_URL = `https://api.freecurrencyapi.com/v1/latest?apikey=${API_KEY}&base_currency=USD`;

  try {
    console.log('📡 正在从 FreeCurrencyAPI 获取汇率数据...');

    // 获取汇率数据
    const response = await fetch(API_URL, {
      headers: {
        'User-Agent': 'AmzOva-Backend/1.0'
      }
    });

    if (!response.ok) {
      throw new Error(`API 请求失败: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (!data.data) {
      throw new Error('API 响应格式错误: 缺少 data 字段');
    }

    console.log(`📊 获取到 ${Object.keys(data.data).length} 种货币的汇率数据`);

    // 优化清理策略：只在每小时的第一次运行时清理，减少DELETE操作消耗
    const now = new Date();
    const shouldCleanup = now.getMinutes() < 10; // 只在每小时的前10分钟内清理

    if (shouldCleanup) {
      const deleteResult = await env.DB.prepare(`
        DELETE FROM exchange_rates
        WHERE created_at < datetime('now', '-24 hours')
      `).run();

      if (deleteResult.changes > 0) {
        console.log(`🗑️ 清理了 ${deleteResult.changes} 条过期汇率数据`);
      }
    }

    // 插入新的汇率数据
    const insertPromises = [];
    const timestamp = new Date().toISOString();

    for (const [currency, rate] of Object.entries(data.data)) {
      const insertPromise = env.DB.prepare(`
        INSERT INTO exchange_rates (base_currency, target_currency, rate, created_at)
        VALUES (?, ?, ?, ?)
      `).bind('USD', currency, rate, timestamp).run();

      insertPromises.push(insertPromise);
    }

    // 批量执行插入操作
    await Promise.all(insertPromises);

    console.log(`💾 成功更新 ${insertPromises.length} 条汇率记录到数据库`);

    // 记录更新日志
    await env.DB.prepare(`
      INSERT INTO system_logs (level, message, data, created_at)
      VALUES (?, ?, ?, ?)
    `).bind(
      'info',
      'Exchange rates updated successfully',
      JSON.stringify({
        currencies_count: Object.keys(data.data).length,
        timestamp: timestamp,
        source: 'freecurrencyapi.com'
      }),
      timestamp
    ).run();

  } catch (error) {
    console.error('💥 汇率更新过程中发生错误:', error);

    // 记录错误日志
    try {
      await env.DB.prepare(`
        INSERT INTO system_logs (level, message, data, created_at)
        VALUES (?, ?, ?, ?)
      `).bind(
        'error',
        'Exchange rates update failed',
        JSON.stringify({
          error: error.message,
          timestamp: new Date().toISOString(),
          source: 'freecurrencyapi.com'
        }),
        new Date().toISOString()
      ).run();
    } catch (logError) {
      console.error('📝 记录错误日志失败:', logError);
    }

    throw error;
  }
}
