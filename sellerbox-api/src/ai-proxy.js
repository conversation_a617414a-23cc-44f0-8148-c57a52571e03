/**
 * Gemini AI代理处理器
 * 统一处理所有AI请求，替换原有的多提供商架构
 */

import { AIMonitor, RequestContext } from './ai-monitor.js';

class GeminiAIProxy {
  constructor(env) {
    this.baseUrl = env.GEMINI_BASE_URL || 'https://gemini-balance-c1w2.onrender.com';
    this.apiKey = env.GEMINI_API_KEY || 'sk-kdjferKjidjrf9jihdhfnghjxkdefKKGHYI';
    this.timeout = parseInt(env.AI_TIMEOUT || '30000');
    this.maxRetries = parseInt(env.AI_MAX_RETRIES || '3');
    this.defaultModel = env.AI_DEFAULT_MODEL || 'gemini-2.5-flash-lite';
    
    // 初始化监控器
    this.monitor = new AIMonitor(env);
    
    // 记录配置信息
    this.monitor.logConfiguration({
      baseUrl: this.baseUrl,
      defaultModel: this.defaultModel,
      timeout: this.timeout,
      maxRetries: this.maxRetries
    });
  }

  /**
   * 处理AI聊天请求
   */
  async handleChatRequest(request) {
    let requestContext = null;
    
    try {
      const data = await request.json();
      
      // 创建请求上下文
      requestContext = new RequestContext(this.monitor, {
        ...data,
        userAgent: request.headers.get('User-Agent')
      });
      
      // 验证请求格式
      this.validateRequest(data);
      
      // 转换为OpenAI格式（如果需要）
      const openaiRequest = this.convertToOpenAIFormat(data);
      
      // 调用Gemini代理
      const response = await this.callGeminiProxy(openaiRequest, requestContext);
      
      // 记录成功
      requestContext.recordSuccess(response);
      
      // 转换响应格式
      return this.createSuccessResponse(response, requestContext.getRequestId());
      
    } catch (error) {
      console.error('Gemini AI Proxy Error:', error);
      
      // 记录错误
      if (requestContext) {
        requestContext.recordError(error, this.getErrorStatusCode(error));
      }
      
      return this.createErrorResponse(error);
    }
  }

  /**
   * 验证请求格式
   */
  validateRequest(data) {
    if (!data.messages || !Array.isArray(data.messages)) {
      throw new Error('Invalid request: messages array is required');
    }

    if (data.messages.length === 0) {
      throw new Error('Invalid request: messages array cannot be empty');
    }

    // 验证消息格式
    for (const message of data.messages) {
      if (!message.role || !message.content) {
        throw new Error('Invalid request: each message must have role and content');
      }
      
      if (!['system', 'user', 'assistant'].includes(message.role)) {
        throw new Error('Invalid request: message role must be system, user, or assistant');
      }
    }
  }

  /**
   * 转换为OpenAI格式
   */
  convertToOpenAIFormat(data) {
    return {
      model: data.model || this.defaultModel,
      messages: data.messages,
      temperature: this.validateTemperature(data.temperature),
      max_tokens: this.validateMaxTokens(data.max_tokens),
      stream: false // 强制关闭流式响应
    };
  }

  /**
   * 验证温度参数
   */
  validateTemperature(temperature) {
    if (temperature === undefined || temperature === null) {
      return 0.7; // 默认值
    }
    
    const temp = parseFloat(temperature);
    if (isNaN(temp) || temp < 0 || temp > 2) {
      return 0.7; // 无效值时使用默认值
    }
    
    return temp;
  }

  /**
   * 验证最大token参数
   */
  validateMaxTokens(maxTokens) {
    if (maxTokens === undefined || maxTokens === null) {
      return 2000; // 默认值
    }
    
    const tokens = parseInt(maxTokens);
    if (isNaN(tokens) || tokens < 1 || tokens > 8192) {
      return 2000; // 无效值时使用默认值
    }
    
    return tokens;
  }

  /**
   * 调用Gemini代理服务
   */
  async callGeminiProxy(request, requestContext = null) {
    let lastError = null;
    
    // 重试机制
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        console.log(`🤖 Gemini API调用 (尝试 ${attempt}/${this.maxRetries}):`, {
          model: request.model,
          messages: request.messages.length,
          temperature: request.temperature,
          max_tokens: request.max_tokens
        });

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);

        const response = await fetch(`${this.baseUrl}/v1/chat/completions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`,
            'User-Agent': 'AmzOva-Backend/2.0'
          },
          body: JSON.stringify(request),
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        console.log(`📊 Gemini API响应状态: ${response.status} ${response.statusText}`);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('❌ Gemini API错误响应:', errorText);
          
          // 根据状态码决定是否重试
          if (this.shouldRetry(response.status)) {
            lastError = new Error(`Gemini API Error: ${response.status} - ${errorText}`);
            
            // 记录重试
            if (requestContext) {
              requestContext.recordRetry(attempt, this.maxRetries, lastError);
            }
            
            // 记录限流信息
            if (response.status === 429) {
              const retryAfter = response.headers.get('Retry-After');
              this.monitor.logRateLimit(retryAfter);
            }
            
            console.log(`⏳ 将在 ${attempt * 1000}ms 后重试...`);
            await this.sleep(attempt * 1000);
            continue;
          } else {
            throw new Error(`Gemini API Error: ${response.status} - ${errorText}`);
          }
        }

        const result = await response.json();
        console.log('✅ Gemini API调用成功');
        
        return result;

      } catch (error) {
        lastError = error;
        
        if (error.name === 'AbortError') {
          console.error(`⏰ 请求超时 (尝试 ${attempt}/${this.maxRetries})`);
        } else if (error.message?.includes('fetch')) {
          console.error(`🌐 网络错误 (尝试 ${attempt}/${this.maxRetries}):`, error.message);
        } else {
          console.error(`❌ 未知错误 (尝试 ${attempt}/${this.maxRetries}):`, error.message);
        }

        // 记录重试
        if (requestContext) {
          requestContext.recordRetry(attempt, this.maxRetries, lastError);
        }
        
        // 最后一次尝试失败时不再等待
        if (attempt < this.maxRetries) {
          console.log(`⏳ 将在 ${attempt * 1000}ms 后重试...`);
          await this.sleep(attempt * 1000);
        }
      }
    }

    // 所有重试都失败了
    throw lastError || new Error('Gemini API调用失败，已达到最大重试次数');
  }

  /**
   * 判断是否应该重试
   */
  shouldRetry(statusCode) {
    // 5xx服务器错误和429限流错误可以重试
    return statusCode >= 500 || statusCode === 429;
  }

  /**
   * 延迟函数
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取错误状态码
   */
  getErrorStatusCode(error) {
    if (error.message.includes('Invalid request')) {
      return 400;
    } else if (error.message.includes('timeout') || error.name === 'AbortError') {
      return 408;
    } else if (error.message.includes('fetch') || error.message.includes('network')) {
      return 503;
    } else if (error.message.includes('429')) {
      return 429;
    } else if (error.message.includes('401') || error.message.includes('403')) {
      return 401;
    } else {
      return 500;
    }
  }

  /**
   * 创建成功响应
   */
  createSuccessResponse(data, requestId = null) {
    return new Response(JSON.stringify({
      success: true,
      data: data,
      timestamp: new Date().toISOString(),
      provider: 'gemini',
      requestId: requestId
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'X-Request-ID': requestId || 'unknown'
      }
    });
  }

  /**
   * 创建错误响应
   */
  createErrorResponse(error) {
    let statusCode = 500;
    let errorMessage = 'AI服务内部错误';

    if (error.message.includes('Invalid request')) {
      statusCode = 400;
      errorMessage = error.message;
    } else if (error.message.includes('timeout') || error.name === 'AbortError') {
      statusCode = 408;
      errorMessage = 'AI服务请求超时，请稍后重试';
    } else if (error.message.includes('fetch') || error.message.includes('network')) {
      statusCode = 503;
      errorMessage = 'AI服务暂时不可用，请稍后重试';
    } else if (error.message.includes('429')) {
      statusCode = 429;
      errorMessage = 'AI服务请求频率过高，请稍后重试';
    } else if (error.message.includes('401') || error.message.includes('403')) {
      statusCode = 401;
      errorMessage = 'AI服务认证失败';
    }

    return new Response(JSON.stringify({
      success: false,
      error: errorMessage,
      details: error.message,
      timestamp: new Date().toISOString(),
      provider: 'gemini'
    }), {
      status: statusCode,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  /**
   * 健康检查
   */
  async healthCheck() {
    const startTime = Date.now();
    
    try {
      const testRequest = {
        model: this.defaultModel,
        messages: [
          {
            role: 'user',
            content: 'Hello, please respond with "OK" to confirm the service is working.'
          }
        ],
        temperature: 0.1,
        max_tokens: 10
      };

      const response = await this.callGeminiProxy(testRequest);
      const responseTime = Date.now() - startTime;

      // 记录健康检查成功
      this.monitor.logHealthCheck(true, responseTime);

      return new Response(JSON.stringify({
        success: true,
        status: 'healthy',
        provider: 'gemini',
        model: this.defaultModel,
        responseTime: responseTime,
        timestamp: new Date().toISOString(),
        config: {
          baseUrl: this.baseUrl,
          timeout: this.timeout,
          maxRetries: this.maxRetries
        }
      }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      // 记录健康检查失败
      this.monitor.logHealthCheck(false, responseTime, error);

      return new Response(JSON.stringify({
        success: false,
        status: 'unhealthy',
        provider: 'gemini',
        error: error.message,
        responseTime: responseTime,
        timestamp: new Date().toISOString()
      }), {
        status: 503,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
  }

  /**
   * 获取支持的模型列表
   */
  async getModels() {
    const models = [
      {
        id: 'gemini-2.0-flash',
        name: 'Gemini 2.0 Flash',
        description: '最新的Gemini模型，平衡性能和质量',
        capabilities: ['text', 'chat', 'json'],
        maxTokens: 8192,
        costLevel: 'medium'
      }
    ];

    return new Response(JSON.stringify({
      success: true,
      data: {
        models: models,
        default: this.defaultModel,
        provider: 'gemini'
      },
      timestamp: new Date().toISOString()
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  /**
   * 获取使用统计
   */
  async getUsageStats() {
    try {
      const stats = await this.monitor.getUsageStats();
      
      return new Response(JSON.stringify({
        success: true,
        data: {
          provider: 'gemini',
          ...stats
        },
        timestamp: new Date().toISOString()
      }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    } catch (error) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Failed to get usage stats',
        message: error.message,
        timestamp: new Date().toISOString()
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
  }

  /**
   * 处理AI图片生成请求
   */
  async handleImageGeneration(request) {
    let requestContext = null;

    try {
      const data = await request.json();

      // 创建请求上下文
      requestContext = new RequestContext(this.monitor, {
        ...data,
        userAgent: request.headers.get('User-Agent'),
        requestType: 'image-generation'
      });

      // 验证图片生成请求格式
      this.validateImageGenerationRequest(data);

      // 调用Gemini图片生成API
      const response = await this.callGeminiImageGeneration(data, requestContext);

      return new Response(JSON.stringify({
        success: true,
        data: response
      }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      });

    } catch (error) {
      console.error('图片生成请求失败:', error);

      if (requestContext) {
        requestContext.recordError(error);
      }

      return new Response(JSON.stringify({
        success: false,
        error: error.message || '图片生成失败'
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
  }

  /**
   * 验证图片生成请求格式
   */
  validateImageGenerationRequest(data) {
    if (!data.messages || !Array.isArray(data.messages)) {
      throw new Error('请求格式错误：缺少messages字段');
    }

    if (data.messages.length === 0) {
      throw new Error('请求格式错误：messages不能为空');
    }

    // 检查是否有用户消息
    const hasUserMessage = data.messages.some(msg => msg.role === 'user');
    if (!hasUserMessage) {
      throw new Error('请求格式错误：至少需要一条用户消息');
    }
  }

  /**
   * 调用Gemini图片生成API - 使用OpenAI兼容格式
   */
  async callGeminiImageGeneration(data, requestContext) {
    const startTime = Date.now();

    try {
      // 使用OpenAI兼容的图片生成格式
      const requestBody = {
        model: 'gemini-2.0-flash-preview-image-generation', // 正确的图片生成模型
        messages: data.messages,
        // 图片生成特定参数
        max_tokens: 1000,
        temperature: 0.7,
        // 添加图片生成提示
        response_format: { type: "text" } // 让模型以文本形式描述或提供图片链接
      };

      console.log('🔄 发送图片生成请求:', JSON.stringify(requestBody, null, 2));

      const response = await fetch(`${this.baseUrl}/v1/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
          'User-Agent': 'AmzOva-ImageGenerator/1.0'
        },
        body: JSON.stringify(requestBody),
        signal: AbortSignal.timeout(90000) // 增加到90秒超时，Cloudflare Workers有100秒的CPU时间限制
      });

      const responseTime = Date.now() - startTime;

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ API调用失败:', response.status, errorText);
        throw new Error(`图片生成API调用失败: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      console.log('📨 收到API响应:', JSON.stringify(result, null, 2));

      // 记录成功请求
      if (requestContext) {
        requestContext.recordSuccess(responseTime, result);
      }

      // 处理OpenAI兼容的响应格式
      if (result.choices && result.choices[0]) {
        const choice = result.choices[0];
        
        // 检查消息内容
        if (choice.message && choice.message.content) {
          const content = choice.message.content;
          
          // 如果内容直接是base64图片数据
          if (typeof content === 'string' && content.startsWith('data:image/')) {
            console.log('✅ 检测到base64图片数据');
            
            // 提取base64数据和MIME类型
            const [mimeInfo, base64Data] = content.split(',');
            const mimeType = mimeInfo.match(/data:([^;]+);base64/)?.[1] || 'image/png';
            
            // 估算文件大小
            const fileSize = Math.round(base64Data.length * 0.75);
            
            console.log('📦 图片Base64长度:', base64Data.length, '估算文件大小:', fileSize, 'bytes');
            
            return {
              image: content,
              width: 1024,
              height: 1024,
              fileSize: fileSize,
              optimized: true
            };
          }
          
          // 检查文本中是否包含图片URL或其他图片标识
          const urlMatch = content.match(/!\[image\]\((https?:\/\/[^)]+)\)|https?:\/\/[^\s]+\.(jpg|jpeg|png|gif|webp)/i);
          if (urlMatch) {
            const imageUrl = urlMatch[1] || urlMatch[0];
            console.log('🖼️ 检测到图片URL:', imageUrl);
            
            try {
              // 下载图片并转换为base64
              console.log('📥 开始下载图片...');
              const imageResponse = await fetch(imageUrl, {
                headers: {
                  'User-Agent': 'AmzOva-ImageGenerator/1.0'
                }
              });
              
              if (imageResponse.ok) {
                const imageBuffer = await imageResponse.arrayBuffer();
                console.log('📦 图片大小:', imageBuffer.byteLength, 'bytes');
                
                // 安全的base64转换
                const uint8Array = new Uint8Array(imageBuffer);
                let binaryString = '';
                const chunkSize = 8192;
                
                for (let i = 0; i < uint8Array.length; i += chunkSize) {
                  const chunk = uint8Array.slice(i, i + chunkSize);
                  binaryString += String.fromCharCode.apply(null, chunk);
                }
                
                const base64 = btoa(binaryString);
                const mimeType = imageResponse.headers.get('content-type') || 'image/png';
                
                console.log('✅ 图片转换成功, MIME类型:', mimeType);
                
                return {
                  image: `data:${mimeType};base64,${base64}`,
                  width: 1024,
                  height: 1024,
                  fileSize: imageBuffer.byteLength,
                  optimized: true
                };
              } else {
                console.error('❌ 图片下载失败，状态码:', imageResponse.status);
              }
            } catch (error) {
              console.error('❌ 下载图片失败:', error.message);
            }
          }
          
          // 如果是纯文本响应，可能包含图片生成的信息
          console.log('📝 收到文本响应:', content.substring(0, 200) + '...');
          
          // 检查是否是描述性文本，说明无法生成图片
          if (content.toLowerCase().includes('cannot') || 
              content.toLowerCase().includes('unable') || 
              content.toLowerCase().includes('sorry')) {
            throw new Error('AI模型无法生成请求的图片内容');
          }
        }
      }

      // 如果没有找到预期的图片数据，返回详细错误
      console.error('❌ API响应格式异常，完整响应:', JSON.stringify(result, null, 2));
      throw new Error('API响应中未找到图片数据，可能是模型不支持图片生成');

    } catch (error) {
      const responseTime = Date.now() - startTime;

      if (requestContext) {
        requestContext.recordError(error, responseTime);
      }

      console.error('❌ 图片生成过程中发生错误:', error);

      // 提供更详细的错误信息
      if (error.name === 'AbortError') {
        throw new Error('图片生成请求超时，请稍后重试');
      } else if (error.message.includes('fetch')) {
        throw new Error('网络连接失败，请检查网络后重试');
      } else if (error.message.includes('aborted')) {
        throw new Error('请求被中断，可能是网络问题或服务器超时');
      }

      throw error;
    }
  }
}

export default GeminiAIProxy;