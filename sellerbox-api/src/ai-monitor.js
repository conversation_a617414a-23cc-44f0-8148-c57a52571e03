/**
 * AI服务监控和日志记录模块
 * 提供统一的性能监控、错误追踪和使用统计
 */

class AIMonitor {
  constructor(env) {
    this.env = env;
    this.enableLogging = env.ENABLE_AI_MONITORING !== 'false';
    this.enableErrorTracking = env.ENABLE_ERROR_TRACKING !== 'false';
  }

  /**
   * 记录AI请求开始
   */
  startRequest(requestId, requestData) {
    if (!this.enableLogging) return null;

    const requestInfo = {
      requestId,
      timestamp: Date.now(),
      model: requestData.model,
      messageCount: requestData.messages?.length || 0,
      temperature: requestData.temperature,
      maxTokens: requestData.max_tokens,
      userAgent: requestData.userAgent || 'unknown'
    };

    console.log('🚀 AI请求开始:', {
      requestId,
      model: requestInfo.model,
      messages: requestInfo.messageCount,
      timestamp: new Date(requestInfo.timestamp).toISOString()
    });

    return requestInfo;
  }

  /**
   * 记录AI请求成功
   */
  recordSuccess(requestInfo, responseData) {
    if (!this.enableLogging || !requestInfo) return;

    const duration = Date.now() - requestInfo.timestamp;
    const usage = responseData.usage || {};

    console.log('✅ AI请求成功:', {
      requestId: requestInfo.requestId,
      duration: `${duration}ms`,
      model: requestInfo.model,
      usage: {
        promptTokens: usage.prompt_tokens || 0,
        completionTokens: usage.completion_tokens || 0,
        totalTokens: usage.total_tokens || 0
      }
    });

    // 记录性能指标
    this.recordPerformanceMetrics(requestInfo, duration, true, usage);
  }

  /**
   * 记录AI请求失败
   */
  recordError(requestInfo, error, statusCode = 500) {
    const duration = requestInfo ? Date.now() - requestInfo.timestamp : 0;

    console.error('❌ AI请求失败:', {
      requestId: requestInfo?.requestId || 'unknown',
      duration: `${duration}ms`,
      model: requestInfo?.model || 'unknown',
      error: error.message,
      statusCode
    });

    // 记录错误统计
    if (this.enableErrorTracking) {
      this.recordErrorMetrics(requestInfo, error, statusCode);
    }

    // 记录性能指标
    if (requestInfo) {
      this.recordPerformanceMetrics(requestInfo, duration, false);
    }
  }

  /**
   * 记录性能指标
   */
  recordPerformanceMetrics(requestInfo, duration, success, usage = {}) {
    // 这里可以集成到实际的监控系统
    // 例如：Cloudflare Analytics、Prometheus等
    
    const metrics = {
      timestamp: new Date().toISOString(),
      requestId: requestInfo.requestId,
      model: requestInfo.model,
      duration,
      success,
      messageCount: requestInfo.messageCount,
      temperature: requestInfo.temperature,
      maxTokens: requestInfo.maxTokens,
      usage: {
        promptTokens: usage.prompt_tokens || 0,
        completionTokens: usage.completion_tokens || 0,
        totalTokens: usage.total_tokens || 0
      }
    };

    // 在开发环境下输出详细指标
    if (this.env.ENVIRONMENT === 'development') {
      console.log('📊 性能指标:', metrics);
    }

    // 这里可以发送到监控服务
    // await this.sendToMonitoringService(metrics);
  }

  /**
   * 记录错误指标
   */
  recordErrorMetrics(requestInfo, error, statusCode) {
    const errorMetrics = {
      timestamp: new Date().toISOString(),
      requestId: requestInfo?.requestId || 'unknown',
      model: requestInfo?.model || 'unknown',
      errorType: this.categorizeError(error, statusCode),
      errorMessage: error.message,
      statusCode,
      stack: error.stack
    };

    // 在开发环境下输出详细错误
    if (this.env.ENVIRONMENT === 'development') {
      console.log('🐛 错误指标:', errorMetrics);
    }

    // 这里可以发送到错误追踪服务
    // await this.sendToErrorTrackingService(errorMetrics);
  }

  /**
   * 错误分类
   */
  categorizeError(error, statusCode) {
    if (error.name === 'AbortError') {
      return 'timeout';
    } else if (error.message?.includes('fetch') || error.message?.includes('network')) {
      return 'network';
    } else if (statusCode === 401 || statusCode === 403) {
      return 'authentication';
    } else if (statusCode === 429) {
      return 'rate_limit';
    } else if (statusCode >= 500) {
      return 'server_error';
    } else if (statusCode >= 400) {
      return 'client_error';
    } else {
      return 'unknown';
    }
  }

  /**
   * 生成请求ID
   */
  generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取使用统计
   */
  async getUsageStats() {
    // 这里可以从实际的存储中获取统计数据
    // 目前返回模拟数据
    return {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      successRate: 0,
      errorBreakdown: {},
      modelUsage: {},
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * 健康检查日志
   */
  logHealthCheck(isHealthy, responseTime, error = null) {
    if (isHealthy) {
      console.log('💚 AI服务健康检查通过:', {
        responseTime: `${responseTime}ms`,
        timestamp: new Date().toISOString()
      });
    } else {
      console.error('💔 AI服务健康检查失败:', {
        responseTime: `${responseTime}ms`,
        error: error?.message || 'unknown',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * 记录配置信息
   */
  logConfiguration(config) {
    if (!this.enableLogging) return;

    console.log('⚙️ AI服务配置:', {
      baseUrl: config.baseUrl,
      defaultModel: config.defaultModel,
      timeout: config.timeout,
      maxRetries: config.maxRetries,
      monitoring: this.enableLogging,
      errorTracking: this.enableErrorTracking
    });
  }

  /**
   * 记录重试信息
   */
  logRetry(attempt, maxAttempts, error) {
    console.warn(`🔄 AI请求重试 (${attempt}/${maxAttempts}):`, {
      error: error.message,
      nextRetryIn: `${attempt * 1000}ms`
    });
  }

  /**
   * 记录限流信息
   */
  logRateLimit(retryAfter) {
    console.warn('🚦 AI服务限流:', {
      message: '请求频率过高',
      retryAfter: retryAfter ? `${retryAfter}秒后重试` : '稍后重试'
    });
  }

  /**
   * 记录服务降级
   */
  logServiceDegradation(reason) {
    console.warn('⬇️ AI服务降级:', {
      reason,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 记录服务恢复
   */
  logServiceRecovery() {
    console.log('⬆️ AI服务恢复正常:', {
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * 请求上下文管理器
 * 管理单个请求的生命周期
 */
class RequestContext {
  constructor(monitor, requestData) {
    this.monitor = monitor;
    this.requestId = monitor.generateRequestId();
    this.requestInfo = monitor.startRequest(this.requestId, requestData);
    this.startTime = Date.now();
  }

  /**
   * 记录成功
   */
  recordSuccess(responseData) {
    this.monitor.recordSuccess(this.requestInfo, responseData);
  }

  /**
   * 记录错误
   */
  recordError(error, statusCode) {
    this.monitor.recordError(this.requestInfo, error, statusCode);
  }

  /**
   * 记录重试
   */
  recordRetry(attempt, maxAttempts, error) {
    this.monitor.logRetry(attempt, maxAttempts, error);
  }

  /**
   * 获取请求ID
   */
  getRequestId() {
    return this.requestId;
  }

  /**
   * 获取持续时间
   */
  getDuration() {
    return Date.now() - this.startTime;
  }
}

export { AIMonitor, RequestContext };