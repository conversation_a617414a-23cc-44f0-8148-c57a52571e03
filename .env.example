# ==========================================
# AmzOva (爱麦蛙) - 环境变量配置示例
# ==========================================
#
# 🎯 使用说明:
# 1. 复制此文件为 .env.local (本地开发)
# 2. 确认 Gemini AI代理配置（已预配置）
# 3. 运行 npm run dev 启动开发服务器
#
# 📚 详细文档: src/lib/ai/README.md
# ==========================================

# ==========================================
# 项目基础信息
# ==========================================

# 项目名称
PROJECT_NAME=AmzOva

# 开发环境配置
NODE_ENV=development
PORT=5173

# 项目域名和联系方式
DOMAIN=amzova.com
EMAIL=<EMAIL>

# API 基础 URL
# 本地开发: http://localhost:8787 (本地 Worker)
# 生产环境: https://api.amzova.com (Cloudflare Worker 自定义域名)
VITE_API_BASE_URL=http://localhost:8787

# ==========================================
# AI 服务配置 (Gemini代理)
# ==========================================

# Gemini AI代理配置 (统一AI服务)
# API地址: 您搭建的Gemini代理服务地址
GEMINI_BASE_URL=https://gemini-balance-c1w2.onrender.com
# API密钥: 您的Gemini代理服务密钥
GEMINI_API_KEY=sk-kdjferKjidjrf9jihdhfnghjxkdefKKGHYI

# AI服务配置
AI_TIMEOUT=30000
AI_MAX_RETRIES=3
AI_DEFAULT_MODEL=gemini-2.0-flash

# 监控配置
ENABLE_AI_MONITORING=true
ENABLE_ERROR_TRACKING=true

# ==========================================
# 其他 API 服务
# ==========================================

# 汇率数据 API (用于实时汇率换算器)
VITE_FREECURRENCY_API_KEY=your_freecurrency_api_key_here

# Cloudflare Turnstile 配置 (防机器人验证)
VITE_TURNSTILE_SITE_KEY=0x4AAAAAABnZT55NXpv9bVdR

# ==========================================
# 开发环境配置
# ==========================================

# 日志级别 (debug | info | warn | error)
LOG_LEVEL=debug

# 启用开发调试工具
VITE_ENABLE_DEBUG_PANEL=true

# 启用性能监控
VITE_ENABLE_PERFORMANCE_MONITORING=true

# ==========================================
# 快速配置指南
# ==========================================
#
# 🚀 快速开始 (2 步完成):
# 1. cp .env.example .env.local
# 2. 确认 Gemini 代理配置 (已预配置):
#    - GEMINI_BASE_URL (已设置)
#    - GEMINI_API_KEY (已设置)
# 3. npm run dev
#
# 💰 成本优势:
# ✅ 使用您自建的Gemini代理服务
# ✅ 完全可控的AI服务成本
# ✅ 无第三方依赖风险
# ✅ 高性能和稳定性保证
#
# 📚 详细文档:
# - src/lib/ai/README.md (AI架构说明)
# - ai-test/ (测试用例和验证)
#
# 🧪 测试连接:
# cd ai-test && npm install && npm run test  (测试基础功能)
# cd ai-test && npm run test-title-fix       (测试标题修复)
# cd ai-test && npm run test-title-analyzer  (测试标题分析)
#
# ==========================================
