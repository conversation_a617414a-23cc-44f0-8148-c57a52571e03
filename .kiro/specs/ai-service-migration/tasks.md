# AI服务迁移实施任务列表

## 任务概述

将现有的双AI提供商架构完全替换为统一的Gemini代理服务，重构AI服务层，更新标题分析器和标题修复器工具。

## 实施任务

- [x] 1. 重构AI服务核心架构
  - 创建新的AI服务客户端，移除旧的提供商代码
  - 实现统一的AI调用接口，支持OpenAI格式
  - 建立模型选择和配置管理系统
  - _需求: 1.1, 2.1, 2.2, 2.3_

- [x] 1.1 创建新的AI服务入口文件
  - 重写 `src/lib/ai/index.ts`，移除Cloudflare和SiliconFlow相关代码
  - 实现 `AIServiceClient` 类，统一管理Gemini代理调用
  - 定义标准的AI请求/响应接口
  - _需求: 1.1, 1.3, 2.1_

- [x] 1.2 实现模型配置管理
  - 创建 `src/lib/ai/models.ts`，定义Gemini模型配置
  - 实现智能模型选择策略（标题分析、标题修复等场景）
  - 配置不同场景的模型参数（temperature、maxTokens等）
  - _需求: 2.2, 5.3_

- [x] 1.3 删除旧的AI服务代码
  - 删除 `src/lib/quotaManager.ts` 文件（Cloudflare配额管理）
  - 清理 `src/lib/ai/index.ts` 中的旧提供商逻辑
  - 移除相关的类型定义和配置
  - _需求: 1.4_

- [x] 2. 重构Workers AI代理服务
  - 替换现有的AI代理端点，统一使用Gemini代理
  - 实现OpenAI格式的请求转换和响应处理
  - 添加统一的错误处理和监控机制
  - _需求: 1.2, 2.4, 2.6_

- [x] 2.1 创建Gemini代理处理器
  - 在 `sellerbox-api/src/` 创建 `ai-proxy.js`
  - 实现 `GeminiAIProxy` 类，处理与Gemini代理的通信
  - 添加请求验证、格式转换、错误处理逻辑
  - _需求: 1.2, 2.4_

- [x] 2.2 更新Workers路由配置
  - 修改 `sellerbox-api/src/index.js`，移除旧的AI端点
  - 添加新的统一AI端点 `/api/ai/chat`
  - 实现健康检查和模型列表端点
  - _需求: 1.2, 2.4_

- [x] 2.3 实现错误处理和监控
  - 添加统一的错误处理机制，提供用户友好的错误信息
  - 实现请求日志记录和性能监控
  - 添加超时控制和重试机制
  - _需求: 2.4, 2.5_

- [x] 3. 更新标题分析器工具
  - 重构标题分析器，使用新的AI服务接口
  - 优化分析提示词，适配Gemini模型特点
  - 保持现有功能和用户界面不变
  - _需求: 3.1, 3.2, 3.3_

- [x] 3.1 重构标题分析核心逻辑
  - 修改 `src/components/tools/TitleAnalyzer.tsx`
  - 使用新的 `aiService.chat()` 接口替换旧的AI调用
  - 优化分析提示词，提高Gemini模型的分析准确性
  - _需求: 3.1, 3.2_

- [x] 3.2 更新分析结果解析
  - 适配Gemini模型的响应格式
  - 优化JSON解析逻辑，提高容错性
  - 保持分析结果的数据结构不变
  - _需求: 3.1, 3.2_

- [x] 3.3 测试标题分析功能
  - 测试各种标题分析场景（不同市场、类别）
  - 验证SEO评分、关键词分析、合规检查功能
  - 确保错误处理和用户体验符合预期
  - _需求: 3.1, 3.2, 3.4_

- [x] 4. 更新标题修复器工具
  - 重构标题修复器，使用新的AI服务接口
  - 优化修复提示词，提高修复质量和准确性
  - 保持现有的三版本修复策略
  - _需求: 4.1, 4.2, 4.3, 4.4_

- [x] 4.1 重构标题修复核心逻辑
  - 修改 `src/components/tools/TitleFixer.tsx`
  - 使用新的 `aiService.chat()` 接口替换旧的AI调用
  - 优化修复提示词，适配Gemini模型的修复能力
  - _需求: 4.1, 4.2_

- [x] 4.2 更新修复结果处理
  - 适配Gemini模型的响应格式
  - 优化三版本修复结果的解析和展示
  - 保持修复说明和合规评分功能
  - _需求: 4.1, 4.2, 4.3_

- [x] 4.3 测试标题修复功能
  - 测试各种违规标题的修复场景
  - 验证三个修复版本的质量和差异化
  - 确保修复后的标题符合Amazon政策
  - _需求: 4.1, 4.2, 4.3_

- [x] 5. 更新环境配置和部署
  - 简化环境变量配置，只保留Gemini相关配置
  - 更新部署脚本和文档
  - 配置生产环境的Gemini代理服务
  - _需求: 6.1, 6.2, 6.3, 6.4_

- [x] 5.1 更新环境变量配置
  - 修改 `.env.example`，移除旧的AI服务配置
  - 添加Gemini代理相关的环境变量
  - 更新Cloudflare Workers的环境变量配置
  - _需求: 6.1, 6.2_

- [x] 5.2 更新项目文档
  - 修改 `README.md`，更新AI服务架构说明
  - 更新 `src/lib/ai/README.md`，反映新的架构设计
  - 添加Gemini代理的配置和使用说明
  - _需求: 6.1, 6.4_

- [x] 5.3 配置生产环境部署
  - 在Cloudflare Workers中配置Gemini API密钥
  - 测试生产环境的AI服务连接
  - 验证所有AI功能在生产环境正常工作
  - _需求: 6.2, 6.3, 6.4_

- [x] 6. 全面测试和验证
  - 进行端到端功能测试
  - 验证性能和稳定性
  - 确保用户体验无感知迁移
  - _需求: 所有需求_

- [x] 6.1 功能完整性测试
  - 测试标题分析器的所有功能（分析、评分、建议）
  - 测试标题修复器的所有功能（修复、说明、评分）
  - 验证错误处理和边界情况
  - _需求: 3.1, 3.2, 3.3, 4.1, 4.2, 4.3_

- [x] 6.2 性能和稳定性测试
  - 测试AI调用的响应时间和成功率
  - 验证并发请求处理能力
  - 测试网络异常和超时场景的处理
  - _需求: 2.4, 2.5_

- [x] 6.3 用户体验验证
  - 验证界面和交互保持不变
  - 测试错误提示的友好性和准确性
  - 确保加载状态和进度提示正常
  - _需求: 3.4, 4.4_

- [x] 6.4 部署验证和监控
  - 在开发环境完成所有测试后部署到生产环境
  - 监控生产环境的AI服务调用情况
  - 收集用户反馈，确保迁移成功
  - _需求: 6.2, 6.3, 6.4_

## 实施注意事项

### 代码质量要求
- 所有新代码必须包含TypeScript类型定义
- 添加必要的错误处理和日志记录
- 保持代码风格与现有项目一致
- 添加关键功能的单元测试

### 向后兼容性
- 保持现有AI调用接口不变
- 确保标题分析器和标题修复器的功能完全一致
- 维持用户界面和交互体验不变

### 性能要求
- AI调用响应时间不超过30秒
- 错误恢复时间不超过5秒
- 支持并发AI请求处理

### 安全要求
- API密钥安全存储在Workers环境变量中
- 实现请求验证和防护机制
- 添加访问日志和异常监控

### 测试覆盖
- 核心AI服务功能100%测试覆盖
- 错误场景和边界情况测试
- 端到端功能验证测试

## 风险控制

### 回滚准备
- 保留当前代码的完整备份
- 准备快速回滚脚本
- 建立回滚决策标准

### 监控告警
- 设置AI服务可用性监控
- 配置错误率和响应时间告警
- 建立问题响应流程

### 渐进部署
- 先在开发环境完成所有测试
- 在测试环境进行压力测试
- 生产环境分阶段部署和验证