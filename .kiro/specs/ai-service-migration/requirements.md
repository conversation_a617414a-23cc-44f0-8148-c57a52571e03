# AI服务迁移需求文档

## 项目概述

将现有的双AI提供商架构（Cloudflare Workers AI + SiliconFlow）完全替换为统一的Gemini代理服务，构建可扩展的AI服务架构，支持未来文字、图片、视频等多模态AI工具接入。

## 需求

### 需求1：完全替换AI服务提供商

**用户故事：** 作为系统管理员，我希望将现有的AI服务完全迁移到自建的Gemini代理，以便获得更好的控制和稳定性。

#### 验收标准

1. WHEN 系统启动时 THEN 应该只连接到Gemini代理服务
2. WHEN 前端调用AI时 THEN 应该通过Workers代理转发到Gemini服务
3. WHEN Workers接收AI请求时 THEN 应该使用OpenAI API格式与Gemini代理通信
4. WHEN 原有的Cloudflare和SiliconFlow配置存在时 THEN 系统应该忽略这些配置
5. WHEN AI服务不可用时 THEN 应该显示统一的错误信息

### 需求2：重构AI服务架构

**用户故事：** 作为开发者，我希望有一个清晰、可扩展的AI服务架构，以便后续轻松接入其他AI功能。

#### 验收标准

1. WHEN 添加新的AI功能时 THEN 应该能够复用现有的服务层
2. WHEN 需要不同模型时 THEN 应该能够根据场景智能选择Gemini模型
3. WHEN 前端调用AI时 THEN 应该通过统一的客户端接口
4. WHEN Workers处理AI请求时 THEN 应该有统一的错误处理和重试机制
5. WHEN 系统运行时 THEN 应该能够监控AI服务的使用情况
6. WHEN 扩展多模态功能时 THEN Workers应该支持文件上传和处理

### 需求3：更新标题分析器工具

**用户故事：** 作为亚马逊卖家，我希望标题分析器能够继续提供准确的AI分析，不受底层服务变更影响。

#### 验收标准

1. WHEN 输入产品标题时 THEN 应该返回详细的分析结果
2. WHEN 分析完成时 THEN 应该提供SEO优化建议
3. WHEN 检测到违规内容时 THEN 应该给出具体的修改建议
4. WHEN AI服务调用失败时 THEN 应该显示友好的错误提示

### 需求4：更新标题修复器工具

**用户故事：** 作为亚马逊卖家，我希望标题修复器能够智能修复不合规的标题，保持原有功能不变。

#### 验收标准

1. WHEN 输入违规标题时 THEN 应该生成3个修复版本
2. WHEN 修复完成时 THEN 应该说明具体的修改内容
3. WHEN 修复后的标题时 THEN 应该符合Amazon最新政策
4. WHEN 用户选择修复版本时 THEN 应该能够一键复制

### 需求5：构建可扩展的AI架构

**用户故事：** 作为系统架构师，我希望新的AI架构能够支持未来的多模态AI功能扩展。

#### 验收标准

1. WHEN 需要添加图片AI功能时 THEN 架构应该支持多模态输入
2. WHEN 需要添加视频AI功能时 THEN 应该能够处理大文件上传
3. WHEN 不同AI功能时 THEN 应该能够使用不同的Gemini模型
4. WHEN 系统扩展时 THEN 应该保持配置的简洁性

### 需求6：环境配置简化

**用户故事：** 作为部署人员，我希望AI服务的配置更加简单，只需要配置Gemini代理相关参数。

#### 验收标准

1. WHEN 配置环境变量时 THEN 只需要设置Gemini相关配置
2. WHEN 部署到不同环境时 THEN 配置应该保持一致
3. WHEN 配置错误时 THEN 应该有清晰的错误提示
4. WHEN 服务启动时 THEN 应该验证配置的有效性

## 技术约束

1. **Cloudflare架构约束**: 
   - 前端部署在Cloudflare Pages，无法直接跨域调用外部AI服务
   - 必须通过Cloudflare Workers (sellerbox-api) 作为代理层
   - Workers需要处理CORS和请求转发
2. **API兼容性**: 必须使用OpenAI API格式与Gemini代理通信
3. **向后兼容**: 现有的AI调用接口保持不变
4. **错误处理**: 统一的错误处理和用户友好的错误信息
5. **性能要求**: AI调用响应时间不超过30秒
6. **扩展性**: 架构支持未来多模态AI功能

## 成功标准

1. **功能完整性**: 标题分析器和标题修复器功能完全正常
2. **性能稳定**: AI服务调用成功率>95%
3. **代码质量**: 新架构代码覆盖率>80%
4. **用户体验**: 用户无感知的服务迁移
5. **可维护性**: 代码结构清晰，便于后续扩展

## 风险评估

1. **服务中断风险**: 迁移过程中可能影响现有功能
2. **性能风险**: 新服务的响应时间可能不同
3. **兼容性风险**: OpenAI格式可能与现有逻辑不完全匹配
4. **配置风险**: 环境变量变更可能导致部署问题

## 迁移策略

1. **阶段性迁移**: 先迁移开发环境，再迁移生产环境
2. **功能验证**: 每个功能迁移后进行完整测试
3. **回滚准备**: 保留原有代码，确保可以快速回滚
4. **监控加强**: 迁移后加强服务监控和日志记录