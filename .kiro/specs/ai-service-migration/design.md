# AI服务迁移设计文档

## 架构概述

基于Cloudflare全栈架构的AI服务重构，将现有的双提供商架构完全替换为统一的Gemini代理服务，构建可扩展的多模态AI服务架构。

## 整体架构设计

```
┌─────────────────────────────────────────────────────────────────┐
│                    Cloudflare Global Network                    │
├─────────────────────────────────────────────────────────────────┤
│  🌍 CDN + DDoS Protection + SSL/TLS + DNS                     │
└─────────────────────────────────────────────────────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
        ┌───────▼──────┐ ┌──────▼──────┐ ┌─────▼─────┐
        │ Pages (前端)  │ │Workers (API)│ │ Gemini代理 │
        │              │ │             │ │           │
        │ React SPA    │ │ AI代理服务   │ │ 您的服务   │
        │ AI客户端     │ │ /api/ai     │ │ OpenAI格式│
        └──────────────┘ └─────────────┘ └───────────┘
                │                │               │
                │                └───────────────┘
                │                        │
        ┌───────▼──────┐          ┌──────▼──────┐
        │ 本地存储      │          │ 使用统计     │
        │ 用户偏好     │          │ 错误监控     │
        │ 缓存数据     │          │ 性能追踪     │
        └──────────────┘          └─────────────┘
```

## 核心组件设计

### 1. 前端AI客户端 (src/lib/ai/)

#### 1.1 统一AI服务接口
```typescript
// src/lib/ai/index.ts
export interface AIServiceConfig {
  baseUrl: string;
  apiKey: string;
  defaultModel: string;
  timeout: number;
}

export interface AIRequest {
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  model?: string;
  temperature?: number;
  maxTokens?: number;
}

export interface AIResponse {
  content: string;
  model: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

class AIServiceClient {
  private config: AIServiceConfig;
  
  constructor(config: AIServiceConfig);
  
  // 统一AI调用接口
  async chat(request: AIRequest): Promise<AIResponse>;
  
  // 检查服务可用性
  isAvailable(): boolean;
  
  // 获取服务状态
  getStatus(): Promise<ServiceStatus>;
}
```

#### 1.2 模型选择策略
```typescript
// src/lib/ai/models.ts
export enum GeminiModel {
  // 文本处理模型
  GEMINI_PRO = 'gemini-pro',
  GEMINI_PRO_VISION = 'gemini-pro-vision',
  
  // 未来扩展
  GEMINI_ULTRA = 'gemini-ultra',
  GEMINI_NANO = 'gemini-nano'
}

export interface ModelConfig {
  model: GeminiModel;
  maxTokens: number;
  temperature: number;
  useCase: string[];
}

export const MODEL_CONFIGS: Record<string, ModelConfig> = {
  'title-analysis': {
    model: GeminiModel.GEMINI_PRO,
    maxTokens: 2000,
    temperature: 0.3,
    useCase: ['标题分析', 'SEO优化']
  },
  'title-fix': {
    model: GeminiModel.GEMINI_PRO,
    maxTokens: 1500,
    temperature: 0.2,
    useCase: ['标题修复', '合规检查']
  },
  'image-analysis': {
    model: GeminiModel.GEMINI_PRO_VISION,
    maxTokens: 3000,
    temperature: 0.4,
    useCase: ['图片分析', '产品识别']
  }
};
```

### 2. Workers AI代理服务 (sellerbox-api/)

#### 2.1 统一AI代理端点
```javascript
// sellerbox-api/src/ai-proxy.js
class GeminiAIProxy {
  constructor(env) {
    this.baseUrl = 'https://itabdvybbfhq.eu-central-1.clawcloudrun.com';
    this.apiKey = env.GEMINI_API_KEY || 'sk-kdjferKjidjrf9jihdhfnghjxkdefKKGHYI';
    this.timeout = 30000;
  }

  // 处理AI请求
  async handleRequest(request) {
    const data = await request.json();
    
    // 验证请求格式
    this.validateRequest(data);
    
    // 转换为OpenAI格式
    const openaiRequest = this.convertToOpenAIFormat(data);
    
    // 调用Gemini代理
    const response = await this.callGeminiProxy(openaiRequest);
    
    // 转换响应格式
    return this.convertResponse(response);
  }

  // 调用Gemini代理服务
  async callGeminiProxy(request) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(`${this.baseUrl}/v1/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
          'User-Agent': 'AmzOva-Backend/2.0'
        },
        body: JSON.stringify(request),
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      
      if (!response.ok) {
        throw new Error(`Gemini API Error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      throw this.handleError(error);
    }
  }
}
```

#### 2.2 路由配置
```javascript
// sellerbox-api/src/routes/ai.js
export const aiRoutes = {
  // 统一AI端点
  '/api/ai/chat': {
    method: 'POST',
    handler: geminiProxy.handleRequest.bind(geminiProxy)
  },
  
  // 健康检查
  '/api/ai/health': {
    method: 'GET',
    handler: geminiProxy.healthCheck.bind(geminiProxy)
  },
  
  // 模型列表
  '/api/ai/models': {
    method: 'GET',
    handler: geminiProxy.getModels.bind(geminiProxy)
  }
};
```

### 3. 业务功能适配

#### 3.1 标题分析器重构
```typescript
// src/components/tools/TitleAnalyzer.tsx
import { aiService } from '../../lib/ai';
import { MODEL_CONFIGS } from '../../lib/ai/models';

export class TitleAnalyzer {
  async analyzeTitleWithAI(title: string, marketplace: string, category: string) {
    const prompt = this.buildAnalysisPrompt(title, marketplace, category);
    
    const response = await aiService.chat({
      messages: [
        {
          role: 'system',
          content: '你是专业的亚马逊SEO分析师，专门分析产品标题的优化潜力。'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      model: MODEL_CONFIGS['title-analysis'].model,
      temperature: MODEL_CONFIGS['title-analysis'].temperature,
      maxTokens: MODEL_CONFIGS['title-analysis'].maxTokens
    });

    return this.parseAnalysisResult(response.content);
  }

  private buildAnalysisPrompt(title: string, marketplace: string, category: string): string {
    return `请分析以下亚马逊产品标题：

【标题】: "${title}"
【市场】: ${marketplace}
【类别】: ${category}

请从以下维度进行分析并返回JSON格式结果：

1. **SEO评分** (0-100分)
2. **关键词分析** (主要关键词、长尾关键词、缺失关键词)
3. **合规性检查** (是否符合Amazon政策)
4. **优化建议** (具体的改进建议)
5. **竞争力评估** (与同类产品的竞争优势)

返回格式：
{
  "seoScore": 85,
  "keywords": {
    "primary": ["主要关键词1", "主要关键词2"],
    "longTail": ["长尾关键词1", "长尾关键词2"],
    "missing": ["建议添加的关键词1", "建议添加的关键词2"]
  },
  "compliance": {
    "isCompliant": true,
    "violations": [],
    "suggestions": ["合规建议1", "合规建议2"]
  },
  "optimization": {
    "strengths": ["优势1", "优势2"],
    "weaknesses": ["不足1", "不足2"],
    "recommendations": ["建议1", "建议2"]
  },
  "competitiveness": {
    "score": 78,
    "advantages": ["竞争优势1", "竞争优势2"],
    "improvements": ["改进建议1", "改进建议2"]
  }
}`;
  }
}
```

#### 3.2 标题修复器重构
```typescript
// src/components/tools/TitleFixer.tsx
import { aiService } from '../../lib/ai';
import { MODEL_CONFIGS } from '../../lib/ai/models';

export class TitleFixer {
  async fixTitleWithAI(
    originalTitle: string,
    errorMessage: string,
    marketplace: string,
    category: string,
    violations: any[]
  ) {
    const prompt = this.buildFixPrompt(originalTitle, errorMessage, marketplace, category, violations);
    
    const response = await aiService.chat({
      messages: [
        {
          role: 'system',
          content: '你是专业的亚马逊标题合规专家，专门修复违规标题。'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      model: MODEL_CONFIGS['title-fix'].model,
      temperature: MODEL_CONFIGS['title-fix'].temperature,
      maxTokens: MODEL_CONFIGS['title-fix'].maxTokens
    });

    return this.parseFixResult(response.content);
  }

  private buildFixPrompt(
    originalTitle: string,
    errorMessage: string,
    marketplace: string,
    category: string,
    violations: any[]
  ): string {
    const violationAnalysis = violations.length > 0
      ? violations.map(v => `- ${v.type}: ${v.message}`).join('\n')
      : '无具体违规项检测到';

    return `作为亚马逊标题合规专家，请修复以下违规标题：

【原标题】: "${originalTitle}"
【错误信息】: "${errorMessage}"
【目标市场】: ${marketplace}
【产品类别】: ${category}
【违规项】:
${violationAnalysis}

请提供3个修复版本，严格按照以下JSON格式返回：
{
  "fixedTitles": [
    "版本1：最小化修改的完整标题",
    "版本2：SEO优化的完整标题", 
    "版本3：转化优化的完整标题"
  ],
  "changes": [
    "版本1修改说明",
    "版本2修改说明",
    "版本3修改说明"
  ],
  "explanation": "详细修复说明",
  "complianceScore": 95
}

修复原则：
1. 严格遵循Amazon 2025年最新政策
2. 保持原有关键词和SEO价值
3. 确保100%合规性
4. 优化用户体验和转化率`;
  }
}
```

## 数据模型设计

### AI服务配置模型
```typescript
interface AIServiceConfig {
  provider: 'gemini';
  baseUrl: string;
  apiKey: string;
  models: {
    textAnalysis: string;
    textGeneration: string;
    imageAnalysis: string;
    videoAnalysis: string;
  };
  limits: {
    maxTokens: number;
    timeout: number;
    retryAttempts: number;
  };
}
```

### AI请求/响应模型
```typescript
interface AIRequest {
  type: 'text' | 'image' | 'video' | 'multimodal';
  messages: Message[];
  model?: string;
  parameters?: {
    temperature?: number;
    maxTokens?: number;
    topP?: number;
  };
  metadata?: {
    userId?: string;
    sessionId?: string;
    feature: string;
  };
}

interface AIResponse {
  content: string;
  model: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  metadata: {
    requestId: string;
    timestamp: string;
    processingTime: number;
  };
}
```

## 错误处理策略

### 1. 分层错误处理
```typescript
// 前端错误处理
class AIErrorHandler {
  static handle(error: AIError): UserFriendlyError {
    switch (error.type) {
      case 'NETWORK_ERROR':
        return new UserFriendlyError('网络连接失败，请检查网络后重试');
      case 'API_ERROR':
        return new UserFriendlyError('AI服务暂时不可用，请稍后重试');
      case 'TIMEOUT_ERROR':
        return new UserFriendlyError('请求超时，请重试');
      case 'QUOTA_EXCEEDED':
        return new UserFriendlyError('今日使用量已达上限，请明天再试');
      default:
        return new UserFriendlyError('服务异常，请联系技术支持');
    }
  }
}
```

### 2. Workers错误处理
```javascript
// Workers错误处理
class WorkersErrorHandler {
  static handleAIError(error, request) {
    const errorResponse = {
      success: false,
      error: this.getErrorMessage(error),
      requestId: this.generateRequestId(),
      timestamp: new Date().toISOString()
    };

    // 记录错误日志
    console.error('AI Proxy Error:', {
      error: error.message,
      stack: error.stack,
      request: this.sanitizeRequest(request)
    });

    return new Response(JSON.stringify(errorResponse), {
      status: this.getStatusCode(error),
      headers: {
        'Content-Type': 'application/json',
        'X-Request-ID': errorResponse.requestId
      }
    });
  }
}
```

## 测试策略

### 1. 单元测试
- AI客户端功能测试
- 错误处理测试
- 模型选择逻辑测试

### 2. 集成测试
- Workers代理功能测试
- 端到端AI调用测试
- 错误场景测试

### 3. 性能测试
- AI调用响应时间测试
- 并发请求处理测试
- 超时和重试机制测试

## 部署配置

### 环境变量配置
```bash
# Gemini AI服务配置
GEMINI_API_KEY=sk-kdjferKjidjrf9jihdhfnghjxkdefKKGHYI
GEMINI_BASE_URL=https://itabdvybbfhq.eu-central-1.clawcloudrun.com

# 服务配置
AI_TIMEOUT=30000
AI_MAX_RETRIES=3
AI_DEFAULT_MODEL=gemini-pro

# 监控配置
ENABLE_AI_MONITORING=true
ENABLE_ERROR_TRACKING=true
```

### Cloudflare Workers配置
```toml
# wrangler.toml
[env.production.vars]
GEMINI_API_KEY = "sk-kdjferKjidjrf9jihdhfnghjxkdefKKGHYI"
GEMINI_BASE_URL = "https://itabdvybbfhq.eu-central-1.clawcloudrun.com"
AI_TIMEOUT = "30000"
```

## 扩展性设计

### 1. 多模态支持架构
```typescript
// 未来扩展：图片分析
interface ImageAnalysisRequest extends AIRequest {
  type: 'image';
  image: {
    url?: string;
    base64?: string;
    mimeType: string;
  };
}

// 未来扩展：视频分析
interface VideoAnalysisRequest extends AIRequest {
  type: 'video';
  video: {
    url: string;
    duration: number;
    format: string;
  };
}
```

### 2. 插件化架构
```typescript
// AI功能插件接口
interface AIPlugin {
  name: string;
  version: string;
  supportedModels: string[];
  
  process(request: AIRequest): Promise<AIResponse>;
  validate(request: AIRequest): boolean;
  getMetadata(): PluginMetadata;
}

// 插件管理器
class AIPluginManager {
  private plugins: Map<string, AIPlugin> = new Map();
  
  register(plugin: AIPlugin): void;
  unregister(name: string): void;
  execute(pluginName: string, request: AIRequest): Promise<AIResponse>;
}
```

## 监控和日志

### 1. 性能监控
- AI调用响应时间
- 成功率统计
- 错误类型分析
- 用户使用模式

### 2. 日志记录
- 请求/响应日志
- 错误详情记录
- 性能指标记录
- 用户行为追踪

这个设计确保了：
1. **完全替换**：移除所有旧的AI提供商代码
2. **架构清晰**：分层设计，职责明确
3. **扩展性强**：支持未来多模态AI功能
4. **Cloudflare兼容**：充分利用Cloudflare生态
5. **用户体验**：无感知迁移，功能保持不变