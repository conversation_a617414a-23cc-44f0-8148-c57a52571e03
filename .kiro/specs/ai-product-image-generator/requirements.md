# AI产品图片生成器需求文档

## 介绍

本项目旨在完善和上架AI产品图片生成器工具，使用Gemini 2.0 Flash图片生成模型，为亚马逊卖家提供专业的产品图片生成和编辑服务。该工具将集成到现有的爱麦蛙(AmzOva)平台中，支持多语言界面和智能图片处理功能。

## 需求

### 需求1：AI服务集成

**用户故事：** 作为开发者，我希望将产品图片生成器与统一的Gemini代理服务集成，以便提供稳定可靠的AI图片生成功能。

#### 验收标准

1. WHEN 系统初始化时 THEN 应该连接到Gemini代理服务 `https://gemini-balance-c1w2.onrender.com`
2. WHEN 调用AI服务时 THEN 应该使用API密钥 `sk-kdjferKjidjrf9jihdhfnghjxkdefKKGHYI`
3. WHEN 进行图片生成时 THEN 应该使用模型 `gemini-2.0-flash-exp-image-generation`
4. WHEN AI服务不可用时 THEN 应该显示适当的错误消息并提供重试选项
5. WHEN API调用超时时 THEN 应该在30秒后超时并显示错误信息

### 需求2：图片上传和处理

**用户故事：** 作为用户，我希望能够上传产品图片作为基础图像，以便AI能够基于现有图片进行背景替换和场景编辑。

#### 验收标准

1. WHEN 用户拖拽图片到上传区域时 THEN 系统应该接受JPG、PNG、WebP格式的图片文件
2. WHEN 图片文件大小超过10MB时 THEN 系统应该显示文件大小错误提示
3. WHEN 图片上传成功时 THEN 系统应该显示图片预览并转换为base64格式
4. WHEN 用户点击更换图片时 THEN 系统应该允许重新选择图片文件
5. IF 图片格式不支持 THEN 系统应该显示格式错误提示

### 需求3：自然语言图片编辑

**用户故事：** 作为用户，我希望能够通过自然语言描述来编辑产品图片，例如"将背景替换为游乐园场景"，以便快速获得所需的图片效果。

#### 验收标准

1. WHEN 用户输入编辑需求时 THEN 系统应该接受中文、英文等多语言描述
2. WHEN 用户提交编辑请求时 THEN AI应该理解背景替换、场景变更、风格调整等指令
3. WHEN 编辑完成时 THEN 系统应该保持产品主体不变，只修改指定的元素
4. WHEN 用户需要继续调整时 THEN 系统应该支持基于上一次结果的迭代编辑
5. IF 编辑描述不清晰 THEN 系统应该提供改进建议

### 需求4：亚马逊规格适配（2025年7月最新标准）

**用户故事：** 作为亚马逊卖家，我希望生成的图片能够符合亚马逊2025年7月最新的图片规格要求，以便直接用于产品listing而不会被拒绝。

#### 验收标准

1. WHEN 用户选择主图规格时 THEN 系统应该生成纯白背景(RGB 255,255,255)、产品占比85%、最小1000x1000像素的图片
2. WHEN 用户选择附图规格时 THEN 系统应该生成最小500x500像素、可展示产品使用场景的图片
3. WHEN 用户选择A+内容图时 THEN 系统应该生成970x600像素或1464x600像素的品牌展示图片
4. WHEN 用户选择品牌店图时 THEN 系统应该生成3000x600像素的Banner图或1200x1200像素的瓷砖图
5. WHEN 生成完成时 THEN 图片应该符合JPEG或PNG格式、文件大小不超过10MB的要求
6. WHEN 选择主图规格时 THEN 系统应该确保无文字、无水印、无边框、产品完整可见
7. WHEN 生成任何规格图片时 THEN 系统应该确保图片清晰度达到72DPI以上

### 需求5：多语言支持

**用户故事：** 作为国际用户，我希望能够使用我熟悉的语言来操作图片生成器，以便更好地理解和使用工具功能。

#### 验收标准

1. WHEN 用户切换语言时 THEN 界面应该支持简体中文、繁体中文、英文、日文、印尼文、越南文
2. WHEN AI处理用户输入时 THEN 系统应该根据用户语言生成对应语言的提示词
3. WHEN 显示错误信息时 THEN 错误消息应该以用户选择的语言显示
4. WHEN 提供使用说明时 THEN 说明文字应该完全本地化
5. IF 用户输入非当前界面语言 THEN AI应该仍能正确理解并处理

### 需求6：使用限制和配额管理

**用户故事：** 作为平台运营者，我希望能够控制用户的使用频率和成本，以便维持服务的可持续性。

#### 验收标准

1. WHEN 免费用户使用时 THEN 系统应该限制每日2次图片生成
2. WHEN 用户达到使用限制时 THEN 系统应该显示限制提示和升级建议
3. WHEN 用户生成图片时 THEN 系统应该记录使用次数和时间戳
4. WHEN 新的一天开始时 THEN 系统应该重置每日使用计数
5. IF 系统检测到滥用 THEN 应该实施额外的冷却时间限制

### 需求7：图片下载和导出

**用户故事：** 作为用户，我希望能够下载生成的图片并选择合适的格式，以便在不同平台使用。

#### 验收标准

1. WHEN 图片生成成功时 THEN 用户应该能够点击下载按钮保存图片
2. WHEN 下载图片时 THEN 系统应该提供PNG、JPG格式选项
3. WHEN 文件下载时 THEN 文件名应该包含时间戳以避免重复
4. WHEN 下载完成时 THEN 系统应该显示成功提示
5. IF 下载失败 THEN 系统应该显示错误信息并提供重试选项

### 需求8：错误处理和用户反馈

**用户故事：** 作为用户，我希望在遇到问题时能够获得清晰的错误信息和解决建议，以便快速解决问题继续使用。

#### 验收标准

1. WHEN AI服务连接失败时 THEN 系统应该显示网络错误提示
2. WHEN 图片生成超时时 THEN 系统应该显示超时错误并建议重试
3. WHEN 输入内容不符合要求时 THEN 系统应该提供具体的改进建议
4. WHEN 发生未知错误时 THEN 系统应该记录错误日志并显示通用错误信息
5. WHEN 用户遇到问题时 THEN 系统应该提供帮助文档链接

### 需求9：性能优化

**用户故事：** 作为用户，我希望图片生成过程能够快速响应，以便提高工作效率。

#### 验收标准

1. WHEN 用户提交生成请求时 THEN 系统应该在3秒内开始处理
2. WHEN AI处理图片时 THEN 系统应该显示进度指示器
3. WHEN 图片生成完成时 THEN 总处理时间应该控制在30秒以内
4. WHEN 用户上传大图片时 THEN 系统应该自动压缩到合适尺寸
5. IF 处理时间过长 THEN 系统应该提供取消选项

### 需求10：内容政策合规处理

**用户故事：** 作为用户，当我的图片生成请求违反Gemini图片生成政策时，我希望能够收到清晰的说明和建议，以便调整我的需求。

#### 验收标准

1. WHEN Gemini返回政策违规错误时 THEN 系统应该解析错误信息并显示中文说明
2. WHEN 检测到人物生成限制时 THEN 系统应该提示"抱歉，当前不支持生成包含人物的图片，请调整您的描述"
3. WHEN 检测到儿童相关内容时 THEN 系统应该提示"为保护儿童安全，系统不支持生成儿童相关图片"
4. WHEN 检测到敏感内容时 THEN 系统应该提示具体的违规原因并建议修改方向
5. WHEN 用户收到政策违规提示时 THEN 系统应该提供替代建议和修改示例
6. IF 多次违规 THEN 系统应该显示政策说明链接和使用指南

### 需求11：安全性和隐私保护

**用户故事：** 作为用户，我希望我的图片和数据能够得到安全保护，不会被滥用或泄露。

#### 验收标准

1. WHEN 用户上传图片时 THEN 图片应该仅在本地处理，不永久存储
2. WHEN AI处理完成时 THEN 临时文件应该自动清理
3. WHEN 用户关闭页面时 THEN 所有图片数据应该从内存中清除
4. WHEN 传输数据时 THEN 所有通信应该使用HTTPS加密
5. IF 检测到恶意内容 THEN 系统应该拒绝处理并记录日志