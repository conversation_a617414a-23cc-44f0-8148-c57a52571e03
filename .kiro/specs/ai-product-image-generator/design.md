# AI产品图片生成器设计文档

## 概述

本设计文档详细描述了AI产品图片生成器的技术架构、组件设计和实现方案。该工具基于Gemini 2.0 Flash图片生成模型，为亚马逊卖家提供专业的产品图片生成和编辑服务，支持自然语言描述的图片编辑和多种亚马逊规格适配。

## 架构设计

### 整体架构

```
┌─────────────────────────────────────────────────────────────────┐
│                    用户界面层 (UI Layer)                         │
├─────────────────────────────────────────────────────────────────┤
│  ProductImageGenerator.tsx - React组件                          │
│  ├── 图片上传区域                                                │
│  ├── 参数配置面板                                                │
│  ├── 预览结果区域                                                │
│  └── 多语言界面支持                                              │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                    业务逻辑层 (Service Layer)                    │
├─────────────────────────────────────────────────────────────────┤
│  ImageGeneratorService.ts - 核心业务逻辑                        │
│  ├── 图片生成管理                                                │
│  ├── 编辑请求处理                                                │
│  ├── 亚马逊规格适配                                              │
│  ├── 使用限制控制                                                │
│  └── 错误处理和重试                                              │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                    AI服务层 (AI Service Layer)                  │
├─────────────────────────────────────────────────────────────────┤
│  GeminiImageService.ts - Gemini图片生成服务                     │
│  ├── 模型配置管理                                                │
│  ├── 提示词优化                                                  │
│  ├── API调用封装                                                 │
│  ├── 响应解析处理                                                │
│  └── 政策违规处理                                                │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                    网络通信层 (Network Layer)                    │
├─────────────────────────────────────────────────────────────────┤
│  Cloudflare Workers代理 - API网关                               │
│  ├── 请求路由: /api/ai/image/generate                           │
│  ├── 请求路由: /api/ai/image/edit                               │
│  ├── 认证和限流                                                  │
│  └── 错误处理和日志                                              │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                    外部服务层 (External Services)               │
├─────────────────────────────────────────────────────────────────┤
│  Gemini代理服务 - https://gemini-balance-c1w2.onrender.com     │
│  ├── 模型: gemini-2.0-flash-exp-image-generation               │
│  ├── API密钥: sk-kdjferKjidjrf9jihdhfnghjxkdefKKGHYI            │
│  ├── 图片生成能力                                                │
│  └── 多轮对话编辑                                                │
└─────────────────────────────────────────────────────────────────┘
```

### 数据流设计

```
用户操作 → UI组件 → 业务逻辑 → AI服务 → 网络请求 → Gemini代理 → 响应处理 → 结果展示
    ↓         ↓         ↓         ↓         ↓         ↓         ↓         ↓
  上传图片   参数验证   提示词生成  API调用   HTTP请求  图片生成   解析响应   显示图片
  输入描述   格式检查   规格适配   错误处理   超时控制  政策检查   错误处理   下载功能
  选择规格   使用限制   多语言化   重试机制   负载均衡  质量优化   状态更新   用户反馈
```

## 组件设计

### 1. 核心组件架构

#### ProductImageGenerator.tsx (主组件)
```typescript
interface ProductImageGeneratorProps {
  onClose?: () => void;
}

interface ComponentState {
  // 图片状态
  uploadedImage: string | null;
  generatedImages: GeneratedImage[];
  currentImageIndex: number;
  
  // 生成参数
  prompt: string;
  amazonSpec: AmazonImageSpec;
  style: ImageStyle;
  aspectRatio: AspectRatio;
  
  // UI状态
  isGenerating: boolean;
  isEditing: boolean;
  error: string | null;
  success: string | null;
  
  // 使用限制
  usageStats: UsageStats;
  
  // 编辑历史
  editHistory: EditHistoryItem[];
}
```

#### GeminiImageService.ts (AI服务)
```typescript
class GeminiImageService {
  // 核心方法
  async generateImage(request: ImageGenerationRequest): Promise<ImageGenerationResult>
  async editImage(request: ImageEditRequest): Promise<ImageGenerationResult>
  async validateContent(prompt: string): Promise<ContentValidationResult>
  
  // 辅助方法
  private buildPrompt(request: ImageGenerationRequest): string
  private parseGeminiResponse(response: any): ImageGenerationResult
  private handlePolicyViolation(error: any): PolicyViolationError
  private optimizeForAmazonSpec(spec: AmazonImageSpec): PromptEnhancement
}
```

### 2. 数据模型设计

#### 图片生成请求模型
```typescript
interface ImageGenerationRequest {
  // 基础参数
  prompt: string;                    // 用户描述
  baseImage?: string;               // 基础图片(base64)
  
  // 规格参数
  amazonSpec: AmazonImageSpec;      // 亚马逊规格
  aspectRatio: AspectRatio;         // 宽高比
  style: ImageStyle;                // 图片风格
  
  // 高级参数
  quality?: 'standard' | 'high';    // 图片质量
  iterations?: number;              // 生成次数
  seed?: number;                    // 随机种子
  
  // 元数据
  userTier: UserTier;              // 用户等级
  language: string;                // 界面语言
  sessionId: string;               // 会话ID
}

interface ImageGenerationResult {
  // 生成结果
  imageData: string;               // 图片数据(base64)
  prompt: string;                  // 实际使用的提示词
  
  // 元数据
  metadata: {
    model: string;                 // 使用的模型
    aspectRatio: string;           // 实际宽高比
    dimensions: {                  // 图片尺寸
      width: number;
      height: number;
    };
    fileSize: number;              // 文件大小(bytes)
    processingTime: number;        // 处理时间(ms)
    timestamp: string;             // 生成时间
    quality: string;               // 图片质量
    complianceCheck: {             // 合规检查
      isCompliant: boolean;
      issues: string[];
      suggestions: string[];
    };
  };
  
  // 错误信息
  warnings?: string[];             // 警告信息
  limitations?: string[];          // 限制说明
}
```

#### 亚马逊规格配置
```typescript
enum AmazonImageSpec {
  MAIN = 'main',                   // 主图
  ADDITIONAL = 'additional',       // 附图
  APLUS = 'aplus',                // A+内容图
  BRAND_STORE = 'brand_store'      // 品牌店图
}

interface AmazonSpecConfig {
  [AmazonImageSpec.MAIN]: {
    dimensions: { min: [1000, 1000], recommended: [2000, 2000] };
    aspectRatio: '1:1';
    background: 'pure_white';      // RGB(255,255,255)
    productCoverage: 0.85;         // 产品占比85%
    restrictions: ['no_text', 'no_watermark', 'no_border'];
    fileFormats: ['JPEG', 'PNG'];
    maxFileSize: 10 * 1024 * 1024; // 10MB
    dpi: 72;
  };
  
  [AmazonImageSpec.ADDITIONAL]: {
    dimensions: { min: [500, 500], recommended: [1600, 1600] };
    aspectRatio: '1:1';
    background: 'flexible';
    productCoverage: 0.7;          // 产品占比70%以上
    restrictions: ['minimal_text'];
    fileFormats: ['JPEG', 'PNG'];
    maxFileSize: 10 * 1024 * 1024;
    dpi: 72;
  };
  
  [AmazonImageSpec.APLUS]: {
    dimensions: { 
      options: [
        [970, 600],   // 标准A+图
        [1464, 600]   // 宽版A+图
      ]
    };
    aspectRatio: '16:9';
    background: 'flexible';
    productCoverage: 0.5;          // 产品占比50%以上
    restrictions: ['brand_appropriate'];
    fileFormats: ['JPEG', 'PNG'];
    maxFileSize: 10 * 1024 * 1024;
    dpi: 72;
  };
  
  [AmazonImageSpec.BRAND_STORE]: {
    dimensions: {
      banner: [3000, 600],         // 品牌店Banner
      tile: [1200, 1200]           // 品牌店瓷砖
    };
    aspectRatio: 'flexible';
    background: 'flexible';
    productCoverage: 0.3;          // 产品占比30%以上
    restrictions: ['brand_consistent'];
    fileFormats: ['JPEG', 'PNG'];
    maxFileSize: 10 * 1024 * 1024;
    dpi: 72;
  };
}
```

### 3. AI服务集成设计

#### Gemini 2.0 Flash图片生成集成
```typescript
class GeminiImageService extends BaseAIService {
  private readonly config = {
    baseUrl: 'https://gemini-balance-c1w2.onrender.com',
    apiKey: 'sk-kdjferKjidjrf9jihdhfnghjxkdefKKGHYI',
    model: 'gemini-2.0-flash-exp-image-generation',
    timeout: 30000,
    maxRetries: 3
  };

  /**
   * 生成产品图片
   */
  async generateImage(request: ImageGenerationRequest): Promise<ImageGenerationResult> {
    // 1. 内容验证
    const validation = await this.validateContent(request.prompt);
    if (!validation.isValid) {
      throw new PolicyViolationError(validation.reason, validation.suggestions);
    }

    // 2. 构建优化提示词
    const optimizedPrompt = this.buildOptimizedPrompt(request);

    // 3. 调用Gemini API
    const apiRequest = {
      model: this.config.model,
      prompt: optimizedPrompt,
      image: request.baseImage,
      parameters: {
        aspect_ratio: request.aspectRatio,
        quality: request.quality || 'standard',
        style: request.style,
        seed: request.seed
      }
    };

    const response = await this.callGeminiAPI('/generate', apiRequest);
    
    // 4. 解析和验证结果
    const result = await this.parseAndValidateResult(response, request);
    
    // 5. 亚马逊规格合规检查
    const complianceCheck = await this.checkAmazonCompliance(result, request.amazonSpec);
    result.metadata.complianceCheck = complianceCheck;

    return result;
  }

  /**
   * 编辑现有图片
   */
  async editImage(request: ImageEditRequest): Promise<ImageGenerationResult> {
    const editPrompt = this.buildEditPrompt(request);
    
    const apiRequest = {
      model: this.config.model,
      prompt: editPrompt,
      base_image: request.baseImage,
      edit_instruction: request.editInstruction,
      preserve_product: request.preserveProduct || true,
      parameters: {
        edit_strength: request.editStrength || 0.7,
        preserve_areas: request.preserveAreas || ['product']
      }
    };

    const response = await this.callGeminiAPI('/edit', apiRequest);
    return this.parseAndValidateResult(response, request);
  }

  /**
   * 构建优化提示词
   */
  private buildOptimizedPrompt(request: ImageGenerationRequest): string {
    const basePrompt = request.prompt;
    const specConfig = AMAZON_SPEC_CONFIGS[request.amazonSpec];
    
    // 基础提示词增强
    let enhancedPrompt = `Professional product photography: ${basePrompt}`;

    // 亚马逊规格要求
    switch (request.amazonSpec) {
      case AmazonImageSpec.MAIN:
        enhancedPrompt += `\n\nAmazon Main Image Requirements:
        - Pure white background (RGB 255,255,255)
        - Product centered and occupying 85% of image area
        - No text overlays, watermarks, or borders
        - High resolution, professional studio lighting
        - Product must be fully visible and in focus`;
        break;
        
      case AmazonImageSpec.ADDITIONAL:
        enhancedPrompt += `\n\nAmazon Additional Image Requirements:
        - Show product features and usage scenarios
        - Product should occupy at least 70% of image area
        - Minimal text allowed for feature callouts
        - Demonstrate product functionality or benefits`;
        break;
        
      case AmazonImageSpec.APLUS:
        enhancedPrompt += `\n\nAmazon A+ Content Requirements:
        - Brand story and lifestyle presentation
        - 16:9 aspect ratio (970x600 or 1464x600 pixels)
        - Professional brand-appropriate styling
        - Can include brand elements and storytelling`;
        break;
        
      case AmazonImageSpec.BRAND_STORE:
        enhancedPrompt += `\n\nAmazon Brand Store Requirements:
        - Brand-consistent visual identity
        - High-impact promotional styling
        - Can include marketing elements and brand messaging
        - Optimized for brand store display`;
        break;
    }

    // 风格要求
    switch (request.style) {
      case 'studio':
        enhancedPrompt += '\n\nStyle: Clean studio photography with professional lighting, minimal shadows, neutral background';
        break;
      case 'lifestyle':
        enhancedPrompt += '\n\nStyle: Lifestyle photography showing product in real-world usage, natural lighting, contextual environment';
        break;
      case 'scene':
        enhancedPrompt += '\n\nStyle: Contextual scene showing product in intended environment, atmospheric lighting, story-driven composition';
        break;
      case 'product':
        enhancedPrompt += '\n\nStyle: Pure product focus with technical precision, even lighting, detail-oriented presentation';
        break;
    }

    // 技术规格
    enhancedPrompt += `\n\nTechnical Requirements:
    - Aspect ratio: ${request.aspectRatio}
    - High resolution and sharp details
    - Professional color accuracy
    - Optimized for e-commerce display`;

    return enhancedPrompt;
  }

  /**
   * 处理政策违规错误
   */
  private handlePolicyViolation(error: any, language: string): PolicyViolationError {
    const errorMessage = error.message || error.error || '';
    
    // 解析常见违规类型
    if (errorMessage.includes('person') || errorMessage.includes('human') || errorMessage.includes('people')) {
      return new PolicyViolationError(
        language === 'en' ? 'Human content not supported' : '不支持生成包含人物的图片',
        language === 'en' ? 
          ['Try describing the product without people', 'Focus on the product itself', 'Use object-only descriptions'] :
          ['请尝试不包含人物的描述', '专注于产品本身', '使用纯产品描述']
      );
    }
    
    if (errorMessage.includes('child') || errorMessage.includes('kid') || errorMessage.includes('baby')) {
      return new PolicyViolationError(
        language === 'en' ? 'Child-related content not supported' : '为保护儿童安全，系统不支持生成儿童相关图片',
        language === 'en' ?
          ['Remove child-related descriptions', 'Focus on adult-oriented product usage', 'Use age-neutral contexts'] :
          ['请移除儿童相关描述', '专注于成人使用场景', '使用年龄中性的场景']
      );
    }
    
    if (errorMessage.includes('inappropriate') || errorMessage.includes('policy')) {
      return new PolicyViolationError(
        language === 'en' ? 'Content violates generation policy' : '内容违反生成政策',
        language === 'en' ?
          ['Use more neutral descriptions', 'Avoid sensitive topics', 'Focus on product features'] :
          ['请使用更中性的描述', '避免敏感话题', '专注于产品特性']
      );
    }

    // 通用违规处理
    return new PolicyViolationError(
      language === 'en' ? 'Content policy violation detected' : '检测到内容政策违规',
      language === 'en' ?
        ['Please modify your description', 'Try a different approach', 'Contact support if issue persists'] :
        ['请修改您的描述', '尝试不同的表达方式', '如问题持续请联系客服']
    );
  }
}
```

## 接口设计

### 1. API端点设计

#### Cloudflare Workers API路由
```typescript
// /api/ai/image/generate - 图片生成
POST /api/ai/image/generate
Content-Type: application/json

Request Body:
{
  "prompt": "string",
  "baseImage": "string?",           // base64编码的图片
  "amazonSpec": "main|additional|aplus|brand_store",
  "aspectRatio": "1:1|3:4|4:3|9:16|16:9",
  "style": "studio|lifestyle|product|scene",
  "quality": "standard|high",
  "language": "zh|en|ja|id|vi|zh-TW",
  "userTier": "free|pro|enterprise",
  "sessionId": "string"
}

Response:
{
  "success": boolean,
  "data": {
    "imageData": "string",          // base64编码的生成图片
    "prompt": "string",             // 实际使用的提示词
    "metadata": {
      "model": "string",
      "processingTime": number,
      "dimensions": { "width": number, "height": number },
      "fileSize": number,
      "complianceCheck": {
        "isCompliant": boolean,
        "issues": string[],
        "suggestions": string[]
      }
    }
  },
  "error": "string?",
  "warnings": string[]
}

// /api/ai/image/edit - 图片编辑
POST /api/ai/image/edit
Content-Type: application/json

Request Body:
{
  "baseImage": "string",            // base64编码的原图
  "editInstruction": "string",      // 编辑指令
  "preserveProduct": boolean,       // 是否保持产品不变
  "editStrength": number,           // 编辑强度 0.1-1.0
  "language": "zh|en|ja|id|vi|zh-TW",
  "sessionId": "string"
}

Response: // 同generate接口
```

### 2. 前端组件接口

#### React组件Props接口
```typescript
interface ProductImageGeneratorProps {
  onClose?: () => void;
  initialPrompt?: string;
  defaultSpec?: AmazonImageSpec;
  onImageGenerated?: (result: ImageGenerationResult) => void;
  onError?: (error: Error) => void;
}

interface ImageUploadProps {
  onImageUpload: (imageData: string) => void;
  onError: (error: string) => void;
  maxSize?: number;
  acceptedFormats?: string[];
}

interface ParameterConfigProps {
  prompt: string;
  onPromptChange: (prompt: string) => void;
  amazonSpec: AmazonImageSpec;
  onSpecChange: (spec: AmazonImageSpec) => void;
  aspectRatio: AspectRatio;
  onRatioChange: (ratio: AspectRatio) => void;
  style: ImageStyle;
  onStyleChange: (style: ImageStyle) => void;
}

interface ImagePreviewProps {
  images: GeneratedImage[];
  currentIndex: number;
  onIndexChange: (index: number) => void;
  onDownload: (image: GeneratedImage) => void;
  onEdit: (editInstruction: string) => void;
  isLoading: boolean;
}
```

## 错误处理

### 1. 错误分类和处理策略

#### 网络错误处理
```typescript
class NetworkErrorHandler {
  static handle(error: NetworkError, language: string): UserFriendlyError {
    switch (error.type) {
      case 'timeout':
        return {
          message: language === 'en' ? 'Request timeout, please try again' : 'AI请求超时，请稍后重试',
          action: 'retry',
          retryDelay: 5000
        };
        
      case 'connection':
        return {
          message: language === 'en' ? 'Network connection failed' : '网络连接失败，请检查网络后重试',
          action: 'check_network',
          retryDelay: 10000
        };
        
      case 'server_error':
        return {
          message: language === 'en' ? 'Server error, please try again later' : '服务器错误，请稍后重试',
          action: 'retry_later',
          retryDelay: 30000
        };
    }
  }
}
```

#### AI服务错误处理
```typescript
class AIServiceErrorHandler {
  static handle(error: AIServiceError, language: string): UserFriendlyError {
    if (error instanceof PolicyViolationError) {
      return {
        message: error.message,
        suggestions: error.suggestions,
        action: 'modify_prompt',
        severity: 'warning'
      };
    }
    
    if (error instanceof QuotaExceededError) {
      return {
        message: language === 'en' ? 'Daily usage limit reached' : '今日使用次数已达上限',
        action: 'upgrade_or_wait',
        nextAvailable: error.resetTime,
        severity: 'info'
      };
    }
    
    if (error instanceof ModelUnavailableError) {
      return {
        message: language === 'en' ? 'AI service temporarily unavailable' : 'AI服务暂时不可用',
        action: 'try_later',
        retryDelay: 60000,
        severity: 'error'
      };
    }
    
    return {
      message: language === 'en' ? 'Unknown error occurred' : '发生未知错误',
      action: 'contact_support',
      severity: 'error'
    };
  }
}
```

### 2. 重试机制设计

```typescript
class RetryManager {
  private static readonly RETRY_CONFIGS = {
    network_timeout: { maxRetries: 3, baseDelay: 1000, backoffMultiplier: 2 },
    server_error: { maxRetries: 2, baseDelay: 5000, backoffMultiplier: 1.5 },
    rate_limit: { maxRetries: 1, baseDelay: 10000, backoffMultiplier: 1 }
  };

  static async executeWithRetry<T>(
    operation: () => Promise<T>,
    errorType: keyof typeof RetryManager.RETRY_CONFIGS,
    onRetry?: (attempt: number, delay: number) => void
  ): Promise<T> {
    const config = this.RETRY_CONFIGS[errorType];
    let lastError: Error;

    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < config.maxRetries) {
          const delay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt);
          onRetry?.(attempt + 1, delay);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError!;
  }
}
```

## 测试策略

### 1. 单元测试

#### 核心服务测试
```typescript
describe('GeminiImageService', () => {
  describe('generateImage', () => {
    it('should generate image with valid prompt', async () => {
      const request: ImageGenerationRequest = {
        prompt: 'A blue wireless headphone on white background',
        amazonSpec: AmazonImageSpec.MAIN,
        aspectRatio: '1:1',
        style: 'studio',
        userTier: UserTier.FREE,
        language: 'en',
        sessionId: 'test-session'
      };

      const result = await geminiImageService.generateImage(request);
      
      expect(result.imageData).toBeDefined();
      expect(result.metadata.complianceCheck.isCompliant).toBe(true);
      expect(result.metadata.dimensions.width).toBeGreaterThanOrEqual(1000);
    });

    it('should handle policy violation gracefully', async () => {
      const request: ImageGenerationRequest = {
        prompt: 'A person using the product',
        amazonSpec: AmazonImageSpec.MAIN,
        aspectRatio: '1:1',
        style: 'lifestyle',
        userTier: UserTier.FREE,
        language: 'zh',
        sessionId: 'test-session'
      };

      await expect(geminiImageService.generateImage(request))
        .rejects.toThrow(PolicyViolationError);
    });
  });

  describe('editImage', () => {
    it('should edit image while preserving product', async () => {
      const request: ImageEditRequest = {
        baseImage: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        editInstruction: 'Change background to office environment',
        preserveProduct: true,
        language: 'en',
        sessionId: 'test-session'
      };

      const result = await geminiImageService.editImage(request);
      
      expect(result.imageData).toBeDefined();
      expect(result.prompt).toContain('preserve product');
    });
  });
});
```

### 2. 集成测试

#### API端点测试
```typescript
describe('Image Generation API', () => {
  it('should handle complete generation workflow', async () => {
    const response = await fetch('/api/ai/image/generate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        prompt: 'Professional product photo of wireless earbuds',
        amazonSpec: 'main',
        aspectRatio: '1:1',
        style: 'studio',
        language: 'en',
        userTier: 'free',
        sessionId: 'integration-test'
      })
    });

    expect(response.status).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data.imageData).toBeDefined();
    expect(data.data.metadata.complianceCheck.isCompliant).toBe(true);
  });
});
```

### 3. 端到端测试

#### 用户流程测试
```typescript
describe('Product Image Generator E2E', () => {
  it('should complete full image generation workflow', async () => {
    // 1. 加载组件
    render(<ProductImageGenerator />);
    
    // 2. 输入描述
    const promptInput = screen.getByPlaceholderText(/请描述您想要生成的产品图片/);
    fireEvent.change(promptInput, { 
      target: { value: '蓝牙耳机放在现代办公桌上，白色背景' } 
    });
    
    // 3. 选择规格
    const specSelect = screen.getByLabelText(/亚马逊图片规格/);
    fireEvent.change(specSelect, { target: { value: 'main' } });
    
    // 4. 点击生成
    const generateButton = screen.getByText(/生成图片/);
    fireEvent.click(generateButton);
    
    // 5. 等待生成完成
    await waitFor(() => {
      expect(screen.getByText(/图片生成成功/)).toBeInTheDocument();
    }, { timeout: 35000 });
    
    // 6. 验证结果显示
    const generatedImage = screen.getByAltText(/生成的产品图片/);
    expect(generatedImage).toBeInTheDocument();
    
    // 7. 测试下载功能
    const downloadButton = screen.getByText(/下载图片/);
    fireEvent.click(downloadButton);
    
    await waitFor(() => {
      expect(screen.getByText(/图片下载成功/)).toBeInTheDocument();
    });
  });
});
```

## 性能优化

### 1. 图片处理优化

#### 图片压缩和格式优化
```typescript
class ImageOptimizer {
  static async optimizeForAmazon(
    imageData: string, 
    spec: AmazonImageSpec
  ): Promise<OptimizedImage> {
    const specConfig = AMAZON_SPEC_CONFIGS[spec];
    
    // 1. 解码图片
    const img = await this.decodeImage(imageData);
    
    // 2. 调整尺寸
    const resized = await this.resizeImage(img, specConfig.dimensions.recommended);
    
    // 3. 优化质量
    const quality = spec === AmazonImageSpec.MAIN ? 95 : 85;
    const optimized = await this.compressImage(resized, quality);
    
    // 4. 格式转换
    const format = this.selectOptimalFormat(optimized, specConfig.fileFormats);
    const final = await this.convertFormat(optimized, format);
    
    return {
      imageData: final.dataUrl,
      format: final.format,
      fileSize: final.fileSize,
      dimensions: final.dimensions,
      compressionRatio: img.fileSize / final.fileSize
    };
  }

  private static async resizeImage(
    img: ImageData, 
    targetDimensions: [number, number]
  ): Promise<ImageData> {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;
    
    canvas.width = targetDimensions[0];
    canvas.height = targetDimensions[1];
    
    // 使用高质量缩放算法
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';
    
    ctx.drawImage(img.element, 0, 0, canvas.width, canvas.height);
    
    return {
      element: canvas,
      width: canvas.width,
      height: canvas.height,
      dataUrl: canvas.toDataURL('image/png'),
      fileSize: this.calculateFileSize(canvas.toDataURL('image/png'))
    };
  }
}
```

### 2. 缓存策略

#### 多层缓存设计
```typescript
class ImageCacheManager {
  private memoryCache = new Map<string, CachedImage>();
  private readonly CACHE_SIZE_LIMIT = 50 * 1024 * 1024; // 50MB
  private readonly CACHE_TTL = 24 * 60 * 60 * 1000; // 24小时

  async get(cacheKey: string): Promise<CachedImage | null> {
    // 1. 检查内存缓存
    const memoryResult = this.memoryCache.get(cacheKey);
    if (memoryResult && !this.isExpired(memoryResult)) {
      return memoryResult;
    }

    // 2. 检查本地存储缓存
    const localResult = await this.getFromLocalStorage(cacheKey);
    if (localResult && !this.isExpired(localResult)) {
      // 回写到内存缓存
      this.memoryCache.set(cacheKey, localResult);
      return localResult;
    }

    return null;
  }

  async set(cacheKey: string, image: GeneratedImage): Promise<void> {
    const cachedImage: CachedImage = {
      ...image,
      cachedAt: Date.now(),
      accessCount: 0
    };

    // 1. 存储到内存缓存
    this.memoryCache.set(cacheKey, cachedImage);
    this.enforceMemoryLimit();

    // 2. 存储到本地存储
    await this.setToLocalStorage(cacheKey, cachedImage);
  }

  private generateCacheKey(request: ImageGenerationRequest): string {
    const keyData = {
      prompt: request.prompt,
      amazonSpec: request.amazonSpec,
      aspectRatio: request.aspectRatio,
      style: request.style,
      baseImageHash: request.baseImage ? this.hashImage(request.baseImage) : null
    };
    
    return btoa(JSON.stringify(keyData)).replace(/[+/=]/g, '');
  }

  private enforceMemoryLimit(): void {
    let totalSize = 0;
    const entries = Array.from(this.memoryCache.entries());
    
    // 计算总大小
    entries.forEach(([_, image]) => {
      totalSize += image.metadata.fileSize;
    });

    // 如果超出限制，按LRU策略清理
    if (totalSize > this.CACHE_SIZE_LIMIT) {
      entries
        .sort((a, b) => a[1].cachedAt - b[1].cachedAt)
        .slice(0, Math.floor(entries.length * 0.3))
        .forEach(([key]) => this.memoryCache.delete(key));
    }
  }
}
```

### 3. 请求优化

#### 请求去重和批处理
```typescript
class RequestOptimizer {
  private pendingRequests = new Map<string, Promise<ImageGenerationResult>>();
  private requestQueue: QueuedRequest[] = [];
  private isProcessing = false;

  async generateImage(request: ImageGenerationRequest): Promise<ImageGenerationResult> {
    const requestKey = this.generateRequestKey(request);
    
    // 检查是否有相同的请求正在处理
    const pendingRequest = this.pendingRequests.get(requestKey);
    if (pendingRequest) {
      return pendingRequest;
    }

    // 创建新的请求Promise
    const requestPromise = this.executeRequest(request);
    this.pendingRequests.set(requestKey, requestPromise);

    try {
      const result = await requestPromise;
      return result;
    } finally {
      this.pendingRequests.delete(requestKey);
    }
  }

  private async executeRequest(request: ImageGenerationRequest): Promise<ImageGenerationResult> {
    // 添加到队列
    return new Promise((resolve, reject) => {
      this.requestQueue.push({
        request,
        resolve,
        reject,
        timestamp: Date.now()
      });

      this.processQueue();
    });
  }

  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.requestQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      while (this.requestQueue.length > 0) {
        const queuedRequest = this.requestQueue.shift()!;
        
        try {
          const result = await geminiImageService.generateImage(queuedRequest.request);
          queuedRequest.resolve(result);
        } catch (error) {
          queuedRequest.reject(error);
        }

        // 添加延迟避免API限制
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } finally {
      this.isProcessing = false;
    }
  }
}
```

这个设计文档涵盖了AI产品图片生成器的完整技术架构，包括组件设计、数据模型、API接口、错误处理、测试策略和性能优化。设计遵循了现有项目的架构模式，并充分考虑了Gemini 2.0 Flash图片生成的特性和限制。