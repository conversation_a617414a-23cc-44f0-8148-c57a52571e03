#!/bin/bash

# Turnstile 配置验证脚本
# 检查所有必需的配置是否正确设置

echo "🔐 Cloudflare Turnstile 配置检查"
echo "================================="

# 检查前端配置
echo "📱 检查前端配置..."
if [ -f ".env.local" ]; then
    if grep -q "VITE_TURNSTILE_SITE_KEY=" .env.local; then
        SITE_KEY=$(grep "VITE_TURNSTILE_SITE_KEY=" .env.local | cut -d'=' -f2)
        if [[ $SITE_KEY == "0x4AAA"* ]] && [[ $SITE_KEY != *"your_real"* ]]; then
            echo "✅ 前端 Site Key 配置正确: ${SITE_KEY:0:20}..."
        else
            echo "❌ 前端 Site Key 需要更新为真实密钥"
            echo "   当前值: $SITE_KEY"
        fi
    else
        echo "❌ 缺少 VITE_TURNSTILE_SITE_KEY 配置"
    fi
else
    echo "❌ 未找到 .env.local 文件"
fi

echo ""

# 检查后端配置
echo "⚡ 检查后端配置..."
if [ -f "sellerbox-api/wrangler.toml" ]; then
    if grep -q "TURNSTILE_SECRET_KEY" sellerbox-api/wrangler.toml; then
        SECRET_KEY=$(grep "TURNSTILE_SECRET_KEY" sellerbox-api/wrangler.toml | cut -d'"' -f2)
        if [[ $SECRET_KEY == "0x4AAA"* ]] && [[ $SECRET_KEY != *"your_real"* ]]; then
            echo "✅ 后端 Secret Key 配置正确: ${SECRET_KEY:0:20}..."
        else
            echo "❌ 后端 Secret Key 需要更新为真实密钥"
            echo "   当前值: $SECRET_KEY"
        fi
    else
        echo "❌ 缺少 TURNSTILE_SECRET_KEY 配置"
    fi
else
    echo "❌ 未找到 sellerbox-api/wrangler.toml 文件"
fi

echo ""
echo "📋 配置步骤提醒:"
echo "1. 访问 https://dash.cloudflare.com/turnstile"
echo "2. 创建新的 Turnstile 应用"
echo "3. 将 Site Key 更新到 .env.local"
echo "4. 将 Secret Key 更新到 sellerbox-api/wrangler.toml"
echo "5. 运行 ./deploy.sh 部署更新"
echo ""