/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        // 亚马逊官方色彩系统
        'amazon': {
          'orange': '#FF9900',
          'orange-dark': '#E6890A',
          'orange-light': '#FFF5E6',
          'orange-border': '#FFD699',
          'squid-ink': '#232F3E',
          'black': '#000000',
          'white': '#FFFFFF',
          'gray-light': '#F8F9FA',
          'gray-border': '#E5E5E5',
        },
        // 保持原有的橙色系以兼容现有代码
        'orange': {
          50: '#fff7ed',
          100: '#ffedd5',
          200: '#fed7aa',
          300: '#fdba74',
          400: '#fb923c',
          500: '#f97316',
          600: '#ea580c',
          700: '#c2410c',
          800: '#9a3412',
          900: '#7c2d12',
        },
      },
      fontFamily: {
        'amazon': ['Amazon Ember', 'Helvetica Neue', 'Arial', 'sans-serif'],
      },
      boxShadow: {
        'amazon-sm': '0 1px 2px 0 rgb(0 0 0 / 0.05)',
        'amazon-md': '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
        'amazon-lg': '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
        'amazon-xl': '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
      },
      borderRadius: {
        'amazon': '8px',
        'amazon-lg': '12px',
        'amazon-xl': '16px',
      },
      spacing: {
        'amazon-xs': '4px',
        'amazon-sm': '8px',
        'amazon-md': '16px',
        'amazon-lg': '24px',
        'amazon-xl': '32px',
      },
    },
  },
  plugins: [],
};
